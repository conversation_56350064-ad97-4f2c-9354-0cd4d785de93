# 公开API - 同步模式说明

## 📋 接口变更说明

为了简化API使用和提高稳定性，公开API文件解析接口已调整为**仅支持同步模式**。

### 🔄 主要变更

#### 1. 文件解析接口简化
**接口路径**: `POST /api/v1/public/files/parse`

**修改前**:
```
参数:
- agentId: Agent标识符
- file: 上传的文件
- async: 是否异步处理（默认true）
- callbackUrl: 异步模式下的回调地址
- priority: 任务优先级
```

**修改后**:
```
参数:
- agentId: Agent标识符
- file: 上传的文件
```

#### 2. 移除异步状态查询接口
- ~~`GET /api/v1/public/files/parse/status/{taskId}`~~ （已屏蔽）

### 🚀 当前可用接口

#### 1. 获取Agent列表
```
GET /api/v1/public/agents
```
**参数**:
- `page`: 页码（默认1）
- `size`: 每页数量（默认10）
- `agentId`: 指定Agent ID（可选）

**响应示例**:
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "agents": [
      {
        "agentId": "test_agent_001",
        "agentName": "测试Agent",
        "description": "用于测试的Agent",
        "status": 1
      }
    ],
    "pagination": {
      "current": 1,
      "size": 10,
      "total": 1,
      "pages": 1
    }
  }
}
```

#### 2. 文件解析接口（同步模式）
```
POST /api/v1/public/files/parse
```
**参数**:
- `agentId`: Agent标识符（必需）
- `file`: 要解析的文件（必需）

**支持的文件格式**:
- PDF: `.pdf`
- 图片: `.jpg`, `.jpeg`, `.png`, `.gif`

**响应示例**:
```json
{
  "code": 200,
  "message": "文件解析成功",
  "data": {
    "parseResult": {
      "documentType": "invoice",
      "extractedData": {
        "invoiceNumber": "INV-2024-001",
        "date": "2024-01-15",
        "amount": "1000.00",
        "currency": "CNY"
      }
    },
    "processingTime": "2.5s",
    "confidence": 0.95
  }
}
```

### 🔐 认证方式

所有公开API接口都需要API Key认证：

**请求头方式（推荐）**:
```
X-API-Key: ak_test001.sk_test123456789abcdef123456789abcdef123456789
```

**Authorization方式**:
```
Authorization: Bearer ak_test001.sk_test123456789abcdef123456789abcdef123456789
```

**查询参数方式**:
```
?api_key=ak_test001.sk_test123456789abcdef123456789abcdef123456789
```

### 🔑 测试API Key

```
开发测试: ak_test001.sk_test123456789abcdef123456789abcdef123456789
演示用途: ak_test002.sk_demo987654321fedcba987654321fedcba987654321
受限测试: ak_demo001.sk_limited_key_for_testing_only_12345678
```

### 📝 使用示例

#### curl示例
```bash
# 1. 获取Agent列表
curl -H "X-API-Key: ak_test001.sk_test123456789abcdef123456789abcdef123456789" \
     http://localhost:8080/api/v1/public/agents

# 2. 文件解析（同步）
curl -H "X-API-Key: ak_test001.sk_test123456789abcdef123456789abcdef123456789" \
     -F "agentId=test_agent_001" \
     -F "file=@document.pdf" \
     http://localhost:8080/api/v1/public/files/parse
```

#### JavaScript示例
```javascript
// 文件解析
const formData = new FormData();
formData.append('agentId', 'test_agent_001');
formData.append('file', fileInput.files[0]);

fetch('/api/v1/public/files/parse', {
  method: 'POST',
  headers: {
    'X-API-Key': 'ak_test001.sk_test123456789abcdef123456789abcdef123456789'
  },
  body: formData
})
.then(response => response.json())
.then(data => {
  console.log('解析结果:', data);
});
```

### ⚠️ 注意事项

1. **文件大小限制**: 最大5MB
2. **处理时间**: 同步模式下，大文件可能需要较长处理时间
3. **超时设置**: 建议客户端设置适当的超时时间（如30秒）
4. **错误处理**: 请妥善处理可能的超时和错误响应

### 🔄 未来计划

异步模式功能将在后续版本中重新启用，届时会提供：
- 异步任务提交
- 任务状态查询
- 回调通知机制
- 批量文件处理

### 📞 技术支持

如有问题请联系：
- 邮箱：<EMAIL>
- 文档：https://docs.sinoair-agent.com
