# SinoairAgent - Agent审核功能PRD

## 1. 文档信息

| 文档信息 | 描述 |
|---------|------|
| 文档名称 | SinoairAgent - Agent审核功能PRD |
| 版本号   | V2.0.0 |
| 作者     | SinoairAgent团队 |
| 创建日期 | 2024-06-24 |
| 最后更新 | 2024-06-25 |
| 状态     | 已确认 |

## 2. 修订历史

| 版本号 | 修订日期 | 修订人 | 修订描述 |
|-------|---------|-------|---------|
| V1.0.0 | 2024-06-24 | SinoairAgent团队 | 初始版本 |
| V2.0.0 | 2024-06-25 | SinoairAgent团队 | 根据功能需求文档更新，明确审批流程和状态流转 |

## 3. 功能概述

### 3.1 背景

SinoairAgent平台允许客户创建和申请发布自定义Agent，为确保平台上的Agent质量和安全性，需要开发一个审核功能，由管理员对客户申请发布的Agent进行审核。

### 3.2 目标

开发一个完整的Agent审核功能，包括审核列表、详情查看和审批操作，确保平台上发布的Agent符合质量标准和安全要求。

### 3.3 用户角色

- **管理员**：负责审核客户申请发布的Agent，具有审批权限
- **客户**：申请发布Agent的用户，可查看审批结果和历史记录

### 3.4 审批流程

#### 3.4.1 状态流转
Agent审批状态流转如下：
- **申请发布** → **审核中** → **审核通过** / **审核不通过**

#### 3.4.2 审批规则
- 一级审批：只需一个管理员审批通过即可
- 审批历史：记录所有审批操作的历史记录
- 通知机制：审批完成后通过站内消息通知申请人

#### 3.4.3 权限控制
- 只有管理员角色可以进行审批操作
- 审批权限通过菜单权限控制

## 4. 功能需求

### 4.1 Agent审核列表

#### 4.1.1 功能描述

提供一个专门的页面，展示所有待审核和已审核的Agent列表，支持多种查询条件筛选。

#### 4.1.2 页面元素

1. **查询条件区域**：
   - Agent名称：文本输入框
   - Agent编码：文本输入框
   - 状态：下拉选择（全部、审批中、审批通过、审批不通过）
   - 分类：下拉选择（从Agent分类表获取）
   - 申请时间范围：日期选择器
   - 查询按钮
   - 重置按钮

2. **数据表格**：
   - Agent名称
   - 编码
   - 描述
   - 状态（审批中，审批通过，审批不通过）
   - 分类
   - 模型信息（展示使用的大模型）
   - 用途（使用场景介绍）
   - API接口（是/否）
   - Chrome插件支持（是/否）
   - 订阅数量
   - 申请时间
   - 操作按钮（详情，审批）

3. **分页控件**：
   - 页码导航
   - 每页显示条数选择

#### 4.1.3 交互说明

- 点击"详情"按钮：跳转到Agent详情页面
- 点击"审批"按钮：跳转到Agent审批页面
- 表格支持排序和筛选
- 表格支持导出Excel功能

### 4.2 Agent审批页面

#### 4.2.1 功能描述

提供详细的Agent信息展示和审批操作界面，帮助管理员全面了解Agent并做出审批决定。

#### 4.2.2 页面元素

1. **Agent基本信息区域**：
   - Agent名称
   - Agent编码
   - 分类
   - 描述
   - 创建者
   - 创建时间
   - 当前版本号
   - 升级内容（如果是版本更新）

2. **Agent配置信息区域**：
   - 使用的大模型
   - 模型参数配置
   - 提示词模板
   - JSON输出模板

3. **使用情况统计区域**：
   - 调用总次数
   - 成功率
   - 平均响应时间
   - 订阅用户数量
   - 最近一周调用趋势图

4. **使用场景展示区域**：
   - 使用场景描述
   - 适用业务类型
   - 目标用户群体

5. **API接口文档区域**：
   - 接口URL
   - 请求参数说明
   - 响应格式说明
   - 示例请求和响应JSON

6. **Chrome插件支持区域**：
   - 支持的网站列表
   - 插件功能说明
   - 效果截图或录播图展示

7. **调用历史记录区域**：
   - 最近10次调用记录表格
   - 调用时间
   - 调用参数
   - 响应结果
   - 响应时间
   - 调用状态

8. **审批操作区域**：
   - 审批意见文本框
   - 审批通过按钮
   - 审批不通过按钮
   - 返回按钮

#### 4.2.3 交互说明

- 点击"审批通过"按钮：
  - 弹出确认对话框
  - 确认后更新Agent状态为"审批通过"
  - 记录审批意见和审批人
  - 返回审核列表页面

- 点击"审批不通过"按钮：
  - 弹出确认对话框
  - 确认后更新Agent状态为"审批不通过"
  - 记录审批意见和审批人
  - 返回审核列表页面

- 点击"返回"按钮：
  - 返回审核列表页面，不做任何状态更改

### 4.3 Agent详情页面

#### 4.3.1 功能描述

展示Agent的详细信息，与审批页面类似，但不包含审批操作区域。

#### 4.3.2 页面元素

与审批页面的1-7项相同，不包含审批操作区域。

#### 4.3.3 交互说明

- 点击"返回"按钮：返回审核列表页面

## 5. 数据模型

### 5.1 数据库表设计

#### 5.1.1 Agent审批记录表（agent_approval_records）

| 字段名 | 类型 | 描述 |
|-------|------|------|
| id | BIGINT | 主键ID |
| agent_id | BIGINT | Agent ID |
| agent_version_id | BIGINT | Agent版本ID |
| approval_status | TINYINT | 审批状态：1-审批中，2-审批通过，3-审批不通过 |
| approval_opinion | TEXT | 审批意见 |
| approver_id | BIGINT | 审批人ID |
| approver_name | VARCHAR(100) | 审批人姓名 |
| approval_time | DATETIME | 审批时间 |
| created_time | DATETIME | 创建时间 |
| updated_time | DATETIME | 更新时间 |
| deleted | TINYINT | 删除标记：0-未删除，1-已删除 |

#### 5.1.2 站内消息表（sys_messages）

新增站内消息表用于通知用户审批结果：

| 字段名 | 类型 | 描述 |
|-------|------|------|
| id | BIGINT | 主键ID |
| user_id | BIGINT | 接收用户ID |
| title | VARCHAR(200) | 消息标题 |
| content | TEXT | 消息内容 |
| message_type | TINYINT | 消息类型：1-系统通知，2-审批通知，3-其他 |
| related_id | BIGINT | 关联业务ID（如Agent ID） |
| related_type | VARCHAR(50) | 关联业务类型（如AGENT_APPROVAL） |
| is_read | TINYINT | 是否已读：0-未读，1-已读 |
| created_time | DATETIME | 创建时间 |
| updated_time | DATETIME | 更新时间 |
| deleted | TINYINT | 删除标记：0-未删除，1-已删除 |

#### 5.1.3 Agent表扩展（agents）

在现有Agent表中添加以下字段：

| 字段名 | 类型 | 描述 |
|-------|------|------|
| approval_status | TINYINT | 审批状态：0-未提交，1-审批中，2-审批通过，3-审批不通过 |
| submit_time | DATETIME | 提交审批时间 |
| last_approval_id | BIGINT | 最近一次审批记录ID |

## 6. 接口设计

### 6.1 获取Agent审核列表

- **URL**: `/api/v1/agent-approval/list`
- **方法**: GET
- **参数**:
  - agentName: String (可选)
  - agentCode: String (可选)
  - status: Integer (可选)
  - categoryId: Long (可选)
  - startTime: String (可选)
  - endTime: String (可选)
  - current: Integer (默认1)
  - size: Integer (默认10)
- **响应**:
  ```json
  {
    "code": 200,
    "message": "操作成功",
    "data": {
      "records": [
        {
          "id": 1,
          "agentName": "订单识别Agent",
          "agentCode": "ORDER_RECOGNITION",
          "description": "识别订单文档并提取关键信息",
          "approvalStatus": 1,
          "categoryName": "文档识别",
          "modelInfo": "千问大模型",
          "purpose": "用于自动识别订单文档中的发货人、收货人等信息",
          "hasApiInterface": true,
          "hasChromePlugin": true,
          "subscriptionCount": 120,
          "submitTime": "2024-06-20 10:00:00"
        }
      ],
      "total": 100,
      "size": 10,
      "current": 1,
      "pages": 10
    }
  }
  ```

### 6.2 获取Agent审批详情

- **URL**: `/api/v1/agent-approval/detail/{agentId}`
- **方法**: GET
- **参数**:
  - agentId: Long (路径参数)
- **响应**:
  ```json
  {
    "code": 200,
    "message": "操作成功",
    "data": {
      "basicInfo": {
        "id": 1,
        "agentName": "订单识别Agent",
        "agentCode": "ORDER_RECOGNITION",
        "categoryName": "文档识别",
        "description": "识别订单文档并提取关键信息",
        "creatorName": "张三",
        "createTime": "2024-06-15 10:00:00",
        "version": "1.0.0",
        "upgradeContent": "首次提交"
      },
      "configInfo": {
        "modelName": "千问大模型",
        "modelConfig": "{\"temperature\":0.7,\"max_tokens\":2000}",
        "promptTemplate": "你是一个专业的订单识别助手...",
        "jsonTemplate": "{\"sender\":{},\"receiver\":{},\"goods\":{}}"
      },
      "usageStats": {
        "totalCalls": 1500,
        "successRate": 98.5,
        "avgResponseTime": 2.3,
        "subscriptionCount": 120,
        "weeklyTrend": [120, 130, 125, 140, 150, 145, 160]
      },
      "usageScenarios": {
        "description": "适用于识别各类订单文档...",
        "businessTypes": ["国际空运", "国内空运"],
        "targetUsers": ["货代操作人员", "客服人员"]
      },
      "apiInterface": {
        "url": "/api/v1/agent-call/recognize/ORDER_RECOGNITION",
        "requestParams": "文件参数: file\n业务参数: businessParams (可选)",
        "responseFormat": "JSON格式，包含发货人、收货人、货物信息等",
        "requestExample": "curl -X POST /api/v1/agent-call/recognize/ORDER_RECOGNITION -F \"file=@order.pdf\"",
        "responseExample": "{\"sender\":{\"name\":\"ABC公司\",\"address\":\"上海市浦东新区\"},\"receiver\":{...}}"
      },
      "chromePlugin": {
        "supportedSites": ["www.example.com", "order.sample.com"],
        "features": ["文档识别", "表单自动填写"],
        "screenshots": ["https://storage.example.com/screenshots/1.png", "https://storage.example.com/screenshots/2.png"]
      },
      "callHistory": [
        {
          "id": 1,
          "callTime": "2024-06-23 15:30:00",
          "params": "{\"file\":\"order123.pdf\"}",
          "response": "{\"sender\":{\"name\":\"ABC公司\",...}}",
          "responseTime": 2.1,
          "status": "成功"
        }
      ]
    }
  }
  ```

### 6.3 提交审批结果

- **URL**: `/api/v1/agent-approval/approve`
- **方法**: POST
- **参数**:
  ```json
  {
    "agentId": 1,
    "approved": true,
    "opinion": "Agent功能完善，符合平台要求"
  }
  ```
- **响应**:
  ```json
  {
    "code": 200,
    "message": "审批成功",
    "data": null
  }
  ```

## 7. 页面原型

### 7.1 Agent审核列表页面

[此处应有页面原型图]

### 7.2 Agent审批页面

[此处应有页面原型图]

### 7.3 Agent详情页面

[此处应有页面原型图]

## 8. 非功能需求

### 8.1 性能需求

- 列表页面加载时间不超过2秒
- 详情页面加载时间不超过3秒
- 审批操作响应时间不超过1秒

### 8.2 安全需求

- 只有具有审批权限的管理员可以访问审核功能
- 所有审批操作需记录操作日志
- 敏感配置信息（如API密钥）需脱敏显示

### 8.3 兼容性需求

- 支持Chrome、Firefox、Edge最新版本
- 支持PC端Web访问
- 页面自适应不同分辨率

## 9. 实现计划

### 9.1 开发阶段

1. 数据库设计与实现
2. 后端API开发
3. 前端页面开发
4. 集成测试
5. 用户验收测试

### 9.2 预计时间线

- 需求分析与设计：1周
- 后端开发：2周
- 前端开发：2周
- 测试与修复：1周
- 上线准备：1周

## 10. 附录

### 10.1 术语表

- **Agent**: 智能助手，用于文档识别和信息提取
- **审批中**: Agent已提交但尚未审核完成的状态
- **审批通过**: Agent审核通过，可以正式发布使用
- **审批不通过**: Agent审核未通过，需要修改后重新提交

### 10.2 参考文档

- SinoairAgent系统架构文档
- Agent管理功能说明文档
- 用户权限管理规范
