# 审核Agent功能需求文档

## 功能概述
开发一个审核Agent的功能，用于管理员审核客户申请发布的Agent。

## 需要补充的信息

1. **审批流程详情**：
   - 审批流程是否需要多级审批？ 回答：不用，只有一级审批，一个管理员审批通过即可；
   - 是否需要审批历史记录？ 回答：需要；记录审批历史；
   - 审批后是否需要通知申请人？ 回答：需要，通过站内消息通知申请人；

2. **Agent状态流转**：
   - Agent从创建到发布的完整状态流转图 回答：该功能审批客户要发布的Agent；其他的不用展示和不用参与审批
   - 各状态的权限控制 回答：申请发布->审核中-》审核通过-》审核不通过

3. **审批指标**：
   - 审批需要关注哪些具体指标？(如安全性、合规性、性能等) 回答：审批管理员会详细了解客户申请发布的Agent，并给出相应的审核结果
   - 是否有审批标准或评分系统？ 回答：无

4. **数据模型扩展**：
   - 现有Agent模型是否需要扩展字段来支持审批流程？ 回答：不需要，无
   - 是否需要新增审批记录表？ 回答：需要

5. **权限设计**：
   - 哪些角色可以进行审批操作？ 回答：管理员
   - 审批权限的分配方式？ 回答：管理员角色，有权限控制菜单，这部分可以不用考虑；

6. **统计与报表**：
   - 是否需要审批相关的统计数据？ 回答：暂不需要
   - 是否需要审批效率的报表？ 回答：暂不需要

## 已明确的需求

1. **菜单与页面**：
   - 单独的审核Agent菜单
   - 查询条件
   - 申请发布Agent数据表格展示

2. **表格字段**：
   - Agent名称
   - 编码
   - 描述
   - 状态（审批中，审批通过，审批不通过）
   - 分类
   - 模型信息（展示xx大模型）
   - 用途（使用场景介绍）
   - API接口
   - Chrome插件效果
   - 订阅数量
   - 操作按钮（详情，审批）

3. **操作功能**：
   - 详情：跳转到Agent详情页面
   - 审批：查看Agent信息并进行审批

4. **审批页面内容**：
   - Agent常见信息
   - 使用情况
   - 当前版本号
   - 升级内容
   - 使用场景
   - Agent调用历史记录
   - API调用请求和响应文档
   - Chrome插件支持的网站
   - 效果图（录播图）
   - 审批原因输入
   - 审核通过/不通过按钮

5. **UI要求**：
   - 与当前系统风格保持一致
