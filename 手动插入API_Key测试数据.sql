-- 手动插入API Key测试数据
-- 如果自动迁移没有执行，可以手动运行这个脚本

-- 检查表是否存在
SELECT COUNT(*) as table_exists FROM information_schema.tables 
WHERE table_schema = DATABASE() AND table_name = 'api_keys';

-- 清理可能存在的测试数据
DELETE FROM api_keys WHERE key_id IN ('ak_test001', 'ak_test002', 'ak_demo001');

-- 插入测试API Key数据
INSERT INTO api_keys (
    key_id, 
    key_secret, 
    key_name, 
    description, 
    user_id, 
    status, 
    rate_limit, 
    daily_limit, 
    monthly_limit, 
    expires_at, 
    created_time, 
    updated_time
) VALUES 
-- 开发测试API Key
-- 完整API Key: ak_test001.sk_test123456789abcdef123456789abcdef123456789
(
    'ak_test001',
    '72b53c1febcf1755f39a299e1789d0429608b685a8aecca52f9d1527c3955168',
    '开发测试API Key',
    '用于开发环境测试的API Key',
    1, -- admin用户
    1, -- 启用
    1000, -- 每小时1000次
    10000, -- 每日10000次
    100000, -- 每月100000次
    DATE_ADD(NOW(), INTERVAL 1 YEAR),
    NOW(),
    NOW()
),

-- 演示API Key
-- 完整API Key: ak_test002.sk_demo987654321fedcba987654321fedcba987654321
(
    'ak_test002',
    'c5f9b283b1c3839ac30ba32e3a4de38da36e7d37ac9f3c3d1625fe2bebf30b4a',
    '演示API Key',
    '用于产品演示的API Key',
    1, -- admin用户
    1, -- 启用
    500, -- 每小时500次
    5000, -- 每日5000次
    50000, -- 每月50000次
    DATE_ADD(NOW(), INTERVAL 6 MONTH),
    NOW(),
    NOW()
),

-- 受限API Key
-- 完整API Key: ak_demo001.sk_limited_key_for_testing_only_12345678
(
    'ak_demo001',
    '6b7a5c4ca45dd44818754523fb8836fe964abb8a5cb66cdaedf9afe8a4ca6c61',
    '受限演示API Key',
    '用于受限环境演示的API Key',
    1, -- admin用户
    1, -- 启用
    100, -- 每小时100次
    1000, -- 每日1000次
    10000, -- 每月10000次
    DATE_ADD(NOW(), INTERVAL 3 MONTH),
    NOW(),
    NOW()
);

-- 验证插入结果
SELECT 
    key_id, 
    key_name, 
    status, 
    rate_limit, 
    daily_limit, 
    monthly_limit,
    expires_at,
    created_time
FROM api_keys 
WHERE key_id IN ('ak_test001', 'ak_test002', 'ak_demo001')
ORDER BY key_id;
