# API Key 测试数据

## 📋 测试API Key列表

我已经为您在数据库中插入了3个测试用的API Key，可以直接使用：

### 1. 开发测试API Key
- **完整API Key**: `ak_test001.sk_test123456789abcdef123456789abcdef123456789`
- **描述**: 用于开发环境测试，具有较高的调用限制
- **状态**: ✅ 启用
- **限制**: 每小时1000次，每日10000次，每月100000次
- **过期时间**: 1年后

### 2. 演示API Key  
- **完整API Key**: `ak_test002.sk_demo987654321fedcba987654321fedcba987654321`
- **描述**: 用于产品演示，具有中等调用限制
- **状态**: ✅ 启用
- **限制**: 每小时500次，每日5000次，每月50000次
- **过期时间**: 6个月后

### 3. 受限演示API Key
- **完整API Key**: `ak_demo001.sk_limited_key_for_testing_only_12345678`
- **描述**: 用于受限环境演示，具有较低的调用限制
- **状态**: ✅ 启用
- **限制**: 每小时100次，每日1000次，每月10000次
- **过期时间**: 3个月后

## 🚀 快速测试

### 使用curl测试
```bash
# 测试获取Agent列表
curl -H "X-API-Key: ak_test001.sk_test123456789abcdef123456789abcdef123456789" \
     http://localhost:8080/api/v1/public/agents

# 测试文件解析（需要上传文件）
curl -H "X-API-Key: ak_test001.sk_test123456789abcdef123456789abcdef123456789" \
     -F "agentId=test_agent" \
     -F "file=@your_file.pdf" \
     -F "async=true" \
     http://localhost:8080/api/v1/public/files/parse
```

### 在Postman中测试
1. 创建新请求
2. 设置URL: `http://localhost:8080/api/v1/public/agents`
3. 在Headers中添加: `X-API-Key: ak_test001.sk_test123456789abcdef123456789abcdef123456789`
4. 发送请求

## 📝 注意事项

1. **启动应用**: 确保应用已启动并执行了数据库迁移
2. **数据库**: 这些数据会在应用启动时自动插入到数据库中
3. **安全**: 这些是测试数据，生产环境请删除相关文件
4. **格式**: API Key格式为 `keyId.keySecret`

## 🔍 验证数据是否插入成功

启动应用后，可以通过以下SQL查询验证数据是否插入成功：

```sql
SELECT key_id, key_name, status, rate_limit, daily_limit, monthly_limit, expires_at
FROM api_keys
WHERE deleted = 0;
```

应该能看到3条记录：ak_test001、ak_test002、ak_demo001

## 🛠️ 手动插入数据

如果自动迁移没有执行，您可以：

1. **使用数据库客户端**（如Navicat、DBeaver等）连接到MySQL数据库
2. **执行SQL脚本**：运行 `手动插入API_Key测试数据.sql` 文件中的SQL语句
3. **验证插入结果**：查询api_keys表确认数据已插入

## 🚀 开始测试

现在您可以启动应用并使用这些API Key来测试公开API接口了！

访问Knife4j文档：http://localhost:8080/doc.html
