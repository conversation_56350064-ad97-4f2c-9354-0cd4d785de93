package com.sinoair.agent.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;

import javax.sql.DataSource;

/**
 * 数据库配置
 * 
 * <AUTHOR> Team
 */
@Configuration
public class DatabaseConfig {

    /**
     * 数据源事务管理器
     */
    @Bean
    @Primary
    @ConditionalOnMissingBean(PlatformTransactionManager.class)
    public PlatformTransactionManager dataSourceTransactionManager(DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }
}
