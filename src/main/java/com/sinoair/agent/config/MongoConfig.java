package com.sinoair.agent.config;

import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoClients;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.mongodb.config.AbstractMongoClientConfiguration;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.convert.DefaultMongoTypeMapper;
import org.springframework.data.mongodb.core.convert.MappingMongoConverter;
import org.springframework.data.mongodb.repository.config.EnableMongoRepositories;

/**
 * MongoDB配置类
 *
 * <AUTHOR> Team
 */
@Slf4j
@Configuration
@EnableMongoRepositories(basePackages = "com.sinoair.agent.repository")
public class MongoConfig extends AbstractMongoClientConfiguration {

    @Value("${spring.data.mongodb.host:localhost}")
    private String host;

    @Value("${spring.data.mongodb.port:27017}")
    private int port;

    @Value("${spring.data.mongodb.database:sinoair_agent_logs}")
    private String database;

    @Value("${spring.data.mongodb.username:}")
    private String username;

    @Value("${spring.data.mongodb.password:}")
    private String password;

    @Value("${spring.data.mongodb.authentication-database:admin}")
    private String authenticationDatabase;

    @Override
    protected String getDatabaseName() {
        return database;
    }

    @Override
    public MongoClient mongoClient() {
        String connectionString;
        
        if (username != null && !username.isEmpty() && password != null && !password.isEmpty()) {
            // 带认证的连接字符串
            connectionString = String.format("mongodb://%s:%s@%s:%d/%s?authSource=%s",
                    username, password, host, port, database, authenticationDatabase);
        } else {
            // 无认证的连接字符串
            connectionString = String.format("mongodb://%s:%d/%s", host, port, database);
        }
        
        log.info("MongoDB连接字符串: {}", connectionString.replaceAll(":[^:@]*@", ":***@"));
        
        try {
            MongoClient client = MongoClients.create(connectionString);
            log.info("MongoDB客户端创建成功");
            return client;
        } catch (Exception e) {
            log.error("MongoDB客户端创建失败", e);
            throw e;
        }
    }

    @Bean
    public MongoTemplate mongoTemplate() throws Exception {
        MongoTemplate mongoTemplate = new MongoTemplate(mongoClient(), getDatabaseName());
        
        // 移除_class字段
        MappingMongoConverter converter = (MappingMongoConverter) mongoTemplate.getConverter();
        converter.setTypeMapper(new DefaultMongoTypeMapper(null));
        
        log.info("MongoTemplate配置完成，数据库: {}", getDatabaseName());
        return mongoTemplate;
    }
}
