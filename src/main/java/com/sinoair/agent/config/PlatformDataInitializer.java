package com.sinoair.agent.config;

import com.sinoair.agent.entity.SysConfig;
import com.sinoair.agent.service.SysConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * 平台配置数据初始化器
 *
 * <AUTHOR> Team
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PlatformDataInitializer implements CommandLineRunner {

    private final SysConfigService sysConfigService;

    @Override
    public void run(String... args) throws Exception {
        initializePlatformConfigs();
    }

    private void initializePlatformConfigs() {
        try {
            // 检查是否已经有平台配置数据
            List<SysConfig> existingConfigs = sysConfigService.getByGroup("大模型平台配置");
            if (!existingConfigs.isEmpty()) {
                log.info("平台配置数据已存在，跳过初始化");
                return;
            }

            log.info("开始初始化平台配置数据...");

            // 创建平台配置数据
            List<SysConfig> platformConfigs = Arrays.asList(
                // 大模型平台配置组
                createConfig("大模型平台配置", "1", "内部大模型LLM", "内部大模型LLM平台"),
                createConfig("大模型平台配置", "2", "内部大模型VLM", "内部大模型VLM平台"),
                createConfig("大模型平台配置", "3", "外部大模型", "外部大模型平台"),

                // 内部大模型LLM的模型配置
                createConfig("内部大模型LLM", "qwen_72b_128k", "qwen_72b_128k", "通义千问72B 128K模型"),
                createConfig("内部大模型LLM", "qwen_72b_32k", "qwen_72b_32k", "通义千问72B 32K模型"),
                createConfig("内部大模型LLM", "deepseek_r1", "deepseek_r1", "DeepSeek R1模型"),
                createConfig("内部大模型LLM", "glm_130b_128k", "glm_130b_128k", "GLM 130B 128K模型"),
                createConfig("内部大模型LLM", "glm_130b_8k_0520", "glm_130b_8k_0520", "GLM 130B 8K 0520模型"),

                // 内部大模型VLM的模型配置
                createConfig("内部大模型VLM", "qwen_vl_72b", "qwen_vl_72b", "通义千问VL 72B模型"),
                createConfig("内部大模型VLM", "glm-4v-plus", "glm-4v-plus", "GLM-4V Plus模型"),

                // 外部大模型的模型配置
                createConfig("外部大模型", "qwq-plus", "qwq-plus", "QWQ Plus模型"),
                createConfig("外部大模型", "qwen-max", "qwen-max", "通义千问Max模型")
            );

            // 保存配置数据
            for (SysConfig config : platformConfigs) {
                sysConfigService.save(config);
            }

            log.info("平台配置数据初始化完成，共添加 {} 条配置", platformConfigs.size());

        } catch (Exception e) {
            log.error("平台配置数据初始化失败", e);
        }
    }

    private SysConfig createConfig(String configGroup, String configKey, String configValue, String description) {
        SysConfig config = new SysConfig();
        config.setConfigGroup(configGroup);
        config.setConfigKey(configKey);
        config.setConfigValue(configValue);
        config.setDescription(description);
        config.setIsEnabled(SysConfig.STATUS_ENABLED);
        return config;
    }
}
