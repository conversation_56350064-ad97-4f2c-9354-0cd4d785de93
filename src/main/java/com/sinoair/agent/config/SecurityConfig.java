package com.sinoair.agent.config;

import com.sinoair.agent.security.ApiKeyAuthenticationFilter;
import com.sinoair.agent.security.JwtAuthenticationEntryPoint;
import com.sinoair.agent.security.JwtAuthenticationFilter;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import java.util.Arrays;

/**
 * Spring Security配置
 * 
 * <AUTHOR> Team
 */
@Configuration
@EnableWebSecurity
@EnableMethodSecurity
@RequiredArgsConstructor
public class SecurityConfig {

    private final JwtAuthenticationEntryPoint jwtAuthenticationEntryPoint;
    private final JwtAuthenticationFilter jwtAuthenticationFilter;
    private final ApiKeyAuthenticationFilter apiKeyAuthenticationFilter;

    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    @Bean
    public AuthenticationManager authenticationManager(AuthenticationConfiguration config) throws Exception {
        return config.getAuthenticationManager();
    }

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http
            .csrf(AbstractHttpConfigurer::disable)
            .cors(cors -> cors.configurationSource(corsConfigurationSource()))
            .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
            .exceptionHandling(exception -> exception.authenticationEntryPoint(jwtAuthenticationEntryPoint))
            .authorizeHttpRequests(auth -> auth
                // 公开接口
                .requestMatchers(
                    "/api/v1/auth/**",
                    "/api/v1/system/health",
                    "/api/test/**",
                    "/s/**",
                    "/doc.html",
                    "/webjars/**",
                    "/swagger-resources/**",
                    "/v3/api-docs/**",
                    "/favicon.ico",
                    "/druid/**",
                    // 静态资源
                    "/",
                    "/index.html",
                    "/login.html",
                    "/register.html",
                    "/customer-portal.html",
                    "/customer-portal.js",
                    "/admin.html",
                    "/admin-main.html",
                    "/admin-simple.html",
                    "/admin.js",
                    "/admin-common.css",
                    "/admin-common.js",
                    "/admin-main.js",
                    "/test.html",
                    "/test-auth.html",
                    "/pages/**",
                    "/static/**",
                    "/assets/**",
                    "/css/**",
                    "/js/**",
                    "/images/**"
                ).permitAll()
                // 公开API接口 - 使用API Key认证
                .requestMatchers("/api/v1/public/**").authenticated()
                // 插件接口
                .requestMatchers("/api/v1/plugin/**").authenticated()
                // Agent调用接口
                .requestMatchers("/api/v1/agent-call/**").permitAll()
                .requestMatchers("/api/v1/agent-approval/**").permitAll()
                // Agent分类接口 - 公开访问
                .requestMatchers("/api/v1/agent-categories/**").permitAll()
                // 公开文件预览接口
                .requestMatchers("/api/v1/files/public/*/preview").permitAll()
                // 所有API接口只需要认证，不需要特定权限
                .requestMatchers("/api/v1/**").authenticated()
                // 其他接口需要认证
                .anyRequest().authenticated()
            )
            .addFilterBefore(apiKeyAuthenticationFilter, UsernamePasswordAuthenticationFilter.class)
            .addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class);

        return http.build();
    }

    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();
        configuration.setAllowedOriginPatterns(Arrays.asList("*"));
        configuration.setAllowedMethods(Arrays.asList("GET", "POST", "PUT", "DELETE", "OPTIONS"));
        configuration.setAllowedHeaders(Arrays.asList("*"));
        configuration.setAllowCredentials(true);
        configuration.setMaxAge(3600L);

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);
        return source;
    }
}
