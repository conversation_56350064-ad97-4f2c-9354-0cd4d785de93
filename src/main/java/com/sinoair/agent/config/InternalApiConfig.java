package com.sinoair.agent.config;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.servers.Server;
import org.springdoc.core.models.GroupedOpenApi;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * 内部管理API配置
 * 专门配置面向内部管理人员的API文档
 * 
 * <AUTHOR> Team
 */
@Configuration
public class InternalApiConfig {

    /**
     * 内部管理API分组配置
     * 使用JWT认证，面向内部管理人员
     */
    @Bean
    public GroupedOpenApi internalApiGroup() {
        return GroupedOpenApi.builder()
                .group("internal")
                .displayName("🔧 内部管理API")
                .pathsToMatch("/api/v1/**")
                .pathsToExclude("/api/v1/public/**")
                .addOpenApiCustomizer(openApi -> {
                    openApi.info(internalApiInfo())
                           .servers(List.of(
                               new Server().url("http://localhost:8080").description("🔧 本地开发环境"),
                               new Server().url("https://admin.sinoair-agent.com").description("🏢 管理后台")
                           ))
                           .components(new Components()
                               .addSecuritySchemes("Bearer", new SecurityScheme()
                                   .type(SecurityScheme.Type.HTTP)
                                   .scheme("bearer")
                                   .bearerFormat("JWT")
                                   .description("JWT认证Token")
                               )
                           )
                           .addSecurityItem(new SecurityRequirement().addList("Bearer"));
                })
                .build();
    }

    /**
     * 内部管理API信息配置
     */
    private Info internalApiInfo() {
        return new Info()
                .title("SinoairAgent 内部管理API")
                .description("""
                        # SinoairAgent 内部管理API文档
                        
                        ## 🏢 平台简介
                        SinoairAgent是一个基于大语言模型的智能文档识别与自动填写平台，专为航空货运业务设计。
                        
                        ## 🛠️ 内部管理功能
                        - **🤖 Agent管理**：创建、编辑、测试和发布AI Agent
                        - **👥 用户管理**：用户账号、角色、权限管理
                        - **⚙️ 系统配置**：平台参数配置和系统监控
                        - **📊 数据统计**：使用情况统计和分析报告
                        - **📁 文件管理**：上传文件的管理和维护
                        - **🔑 API Key管理**：客户API Key的创建和管理
                        
                        ## 🔐 认证方式
                        内部管理接口使用JWT Bearer Token进行认证：
                        ```
                        Authorization: Bearer <your-jwt-token>
                        ```
                        
                        ## 🚀 快速开始
                        1. 调用 `/api/v1/auth/login` 接口登录获取Token
                        2. 使用Token调用其他需要认证的接口
                        3. 查看各个模块的接口文档了解详细用法
                        
                        ## 👤 示例用户
                        - 管理员：admin / password123
                        - 普通用户：user1 / password123
                        
                        ## 🔧 开发环境
                        - 本地开发：http://localhost:8080
                        - 管理后台：https://admin.sinoair-agent.com
                        
                        ## 📞 技术支持
                        如有问题请联系内部技术支持团队。
                        - 邮箱：<EMAIL>
                        - 内部文档：https://internal-docs.sinoair-agent.com
                        """)
                .version("1.0.0")
                .contact(new Contact()
                        .name("SinoairAgent内部团队")
                        .email("<EMAIL>")
                        .url("https://admin.sinoair-agent.com")
                )
                .license(new License()
                        .name("Internal Use Only")
                        .url("https://www.sinoair-agent.com/internal-license")
                );
    }
}
