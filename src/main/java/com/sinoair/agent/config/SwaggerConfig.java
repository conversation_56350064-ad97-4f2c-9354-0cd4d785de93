package com.sinoair.agent.config;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.servers.Server;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * Swagger配置
 * 
 * <AUTHOR> Team
 */
@Configuration
public class SwaggerConfig {

    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(apiInfo())
                .servers(List.of(
                        new Server().url("http://localhost:8080").description("本地开发环境"),
                        new Server().url("https://api.sinoair-agent.com").description("生产环境")
                ))
                .components(new Components()
                        .addSecuritySchemes("Bearer", new SecurityScheme()
                                .type(SecurityScheme.Type.HTTP)
                                .scheme("bearer")
                                .bearerFormat("JWT")
                                .description("JWT认证Token")
                        )
                        .addSecuritySchemes("ApiKey", new SecurityScheme()
                                .type(SecurityScheme.Type.APIKEY)
                                .in(SecurityScheme.In.HEADER)
                                .name("X-API-Key")
                                .description("API Key认证，格式：keyId.keySecret")
                        )
                )
                .addSecurityItem(new SecurityRequirement().addList("Bearer"))
                .addSecurityItem(new SecurityRequirement().addList("ApiKey"));
    }

    private Info apiInfo() {
        return new Info()
                .title("SinoairAgent平台API文档")
                .description("""
                        # SinoairAgent平台API文档
                        
                        ## 平台简介
                        SinoairAgent是一个基于大语言模型的智能文档识别与自动填写平台，专为航空货运业务设计。
                        
                        ## 主要功能
                        - **Agent管理**：创建、编辑、测试和发布AI Agent
                        - **文档识别**：支持多种格式文档的智能识别和信息提取
                        - **自动回填**：将识别结果自动回填到目标网页
                        - **用户管理**：完整的用户认证和权限管理体系
                        - **文件管理**：安全的文件上传、存储和管理
                        
                        ## 认证方式
                        本API使用JWT Bearer Token进行认证。请先调用登录接口获取Token，然后在请求头中添加：
                        ```
                        Authorization: Bearer <your-token>
                        ```
                        
                        ## 快速开始
                        1. 调用 `/api/v1/auth/login` 接口登录获取Token
                        2. 使用Token调用其他需要认证的接口
                        3. 查看各个模块的接口文档了解详细用法
                        
                        ## 示例用户
                        - 管理员：admin / password123
                        - 普通用户：user1 / password123
                        
                        ## 技术支持
                        如有问题请联系技术支持团队。
                        """)
                .version("1.0.0")
                .contact(new Contact()
                        .name("SinoairAgent团队")
                        .email("<EMAIL>")
                        .url("https://www.sinoair-agent.com")
                )
                .license(new License()
                        .name("MIT License")
                        .url("https://opensource.org/licenses/MIT")
                );
    }
}
