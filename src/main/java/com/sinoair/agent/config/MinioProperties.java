package com.sinoair.agent.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * MinIO配置属性
 * 
 * <AUTHOR> Team
 */
@Data
@Component
@ConfigurationProperties(prefix = "app.minio")
public class MinioProperties {

    /**
     * MinIO服务端点
     */
    private String endpoint = "http://localhost:9000";

    /**
     * 访问密钥
     */
    private String accessKey = "minioadmin";

    /**
     * 秘密密钥
     */
    private String secretKey = "minioadmin";

    /**
     * 存储桶名称
     */
    private String bucketName = "sinoair-agent";

    /**
     * 区域
     */
    private String region = "us-east-1";

    /**
     * 是否启用MinIO存储
     */
    private boolean enabled = true;

    /**
     * 连接超时时间（毫秒）
     */
    private long connectTimeout = 10000;

    /**
     * 写入超时时间（毫秒）
     */
    private long writeTimeout = 60000;

    /**
     * 读取超时时间（毫秒）
     */
    private long readTimeout = 10000;
}
