package com.sinoair.agent.config;

import com.sinoair.agent.entity.OperationLog;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.index.Index;
import org.springframework.data.mongodb.core.index.IndexOperations;
import org.springframework.stereotype.Component;

/**
 * MongoDB初始化器
 * 用于创建集合和索引
 *
 * <AUTHOR> Team
 */
@Slf4j
// @Component
@Order(100)
@RequiredArgsConstructor
public class MongoInitializer implements CommandLineRunner {

    private final MongoTemplate mongoTemplate;

    @Override
    public void run(String... args) throws Exception {
        try {
            log.info("开始初始化MongoDB集合和索引...");
            
            // 创建操作日志集合
            createOperationLogCollection();
            
            // 创建索引
            createIndexes();
            
            log.info("MongoDB初始化完成");
        } catch (Exception e) {
            log.error("MongoDB初始化失败", e);
        }
    }

    /**
     * 创建操作日志集合
     */
    private void createOperationLogCollection() {
        try {
            String collectionName = "operation_logs";
            
            // 检查集合是否存在
            if (!mongoTemplate.collectionExists(collectionName)) {
                // 创建集合
                mongoTemplate.createCollection(collectionName);
                log.info("创建MongoDB集合: {}", collectionName);
            } else {
                log.info("MongoDB集合已存在: {}", collectionName);
            }
            
            // 测试插入一条记录来验证集合是否正常工作
            testInsertOperationLog();
            
        } catch (Exception e) {
            log.error("创建操作日志集合失败", e);
        }
    }

    /**
     * 创建索引
     */
    private void createIndexes() {
        try {
            IndexOperations indexOps = mongoTemplate.indexOps(OperationLog.class);
            
            // 为常用查询字段创建索引
            indexOps.ensureIndex(new Index().on("userId", org.springframework.data.domain.Sort.Direction.ASC)
                                           .on("operationTime", org.springframework.data.domain.Sort.Direction.DESC));
            
            indexOps.ensureIndex(new Index().on("module", org.springframework.data.domain.Sort.Direction.ASC)
                                           .on("operationTime", org.springframework.data.domain.Sort.Direction.DESC));
            
            indexOps.ensureIndex(new Index().on("operationType", org.springframework.data.domain.Sort.Direction.ASC)
                                           .on("operationTime", org.springframework.data.domain.Sort.Direction.DESC));
            
            indexOps.ensureIndex(new Index().on("operationTime", org.springframework.data.domain.Sort.Direction.DESC));
            
            indexOps.ensureIndex(new Index().on("status", org.springframework.data.domain.Sort.Direction.ASC)
                                           .on("operationTime", org.springframework.data.domain.Sort.Direction.DESC));
            
            indexOps.ensureIndex(new Index().on("clientIp", org.springframework.data.domain.Sort.Direction.ASC)
                                           .on("operationTime", org.springframework.data.domain.Sort.Direction.DESC));
            
            log.info("MongoDB索引创建完成");
        } catch (Exception e) {
            log.error("创建MongoDB索引失败", e);
        }
    }

    /**
     * 测试插入操作日志
     */
    private void testInsertOperationLog() {
        try {
            // 检查是否已有测试数据
            long count = mongoTemplate.count(
                org.springframework.data.mongodb.core.query.Query.query(
                    org.springframework.data.mongodb.core.query.Criteria.where("operationDesc").is("系统初始化测试")
                ), 
                OperationLog.class
            );
            
            if (count == 0) {
                // 创建测试日志
                OperationLog testLog = OperationLog.builder()
                        .userId(0L)
                        .username("system")
                        .realName("系统")
                        .module("系统管理")
                        .operationType(OperationLog.OperationType.CREATE.getCode())
                        .operationDesc("系统初始化测试")
                        .requestMethod("SYSTEM")
                        .requestUrl("/system/init")
                        .responseCode(200)
                        .responseMessage("初始化成功")
                        .executionTime(0L)
                        .clientIp("127.0.0.1")
                        .userAgent("System")
                        .status(OperationLog.Status.SUCCESS.getCode())
                        .operationTime(java.time.LocalDateTime.now())
                        .createdTime(java.time.LocalDateTime.now())
                        .build();
                
                mongoTemplate.save(testLog);
                log.info("插入测试日志成功，日志ID: {}", testLog.getId());
            } else {
                log.info("测试日志已存在，跳过插入");
            }
        } catch (Exception e) {
            log.error("测试插入操作日志失败", e);
        }
    }
}
