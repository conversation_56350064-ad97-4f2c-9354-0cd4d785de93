package com.sinoair.agent.config;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.servers.Server;
import org.springdoc.core.models.GroupedOpenApi;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * 公开API配置
 * 专门配置面向第三方开发者的公开API文档
 * 
 * <AUTHOR> Team
 */
@Configuration
public class PublicApiConfig {

    /**
     * 公开API分组配置
     * 使用API Key认证，面向第三方开发者
     */
    @Bean
    public GroupedOpenApi publicApiGroup() {
        return GroupedOpenApi.builder()
                .group("public")
                .displayName("🌐 公开API")
                .pathsToMatch("/api/v1/public/**")
                .addOpenApiCustomizer(openApi -> {
                    openApi.info(publicApiInfo())
                           .servers(List.of(
                               new Server().url("https://api.sinoair-agent.com").description("🚀 生产环境"),
                               new Server().url("http://localhost:8080").description("🔧 本地开发环境")
                           ))
                           .components(new Components()
                               .addSecuritySchemes("ApiKey", new SecurityScheme()
                                   .type(SecurityScheme.Type.APIKEY)
                                   .in(SecurityScheme.In.HEADER)
                                   .name("X-API-Key")
                                   .description("API Key认证，格式：keyId.keySecret")
                               )
                           )
                           .addSecurityItem(new SecurityRequirement().addList("ApiKey"));
                })
                .build();
    }

    /**
     * 公开API信息配置
     */
    private Info publicApiInfo() {
        return new Info()
                .title("SinoairAgent 公开API")
                .description("""
                        # SinoairAgent 公开API文档
                        
                        ## 🚀 服务简介
                        SinoairAgent为开发者提供强大的文档识别和解析API服务，基于先进的AI技术，支持多种文档格式的智能识别。
                        
                        ## ✨ 主要功能
                        - **📄 文档解析**：支持PDF、图片等格式的智能识别
                        - **🔄 结构化提取**：将文档内容转换为结构化JSON数据
                        - **⚡ 异步处理**：支持大文件的异步处理模式
                        - **📊 实时状态**：提供任务状态查询和进度跟踪
                        
                        ## 🔐 认证方式
                        公开API使用API Key进行认证，支持以下三种方式：
                        
                        ### 1. HTTP Header（推荐）
                        ```
                        X-API-Key: ak_1234567890abcdef.abcdef1234567890abcdef1234567890abcdef12
                        ```
                        
                        ### 2. Authorization Header
                        ```
                        Authorization: Bearer ak_1234567890abcdef.abcdef1234567890abcdef1234567890abcdef12
                        ```
                        
                        ### 3. 查询参数
                        ```
                        ?api_key=ak_1234567890abcdef.abcdef1234567890abcdef1234567890abcdef12
                        ```
                        
                        ## 🎯 API Key申请
                        1. 访问客户门户：https://portal.sinoair-agent.com
                        2. 注册账号并完成邮箱验证
                        3. 登录后在控制台创建API Key
                        4. 复制并保存完整的API Key
                        
                        ## 🚀 快速开始
                        1. 获取Agent列表：`GET /api/v1/public/agents`
                        2. 上传文件解析：`POST /api/v1/public/files/parse`
                        3. 查询解析状态：`GET /api/v1/public/files/parse/status/{taskId}`
                        
                        ## ⚠️ 使用限制
                        - 文件大小：最大5MB
                        - 请求频率：根据您的API Key配额
                        - 支持格式：PDF、JPG、PNG、GIF
                        
                        ## 📞 技术支持
                        - 邮箱：<EMAIL>
                        - 文档：https://docs.sinoair-agent.com
                        - 门户：https://portal.sinoair-agent.com
                        """)
                .version("1.0.0")
                .contact(new Contact()
                        .name("SinoairAgent API团队")
                        .email("<EMAIL>")
                        .url("https://portal.sinoair-agent.com")
                )
                .license(new License()
                        .name("Commercial License")
                        .url("https://www.sinoair-agent.com/api-license")
                );
    }
}
