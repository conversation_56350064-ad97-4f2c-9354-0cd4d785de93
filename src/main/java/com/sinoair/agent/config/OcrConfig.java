package com.sinoair.agent.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * OCR配置类
 * 
 * <AUTHOR> Team
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "app.ocr")
public class OcrConfig {

    /**
     * Tesseract配置
     */
    private TesseractConfig tesseract = new TesseractConfig();

    /**
     * 支持的图片格式
     */
    private List<String> supportedFormats = List.of("jpg", "jpeg", "png", "bmp", "tiff", "gif", "webp");

    /**
     * 最大文件大小（MB）
     */
    private Integer maxFileSize = 10;

    /**
     * Tesseract配置
     */
    @Data
    public static class TesseractConfig {
        /**
         * Tesseract数据路径
         */
        private String dataPath;

        /**
         * 支持的语言
         */
        private String language = "chi_sim+eng";

        /**
         * OCR引擎模式
         */
        private Integer oem = 3;

        /**
         * 页面分割模式
         */
        private Integer psm = 6;

        /**
         * 图片预处理配置
         */
        private PreprocessingConfig preprocessing = new PreprocessingConfig();
    }

    /**
     * 图片预处理配置
     */
    @Data
    public static class PreprocessingConfig {
        /**
         * 是否启用图片预处理
         */
        private Boolean enabled = true;

        /**
         * 图片缩放因子
         */
        private Double scaleFactor = 2.0;

        /**
         * 是否进行灰度处理
         */
        private Boolean grayscale = true;

        /**
         * 是否进行降噪处理
         */
        private Boolean denoise = true;
    }
}
