package com.sinoair.agent.config;

import com.alibaba.druid.pool.DruidDataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Connection;

/**
 * 启动检查类
 * 检查数据库连接、Redis连接等基础服务
 */
@Slf4j
@Component
public class StartupCheck implements CommandLineRunner {
    
    @Autowired
    private DataSource dataSource;

    @Autowired(required = false)
    private StringRedisTemplate redisTemplate;

    @Value("${server.port:8080}")
    private int serverPort;

    @Value("${mail.enabled:true}")
    private boolean mailEnabled;
    
    @Override
    public void run(String... args) throws Exception {
        log.info("=== SINOAIR-AGENT 智能体矩阵平台启动检查 ===");
        
        // 检查数据库连接
        checkDatabase();
        
        // 检查Redis连接
        checkRedis();
        
        // 检查邮件配置
        checkMailConfig();
        
        // 检查Druid数据源
        checkDruidDataSource();

        log.info("=== 启动检查完成 ===");
        log.info("应用已启动，访问地址：http://localhost:{}", serverPort);
        log.info("测试接口：http://localhost:{}/test/health", serverPort);
        log.info("Druid监控：http://localhost:{}/druid/ (admin/admin)", serverPort);

        if (!mailEnabled) {
            log.info("开发提示：邮件功能已禁用，可以使用固定验证码 123456 进行登录测试");
        }
    }
    
    private void checkDatabase() {
        try (Connection connection = dataSource.getConnection()) {
            if (connection.isValid(5)) {
                log.info("✓ 数据库连接正常");
            } else {
                log.error("✗ 数据库连接异常");
            }
        } catch (Exception e) {
            log.error("✗ 数据库连接失败：{}", e.getMessage());
        }
    }
    
    private void checkRedis() {
        if (redisTemplate == null) {
            log.warn("⚠ Redis未配置或不可用（开发模式）");
            return;
        }

        try {
            redisTemplate.opsForValue().set("startup_check", "ok");
            String value = redisTemplate.opsForValue().get("startup_check");
            if ("ok".equals(value)) {
                log.info("✓ Redis连接正常");
                redisTemplate.delete("startup_check");
            } else {
                log.error("✗ Redis连接异常");
            }
        } catch (Exception e) {
            log.error("✗ Redis连接失败：{}", e.getMessage());
        }
    }
    
    private void checkMailConfig() {
        if (mailEnabled) {
            log.info("✓ 邮件服务已启用");
        } else {
            log.info("⚠ 邮件服务已禁用（开发模式）");
        }
    }

    private void checkDruidDataSource() {
        try {
            if (dataSource instanceof DruidDataSource) {
                DruidDataSource druidDataSource = (DruidDataSource) dataSource;
                log.info("✓ Druid数据源配置成功");
                log.info("  - 初始连接数：{}", druidDataSource.getInitialSize());
                log.info("  - 最大连接数：{}", druidDataSource.getMaxActive());
                log.info("  - 最小空闲连接数：{}", druidDataSource.getMinIdle());
            } else {
                log.warn("⚠ 当前使用的不是Druid数据源：{}", dataSource.getClass().getSimpleName());
            }
        } catch (Exception e) {
            log.error("✗ Druid数据源检查失败：{}", e.getMessage());
        }
    }
}
