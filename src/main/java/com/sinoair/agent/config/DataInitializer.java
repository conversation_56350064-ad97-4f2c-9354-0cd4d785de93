package com.sinoair.agent.config;

import com.sinoair.agent.entity.*;
import com.sinoair.agent.mapper.*;
import com.sinoair.agent.service.UserRoleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 数据初始化器
 * 
 * <AUTHOR> Team
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DataInitializer implements CommandLineRunner {

    private final RoleMapper roleMapper;
    private final UserMapper userMapper;
    private final AgentCategoryMapper categoryMapper;
    private final BusinessTypeMapper businessTypeMapper;
    private final AgentMapper agentMapper;
    private final BusinessTemplateMapper businessTemplateMapper;
    private final UserRoleService userRoleService;
    private final PasswordEncoder passwordEncoder;

    @Override
    public void run(String... args) throws Exception {
        log.info("开始初始化数据...");
        
        try {
            initRoles();
            initUsers();
            initCategories();
            initBusinessTypes();
            initBusinessTemplates();
            initAgents();

            log.info("数据初始化完成");
        } catch (Exception e) {
            log.error("数据初始化失败", e);
        }
    }

    private void initRoles() {
        // 检查是否已有角色数据（V3迁移脚本已经创建了默认角色）
        if (roleMapper.selectCount(null) > 0) {
            log.info("角色数据已存在，跳过初始化");
            return;
        }

        // 如果没有角色数据，创建基本角色（通常不会执行到这里，因为V3脚本已经创建了）
        Role adminRole = new Role();
        adminRole.setRoleName("系统管理员");
        adminRole.setRoleCode("ADMIN");
        adminRole.setDescription("系统管理员角色，拥有所有权限");
        adminRole.setStatus(1);
        adminRole.setSortOrder(1);
        adminRole.setCreatedBy(1L);
        adminRole.setCreatedTime(LocalDateTime.now());
        adminRole.setUpdatedTime(LocalDateTime.now());
        roleMapper.insert(adminRole);

        Role userRole = new Role();
        userRole.setRoleName("普通用户");
        userRole.setRoleCode("USER");
        userRole.setDescription("普通用户角色，基础权限");
        userRole.setStatus(1);
        userRole.setSortOrder(2);
        userRole.setCreatedBy(1L);
        userRole.setCreatedTime(LocalDateTime.now());
        userRole.setUpdatedTime(LocalDateTime.now());
        roleMapper.insert(userRole);

        log.info("初始化角色数据完成");
    }

    private void initUsers() {
        if (userMapper.selectCount(null) > 0) {
            log.info("用户数据已存在，跳过初始化");
            return;
        }

        Role adminRole = roleMapper.findByRoleCode("ADMIN");
        Role userRole = roleMapper.findByRoleCode("USER");

        String encodedPassword = passwordEncoder.encode("password123");

        if (adminRole != null) {
            log.info("创建admin用户，密码哈希: {}", encodedPassword);

            User admin = new User();
            admin.setUsername("admin");
            admin.setPassword(encodedPassword);
            admin.setRealName("系统管理员");
            admin.setEmail("<EMAIL>");
            admin.setStatus(1);
            admin.setCreatedBy(1L);
            admin.setCreatedTime(LocalDateTime.now());
            admin.setUpdatedTime(LocalDateTime.now());
            userMapper.insert(admin);

            // 为admin用户分配管理员角色
            userRoleService.assignRolesToUser(admin.getId(), List.of(adminRole.getId()));

            log.info("admin用户创建成功");
        }

        if (userRole != null) {
            User user = new User();
            user.setUsername("user1");
            user.setPassword(encodedPassword);
            user.setRealName("普通用户");
            user.setEmail("<EMAIL>");
            user.setStatus(1);
            user.setCreatedBy(1L);
            user.setCreatedTime(LocalDateTime.now());
            user.setUpdatedTime(LocalDateTime.now());
            userMapper.insert(user);

            // 为user1用户分配普通用户角色
            userRoleService.assignRolesToUser(user.getId(), List.of(userRole.getId()));

            log.info("user1用户创建成功");
        }

        log.info("初始化用户数据完成");
    }

    private void initCategories() {
        if (categoryMapper.selectCount(null) > 0) {
            return;
        }

        AgentCategory category = new AgentCategory();
        category.setCategoryName("文档识别");
        category.setCategoryCode("DOCUMENT_RECOGNITION");
        category.setDescription("各类文档识别Agent");
        category.setParentId(0L);
        category.setLevel(1);
        category.setSortOrder(1);
        category.setStatus(1);
        category.setCreatedTime(LocalDateTime.now());
        categoryMapper.insert(category);

        log.info("初始化分类数据完成");
    }

    private void initBusinessTypes() {
        if (businessTypeMapper.selectCount(null) > 0) {
            return;
        }

        BusinessType orderType = new BusinessType();
        orderType.setTypeName("订单处理");
        orderType.setTypeCode("ORDER_PROCESSING");
        orderType.setDescription("订单文档处理和信息提取");
        orderType.setJsonSchema("{\"type\":\"object\",\"properties\":{\"consignee\":{\"type\":\"object\"},\"shipper\":{\"type\":\"object\"},\"cargo\":{\"type\":\"object\"}}}");
        orderType.setFileTypes("jpg,png,pdf");
        orderType.setStatus(1);
        orderType.setCreatedTime(LocalDateTime.now());
        businessTypeMapper.insert(orderType);

        log.info("初始化业务类型数据完成");
    }

    private void initAgents() {
        if (agentMapper.selectCount(null) > 0) {
            return;
        }

        AgentCategory category = categoryMapper.findByCategoryCode("DOCUMENT_RECOGNITION");
        BusinessType businessType = businessTypeMapper.findByTypeCode("ORDER_PROCESSING");
        User admin = userMapper.findByUsername("admin");

        if (category != null && businessType != null && admin != null) {
            Agent agent = new Agent();
            agent.setAgentName("订单识别Agent");
            agent.setAgentCode("ORDER_RECOGNITION");
            agent.setDescription("识别订单文档并提取收发货人、货物等关键信息");
            agent.setCategoryId(category.getId());
            agent.setBusinessTypeId(businessType.getId());
            agent.setAgentType(Agent.TYPE_INTERNAL_LLM);
            agent.setConfig("{\"llm_config\":{\"provider\":\"qianwen\",\"model\":\"qwen-plus\",\"temperature\":0.7,\"max_tokens\":2000}}");
            agent.setPromptTemplate("请仔细分析以下订单文档，提取收货人、发货人、货物信息等关键数据，按照指定的JSON格式返回结果。");
            agent.setJsonTemplate("{\"consignee\":{\"name\":\"\",\"address\":\"\",\"phone\":\"\"},\"shipper\":{\"name\":\"\",\"address\":\"\",\"phone\":\"\"},\"cargo\":{\"weight\":0,\"pieces\":0,\"description\":\"\"}}");
            agent.setVersion("1.0.0");
            agent.setStatus(Agent.STATUS_PUBLISHED);
            agent.setCreatorId(admin.getId());
            agent.setCreatedTime(LocalDateTime.now());
            agent.setUpdatedTime(LocalDateTime.now());
            agent.setPublishedTime(LocalDateTime.now());
            agentMapper.insert(agent);
        }

        log.info("初始化Agent数据完成");
    }

    private void initBusinessTemplates() {
        if (businessTemplateMapper.selectCount(null) > 0) {
            log.info("业务模板数据已存在，跳过初始化");
            return;
        }

        log.info("开始初始化业务模板数据...");

        // 发票识别模板
        BusinessTemplate invoiceTemplate = new BusinessTemplate();
        invoiceTemplate.setTemplateName("发票识别");
        invoiceTemplate.setTemplateCode("INVOICE_RECOGNITION");
        invoiceTemplate.setDescription("识别各类发票信息，包括开票方、金额、日期等");
        invoiceTemplate.setJsonTemplate("{\n" +
                "  \"invoice_number\": \"发票号码\",\n" +
                "  \"invoice_date\": \"开票日期\",\n" +
                "  \"seller_name\": \"销售方名称\",\n" +
                "  \"seller_tax_number\": \"销售方税号\",\n" +
                "  \"buyer_name\": \"购买方名称\",\n" +
                "  \"buyer_tax_number\": \"购买方税号\",\n" +
                "  \"total_amount\": \"价税合计\",\n" +
                "  \"tax_amount\": \"税额\"\n" +
                "}");
        invoiceTemplate.setCategory("finance");
        invoiceTemplate.setStatus(1);
        invoiceTemplate.setCreatedTime(LocalDateTime.now());
        invoiceTemplate.setUpdatedTime(LocalDateTime.now());

        // 运单识别模板
        BusinessTemplate waybillTemplate = new BusinessTemplate();
        waybillTemplate.setTemplateName("运单识别");
        waybillTemplate.setTemplateCode("WAYBILL_RECOGNITION");
        waybillTemplate.setDescription("识别航空运单信息，包括运单号、发货人、收货人等");
        waybillTemplate.setJsonTemplate("{\n" +
                "  \"waybill_number\": \"运单号\",\n" +
                "  \"origin\": \"起运地\",\n" +
                "  \"destination\": \"目的地\",\n" +
                "  \"shipper_name\": \"发货人姓名\",\n" +
                "  \"shipper_address\": \"发货人地址\",\n" +
                "  \"consignee_name\": \"收货人姓名\",\n" +
                "  \"consignee_address\": \"收货人地址\",\n" +
                "  \"goods_description\": \"货物描述\",\n" +
                "  \"weight\": \"重量\",\n" +
                "  \"pieces\": \"件数\"\n" +
                "}");
        waybillTemplate.setCategory("logistics");
        waybillTemplate.setStatus(1);
        waybillTemplate.setCreatedTime(LocalDateTime.now());
        waybillTemplate.setUpdatedTime(LocalDateTime.now());

        // 合同识别模板
        BusinessTemplate contractTemplate = new BusinessTemplate();
        contractTemplate.setTemplateName("合同识别");
        contractTemplate.setTemplateCode("CONTRACT_RECOGNITION");
        contractTemplate.setDescription("识别合同关键信息，包括甲乙方、金额、期限等");
        contractTemplate.setJsonTemplate("{\n" +
                "  \"contract_number\": \"合同编号\",\n" +
                "  \"contract_title\": \"合同标题\",\n" +
                "  \"party_a\": \"甲方名称\",\n" +
                "  \"party_b\": \"乙方名称\",\n" +
                "  \"contract_amount\": \"合同金额\",\n" +
                "  \"start_date\": \"开始日期\",\n" +
                "  \"end_date\": \"结束日期\"\n" +
                "}");
        contractTemplate.setCategory("legal");
        contractTemplate.setStatus(1);
        contractTemplate.setCreatedTime(LocalDateTime.now());
        contractTemplate.setUpdatedTime(LocalDateTime.now());

        // 保存所有模板
        businessTemplateMapper.insert(invoiceTemplate);
        businessTemplateMapper.insert(waybillTemplate);
        businessTemplateMapper.insert(contractTemplate);

        log.info("业务模板数据初始化完成，共创建 {} 个模板", 3);
    }
}
