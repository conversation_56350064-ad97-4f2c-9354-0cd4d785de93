package com.sinoair.agent.common;

import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.Data;

import java.util.List;

/**
 * 分页响应结果类
 * 
 * <AUTHOR> Team
 */
@Data
public class PageResult<T> {

    private List<T> list;
    private Pagination pagination;

    // 便捷方法
    public List<T> getRecords() {
        return list;
    }

    public Long getTotal() {
        return pagination != null ? pagination.getTotal() : 0L;
    }

    public Long getCurrent() {
        return pagination != null ? pagination.getPage() : 1L;
    }

    public Long getSize() {
        return pagination != null ? pagination.getSize() : 10L;
    }

    public PageResult() {}

    public PageResult(List<T> list, long total, long page, long size) {
        this.list = list;
        this.pagination = new Pagination(page, size, total);
    }

    public static <T> PageResult<T> of(List<T> list, long total, long page, long size) {
        return new PageResult<>(list, total, page, size);
    }

    public static <T> PageResult<T> of(IPage<T> page) {
        return new PageResult<>(page.getRecords(), page.getTotal(), page.getCurrent(), page.getSize());
    }

    /**
     * 分页信息
     */
    @Data
    public static class Pagination {
        private Long page;      // 当前页码
        private Long size;      // 每页大小
        private Long total;     // 总记录数
        private Long pages;     // 总页数

        public Pagination() {}

        public Pagination(Long page, Long size, Long total) {
            this.page = page;
            this.size = size;
            this.total = total;
            this.pages = (total + size - 1) / size; // 计算总页数
        }
    }
}
