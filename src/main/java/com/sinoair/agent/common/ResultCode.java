package com.sinoair.agent.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 响应状态码枚举
 * 
 * <AUTHOR> Team
 */
@Getter
@AllArgsConstructor
public enum ResultCode {

    // 成功
    SUCCESS(200, "成功"),

    // 客户端错误 4xx
    BAD_REQUEST(400, "请求参数错误"),
    UNAUTHORIZED(401, "未授权"),
    FORBIDDEN(403, "禁止访问"),
    NOT_FOUND(404, "资源不存在"),
    METHOD_NOT_ALLOWED(405, "请求方法不允许"),
    CONFLICT(409, "资源冲突"),
    TOO_MANY_REQUESTS(429, "请求过于频繁"),

    // 服务器错误 5xx
    INTERNAL_SERVER_ERROR(500, "服务器内部错误"),
    BAD_GATEWAY(502, "网关错误"),
    SERVICE_UNAVAILABLE(503, "服务不可用"),
    GATEWAY_TIMEOUT(504, "网关超时"),

    // 业务错误 1xxx
    AGENT_NOT_FOUND(1001, "Agent不存在"),
    AGENT_STATUS_ERROR(1002, "Agent状态错误"),
    FILE_FORMAT_NOT_SUPPORTED(1003, "文件格式不支持"),
    FILE_SIZE_EXCEEDED(1004, "文件大小超限"),
    AI_RECOGNITION_FAILED(1005, "AI识别失败"),
    SCRIPT_EXECUTION_FAILED(1006, "脚本执行失败"),
    AGENT_CODE_EXISTS(1007, "Agent编码已存在"),
    AGENT_NAME_EXISTS(1008, "Agent名称已存在"),
    AGENT_TESTING_FAILED(1009, "Agent测试失败"),
    AGENT_PUBLISH_FAILED(1010, "Agent发布失败"),
    
    // 用户相关错误 2xxx
    USER_NOT_FOUND(2001, "用户不存在"),
    USERNAME_EXISTS(2002, "用户名已存在"),
    PASSWORD_ERROR(2003, "密码错误"),
    USER_DISABLED(2004, "用户已被禁用"),
    ROLE_NOT_FOUND(2005, "角色不存在"),
    PERMISSION_DENIED(2006, "权限不足"),
    TOKEN_EXPIRED(2007, "Token已过期"),
    TOKEN_INVALID(2008, "Token无效"),
    
    // 文件相关错误 3xxx
    FILE_NOT_FOUND(3001, "文件不存在"),
    FILE_UPLOAD_FAILED(3002, "文件上传失败"),
    FILE_DELETE_FAILED(3003, "文件删除失败"),
    FILE_TYPE_NOT_ALLOWED(3004, "文件类型不允许"),
    FILE_EMPTY(3005, "文件为空"),
    FILE_DOWNLOAD_FAILED(3006, "文件下载失败"),
    
    // 识别相关错误 4xxx
    RECOGNITION_FAILED(4001, "识别失败"),
    RECOGNITION_TIMEOUT(4002, "识别超时"),
    RECOGNITION_QUEUE_FULL(4003, "识别队列已满"),
    RECOGNITION_RESULT_NOT_FOUND(4004, "识别结果不存在"),
    
    // 脚本相关错误 5xxx
    SCRIPT_NOT_FOUND(5001, "脚本不存在"),
    SCRIPT_SYNTAX_ERROR(5002, "脚本语法错误"),
    SCRIPT_EXECUTION_TIMEOUT(5003, "脚本执行超时"),
    SCRIPT_TARGET_PAGE_NOT_FOUND(5004, "目标页面不存在"),
    
    // 发布相关错误 6xxx
    PUBLICATION_NOT_FOUND(6001, "发布记录不存在"),
    PUBLICATION_ALREADY_REVIEWED(6002, "发布申请已审核"),
    PUBLICATION_REVIEW_FAILED(6003, "发布审核失败"),
    SHORT_URL_NOT_FOUND(6004, "短链接不存在"),
    ACCESS_TOKEN_INVALID(6005, "访问Token无效");

    private final Integer code;
    private final String message;
}
