package com.sinoair.agent.aspect;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sinoair.agent.annotation.OperationLogRecord;
import com.sinoair.agent.common.Result;
import com.sinoair.agent.entity.OperationLog;
import com.sinoair.agent.security.UserPrincipal;
import com.sinoair.agent.util.LogRecordUtil;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 操作日志AOP切面
 * 自动记录带有@OperationLogRecord注解的方法的操作日志
 *
 * <AUTHOR> Team
 */
@Slf4j
@Aspect
@Component
@RequiredArgsConstructor
public class OperationLogAspect {

    private final LogRecordUtil logRecordUtil;
    private final ObjectMapper objectMapper;

    /**
     * 定义切点：所有带有@OperationLogRecord注解的方法
     */
    @Pointcut("@annotation(com.sinoair.agent.annotation.OperationLogRecord)")
    public void operationLogPointcut() {
    }

    /**
     * 环绕通知：记录操作日志
     */
    @Around("operationLogPointcut()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();

        // 获取方法签名和注解
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        OperationLogRecord annotation = method.getAnnotation(OperationLogRecord.class);

        // 获取方法参数
        Object[] args = joinPoint.getArgs();
        String[] paramNames = signature.getParameterNames();

        // 构建请求参数Map
        Map<String, Object> requestParams = new HashMap<>();
        if (annotation.recordParams() && args != null && paramNames != null) {
            for (int i = 0; i < args.length && i < paramNames.length; i++) {
                if (args[i] != null && !(args[i] instanceof HttpServletRequest)) {
                    // 过滤敏感信息
                    if (isSensitiveParam(paramNames[i])) {
                        requestParams.put(paramNames[i], "***");
                    } else {
                        // 处理文件类型参数，避免序列化错误
                        Object paramValue = processParameterValue(args[i], paramNames[i]);
                        requestParams.put(paramNames[i], paramValue);
                    }
                }
            }
        }

        Object result = null;
        String status = OperationLog.Status.SUCCESS.getCode();
        String errorMessage = null;
        Map<String, Object> responseResult = new HashMap<>();

        try {
            // 执行目标方法
            result = joinPoint.proceed();

            // 记录响应结果
            if (annotation.recordResult() && result != null) {
                if (result instanceof Result) {
                    Result<?> apiResult = (Result<?>) result;
                    responseResult.put("code", apiResult.getCode());
                    responseResult.put("message", apiResult.getMessage());
                    responseResult.put("success", apiResult.isSuccess());
                    responseResult.put("timestamp", apiResult.getTimestamp());

                    // 判断业务是否成功
                    if (!apiResult.isSuccess()) {
                        status = OperationLog.Status.FAILED.getCode();
                        errorMessage = apiResult.getMessage();
                    }

                    // 安全地处理响应数据
                    Object data = apiResult.getData();
                    if (data != null) {
                        Object processedData = processResponseData(data);
                        responseResult.put("data", processedData);
                    }
                } else {
                    // 处理非Result类型的响应
                    Object processedResult = processResponseData(result);
                    responseResult.put("result", processedResult);
                }
            }

        } catch (Exception e) {
            status = OperationLog.Status.ERROR.getCode();
            errorMessage = e.getMessage();
            responseResult.put("error", e.getClass().getSimpleName());
            responseResult.put("errorMessage", e.getMessage());
            throw e;
        } finally {
            // 计算执行时间
            long executionTime = System.currentTimeMillis() - startTime;

            // 记录操作日志
            try {
                recordOperationLog(annotation, requestParams, responseResult, status,
                                 errorMessage, executionTime);
            } catch (Exception e) {
                log.error("记录操作日志失败", e);
            }
        }

        return result;
    }

    /**
     * 记录操作日志
     */
    private void recordOperationLog(OperationLogRecord annotation, Map<String, Object> requestParams,
                                   Map<String, Object> responseResult, String status,
                                   String errorMessage, long executionTime) {

        String module = annotation.module();
        String operationType = annotation.operationType();
        String operationDesc = annotation.operationDesc();

        // 如果注解中没有指定，尝试从HTTP请求中获取
        if (module.isEmpty() || operationType.isEmpty() || operationDesc.isEmpty()) {
            try {
                ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
                if (attributes != null) {
                    HttpServletRequest request = attributes.getRequest();
                    String requestUrl = request.getRequestURL().toString();
                    String requestMethod = request.getMethod();

                    if (module.isEmpty()) {
                        module = extractModuleFromUrl(requestUrl);
                    }
                    if (operationType.isEmpty()) {
                        operationType = mapHttpMethodToOperationType(requestMethod);
                    }
                    if (operationDesc.isEmpty()) {
                        operationDesc = String.format("%s %s", requestMethod, requestUrl);
                    }
                }
            } catch (Exception e) {
                log.debug("从HTTP请求中提取日志信息失败", e);
            }
        }

        // 构建完整的日志对象
        try {
            OperationLog.OperationLogBuilder logBuilder = OperationLog.builder()
                    .module(module)
                    .operationType(operationType)
                    .operationDesc(operationDesc)
                    .requestParams(requestParams)
                    .responseResult(responseResult)
                    .status(status)
                    .errorMessage(errorMessage)
                    .executionTime(executionTime)
                    .operationTime(LocalDateTime.now())
                    .createdTime(LocalDateTime.now());

            // 从响应结果中提取状态码和消息
            if (responseResult != null) {
                Object code = responseResult.get("code");
                if (code instanceof Integer) {
                    logBuilder.responseCode((Integer) code);
                }
                Object message = responseResult.get("message");
                if (message instanceof String) {
                    logBuilder.responseMessage((String) message);
                }
            }

            // 获取当前用户信息
            setUserInfo(logBuilder);

            // 获取HTTP请求信息
            setHttpRequestInfo(logBuilder);

            // 保存日志
            OperationLog operationLog = logBuilder.build();
            // logRecordUtil.saveLogDirectly(operationLog);

        } catch (Exception e) {
            log.error("记录操作日志失败", e);
        }
    }



    /**
     * 从URL中提取模块名
     */
    private String extractModuleFromUrl(String url) {
        try {
            // 提取 /api/v1/ 后面的第一个路径段作为模块名
            String[] parts = url.split("/");
            for (int i = 0; i < parts.length; i++) {
                if ("api".equals(parts[i]) && i + 2 < parts.length) {
                    String module = parts[i + 2];
                    // 转换为中文模块名
                    return mapModuleNameToChinese(module);
                }
            }
        } catch (Exception e) {
            log.debug("从URL提取模块名失败: {}", url, e);
        }
        return "未知模块";
    }

    /**
     * 将HTTP方法映射为操作类型
     */
    private String mapHttpMethodToOperationType(String httpMethod) {
        switch (httpMethod.toUpperCase()) {
            case "GET":
                return OperationLog.OperationType.QUERY.getCode();
            case "POST":
                return OperationLog.OperationType.CREATE.getCode();
            case "PUT":
                return OperationLog.OperationType.UPDATE.getCode();
            case "DELETE":
                return OperationLog.OperationType.DELETE.getCode();
            default:
                return httpMethod;
        }
    }

    /**
     * 将英文模块名映射为中文
     */
    private String mapModuleNameToChinese(String moduleName) {
        switch (moduleName.toLowerCase()) {
            case "users":
                return "用户管理";
            case "agents":
                return "Agent管理";
            case "files":
                return "文件管理";
            case "auth":
                return "认证管理";
            case "system":
                return "系统管理";
            case "recognition":
                return "识别服务";
            case "templates":
                return "模板管理";
            case "roles":
                return "角色管理";
            case "permissions":
                return "权限管理";
            default:
                return moduleName;
        }
    }

    /**
     * 设置用户信息
     */
    private void setUserInfo(OperationLog.OperationLogBuilder logBuilder) {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null && authentication.isAuthenticated() &&
                !"anonymousUser".equals(authentication.getPrincipal())) {

                Object principal = authentication.getPrincipal();
                if (principal instanceof UserPrincipal) {
                    UserPrincipal userPrincipal = (UserPrincipal) principal;
                    logBuilder.userId(userPrincipal.getId())
                             .username(userPrincipal.getUsername())
                             .realName(userPrincipal.getRealName());
                } else if (principal instanceof String) {
                    logBuilder.username((String) principal);
                }
            }
        } catch (Exception e) {
            log.error("设置用户信息失败", e);
        }
    }

    /**
     * 设置HTTP请求信息
     */
    private void setHttpRequestInfo(OperationLog.OperationLogBuilder logBuilder) {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();

                String requestUrl = request.getRequestURL().toString();
                String queryString = request.getQueryString();
                if (queryString != null && !queryString.isEmpty()) {
                    requestUrl += "?" + queryString;
                }

                logBuilder.requestMethod(request.getMethod())
                         .requestUrl(requestUrl)
                         .clientIp(getClientIpAddress(request))
                         .userAgent(request.getHeader("User-Agent"));
            }
        } catch (Exception e) {
            log.error("设置HTTP请求信息失败", e);
        }
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String[] headerNames = {
            "X-Forwarded-For",
            "X-Real-IP",
            "Proxy-Client-IP",
            "WL-Proxy-Client-IP",
            "HTTP_CLIENT_IP",
            "HTTP_X_FORWARDED_FOR"
        };

        for (String headerName : headerNames) {
            String ip = request.getHeader(headerName);
            if (ip != null && !ip.isEmpty() && !"unknown".equalsIgnoreCase(ip)) {
                if (ip.contains(",")) {
                    ip = ip.split(",")[0].trim();
                }
                return ip;
            }
        }

        return request.getRemoteAddr();
    }

    /**
     * 处理参数值，特别处理文件类型参数
     */
    private Object processParameterValue(Object paramValue, String paramName) {
        if (paramValue == null) {
            return null;
        }

        try {
            // 处理MultipartFile类型
            if (paramValue instanceof MultipartFile) {
                MultipartFile file = (MultipartFile) paramValue;
                Map<String, Object> fileInfo = new HashMap<>();
                fileInfo.put("fileName", file.getOriginalFilename());
                fileInfo.put("fileSize", file.getSize());
                fileInfo.put("contentType", file.getContentType());
                fileInfo.put("parameterType", "MultipartFile");
                return fileInfo;
            }

            // 处理File类型
            if (paramValue instanceof File) {
                File file = (File) paramValue;
                Map<String, Object> fileInfo = new HashMap<>();
                fileInfo.put("fileName", file.getName());
                fileInfo.put("filePath", file.getAbsolutePath());
                fileInfo.put("fileSize", file.length());
                fileInfo.put("parameterType", "File");
                return fileInfo;
            }

            // 处理MultipartFile数组
            if (paramValue instanceof MultipartFile[]) {
                MultipartFile[] files = (MultipartFile[]) paramValue;
                Map<String, Object> filesInfo = new HashMap<>();
                filesInfo.put("fileCount", files.length);
                filesInfo.put("parameterType", "MultipartFile[]");

                if (files.length > 0) {
                    StringBuilder fileNames = new StringBuilder();
                    long totalSize = 0;
                    for (int i = 0; i < Math.min(files.length, 5); i++) { // 最多记录5个文件名
                        if (i > 0) fileNames.append(", ");
                        fileNames.append(files[i].getOriginalFilename());
                        totalSize += files[i].getSize();
                    }
                    if (files.length > 5) {
                        fileNames.append("...");
                    }
                    filesInfo.put("fileNames", fileNames.toString());
                    filesInfo.put("totalSize", totalSize);
                }
                return filesInfo;
            }

            // 处理其他可能导致序列化问题的类型
            String className = paramValue.getClass().getName();

            // 避免序列化Spring相关的对象和其他可能有问题的类型
            if (className.startsWith("org.springframework.") ||
                className.startsWith("jakarta.servlet.") ||
                className.startsWith("javax.servlet.") ||
                className.startsWith("java.io.") ||
                className.startsWith("java.nio.") ||
                className.startsWith("sun.") ||
                className.startsWith("com.sun.") ||
                className.contains("InputStream") ||
                className.contains("OutputStream") ||
                className.contains("Reader") ||
                className.contains("Writer")) {
                return String.format("[%s: %s]", paramValue.getClass().getSimpleName(),
                                   getSafeToString(paramValue));
            }

            // 对于复杂对象，使用安全的toString方法并限制字符串长度
            String valueStr = getSafeToString(paramValue);
            if (valueStr.length() > 1000) {
                valueStr = valueStr.substring(0, 1000) + "...";
            }

            return paramValue;

        } catch (Exception e) {
            // 如果处理过程中出现任何异常，返回安全的字符串表示
            log.debug("处理参数值时出现异常: {}", paramName, e);
            try {
                return String.format("[%s: %s]", paramValue.getClass().getSimpleName(),
                                   getSafeToString(paramValue).substring(0, Math.min(100, getSafeToString(paramValue).length())));
            } catch (Exception ex) {
                return String.format("[%s: 无法序列化]", paramValue.getClass().getSimpleName());
            }
        }
    }

    /**
     * 处理响应数据，避免序列化问题
     */
    private Object processResponseData(Object data) {
        if (data == null) {
            return null;
        }

        try {
            // 处理文件类型
            if (data instanceof File) {
                File file = (File) data;
                Map<String, Object> fileInfo = new HashMap<>();
                fileInfo.put("fileName", file.getName());
                fileInfo.put("fileSize", file.length());
                fileInfo.put("dataType", "File");
                return fileInfo;
            }

            // 处理MultipartFile类型
            if (data instanceof MultipartFile) {
                MultipartFile file = (MultipartFile) data;
                Map<String, Object> fileInfo = new HashMap<>();
                fileInfo.put("fileName", file.getOriginalFilename());
                fileInfo.put("fileSize", file.getSize());
                fileInfo.put("contentType", file.getContentType());
                fileInfo.put("dataType", "MultipartFile");
                return fileInfo;
            }

            // 处理字符串类型，限制长度
            if (data instanceof String) {
                String str = (String) data;
                if (str.length() > 1000) {
                    return str.substring(0, 1000) + "...";
                }
                return str;
            }

            // 处理基本类型和包装类型
            if (data instanceof Number || data instanceof Boolean ||
                data instanceof Character || data.getClass().isPrimitive()) {
                return data;
            }

            // 对于复杂对象，使用安全的toString方法
            String dataStr = getSafeToString(data);
            if (dataStr.length() > 500) {
                dataStr = dataStr.substring(0, 500) + "...";
            }

            // 如果字符串包含对象地址，说明toString()方法没有被重写，返回类型信息
            if (dataStr.contains("@") && dataStr.matches(".*@[0-9a-f]+.*")) {
                return String.format("[%s: 数据已记录]", data.getClass().getSimpleName());
            }

            return dataStr;

        } catch (Exception e) {
            log.debug("处理响应数据时出现异常", e);
            return String.format("[%s: 无法序列化]", data.getClass().getSimpleName());
        }
    }

    /**
     * 安全的JSON序列化方法（实际上我们不需要手动序列化，因为MongoDB会自动处理Map）
     * 这个方法主要用于调试和日志输出
     */
    private String safeJsonSerialize(Object obj) {
        if (obj == null) {
            return null;
        }

        try {
            return objectMapper.writeValueAsString(obj);
        } catch (Exception e) {
            log.debug("JSON序列化失败: {}", e.getMessage());

            // 简化处理：直接返回对象的安全字符串表示
            if (obj instanceof Map) {
                Map<?, ?> map = (Map<?, ?>) obj;
                return String.format("{\"mapSize\":%d,\"type\":\"Map\",\"error\":\"序列化失败\"}", map.size());
            }

            // 对于非Map类型，返回安全的字符串表示
            String safeStr = getSafeToString(obj);
            return String.format("{\"error\":\"序列化失败\",\"type\":\"%s\",\"value\":\"%s\"}",
                               obj.getClass().getSimpleName(),
                               safeStr.substring(0, Math.min(100, safeStr.length())));
        }
    }

    /**
     * 安全的toString方法，避免反射错误
     */
    private String getSafeToString(Object obj) {
        if (obj == null) {
            return "null";
        }

        try {
            String str = obj.toString();
            // 限制字符串长度
            if (str.length() > 200) {
                str = str.substring(0, 200) + "...";
            }
            return str;
        } catch (Exception e) {
            // 如果toString()也失败了，返回类名
            return obj.getClass().getSimpleName() + "@" + Integer.toHexString(obj.hashCode());
        }
    }

    /**
     * 判断是否为敏感参数
     */
    private boolean isSensitiveParam(String paramName) {
        if (paramName == null) {
            return false;
        }
        String lowerParamName = paramName.toLowerCase();
        return lowerParamName.contains("password") ||
               lowerParamName.contains("pwd") ||
               lowerParamName.contains("secret") ||
               lowerParamName.contains("token") ||
               lowerParamName.contains("key");
    }
}
