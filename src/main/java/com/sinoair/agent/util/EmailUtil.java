package com.sinoair.agent.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Component;

import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import java.io.File;
import java.util.List;

/**
 * # FILE INFO #
 * # Created with IntelliJ IDEA #
 * # Author: Liu·Ce #
 * # Date: 2025/7/4 #
 * # Time: 15:53 #
 *
 * 邮件发送工具类
 * 提供发送简单文本邮件、HTML邮件、带附件邮件的功能
 */
@Component
public class EmailUtil {

    private static final Logger logger = LoggerFactory.getLogger(EmailUtil.class);

    @Autowired
    private JavaMailSender mailSender;

    @Value("${spring.mail.username}")
    private String from;

    /**
     * 发送简单文本邮件
     *
     * @param to 收件人邮箱
     * @param subject 邮件主题
     * @param content 邮件内容
     * @return 发送结果
     */
    public boolean sendSimpleMail(String to, String subject, String content) {
        try {
            SimpleMailMessage message = new SimpleMailMessage();
            message.setFrom(from);
            message.setTo(to);
            message.setSubject(subject);
            message.setText(content);

            mailSender.send(message);
            logger.info("简单邮件发送成功，收件人：{}, 主题：{}", to, subject);
            return true;
        } catch (Exception e) {
            logger.error("简单邮件发送失败，收件人：{}, 主题：{}, 错误信息：{}", to, subject, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 发送简单文本邮件（多个收件人）
     *
     * @param toList 收件人邮箱列表
     * @param subject 邮件主题
     * @param content 邮件内容
     * @return 发送结果
     */
    public boolean sendSimpleMail(List<String> toList, String subject, String content) {
        try {
            SimpleMailMessage message = new SimpleMailMessage();
            message.setFrom(from);
            message.setTo(toList.toArray(new String[0]));
            message.setSubject(subject);
            message.setText(content);

            mailSender.send(message);
            logger.info("简单邮件发送成功，收件人：{}, 主题：{}", toList, subject);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("简单邮件发送失败，收件人：{}, 主题：{}, 错误信息：{}", toList, subject, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 发送HTML格式邮件
     *
     * @param to 收件人邮箱
     * @param subject 邮件主题
     * @param content HTML内容
     * @return 发送结果
     */
    public boolean sendHtmlMail(String to, String subject, String content) {
        try {
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");

            helper.setFrom(from);
            helper.setTo(to);
            helper.setSubject(subject);
            helper.setText(content, true); // true表示HTML格式

            mailSender.send(message);
            logger.info("HTML邮件发送成功，收件人：{}, 主题：{}", to, subject);
            return true;
        } catch (MessagingException e) {
            logger.error("HTML邮件发送失败，收件人：{}, 主题：{}, 错误信息：{}", to, subject, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 发送HTML格式邮件（多个收件人）
     *
     * @param toList 收件人邮箱列表
     * @param subject 邮件主题
     * @param content HTML内容
     * @return 发送结果
     */
    public boolean sendHtmlMail(List<String> toList, String subject, String content) {
        try {
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");

            helper.setFrom(from);
            helper.setTo(toList.toArray(new String[0]));
            helper.setSubject(subject);
            helper.setText(content, true); // true表示HTML格式

            mailSender.send(message);
            logger.info("HTML邮件发送成功，收件人：{}, 主题：{}", toList, subject);
            return true;
        } catch (MessagingException e) {
            logger.error("HTML邮件发送失败，收件人：{}, 主题：{}, 错误信息：{}", toList, subject, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 发送带附件的邮件
     *
     * @param to 收件人邮箱
     * @param subject 邮件主题
     * @param content 邮件内容
     * @param filePath 附件文件路径
     * @param fileName 附件文件名（可选，如果为null则使用文件原名）
     * @return 发送结果
     */
    public boolean sendAttachmentMail(String to, String subject, String content, String filePath, String fileName) {
        try {
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");

            helper.setFrom(from);
            helper.setTo(to);
            helper.setSubject(subject);
            helper.setText(content);

            // 添加附件
            FileSystemResource file = new FileSystemResource(new File(filePath));
            if (file.exists()) {
                String attachmentName = fileName != null ? fileName : file.getFilename();
                helper.addAttachment(attachmentName, file);
            } else {
                logger.warn("附件文件不存在：{}", filePath);
                return false;
            }

            mailSender.send(message);
            logger.info("带附件邮件发送成功，收件人：{}, 主题：{}, 附件：{}", to, subject, filePath);
            return true;
        } catch (MessagingException e) {
            logger.error("带附件邮件发送失败，收件人：{}, 主题：{}, 错误信息：{}", to, subject, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 发送带多个附件的邮件
     *
     * @param to 收件人邮箱
     * @param subject 邮件主题
     * @param content 邮件内容
     * @param filePaths 附件文件路径列表
     * @return 发送结果
     */
    public boolean sendAttachmentMail(String to, String subject, String content, List<String> filePaths) {
        try {
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");

            helper.setFrom(from);
            helper.setTo(to);
            helper.setSubject(subject);
            helper.setText(content);

            // 添加多个附件
            for (String filePath : filePaths) {
                FileSystemResource file = new FileSystemResource(new File(filePath));
                if (file.exists()) {
                    helper.addAttachment(file.getFilename(), file);
                } else {
                    logger.warn("附件文件不存在：{}", filePath);
                }
            }

            mailSender.send(message);
            logger.info("带多个附件邮件发送成功，收件人：{}, 主题：{}, 附件：{}", to, subject, filePaths);
            return true;
        } catch (MessagingException e) {
            logger.error("带多个附件邮件发送失败，收件人：{}, 主题：{}, 错误信息：{}", to, subject, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 发送HTML格式邮件并带附件
     *
     * @param to 收件人邮箱
     * @param subject 邮件主题
     * @param htmlContent HTML内容
     * @param filePath 附件文件路径
     * @param fileName 附件文件名（可选，如果为null则使用文件原名）
     * @return 发送结果
     */
    public boolean sendHtmlAttachmentMail(String to, String subject, String htmlContent, String filePath, String fileName) {
        try {
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");

            helper.setFrom(from);
            helper.setTo(to);
            helper.setSubject(subject);
            helper.setText(htmlContent, true); // true表示HTML格式

            // 添加附件
            FileSystemResource file = new FileSystemResource(new File(filePath));
            if (file.exists()) {
                String attachmentName = fileName != null ? fileName : file.getFilename();
                helper.addAttachment(attachmentName, file);
            } else {
                logger.warn("附件文件不存在：{}", filePath);
                return false;
            }

            mailSender.send(message);
            logger.info("HTML带附件邮件发送成功，收件人：{}, 主题：{}, 附件：{}", to, subject, filePath);
            return true;
        } catch (MessagingException e) {
            logger.error("HTML带附件邮件发送失败，收件人：{}, 主题：{}, 错误信息：{}", to, subject, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 发送邮件（支持抄送和密送）
     *
     * @param to 收件人邮箱
     * @param cc 抄送邮箱列表（可为null）
     * @param bcc 密送邮箱列表（可为null）
     * @param subject 邮件主题
     * @param content 邮件内容
     * @param isHtml 是否为HTML格式
     * @return 发送结果
     */
    public boolean sendMail(String to, List<String> cc, List<String> bcc, String subject, String content, boolean isHtml) {
        try {
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");

            helper.setFrom(from);
            helper.setTo(to);
            helper.setSubject(subject);
            helper.setText(content, isHtml);

            // 设置抄送
            if (cc != null && !cc.isEmpty()) {
                helper.setCc(cc.toArray(new String[0]));
            }

            // 设置密送
            if (bcc != null && !bcc.isEmpty()) {
                helper.setBcc(bcc.toArray(new String[0]));
            }

            mailSender.send(message);
            logger.info("邮件发送成功，收件人：{}, 抄送：{}, 密送：{}, 主题：{}", to, cc, bcc, subject);
            return true;
        } catch (MessagingException e) {
            logger.error("邮件发送失败，收件人：{}, 主题：{}, 错误信息：{}", to, subject, e.getMessage(), e);
            return false;
        }
    }
}
