package com.sinoair.agent.util;

import java.security.SecureRandom;

/**
 * 密码生成工具类
 */
public class PasswordUtil {
    
    // 密码字符集：大写字母、小写字母、数字、特殊符号
    private static final String UPPERCASE = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    private static final String LOWERCASE = "abcdefghijklmnopqrstuvwxyz";
    private static final String DIGITS = "0123456789";
    private static final String SYMBOLS = "!@#$%^&*()_+-=[]{}|;:,.<>?";
    
    private static final String ALL_CHARS = UPPERCASE + LOWERCASE + DIGITS + SYMBOLS;
    private static final SecureRandom RANDOM = new SecureRandom();
    
    /**
     * 生成指定长度的随机密码
     * 包含大写字母、小写字母、数字和特殊符号
     * 
     * @param length 密码长度
     * @return 随机密码
     */
    public static String generateRandomPassword(int length) {
        if (length < 4) {
            throw new IllegalArgumentException("密码长度至少为4位");
        }
        
        StringBuilder password = new StringBuilder(length);
        
        // 确保密码包含每种类型的字符至少一个
        password.append(UPPERCASE.charAt(RANDOM.nextInt(UPPERCASE.length())));
        password.append(LOWERCASE.charAt(RANDOM.nextInt(LOWERCASE.length())));
        password.append(DIGITS.charAt(RANDOM.nextInt(DIGITS.length())));
        password.append(SYMBOLS.charAt(RANDOM.nextInt(SYMBOLS.length())));
        
        // 填充剩余长度
        for (int i = 4; i < length; i++) {
            password.append(ALL_CHARS.charAt(RANDOM.nextInt(ALL_CHARS.length())));
        }
        
        // 打乱字符顺序
        return shuffleString(password.toString());
    }
    
    /**
     * 生成8位随机密码（默认长度）
     * 
     * @return 8位随机密码
     */
    public static String generateRandomPassword() {
        return generateRandomPassword(8);
    }
    
    /**
     * 打乱字符串中字符的顺序
     * 
     * @param input 输入字符串
     * @return 打乱后的字符串
     */
    private static String shuffleString(String input) {
        char[] chars = input.toCharArray();
        
        // Fisher-Yates洗牌算法
        for (int i = chars.length - 1; i > 0; i--) {
            int j = RANDOM.nextInt(i + 1);
            char temp = chars[i];
            chars[i] = chars[j];
            chars[j] = temp;
        }
        
        return new String(chars);
    }
}
