package com.sinoair.agent.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sinoair.agent.entity.OperationLog;
import com.sinoair.agent.entity.User;
import com.sinoair.agent.security.UserPrincipal;
import com.sinoair.agent.service.OperationLogService;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 日志记录工具类
 * 提供便捷的日志记录方法
 *
 * <AUTHOR> Team
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class LogRecordUtil {

    private final OperationLogService operationLogService;
    private final ObjectMapper objectMapper;

    /**
     * 记录操作日志
     */
    @Async("taskExecutor")
    public void recordLog(String module, String operationType, String operationDesc) {
        recordLogSync(module, operationType, operationDesc, null, null, null);
    }

    /**
     * 记录操作日志（带请求参数）
     */
    @Async("taskExecutor")
    public void recordLog(String module, String operationType, String operationDesc,
                         Map<String, Object> requestParams) {
        recordLogSync(module, operationType, operationDesc, requestParams, null, null);
    }

    /**
     * 记录操作日志（带请求参数和响应结果）
     */
    @Async("taskExecutor")
    public void recordLog(String module, String operationType, String operationDesc,
                         Map<String, Object> requestParams, Map<String, Object> responseResult) {
        recordLogSync(module, operationType, operationDesc, requestParams, responseResult, null);
    }

    /**
     * 记录完整操作日志
     */
    @Async("taskExecutor")
    public void recordLog(String module, String operationType, String operationDesc,
                         Map<String, Object> requestParams, Map<String, Object> responseResult,
                         Map<String, Object> businessData) {
        recordLogSync(module, operationType, operationDesc, requestParams, responseResult, businessData);
    }

    /**
     * 同步记录完整操作日志
     */
    private void recordLogSync(String module, String operationType, String operationDesc,
                              Map<String, Object> requestParams, Map<String, Object> responseResult,
                              Map<String, Object> businessData) {
        try {
            OperationLog operationLog = buildOperationLog(module, operationType, operationDesc,
                                                         requestParams, responseResult, businessData);
            // operationLogService.saveLog(operationLog);
            log.debug("操作日志记录成功: module={}, operationType={}, desc={}", module, operationType, operationDesc);
        } catch (Exception e) {
            log.error("记录操作日志失败: module={}, operationType={}, desc={}", module, operationType, operationDesc, e);
        }
    }

    /**
     * 记录成功操作日志
     */
    @Async("taskExecutor")
    public void recordSuccessLog(String module, String operationType, String operationDesc,
                                Map<String, Object> requestParams, Map<String, Object> responseResult) {
        recordLogWithStatus(module, operationType, operationDesc, requestParams, responseResult,
                           OperationLog.Status.SUCCESS.getCode(), null, null);
    }

    /**
     * 记录失败操作日志
     */
    @Async("taskExecutor")
    public void recordFailedLog(String module, String operationType, String operationDesc,
                               Map<String, Object> requestParams, String errorMessage) {
        recordLogWithStatus(module, operationType, operationDesc, requestParams, null,
                           OperationLog.Status.FAILED.getCode(), errorMessage, null);
    }

    /**
     * 记录错误操作日志
     */
    @Async("taskExecutor")
    public void recordErrorLog(String module, String operationType, String operationDesc,
                              Map<String, Object> requestParams, String errorMessage, Exception exception) {
        Map<String, Object> errorData = new HashMap<>();
        if (exception != null) {
            errorData.put("exceptionClass", exception.getClass().getSimpleName());
            errorData.put("exceptionMessage", exception.getMessage());
        }

        recordLogWithStatus(module, operationType, operationDesc, requestParams, null,
                           OperationLog.Status.ERROR.getCode(), errorMessage, errorData);
    }

    /**
     * 记录带状态的操作日志
     */
    @Async("taskExecutor")
    public void recordLog(String module, String operationType, String operationDesc,
                         Map<String, Object> requestParams, Map<String, Object> responseResult,
                         String status, String errorMessage, Map<String, Object> businessData) {
        recordLogWithStatus(module, operationType, operationDesc, requestParams, responseResult,
                           status, errorMessage, businessData);
    }

    /**
     * 同步记录带状态的操作日志
     */
    private void recordLogWithStatus(String module, String operationType, String operationDesc,
                                    Map<String, Object> requestParams, Map<String, Object> responseResult,
                                    String status, String errorMessage, Map<String, Object> businessData) {
        try {
            OperationLog operationLog = buildOperationLog(module, operationType, operationDesc,
                                                         requestParams, responseResult, businessData);
            operationLog.setStatus(status);
            operationLog.setErrorMessage(errorMessage);

            operationLogService.saveLog(operationLog);
            log.debug("操作日志记录成功: module={}, operationType={}, status={}", module, operationType, status);
        } catch (Exception e) {
            log.error("记录操作日志失败: module={}, operationType={}, status={}", module, operationType, status, e);
        }
    }

    /**
     * 构建操作日志对象
     */
    private OperationLog buildOperationLog(String module, String operationType, String operationDesc, 
                                          Map<String, Object> requestParams, Map<String, Object> responseResult,
                                          Map<String, Object> businessData) {
        OperationLog.OperationLogBuilder builder = OperationLog.builder()
                .module(module)
                .operationType(operationType)
                .operationDesc(operationDesc)
                .requestParams(requestParams)
                .responseResult(responseResult)
                .businessData(businessData)
                .operationTime(LocalDateTime.now())
                .createdTime(LocalDateTime.now());

        // 获取当前用户信息
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null && authentication.isAuthenticated() &&
                !"anonymousUser".equals(authentication.getPrincipal())) {

                Object principal = authentication.getPrincipal();
                if (principal instanceof UserPrincipal) {
                    UserPrincipal userPrincipal = (UserPrincipal) principal;
                    builder.userId(userPrincipal.getId())
                           .username(userPrincipal.getUsername())
                           .realName(userPrincipal.getRealName());
                    log.debug("获取用户信息成功: userId={}, username={}, realName={}",
                             userPrincipal.getId(), userPrincipal.getUsername(), userPrincipal.getRealName());
                } else if (principal instanceof User) {
                    User user = (User) principal;
                    builder.userId(user.getId())
                           .username(user.getUsername())
                           .realName(user.getRealName());
                    log.debug("获取用户信息成功(User): userId={}, username={}, realName={}",
                             user.getId(), user.getUsername(), user.getRealName());
                } else if (principal instanceof String) {
                    builder.username((String) principal);
                    log.debug("获取用户名成功: username={}", principal);
                } else {
                    log.debug("未知的Principal类型: {}", principal.getClass().getName());
                }
            } else {
                log.debug("用户未认证或为匿名用户");
            }
        } catch (Exception e) {
            log.error("获取用户信息失败", e);
        }

        // 获取HTTP请求信息
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();

                String requestUrl = request.getRequestURL().toString();
                String queryString = request.getQueryString();
                if (queryString != null && !queryString.isEmpty()) {
                    requestUrl += "?" + queryString;
                }

                builder.requestMethod(request.getMethod())
                       .requestUrl(requestUrl)
                       .clientIp(getClientIpAddress(request))
                       .userAgent(request.getHeader("User-Agent"));

                // 获取请求体数据（如果是POST/PUT请求）
                if ("POST".equalsIgnoreCase(request.getMethod()) ||
                    "PUT".equalsIgnoreCase(request.getMethod()) ||
                    "PATCH".equalsIgnoreCase(request.getMethod())) {
                    Map<String, Object> requestBody = extractRequestBody(request);
                    if (requestBody != null && !requestBody.isEmpty()) {
                        builder.requestBody(requestBody);
                    }
                }

                log.debug("获取HTTP请求信息成功: method={}, url={}, ip={}",
                         request.getMethod(), requestUrl, getClientIpAddress(request));
            } else {
                log.debug("无法获取HTTP请求信息，RequestAttributes为null");
            }
        } catch (Exception e) {
            log.error("获取HTTP请求信息失败", e);
        }

        // 设置默认状态
        if (builder.build().getStatus() == null) {
            builder.status(OperationLog.Status.SUCCESS.getCode());
        }

        return builder.build();
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String[] headerNames = {
            "X-Forwarded-For",
            "X-Real-IP",
            "Proxy-Client-IP",
            "WL-Proxy-Client-IP",
            "HTTP_CLIENT_IP",
            "HTTP_X_FORWARDED_FOR"
        };

        for (String headerName : headerNames) {
            String ip = request.getHeader(headerName);
            if (ip != null && !ip.isEmpty() && !"unknown".equalsIgnoreCase(ip)) {
                // 多级反向代理的情况下，第一个IP为客户端真实IP
                if (ip.contains(",")) {
                    ip = ip.split(",")[0].trim();
                }
                return ip;
            }
        }

        return request.getRemoteAddr();
    }

    /**
     * 提取请求体数据
     */
    private Map<String, Object> extractRequestBody(HttpServletRequest request) {
        Map<String, Object> requestBody = new HashMap<>();
        
        try {
            // 获取表单参数
            Map<String, String[]> parameterMap = request.getParameterMap();
            for (Map.Entry<String, String[]> entry : parameterMap.entrySet()) {
                String key = entry.getKey();
                String[] values = entry.getValue();
                
                // 过滤敏感信息
                if (isSensitiveField(key)) {
                    requestBody.put(key, "***");
                } else {
                    if (values.length == 1) {
                        requestBody.put(key, values[0]);
                    } else {
                        requestBody.put(key, values);
                    }
                }
            }
        } catch (Exception e) {
            log.debug("提取请求体数据失败", e);
        }
        
        return requestBody;
    }

    /**
     * 判断是否为敏感字段
     */
    private boolean isSensitiveField(String fieldName) {
        if (fieldName == null) {
            return false;
        }
        
        String lowerFieldName = fieldName.toLowerCase();
        return lowerFieldName.contains("password") || 
               lowerFieldName.contains("pwd") || 
               lowerFieldName.contains("secret") || 
               lowerFieldName.contains("token") || 
               lowerFieldName.contains("key");
    }

    /**
     * 记录Agent相关操作日志
     */
    public void recordAgentLog(String operationType, String operationDesc, Long agentId, 
                              String agentName, Map<String, Object> agentData) {
        Map<String, Object> businessData = new HashMap<>();
        businessData.put("agentId", agentId);
        businessData.put("agentName", agentName);
        if (agentData != null) {
            businessData.putAll(agentData);
        }
        
        recordLog("Agent管理", operationType, operationDesc, null, null, businessData);
    }

    /**
     * 记录文件操作日志
     */
    public void recordFileLog(String operationType, String operationDesc, String fileName, 
                             Long fileSize, String fileType) {
        Map<String, Object> businessData = new HashMap<>();
        businessData.put("fileName", fileName);
        businessData.put("fileSize", fileSize);
        businessData.put("fileType", fileType);
        
        recordLog("文件管理", operationType, operationDesc, null, null, businessData);
    }

    /**
     * 记录用户操作日志
     */
    public void recordUserLog(String operationType, String operationDesc, Long targetUserId,
                             String targetUsername) {
        Map<String, Object> businessData = new HashMap<>();
        businessData.put("targetUserId", targetUserId);
        businessData.put("targetUsername", targetUsername);

        recordLog("用户管理", operationType, operationDesc, null, null, businessData);
    }

    /**
     * 直接保存日志（同步方式，用于AOP切面）
     */
    public void saveLogDirectly(OperationLog operationLog) {
        try {
            operationLogService.saveLog(operationLog);
            log.debug("直接保存日志成功: module={}, operationType={}, userId={}",
                     operationLog.getModule(), operationLog.getOperationType(), operationLog.getUserId());
        } catch (Exception e) {
            log.error("直接保存日志失败: module={}, operationType={}",
                     operationLog.getModule(), operationLog.getOperationType(), e);
        }
    }
}
