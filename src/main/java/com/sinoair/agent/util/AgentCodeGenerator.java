package com.sinoair.agent.util;

import lombok.extern.slf4j.Slf4j;

import java.util.HashSet;
import java.util.Set;

/**
 * Agent编码生成工具类
 * 用于生成6位随机数字和字母组合的Agent编码
 * 
 * <AUTHOR> Team
 */
@Slf4j
public class AgentCodeGenerator {
    
    /**
     * 字符集：数字0-9 + 大写字母A-Z (共36个字符)
     */
    private static final String CHARACTERS = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    
    /**
     * 编码长度
     */
    private static final int CODE_LENGTH = 6;
    
    /**
     * 生成6位随机编码（数字+大写字母）
     * 
     * @return 6位随机编码
     */
    public static String generateRandomCode() {
        StringBuilder code = new StringBuilder();
        java.util.Random random = new java.util.Random();
        
        for (int i = 0; i < CODE_LENGTH; i++) {
            int index = random.nextInt(CHARACTERS.length());
            code.append(CHARACTERS.charAt(index));
        }
        
        return code.toString();
    }
    
    /**
     * 批量生成编码用于测试（检查重复率）
     * 
     * @param count 生成数量
     * @return 生成的编码集合
     */
    public static Set<String> generateBatchCodes(int count) {
        Set<String> codes = new HashSet<>();
        int duplicateCount = 0;
        
        for (int i = 0; i < count; i++) {
            String code = generateRandomCode();
            if (!codes.add(code)) {
                duplicateCount++;
                log.warn("发现重复编码: {}", code);
            }
        }
        
        log.info("生成{}个编码，其中重复{}个，重复率: {:.2f}%", 
                count, duplicateCount, (double) duplicateCount / count * 100);
        
        return codes;
    }
    
    /**
     * 计算理论上的编码容量
     * 
     * @return 理论编码容量
     */
    public static long getTheoreticalCapacity() {
        return (long) Math.pow(CHARACTERS.length(), CODE_LENGTH);
    }
    
    /**
     * 验证编码格式是否正确
     * 
     * @param code 待验证的编码
     * @return 是否符合格式要求
     */
    public static boolean isValidCode(String code) {
        if (code == null || code.length() != CODE_LENGTH) {
            return false;
        }
        
        for (char c : code.toCharArray()) {
            if (CHARACTERS.indexOf(c) == -1) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 测试方法
     */
    public static void main(String[] args) {
        log.info("Agent编码生成器测试");
        log.info("字符集: {}", CHARACTERS);
        log.info("编码长度: {}", CODE_LENGTH);
        log.info("理论容量: {} (约{}万)", getTheoreticalCapacity(), getTheoreticalCapacity() / 10000);
        
        // 生成一些示例编码
        log.info("示例编码:");
        for (int i = 0; i < 10; i++) {
            String code = generateRandomCode();
            log.info("  {}: {}", i + 1, code);
        }
        
        // 测试重复率
        log.info("重复率测试:");
        generateBatchCodes(10000);
    }
}
