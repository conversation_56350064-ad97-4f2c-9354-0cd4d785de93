package com.sinoair.agent.util;

import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.Socket;
import java.net.SocketTimeoutException;

/**
 * 网络连接测试工具
 * 
 * <AUTHOR> Team
 */
@Slf4j
public class NetworkTestUtil {

    /**
     * 测试TCP连接
     * 
     * @param host 主机地址
     * @param port 端口号
     * @param timeoutMs 超时时间（毫秒）
     * @return 连接是否成功
     */
    public static boolean testTcpConnection(String host, int port, int timeoutMs) {
        try (Socket socket = new Socket()) {
            socket.connect(new InetSocketAddress(host, port), timeoutMs);
            log.info("TCP连接测试成功: {}:{}", host, port);
            return true;
        } catch (SocketTimeoutException e) {
            log.warn("TCP连接超时: {}:{} ({}ms)", host, port, timeoutMs);
            return false;
        } catch (IOException e) {
            log.warn("TCP连接失败: {}:{} - {}", host, port, e.getMessage());
            return false;
        }
    }

    /**
     * 从URL中提取主机名
     * 
     * @param url URL地址
     * @return 主机名
     */
    public static String extractHost(String url) {
        try {
            return url.replaceAll("^https?://", "").split(":")[0];
        } catch (Exception e) {
            log.warn("提取主机名失败: {}", url, e);
            return "localhost";
        }
    }

    /**
     * 从URL中提取端口号
     * 
     * @param url URL地址
     * @param defaultPort 默认端口
     * @return 端口号
     */
    public static int extractPort(String url, int defaultPort) {
        try {
            String[] parts = url.replaceAll("^https?://", "").split(":");
            return parts.length > 1 ? Integer.parseInt(parts[1]) : defaultPort;
        } catch (Exception e) {
            log.warn("提取端口号失败: {}, 使用默认端口: {}", url, defaultPort);
            return defaultPort;
        }
    }

    /**
     * 测试MinIO连接
     * 
     * @param endpoint MinIO端点
     * @return 连接是否成功
     */
    public static boolean testMinioConnection(String endpoint) {
        String host = extractHost(endpoint);
        int port = extractPort(endpoint, 9000);
        
        log.info("测试MinIO连接: {}:{}", host, port);
        
        // 测试多种超时时间
        int[] timeouts = {5000, 10000, 30000};
        
        for (int timeout : timeouts) {
            if (testTcpConnection(host, port, timeout)) {
                return true;
            }
        }
        
        return false;
    }
}
