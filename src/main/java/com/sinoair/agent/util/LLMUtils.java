package com.sinoair.agent.util;

import lombok.extern.slf4j.Slf4j;

/**
 * 这里写功能描述
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/07/11/11:23
 */
@Slf4j
public class LLMUtils {

    public static String getSystemPrompt(String promptTemplate, String jsonTemplate) {
        StringBuilder prompt = new StringBuilder();

        log.info("系统封装提示词");
        prompt.append(promptTemplate).append("\n\n");
        prompt.append("这是一个JSON的例子给你参考，请按照这个格式返回结果，字段值可以为空：\n");
        // prompt.append("请严格按照以下JSON格式返回结果，不要添加任何额外的解释或文本：\n");
        prompt.append(jsonTemplate).append("\n\n");
        // prompt.append("请确保返回的是有效的JSON格式，包含所有必要的字段，即使某些字段值为空。不要在JSON前后添加任何额外文本、解释或代码块标记。\n\n");

        return prompt.toString();
    }

    public static String trimJsonTag(String content) {
        if (content == null || content.trim().isEmpty()) {
            return "{}";
        }
        content = content.trim();
        // 移除前置的```json
        if (content.startsWith("```json")) {
            content = content.substring(7);
        }
        // 移除最后的```
        if (content.endsWith("```")) {
            content = content.substring(0, content.length() - 3);
        }
        content = content.trim();
        return content;
    }
}
