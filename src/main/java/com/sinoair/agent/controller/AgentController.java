package com.sinoair.agent.controller;

import com.sinoair.agent.annotation.OperationLogRecord;
import com.sinoair.agent.common.PageResult;
import com.sinoair.agent.common.Result;
import com.sinoair.agent.dto.request.CreateAgentRequest;
import com.sinoair.agent.dto.request.CreateVersionRequest;
import com.sinoair.agent.dto.response.AgentVO;
import com.sinoair.agent.entity.Agent;
import com.sinoair.agent.entity.AgentApprovalRecord;
import com.sinoair.agent.entity.AgentDebugHistory;
import com.sinoair.agent.entity.AgentVersion;
import com.sinoair.agent.security.UserPrincipal;
import com.sinoair.agent.service.AgentApprovalService;
import com.sinoair.agent.service.AgentDebugHistoryService;
import com.sinoair.agent.service.AgentService;
import com.sinoair.agent.service.AgentVersionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Agent控制器
 * 
 * <AUTHOR> Team
 */
@Tag(name = "Agent管理", description = "Agent的创建、编辑、删除、查询等管理功能")
@RestController
@RequestMapping("/api/v1/agents")
@RequiredArgsConstructor
public class AgentController {

    private final AgentService agentService;
    private final AgentVersionService agentVersionService;
    private final AgentDebugHistoryService debugHistoryService;
    private final AgentApprovalService agentApprovalService;

    @Operation(summary = "创建Agent", description = "创建新的Agent")
    @PostMapping
    @OperationLogRecord(module = "Agent管理", operationType = "CREATE", operationDesc = "创建新的Agent")
    public Result<AgentVO> createAgent(@Valid @RequestBody CreateAgentRequest request) {
        return agentService.createAgent(request);
    }

    @Operation(summary = "更新Agent", description = "更新指定ID的Agent信息")
    @PutMapping("/{id}")
    @OperationLogRecord(module = "Agent管理", operationType = "UPDATE", operationDesc = "更新Agent信息")
    public Result<AgentVO> updateAgent(
            @Parameter(description = "Agent ID") @PathVariable Long id,
            @Valid @RequestBody CreateAgentRequest request) {
        return agentService.updateAgent(id, request);
    }

    @Operation(summary = "获取Agent详情", description = "根据ID获取Agent详细信息")
    @GetMapping("/{id}")
    public Result<AgentVO> getAgent(@Parameter(description = "Agent ID") @PathVariable Long id) {
        return agentService.getAgent(id);
    }

    @Operation(summary = "删除Agent", description = "删除指定ID的Agent")
    @DeleteMapping("/{id}")
    @OperationLogRecord(module = "Agent管理", operationType = "DELETE", operationDesc = "删除Agent")
    public Result<String> deleteAgent(@Parameter(description = "Agent ID") @PathVariable Long id) {
        return agentService.deleteAgent(id);
    }

    @Operation(summary = "查询Agent列表", description = "分页查询Agent列表，支持多条件筛选")
    @GetMapping
    public Result<PageResult<AgentVO>> getAgentList(
            @Parameter(description = "分类ID") @RequestParam(required = false) Long categoryId,
            @Parameter(description = "业务类型ID") @RequestParam(required = false) Long businessTypeId,
            @Parameter(description = "状态") @RequestParam(required = false) Integer status,
            @Parameter(description = "关键词") @RequestParam(required = false) String keyword,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") int size,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        return agentService.getAgentList(categoryId, businessTypeId, status, keyword, page, size, userPrincipal);
    }

    @Operation(summary = "检查Agent名称是否存在", description = "检查指定的Agent名称是否已被使用")
    @GetMapping("/check-name")
    public Result<Boolean> checkAgentNameExists(
            @Parameter(description = "Agent名称") @RequestParam String agentName,
            @Parameter(description = "排除的Agent ID") @RequestParam(required = false) Long excludeId) {
        return agentService.checkAgentNameExists(agentName, excludeId);
    }

    @Operation(summary = "发布Agent", description = "将Agent状态设置为已发布")
    @PostMapping("/{id}/publish")
    @OperationLogRecord(module = "Agent管理", operationType = "PUBLISH", operationDesc = "发布Agent")
    public Result<String> publishAgent(@Parameter(description = "Agent ID") @PathVariable Long id) {
        return agentService.publishAgent(id);
    }

    @Operation(summary = "下线Agent", description = "将Agent状态设置为已下线")
    @PostMapping("/{id}/unpublish")
    @OperationLogRecord(module = "Agent管理", operationType = "UNPUBLISH", operationDesc = "下线Agent")
    public Result<String> unpublishAgent(@Parameter(description = "Agent ID") @PathVariable Long id) {
        return agentService.unpublishAgent(id);
    }

    @Operation(summary = "克隆Agent", description = "基于现有Agent创建副本")
    @PostMapping("/{id}/clone")
    @OperationLogRecord(module = "Agent管理", operationType = "CLONE", operationDesc = "克隆Agent")
    public Result<AgentVO> cloneAgent(
            @Parameter(description = "Agent ID") @PathVariable Long id,
            @RequestBody java.util.Map<String, String> request) {
        String newName = request.get("newName");
        return agentService.cloneAgent(id, newName);
    }

    @Operation(summary = "获取Agent版本列表", description = "获取指定Agent的所有版本历史")
    @GetMapping("/{id}/versions")
    public Result<List<AgentVersion>> getVersionList(@Parameter(description = "Agent ID") @PathVariable Long id) {
        return agentVersionService.getVersionList(id);
    }



    @Operation(summary = "创建版本快照", description = "为Agent创建版本快照")
    @PostMapping("/{id}/snapshot")
    @OperationLogRecord(module = "Agent管理", operationType = "CREATE", operationDesc = "创建Agent版本快照")
    public Result<AgentVersion> createVersionSnapshot(
            @Parameter(description = "Agent ID") @PathVariable Long id,
            @RequestBody java.util.Map<String, String> request) {
        String changeLog = request.get("changeLog");
        return agentVersionService.createVersionSnapshot(id, changeLog);
    }

    @Operation(summary = "创建新版本", description = "基于调试台参数创建Agent新版本")
    @PostMapping("/{id}/versions")
    @OperationLogRecord(module = "Agent管理", operationType = "CREATE", operationDesc = "创建Agent新版本")
    public Result<AgentVersion> createNewVersion(
            @Parameter(description = "Agent ID") @PathVariable Long id,
            @Valid @RequestBody CreateVersionRequest request) {
        return agentVersionService.createNewVersion(id, request);
    }

    @Operation(summary = "更新版本状态为测试中", description = "将指定版本的状态更新为测试中")
    @PutMapping("/versions/{versionId}/status/testing")
    @OperationLogRecord(module = "Agent管理", operationType = "UPDATE", operationDesc = "更新版本状态为测试中")
    public Result<String> updateVersionStatusToTesting(
            @Parameter(description = "版本ID") @PathVariable Long versionId) {
        return agentVersionService.updateVersionStatusToTesting(versionId);
    }

    @Operation(summary = "回滚到指定版本", description = "将Agent回滚到指定版本")
    @PostMapping("/{id}/rollback")
    @OperationLogRecord(module = "Agent管理", operationType = "ROLLBACK", operationDesc = "回滚Agent版本")
    public Result<String> rollbackToVersion(
            @Parameter(description = "Agent ID") @PathVariable Long id,
            @RequestBody java.util.Map<String, String> request) {
        String versionNumber = request.get("versionNumber");
        return agentVersionService.rollbackToVersion(id, versionNumber);
    }

    // ==================== 调试历史相关接口 ====================

    @Operation(summary = "获取调试历史", description = "获取Agent的调试历史记录")
    @GetMapping("/{id}/debug-history")
    public Result<List<AgentDebugHistory>> getDebugHistory(
            @Parameter(description = "Agent ID") @PathVariable Long id) {
        return debugHistoryService.getDebugHistoryByAgentId(id);
    }

    @Operation(summary = "分页查询调试历史", description = "分页查询Agent的调试历史记录")
    @GetMapping("/{id}/debug-history/page")
    public Result<IPage<AgentDebugHistory>> getDebugHistoryPage(
            @Parameter(description = "Agent ID") @PathVariable Long id,
            @Parameter(description = "用户ID") @RequestParam(required = false) Long userId,
            @Parameter(description = "是否成功") @RequestParam(required = false) Boolean success,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") int size) {
        return debugHistoryService.getDebugHistoryPage(id, userId, success, page, size);
    }

    @Operation(summary = "获取调试历史详情", description = "根据ID获取调试历史详情")
    @GetMapping("/debug-history/{debugId}")
    public Result<AgentDebugHistory> getDebugHistoryDetail(
            @Parameter(description = "调试历史ID") @PathVariable Long debugId) {
        return debugHistoryService.getDebugHistoryById(debugId);
    }

    @Operation(summary = "获取调试统计信息", description = "获取Agent的调试统计信息")
    @GetMapping("/{id}/debug-statistics")
    public Result<Map<String, Object>> getDebugStatistics(
            @Parameter(description = "Agent ID") @PathVariable Long id) {
        return debugHistoryService.getDebugStatistics(id);
    }

    @Operation(summary = "获取最近调试记录", description = "获取Agent最近的调试记录")
    @GetMapping("/{id}/debug-history/recent")
    public Result<List<AgentDebugHistory>> getRecentDebugHistory(
            @Parameter(description = "Agent ID") @PathVariable Long id,
            @Parameter(description = "记录数量") @RequestParam(defaultValue = "10") Integer limit) {
        return debugHistoryService.getRecentDebugHistory(id, limit);
    }

    @Operation(summary = "保存调试历史", description = "保存调试历史记录")
    @PostMapping("/{id}/debug-history")
    public Result<AgentDebugHistory> saveDebugHistory(
            @Parameter(description = "Agent ID") @PathVariable Long id,
            @Valid @RequestBody AgentDebugHistory debugHistory) {
        debugHistory.setAgentId(id);
        return debugHistoryService.saveDebugHistory(debugHistory);
    }

    @Operation(summary = "删除调试历史", description = "删除指定的调试历史记录")
    @DeleteMapping("/debug-history/{debugId}")
    public Result<String> deleteDebugHistory(
            @Parameter(description = "调试历史ID") @PathVariable Long debugId) {
        return debugHistoryService.deleteDebugHistory(debugId);
    }

    @Operation(summary = "批量删除调试历史", description = "批量删除调试历史记录")
    @DeleteMapping("/debug-history/batch")
    public Result<String> batchDeleteDebugHistory(
            @RequestBody List<Long> ids) {
        return debugHistoryService.batchDeleteDebugHistory(ids);
    }
}
