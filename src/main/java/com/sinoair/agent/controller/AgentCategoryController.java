package com.sinoair.agent.controller;

import com.sinoair.agent.common.Result;
import com.sinoair.agent.entity.AgentCategory;
import com.sinoair.agent.service.AgentCategoryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * Agent分类管理控制器
 * 
 * <AUTHOR> Team
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/agent-categories")
@RequiredArgsConstructor
@Tag(name = "Agent分类管理", description = "Agent分类的增删改查等管理功能")
public class AgentCategoryController {

    private final AgentCategoryService agentCategoryService;

    @Operation(summary = "获取所有分类", description = "获取所有Agent分类列表（兼容旧接口）")
    @GetMapping
    public Result<List<AgentCategory>> getAllCategories() {
        try {
            List<AgentCategory> categories = agentCategoryService.getAllActiveCategories();
            return Result.success("获取分类成功", categories);
        } catch (Exception e) {
            log.error("获取分类失败", e);
            return Result.error("获取分类失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取所有可用分类", description = "获取所有状态为可用的Agent分类列表")
    @GetMapping("/active")
    public Result<List<AgentCategory>> getActiveCategories() {
        try {
            List<AgentCategory> categories = agentCategoryService.getAllActiveCategories();
            return Result.success("获取可用分类成功", categories);
        } catch (Exception e) {
            log.error("获取可用分类失败", e);
            return Result.error("获取可用分类失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取分类树形结构", description = "获取Agent分类的树形结构")
    @GetMapping("/tree")
    public Result<List<AgentCategory>> getCategoryTree() {
        try {
            List<AgentCategory> categories = agentCategoryService.getCategoryTree();
            return Result.success("获取分类树形结构成功", categories);
        } catch (Exception e) {
            log.error("获取分类树形结构失败", e);
            return Result.error("获取分类树形结构失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取根分类", description = "获取所有根级分类")
    @GetMapping("/root")
    public Result<List<AgentCategory>> getRootCategories() {
        try {
            List<AgentCategory> categories = agentCategoryService.getRootCategories();
            return Result.success("获取根分类成功", categories);
        } catch (Exception e) {
            log.error("获取根分类失败", e);
            return Result.error("获取根分类失败: " + e.getMessage());
        }
    }

    @Operation(summary = "根据父级ID获取子分类", description = "根据父级分类ID获取其下的子分类")
    @GetMapping("/children/{parentId}")
    public Result<List<AgentCategory>> getChildrenCategories(
            @Parameter(description = "父级分类ID") @PathVariable Long parentId) {
        try {
            List<AgentCategory> categories = agentCategoryService.getByParentId(parentId);
            return Result.success("获取子分类成功", categories);
        } catch (Exception e) {
            log.error("获取子分类失败: parentId={}", parentId, e);
            return Result.error("获取子分类失败: " + e.getMessage());
        }
    }

    @Operation(summary = "根据分类代码获取分类", description = "根据分类代码获取分类信息")
    @GetMapping("/code/{categoryCode}")
    public Result<AgentCategory> getCategoryByCode(
            @Parameter(description = "分类代码") @PathVariable String categoryCode) {
        try {
            AgentCategory category = agentCategoryService.getByCategoryCode(categoryCode);
            if (category == null) {
                return Result.error("分类不存在");
            }
            return Result.success("获取分类成功", category);
        } catch (Exception e) {
            log.error("根据分类代码获取分类失败: categoryCode={}", categoryCode, e);
            return Result.error("获取分类失败: " + e.getMessage());
        }
    }

    @Operation(summary = "根据ID获取分类详情", description = "根据分类ID获取分类详细信息")
    @GetMapping("/{id}")
    public Result<AgentCategory> getCategoryById(
            @Parameter(description = "分类ID") @PathVariable Long id) {
        try {
            AgentCategory category = agentCategoryService.getById(id);
            if (category == null) {
                return Result.error("分类不存在");
            }
            return Result.success("获取分类详情成功", category);
        } catch (Exception e) {
            log.error("根据ID获取分类详情失败: id={}", id, e);
            return Result.error("获取分类详情失败: " + e.getMessage());
        }
    }

    @Operation(summary = "创建分类", description = "创建新的Agent分类")
    @PostMapping
    public Result<String> createCategory(@Valid @RequestBody AgentCategory category) {
        try {
            boolean success = agentCategoryService.createCategory(category);
            if (success) {
                return Result.success("创建分类成功");
            } else {
                return Result.error("创建分类失败");
            }
        } catch (Exception e) {
            log.error("创建分类失败: categoryName={}", category.getCategoryName(), e);
            return Result.error("创建分类失败: " + e.getMessage());
        }
    }

    @Operation(summary = "更新分类", description = "更新Agent分类信息")
    @PutMapping("/{id}")
    public Result<String> updateCategory(
            @Parameter(description = "分类ID") @PathVariable Long id,
            @Valid @RequestBody AgentCategory category) {
        try {
            category.setId(id);
            boolean success = agentCategoryService.updateCategory(category);
            if (success) {
                return Result.success("更新分类成功");
            } else {
                return Result.error("更新分类失败");
            }
        } catch (Exception e) {
            log.error("更新分类失败: id={}, categoryName={}", id, category.getCategoryName(), e);
            return Result.error("更新分类失败: " + e.getMessage());
        }
    }

    @Operation(summary = "删除分类", description = "删除Agent分类")
    @DeleteMapping("/{id}")
    public Result<String> deleteCategory(
            @Parameter(description = "分类ID") @PathVariable Long id) {
        try {
            boolean success = agentCategoryService.deleteCategory(id);
            if (success) {
                return Result.success("删除分类成功");
            } else {
                return Result.error("删除分类失败");
            }
        } catch (Exception e) {
            log.error("删除分类失败: id={}", id, e);
            return Result.error("删除分类失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取分类统计信息", description = "获取分类数量统计")
    @GetMapping("/statistics")
    public Result<Map<String, Object>> getCategoryStatistics() {
        try {
            Long categoryCount = agentCategoryService.getCategoryCount();
            List<Map<String, Object>> agentCountByCategory = agentCategoryService.countAgentsByCategory();
            
            Map<String, Object> statistics = Map.of(
                "categoryCount", categoryCount,
                "agentCountByCategory", agentCountByCategory
            );
            
            return Result.success("获取分类统计信息成功", statistics);
        } catch (Exception e) {
            log.error("获取分类统计信息失败", e);
            return Result.error("获取分类统计信息失败: " + e.getMessage());
        }
    }

    @Operation(summary = "检查分类是否有子分类", description = "检查指定分类是否包含子分类")
    @GetMapping("/{id}/has-children")
    public Result<Boolean> hasChildren(
            @Parameter(description = "分类ID") @PathVariable Long id) {
        try {
            boolean hasChildren = agentCategoryService.hasChildren(id);
            return Result.success("检查完成", hasChildren);
        } catch (Exception e) {
            log.error("检查分类是否有子分类失败: id={}", id, e);
            return Result.error("检查失败: " + e.getMessage());
        }
    }
}
