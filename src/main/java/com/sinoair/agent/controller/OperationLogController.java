package com.sinoair.agent.controller;

import com.sinoair.agent.annotation.OperationLogRecord;
import com.sinoair.agent.common.Result;
import com.sinoair.agent.entity.OperationLog;
import com.sinoair.agent.service.OperationLogService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 操作日志管理控制器
 *
 * <AUTHOR> Team
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/logs")
@RequiredArgsConstructor
@Tag(name = "操作日志管理", description = "操作日志的查询、统计、导出等功能")
public class OperationLogController {

    private final OperationLogService operationLogService;

    @Operation(summary = "分页查询操作日志")
    @GetMapping
    public Result<Page<OperationLog>> getLogs(
            @Parameter(description = "页码，从0开始") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") int size,
            @Parameter(description = "用户ID") @RequestParam(required = false) Long userId,
            @Parameter(description = "用户名") @RequestParam(required = false) String username,
            @Parameter(description = "模块") @RequestParam(required = false) String module,
            @Parameter(description = "操作类型") @RequestParam(required = false) String operationType,
            @Parameter(description = "状态") @RequestParam(required = false) String status,
            @Parameter(description = "开始时间") @RequestParam(required = false) 
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam(required = false) 
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {

        Pageable pageable = PageRequest.of(page, size);
        Page<OperationLog> logs = operationLogService.findLogsByConditions(
                userId, username, module, operationType, status, startTime, endTime, pageable);
        
        return Result.success(logs);
    }

    @Operation(summary = "根据ID查询日志详情")
    @GetMapping("/{id}")
    public Result<OperationLog> getLogById(@PathVariable String id) {
        OperationLog log = operationLogService.findById(id);
        if (log == null) {
            return Result.error("日志不存在");
        }
        return Result.success(log);
    }

    @Operation(summary = "获取最近操作日志")
    @GetMapping("/recent")
    public Result<List<OperationLog>> getRecentLogs(
            @Parameter(description = "数量限制") @RequestParam(defaultValue = "10") int limit) {
        List<OperationLog> logs = operationLogService.getRecentLogs(limit);
        return Result.success(logs);
    }

    @Operation(summary = "获取指定用户的最近操作日志")
    @GetMapping("/recent/user/{userId}")
    public Result<List<OperationLog>> getRecentLogsByUserId(
            @PathVariable Long userId,
            @Parameter(description = "数量限制") @RequestParam(defaultValue = "10") int limit) {
        List<OperationLog> logs = operationLogService.getRecentLogsByUserId(userId, limit);
        return Result.success(logs);
    }

    @Operation(summary = "获取操作统计信息")
    @GetMapping("/statistics")
    public Result<Map<String, Object>> getOperationStatistics(
            @Parameter(description = "开始时间") @RequestParam(required = false) 
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam(required = false) 
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {
        
        Map<String, Object> statistics;
        if (startTime != null && endTime != null) {
            statistics = operationLogService.getOperationStatistics(startTime, endTime);
        } else {
            statistics = operationLogService.getOperationStatistics();
        }
        
        return Result.success(statistics);
    }

    @Operation(summary = "获取用户操作统计")
    @GetMapping("/statistics/user/{userId}")
    public Result<Map<String, Object>> getUserOperationStatistics(@PathVariable Long userId) {
        Map<String, Object> statistics = operationLogService.getUserOperationStatistics(userId);
        return Result.success(statistics);
    }

    @Operation(summary = "获取模块操作统计")
    @GetMapping("/statistics/modules")
    public Result<Map<String, Object>> getModuleOperationStatistics() {
        Map<String, Object> statistics = operationLogService.getModuleOperationStatistics();
        return Result.success(statistics);
    }

    @Operation(summary = "获取操作类型统计")
    @GetMapping("/statistics/types")
    public Result<Map<String, Object>> getOperationTypeStatistics() {
        Map<String, Object> statistics = operationLogService.getOperationTypeStatistics();
        return Result.success(statistics);
    }

    @Operation(summary = "获取每日操作统计")
    @GetMapping("/statistics/daily")
    public Result<List<Map<String, Object>>> getDailyOperationStatistics(
            @Parameter(description = "统计天数") @RequestParam(defaultValue = "30") int days) {
        List<Map<String, Object>> statistics = operationLogService.getDailyOperationStatistics(days);
        return Result.success(statistics);
    }

    @Operation(summary = "获取慢操作日志")
    @GetMapping("/slow")
    public Result<Page<OperationLog>> getSlowOperations(
            @Parameter(description = "页码，从0开始") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") int size,
            @Parameter(description = "执行时间阈值（毫秒）") @RequestParam(defaultValue = "1000") Long thresholdMs) {
        
        Pageable pageable = PageRequest.of(page, size);
        Page<OperationLog> logs = operationLogService.getSlowOperations(thresholdMs, pageable);
        return Result.success(logs);
    }

    @Operation(summary = "导出操作日志")
    @GetMapping("/export")
    public ResponseEntity<String> exportLogs(
            @Parameter(description = "用户ID") @RequestParam(required = false) Long userId,
            @Parameter(description = "模块") @RequestParam(required = false) String module,
            @Parameter(description = "操作类型") @RequestParam(required = false) String operationType,
            @Parameter(description = "开始时间") @RequestParam(required = false) 
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @Parameter(description = "结束时间") @RequestParam(required = false) 
            @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {
        
        String csvData = operationLogService.exportLogs(userId, module, operationType, startTime, endTime);
        
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.TEXT_PLAIN);
        headers.setContentDispositionFormData("attachment", "operation_logs.csv");
        
        return ResponseEntity.ok()
                .headers(headers)
                .body(csvData);
    }

    @Operation(summary = "批量删除日志", description = "此功能已禁用，仅供管理员通过后台操作")
    @DeleteMapping("/batch")
    @OperationLogRecord(module = "日志管理", operationType = "DELETE_ATTEMPT", operationDesc = "尝试批量删除操作日志")
    public Result<Void> deleteLogs(@RequestBody List<String> logIds) {
        log.warn("尝试通过API批量删除日志被拒绝，请求日志ID数量: {}", logIds != null ? logIds.size() : 0);
        return Result.error("为了数据安全，不允许通过网页删除操作日志。如需清理日志，请联系系统管理员。");
    }

    @Operation(summary = "清理过期日志", description = "此功能已禁用，仅供系统管理员通过定时任务执行")
    @DeleteMapping("/cleanup")
    @OperationLogRecord(module = "日志管理", operationType = "CLEANUP_ATTEMPT", operationDesc = "尝试清理过期操作日志")
    public Result<Void> cleanupExpiredLogs(
            @Parameter(description = "保留天数") @RequestParam(defaultValue = "90") int retentionDays) {

        log.warn("尝试通过API清理过期日志被拒绝，保留天数参数: {}", retentionDays);
        return Result.error("为了数据安全，不允许通过网页清理日志。日志清理由系统定时任务自动执行。");
    }
}
