package com.sinoair.agent.controller;

import com.sinoair.agent.annotation.OperationLogRecord;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import com.sinoair.agent.common.Result;
import com.sinoair.agent.entity.BusinessTemplate;
import com.sinoair.agent.service.BusinessTemplateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 业务模板管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/business-templates")
@RequiredArgsConstructor
@Tag(name = "业务模板管理", description = "业务JSON模板的增删改查")
public class BusinessTemplateController {

    private final BusinessTemplateService businessTemplateService;

    @Operation(summary = "获取业务模板列表")
    @GetMapping
    public Result<List<BusinessTemplate>> getTemplates(
            @RequestParam(required = false) String category,
            @RequestParam(required = false) String keyword) {

        List<BusinessTemplate> templates;

        if (category != null && !category.isEmpty()) {
            templates = businessTemplateService.getByCategory(category);
        } else {
            templates = businessTemplateService.list();
        }

        // 简单的关键词过滤
        if (keyword != null && !keyword.isEmpty()) {
            templates = templates.stream()
                    .filter(t -> t.getTemplateName().contains(keyword)
                            || t.getTemplateCode().contains(keyword)
                            || (t.getDescription() != null && t.getDescription().contains(keyword)))
                    .toList();
        }

        return Result.success(templates);
    }

    @Operation(summary = "获取所有启用的模板")
    @GetMapping("/active")
    public Result<List<BusinessTemplate>> getActiveTemplates() {
        List<BusinessTemplate> templates = businessTemplateService.getActiveTemplates();
        return Result.success(templates);
    }

    @Operation(summary = "按分类获取模板")
    @GetMapping("/by-category")
    public Result<Map<String, List<BusinessTemplate>>> getTemplatesByCategory() {
        List<BusinessTemplate> templates = businessTemplateService.getActiveTemplates();
        Map<String, List<BusinessTemplate>> grouped = templates.stream()
                .collect(Collectors.groupingBy(BusinessTemplate::getCategory));
        return Result.success(grouped);
    }

    @Operation(summary = "根据ID获取模板详情")
    @GetMapping("/{id}")
    public Result<BusinessTemplate> getTemplate(@PathVariable Long id) {
        BusinessTemplate template = businessTemplateService.getById(id);
        if (template == null) {
            return Result.error("模板不存在");
        }
        return Result.success(template);
    }

    @Operation(summary = "创建业务模板")
    @PostMapping
    @OperationLogRecord(module = "业务模板管理", operationType = "CREATE", operationDesc = "创建业务模板")
    public Result<BusinessTemplate> createTemplate(@RequestBody BusinessTemplate template) {
        // 检查编码是否重复
        BusinessTemplate existing = businessTemplateService.getByCode(template.getTemplateCode());
        if (existing != null) {
            return Result.error("模板编码已存在");
        }
        
        boolean success = businessTemplateService.save(template);
        if (success) {
            return Result.success(template);
        } else {
            return Result.error("创建失败");
        }
    }

    @Operation(summary = "更新业务模板")
    @PutMapping("/{id}")
    @OperationLogRecord(module = "业务模板管理", operationType = "UPDATE", operationDesc = "更新业务模板")
    public Result<BusinessTemplate> updateTemplate(@PathVariable Long id, @RequestBody BusinessTemplate template) {
        BusinessTemplate existing = businessTemplateService.getById(id);
        if (existing == null) {
            return Result.error("模板不存在");
        }
        
        // 检查编码是否重复（排除自己）
        BusinessTemplate codeCheck = businessTemplateService.getByCode(template.getTemplateCode());
        if (codeCheck != null && !codeCheck.getId().equals(id)) {
            return Result.error("模板编码已存在");
        }
        
        template.setId(id);
        boolean success = businessTemplateService.updateById(template);
        if (success) {
            return Result.success(template);
        } else {
            return Result.error("更新失败");
        }
    }

    @Operation(summary = "删除业务模板")
    @DeleteMapping("/{id}")
    @OperationLogRecord(module = "业务模板管理", operationType = "DELETE", operationDesc = "删除业务模板")
    public Result<Void> deleteTemplate(@PathVariable Long id) {
        boolean success = businessTemplateService.removeById(id);
        if (success) {
            return Result.success();
        } else {
            return Result.error("删除失败");
        }
    }
}
