package com.sinoair.agent.controller;

import com.sinoair.agent.annotation.OperationLogRecord;
import com.sinoair.agent.common.Result;
import com.sinoair.agent.entity.PageBinding;
import com.sinoair.agent.service.PageBindingService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 多步骤绑定控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/multi-step-bindings")
@Tag(name = "多步骤绑定管理", description = "多步骤页面绑定配置管理")
public class MultiStepBindingController {

    @Autowired
    private PageBindingService pageBindingService;

    @Operation(summary = "创建多步骤绑定配置")
    @PostMapping
    @OperationLogRecord(module = "多步骤绑定管理", operationType = "CREATE", operationDesc = "创建多步骤绑定配置")
    public Result<String> createMultiStepBinding(@RequestBody MultiStepBindingRequest request) {
        try {
            log.info("创建多步骤绑定配置: {}", request.getBindingName());

            // 验证请求数据
            if (request.getMainBinding() == null || request.getSubSteps() == null || request.getSubSteps().isEmpty()) {
                return Result.error("主绑定和子步骤不能为空");
            }

            // 保存多步骤绑定
            boolean success = pageBindingService.saveMultiStepBinding(request.getMainBinding(), request.getSubSteps());
            
            if (success) {
                return Result.success("多步骤绑定配置创建成功");
            } else {
                return Result.error("多步骤绑定配置创建失败");
            }
        } catch (Exception e) {
            log.error("创建多步骤绑定配置失败", e);
            return Result.error("创建多步骤绑定配置失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取多步骤绑定详情")
    @GetMapping("/{bindingId}")
    public Result<MultiStepBindingResponse> getMultiStepBinding(@PathVariable Long bindingId) {
        try {
            PageBinding mainBinding = pageBindingService.getById(bindingId);
            if (mainBinding == null || mainBinding.getIsMultiStep() != 1) {
                return Result.error("多步骤绑定不存在");
            }

            List<PageBinding> subSteps = pageBindingService.findSubStepsByParentId(bindingId);
            
            MultiStepBindingResponse response = new MultiStepBindingResponse();
            response.setMainBinding(mainBinding);
            response.setSubSteps(subSteps);
            response.setTotalSteps(subSteps.size() + 1);

            return Result.success(response);
        } catch (Exception e) {
            log.error("获取多步骤绑定详情失败: bindingId={}", bindingId, e);
            return Result.error("获取多步骤绑定详情失败: " + e.getMessage());
        }
    }

    @Operation(summary = "更新多步骤绑定配置")
    @PutMapping("/{bindingId}")
    @OperationLogRecord(module = "多步骤绑定管理", operationType = "UPDATE", operationDesc = "更新多步骤绑定配置")
    public Result<String> updateMultiStepBinding(@PathVariable Long bindingId,
                                               @RequestBody MultiStepBindingRequest request) {
        try {
            log.info("更新多步骤绑定配置: bindingId={}", bindingId);

            // 验证主绑定是否存在
            PageBinding existingBinding = pageBindingService.getById(bindingId);
            if (existingBinding == null || existingBinding.getIsMultiStep() != 1) {
                return Result.error("多步骤绑定不存在");
            }

            // 更新主绑定
            request.getMainBinding().setId(bindingId);
            pageBindingService.updateById(request.getMainBinding());

            // 删除旧的子步骤
            List<PageBinding> oldSubSteps = pageBindingService.findSubStepsByParentId(bindingId);
            for (PageBinding oldStep : oldSubSteps) {
                pageBindingService.removeById(oldStep.getId());
            }

            // 保存新的子步骤
            for (int i = 0; i < request.getSubSteps().size(); i++) {
                PageBinding subStep = request.getSubSteps().get(i);
                subStep.setId(null); // 清空ID，作为新记录插入
                subStep.setIsMultiStep(1);
                subStep.setParentBindingId(bindingId);
                subStep.setStepOrder(i + 2);
                subStep.setIsFinalStep(i == request.getSubSteps().size() - 1 ? 1 : 0);
                pageBindingService.save(subStep);
            }

            return Result.success("多步骤绑定配置更新成功");
        } catch (Exception e) {
            log.error("更新多步骤绑定配置失败: bindingId={}", bindingId, e);
            return Result.error("更新多步骤绑定配置失败: " + e.getMessage());
        }
    }

    @Operation(summary = "删除多步骤绑定配置")
    @DeleteMapping("/{bindingId}")
    @OperationLogRecord(module = "多步骤绑定管理", operationType = "DELETE", operationDesc = "删除多步骤绑定配置")
    public Result<String> deleteMultiStepBinding(@PathVariable Long bindingId) {
        try {
            log.info("删除多步骤绑定配置: bindingId={}", bindingId);

            // 验证主绑定是否存在
            PageBinding mainBinding = pageBindingService.getById(bindingId);
            if (mainBinding == null || mainBinding.getIsMultiStep() != 1) {
                return Result.error("多步骤绑定不存在");
            }

            // 删除所有子步骤
            List<PageBinding> subSteps = pageBindingService.findSubStepsByParentId(bindingId);
            for (PageBinding subStep : subSteps) {
                pageBindingService.removeById(subStep.getId());
            }

            // 删除主绑定
            pageBindingService.removeById(bindingId);

            return Result.success("多步骤绑定配置删除成功");
        } catch (Exception e) {
            log.error("删除多步骤绑定配置失败: bindingId={}", bindingId, e);
            return Result.error("删除多步骤绑定配置失败: " + e.getMessage());
        }
    }


    // 内部类：请求和响应对象
    public static class MultiStepBindingRequest {
        private String bindingName;
        private PageBinding mainBinding;
        private List<PageBinding> subSteps;

        // getters and setters
        public String getBindingName() { return bindingName; }
        public void setBindingName(String bindingName) { this.bindingName = bindingName; }
        public PageBinding getMainBinding() { return mainBinding; }
        public void setMainBinding(PageBinding mainBinding) { this.mainBinding = mainBinding; }
        public List<PageBinding> getSubSteps() { return subSteps; }
        public void setSubSteps(List<PageBinding> subSteps) { this.subSteps = subSteps; }
    }

    public static class MultiStepBindingResponse {
        private PageBinding mainBinding;
        private List<PageBinding> subSteps;
        private Integer totalSteps;

        // getters and setters
        public PageBinding getMainBinding() { return mainBinding; }
        public void setMainBinding(PageBinding mainBinding) { this.mainBinding = mainBinding; }
        public List<PageBinding> getSubSteps() { return subSteps; }
        public void setSubSteps(List<PageBinding> subSteps) { this.subSteps = subSteps; }
        public Integer getTotalSteps() { return totalSteps; }
        public void setTotalSteps(Integer totalSteps) { this.totalSteps = totalSteps; }
    }
}
