package com.sinoair.agent.controller;

import cn.hutool.crypto.digest.MD5;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sinoair.agent.dto.response.UserSubscriptionDTO;
import com.sinoair.agent.entity.CallHistory;
import com.sinoair.agent.entity.User;
import com.sinoair.agent.dto.response.UserVO;
import com.sinoair.agent.service.AgentService;
import com.sinoair.agent.service.CallHistoryService;
import com.sinoair.agent.service.UserAgentSubscriptionService;
import com.sinoair.agent.service.UserService;
import com.sinoair.agent.security.JwtTokenProvider;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Vue仪表盘API控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/dashboard")
@CrossOrigin(originPatterns = "*", allowCredentials = "true")
public class VueDashboardController {

    @Autowired
    private CallHistoryService callHistoryService;

    @Autowired
    private AgentService agentService;

    @Autowired
    private UserAgentSubscriptionService userAgentSubscriptionService;

    @Autowired
    private UserService userService;

    @Autowired
    private JwtTokenProvider jwtTokenProvider;

    /**
     * 插件下载相关常量
     */
    private static class Constants {
        public static final String APP_ID = "c71194b17ab2428f905e16755771b8d6";
        public static final String APP_SECRET = "wW4x3ZgRdFc7D9sE8tb97nF8VbCU45D9";
        public static final String DOWNLOAD_URL = "https://ecdown.sinoair.com/app/publish/app/download";
        public static final String VERSION = "1.0";
    }

    /**
     * 从Token中获取用户信息
     */
    private User getUserFromToken(HttpServletRequest request) {
        String authHeader = request.getHeader("Authorization");
        String token = null;
        if (authHeader != null && authHeader.startsWith("Bearer ")) {
            token = authHeader.substring(7);
        }

        if (token == null || !jwtTokenProvider.validateToken(token)) {
            return null;
        }

        try {
            Long userId = jwtTokenProvider.getUserIdFromToken(token);
            UserVO userVO = userService.getUserById(userId);
            if (userVO != null) {
                // 将UserVO转换为User实体
                return convertUserVOToUser(userVO);
            }
            return null;
        } catch (Exception e) {
            log.error("从Token获取用户信息失败", e);
            return null;
        }
    }

    /**
     * 将UserVO转换为User实体
     */
    private User convertUserVOToUser(UserVO userVO) {
        User user = new User();
        user.setId(userVO.getId());
        user.setUsername(userVO.getUsername());
        user.setEmail(userVO.getEmail());
        user.setRealName(userVO.getRealName());
        user.setLastLoginTime(userVO.getLastLoginTime());
        return user;
    }

    /**
     * 计算签名
     */
    private static String getSign(String version, String timestamp) {
        String signStr = Constants.APP_ID + "|%|" + version + "|%|" + timestamp + "|%|" + Constants.APP_SECRET;
        return MD5.create().digestHex(signStr);
    }

    /**
     * 获取仪表盘统计数据
     */
    @GetMapping("/stats")
    public Map<String, Object> getDashboardStats(HttpServletRequest request) {
        Map<String, Object> response = new HashMap<>();

        User user = getUserFromToken(request);
        if (user == null) {
            response.put("success", false);
            response.put("message", "Token无效或已过期");
            response.put("code", 401);
            return response;
        }

        try {
            // 获取订阅数据
            int subscriptionCount = userAgentSubscriptionService.getUserActiveSubscriptionCount(user.getId());
            List<UserSubscriptionDTO> activeSubscriptions = userAgentSubscriptionService.getUserActiveSubscriptions(user.getId());

            // 获取调用统计数据
            Long monthlyCallCount = callHistoryService.getMonthlyCallCount(user.getId());
            Long todayCallCount = callHistoryService.getTodayCallCount(user.getId());
            Double successRate = callHistoryService.getSuccessRate(user.getId());

            // 构建统计数据
            Map<String, Object> stats = new HashMap<>();
            stats.put("subscriptions", subscriptionCount);
            stats.put("monthlyCalls", monthlyCallCount);
            stats.put("todayCalls", todayCallCount);
            stats.put("remainingCalls", 8766); // 暂时保留模拟数据
            stats.put("successRate", Double.parseDouble(String.format("%.1f", successRate)));

            // 构建响应数据
            Map<String, Object> data = new HashMap<>();
            data.put("user", Map.of(
                "id", user.getId(),
                "email", user.getEmail(),
                "realName", user.getRealName() != null ? user.getRealName() : user.getUsername(),
                "lastLoginTime", user.getLastLoginTime()
            ));
            data.put("stats", stats);
            data.put("activeSubscriptions", activeSubscriptions);

            response.put("success", true);
            response.put("data", data);
            response.put("code", 200);

            log.info("仪表盘统计数据获取成功，用户：{}，订阅数：{}，本月调用：{}，今日调用：{}，成功率：{}%",
                    user.getId(), subscriptionCount, monthlyCallCount, todayCallCount, successRate);

        } catch (Exception e) {
            log.error("获取仪表盘统计数据失败，用户：{}", user.getId(), e);
            response.put("success", false);
            response.put("message", "获取统计数据失败");
            response.put("code", 500);
        }

        return response;
    }

    /**
     * 获取最近调用记录
     */
    @GetMapping("/recent-calls")
    public Map<String, Object> getRecentCalls(HttpServletRequest request,
                                             @RequestParam(defaultValue = "5") Integer limit) {
        Map<String, Object> response = new HashMap<>();

        User user = getUserFromToken(request);
        if (user == null) {
            response.put("success", false);
            response.put("message", "Token无效或已过期");
            response.put("code", 401);
            return response;
        }

        try {
            List<CallHistory> recentCalls = callHistoryService.getRecentCallHistory(user.getId(), limit);
            
            response.put("success", true);
            response.put("data", recentCalls);
            response.put("code", 200);

            log.info("获取用户{}最近调用记录成功，记录数：{}", user.getId(), recentCalls.size());

        } catch (Exception e) {
            log.error("获取用户{}最近调用记录失败", user.getId(), e);
            response.put("success", false);
            response.put("message", "获取调用记录失败");
            response.put("code", 500);
            response.put("data", new ArrayList<>());
        }

        return response;
    }

    /**
     * 获取调用历史（分页）
     */
    @GetMapping("/call-history")
    public Map<String, Object> getCallHistory(HttpServletRequest request,
                                            @RequestParam(defaultValue = "1") Integer page,
                                            @RequestParam(defaultValue = "10") Integer size,
                                            @RequestParam(required = false) String agentName,
                                            @RequestParam(required = false) String callType) {
        Map<String, Object> response = new HashMap<>();

        User user = getUserFromToken(request);
        if (user == null) {
            response.put("success", false);
            response.put("message", "Token无效或已过期");
            response.put("code", 401);
            return response;
        }

        try {
            Page<CallHistory> pageObj = new Page<>(page, size);
            IPage<CallHistory> historyPage = callHistoryService.getCallHistoryByUserId(
                user.getId(), pageObj, agentName, callType, null, null);

            // 计算统计信息
            long successCount = callHistoryService.getSuccessCountByUserId(user.getId());
            long failedCount = callHistoryService.getFailedCountByUserId(user.getId());

            Map<String, Object> data = new HashMap<>();
            data.put("records", historyPage.getRecords());
            data.put("total", historyPage.getTotal());
            data.put("pages", historyPage.getPages());
            data.put("current", historyPage.getCurrent());
            data.put("size", historyPage.getSize());
            data.put("successCount", successCount);
            data.put("failedCount", failedCount);

            response.put("success", true);
            response.put("data", data);
            response.put("code", 200);

        } catch (Exception e) {
            log.error("查询调用历史失败，用户：{}", user.getId(), e);
            response.put("success", false);
            response.put("message", "查询调用历史失败");
            response.put("code", 500);
        }

        return response;
    }

    /**
     * 获取调用历史详情
     */
    @GetMapping("/call-history/{id}")
    public Map<String, Object> getCallHistoryDetail(@PathVariable Long id, HttpServletRequest request) {
        Map<String, Object> response = new HashMap<>();

        User user = getUserFromToken(request);
        if (user == null) {
            response.put("success", false);
            response.put("message", "Token无效或已过期");
            response.put("code", 401);
            return response;
        }

        try {
            CallHistory history = callHistoryService.getCallHistoryById(id, user.getId());
            if (history != null) {
                response.put("success", true);
                response.put("data", history);
                response.put("code", 200);
            } else {
                response.put("success", false);
                response.put("message", "调用记录不存在或无权限访问");
                response.put("code", 404);
            }
        } catch (Exception e) {
            log.error("获取调用历史详情失败，ID：{}，用户：{}", id, user.getId(), e);
            response.put("success", false);
            response.put("message", "获取详情失败");
            response.put("code", 500);
        }

        return response;
    }

    /**
     * 获取用户订阅信息
     */
    @GetMapping("/subscriptions")
    public Map<String, Object> getUserSubscriptions(HttpServletRequest request) {
        Map<String, Object> response = new HashMap<>();

        User user = getUserFromToken(request);
        if (user == null) {
            response.put("success", false);
            response.put("message", "Token无效或已过期");
            response.put("code", 401);
            return response;
        }

        try {
            List<UserSubscriptionDTO> userSubscriptions = userAgentSubscriptionService.getUserSubscriptions(user.getId());
            
            response.put("success", true);
            response.put("data", userSubscriptions);
            response.put("code", 200);

        } catch (Exception e) {
            log.error("获取用户订阅信息失败，用户：{}", user.getId(), e);
            response.put("success", false);
            response.put("message", "获取订阅信息失败");
            response.put("code", 500);
        }

        return response;
    }

    /**
     * 下载插件文件 - 从远程服务器下载
     */
    @GetMapping("/download-plugin")
    public ResponseEntity<Resource> downloadPlugin(HttpServletRequest request) {
        User user = getUserFromToken(request);
        if (user == null) {
            return ResponseEntity.status(401).build();
        }

        try {
            // 生成时间戳
            String timestamp = String.valueOf(System.currentTimeMillis());

            // 计算签名
            String sign = getSign(Constants.VERSION, timestamp);

            // 构建下载URL
            String downloadUrl = Constants.DOWNLOAD_URL +
                "?appId=" + Constants.APP_ID +
                "&timestamp=" + timestamp +
                "&version=" + Constants.VERSION +
                "&sign=" + sign;

            log.info("开始从远程服务器下载插件文件，URL: {}", downloadUrl);

            // 使用HttpClient下载文件
            HttpClient client = HttpClient.newBuilder()
                    .connectTimeout(Duration.ofSeconds(30))
                    .build();

            HttpRequest httpRequest = HttpRequest.newBuilder()
                    .uri(URI.create(downloadUrl))
                    .timeout(Duration.ofSeconds(60))
                    .GET()
                    .build();

            HttpResponse<byte[]> response = client.send(httpRequest, HttpResponse.BodyHandlers.ofByteArray());

            if (response.statusCode() != 200) {
                log.error("下载插件文件失败，HTTP状态码: {}", response.statusCode());
                return ResponseEntity.status(response.statusCode()).build();
            }

            byte[] fileData = response.body();
            if (fileData == null || fileData.length == 0) {
                log.error("下载的插件文件为空");
                return ResponseEntity.status(500).build();
            }

            // 创建ByteArrayResource
            ByteArrayResource resource = new ByteArrayResource(fileData);

            HttpHeaders headers = new HttpHeaders();
            headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"sinoair-agent-chrome-plugin.zip\"");
            headers.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_OCTET_STREAM_VALUE);

            log.info("用户 {} 下载Chrome插件包成功，文件大小: {} bytes", user.getEmail(), fileData.length);

            return ResponseEntity.ok()
                    .headers(headers)
                    .body(resource);

        } catch (Exception e) {
            log.error("下载Chrome插件失败", e);
            return ResponseEntity.status(500).build();
        }
    }


}
