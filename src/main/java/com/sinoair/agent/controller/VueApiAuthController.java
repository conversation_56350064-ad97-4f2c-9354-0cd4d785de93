package com.sinoair.agent.controller;

import cn.hutool.crypto.digest.MD5;
import com.sinoair.agent.entity.User;
import com.sinoair.agent.dto.response.UserVO;
import com.sinoair.agent.service.EmailService;
import com.sinoair.agent.service.UserService;
import com.sinoair.agent.service.CaptchaService;
import com.sinoair.agent.service.UserAgentSubscriptionService;
import com.sinoair.agent.mapper.UserMapper;
import com.sinoair.agent.security.JwtTokenProvider;
import com.sinoair.agent.security.UserPrincipal;
import com.sinoair.agent.util.PasswordUtil;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import java.util.ArrayList;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * Vue前端专用认证API控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/vue-auth")
@CrossOrigin(originPatterns = "*", allowCredentials = "true")
public class VueApiAuthController {
    
    @Autowired
    private EmailService emailService;

    @Autowired
    private UserService userService;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private JwtTokenProvider jwtTokenProvider;

    @Autowired
    private CaptchaService captchaService;

    @Autowired
    private UserAgentSubscriptionService userAgentSubscriptionService;

    /**
     * Vue前端生成图形验证码API
     * GET /api/v1/vue-auth/captcha
     */
    @GetMapping("/captcha")
    public Map<String, Object> generateCaptcha() {
        Map<String, Object> response = new HashMap<>();

        try {
            Map<String, String> captchaData = captchaService.generateCaptcha();

            response.put("success", true);
            response.put("message", "图形验证码生成成功");
            response.put("data", captchaData);
            response.put("code", 200);

            log.debug("图形验证码生成成功，ID：{}", captchaData.get("captchaId"));

        } catch (Exception e) {
            log.error("生成图形验证码异常：{}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "生成图形验证码失败，请稍后重试");
            response.put("code", 500);
        }

        return response;
    }

    /**
     * Vue前端发送验证码API
     * POST /api/v1/vue-auth/send-code
     */
    @PostMapping("/send-code")
    public Map<String, Object> sendCode(@RequestBody Map<String, String> request) {
        Map<String, Object> response = new HashMap<>();
        String email = request.get("email");
        String captchaId = request.get("captchaId");
        String captchaCode = request.get("captchaCode");

        if (email == null || email.trim().isEmpty()) {
            response.put("success", false);
            response.put("message", "邮箱不能为空");
            response.put("code", 400);
            return response;
        }

        if (captchaId == null || captchaId.trim().isEmpty()) {
            response.put("success", false);
            response.put("message", "图形验证码ID不能为空");
            response.put("code", 400);
            return response;
        }

        if (captchaCode == null || captchaCode.trim().isEmpty()) {
            response.put("success", false);
            response.put("message", "图形验证码不能为空");
            response.put("code", 400);
            return response;
        }
        
        try {
            // 验证图形验证码
            if (!captchaService.verifyCaptcha(captchaId, captchaCode)) {
                response.put("success", false);
                response.put("message", "图形验证码错误或已过期");
                response.put("code", 401);
                log.warn("图形验证码验证失败，邮箱：{}，验证码ID：{}", email, captchaId);
                return response;
            }

            // 检查发送频率
            if (!emailService.checkSendFrequency(email)) {
                response.put("success", false);
                response.put("message", "发送过于频繁，请稍后再试");
                response.put("code", 429);
                return response;
            }

            // 验证邮箱格式（基础验证）
            if (!isValidEmail(email)) {
                response.put("success", false);
                response.put("message", "邮箱格式不正确");
                response.put("code", 400);
                return response;
            }

            log.info("邮箱验证通过，准备发送验证码到：{}", email);

            // 生成验证码
            String code = emailService.generateVerificationCode();

            // 发送邮件
            if (emailService.sendVerificationCode(email, code)) {
                // 存储验证码到Redis
                emailService.storeVerificationCode(email, code);
                response.put("success", true);
                response.put("message", "验证码发送成功");
                response.put("code", 200);
                log.info("验证码发送成功，邮箱：{}", email);
            } else {
                response.put("success", false);
                response.put("message", "验证码发送失败，请稍后重试");
                response.put("code", 500);
            }
        } catch (Exception e) {
            log.error("发送验证码异常，邮箱：{}，错误：{}", email, e.getMessage(), e);
            response.put("success", false);
            response.put("message", "系统异常，请稍后重试");
            response.put("code", 500);
        }
        
        return response;
    }
    
    /**
     * Vue前端登录API
     * POST /api/auth/login
     */
    @PostMapping("/login")
    public Map<String, Object> login(@RequestBody Map<String, String> request,
                                   HttpServletRequest httpRequest) {
        Map<String, Object> response = new HashMap<>();
        String email = request.get("email");
        String verificationCode = request.get("verificationCode");
        
        if (email == null || email.trim().isEmpty()) {
            response.put("success", false);
            response.put("message", "邮箱不能为空");
            response.put("code", 400);
            return response;
        }
        
        if (verificationCode == null || verificationCode.trim().isEmpty()) {
            response.put("success", false);
            response.put("message", "验证码不能为空");
            response.put("code", 400);
            return response;
        }
        
        try {
            // 验证验证码
            if (!emailService.verifyCode(email, verificationCode)) {
                response.put("success", false);
                response.put("message", "验证码错误或已过期");
                response.put("code", 401);
                return response;
            }

            // 查找或创建用户
            User user = findOrCreateUser(email);
            if (user == null) {
                response.put("success", false);
                response.put("message", "用户创建失败，请稍后重试");
                response.put("code", 500);
                return response;
            }

            // 检查用户审核状态
            if (user.getApprovalStatus() == null || user.getApprovalStatus() != 1) {
                String statusMessage;
                switch (user.getApprovalStatus() != null ? user.getApprovalStatus() : 0) {
                    case 0:
                        statusMessage = "您的账户正在审核中，请耐心等待";
                        break;
                    case 2:
                        statusMessage = "您的账户审核未通过，请联系管理员";
                        break;
                    default:
                        statusMessage = "您的账户状态异常，请联系管理员";
                        break;
                }
                response.put("success", false);
                response.put("message", statusMessage);
                response.put("code", 403);
                log.warn("用户审核状态不通过，邮箱：{}，审核状态：{}", email, user.getApprovalStatus());
                return response;
            }

            // 记录登录信息
            String clientIp = getClientIpAddress(httpRequest);
            updateLastLoginInfo(user.getId(), clientIp);

            // 重新查询用户信息（获取最新的登录时间）
            user = userMapper.selectById(user.getId());

            // 创建Authentication对象
            UserPrincipal userPrincipal = UserPrincipal.create(user, "PLATFORM", new ArrayList<>());
            Authentication authentication = new UsernamePasswordAuthenticationToken(
                userPrincipal, null, userPrincipal.getAuthorities());

            // 生成JWT Token
            String token = jwtTokenProvider.generateToken(authentication);

            // 构建用户信息
            Map<String, Object> userInfo = new HashMap<>();
            userInfo.put("id", user.getId());
            userInfo.put("email", user.getEmail());
            userInfo.put("username", user.getUsername());
            userInfo.put("realName", user.getRealName());
            userInfo.put("lastLoginTime", user.getLastLoginTime());
            userInfo.put("createdTime", user.getCreatedTime());

            // 构建响应数据
            Map<String, Object> data = new HashMap<>();
            data.put("user", userInfo);
            data.put("token", token);

            response.put("success", true);
            response.put("message", "登录成功");
            response.put("data", data);
            response.put("code", 200);

            log.info("用户登录成功，邮箱：{}，用户ID：{}，IP：{}，Token已生成",
                    email, user.getId(), clientIp);

        } catch (Exception e) {
            log.error("登录异常，邮箱：{}，错误：{}", email, e.getMessage(), e);
            response.put("success", false);
            response.put("message", "登录失败，请稍后重试");
            response.put("code", 500);
        }
        
        return response;
    }
    
    /**
     * Vue前端获取用户信息API
     * GET /api/v1/vue-auth/user-info
     */
    @GetMapping("/user-info")
    public Map<String, Object> getUserProfile(HttpServletRequest request) {
        Map<String, Object> response = new HashMap<>();

        // 从请求头中获取Token
        String authHeader = request.getHeader("Authorization");
        String token = null;
        if (authHeader != null && authHeader.startsWith("Bearer ")) {
            token = authHeader.substring(7);
        }

        if (token == null || !jwtTokenProvider.validateToken(token)) {
            response.put("success", false);
            response.put("message", "Token无效或已过期");
            response.put("code", 401);
            return response;
        }

        try {
            // 从Token中获取用户信息
            Long userId = jwtTokenProvider.getUserIdFromToken(token);

            // 查询用户详细信息
            User user = userMapper.selectById(userId);
            if (user == null || user.getStatus() != 1) {
                response.put("success", false);
                response.put("message", "用户不存在");
                response.put("code", 404);
                return response;
            }

            // 构建用户信息
            Map<String, Object> userInfo = new HashMap<>();
            userInfo.put("id", user.getId());
            userInfo.put("email", user.getEmail());
            userInfo.put("username", user.getUsername());
            userInfo.put("realName", user.getRealName());
            userInfo.put("lastLoginTime", user.getLastLoginTime());
            userInfo.put("createdTime", user.getCreatedTime());

            response.put("success", true);
            response.put("data", userInfo);
            response.put("code", 200);

            return response;
        } catch (Exception e) {
            log.error("获取用户信息失败", e);
            response.put("success", false);
            response.put("message", "获取用户信息失败");
            response.put("code", 500);
            return response;
        }
    }
    
    /**
     * Vue前端登出API
     * POST /api/v1/auth/vue-logout
     */
    @PostMapping("/vue-logout")
    public Map<String, Object> logout(HttpServletRequest request) {
        Map<String, Object> response = new HashMap<>();

        try {
            // 从请求头中获取Token
            String authHeader = request.getHeader("Authorization");
            String token = null;
            if (authHeader != null && authHeader.startsWith("Bearer ")) {
                token = authHeader.substring(7);
            }

            if (token != null && jwtTokenProvider.validateToken(token)) {
                Long userId = jwtTokenProvider.getUserIdFromToken(token);
                log.info("用户登出，用户ID：{}", userId);
            }

            // 对于JWT Token，登出主要是前端删除Token
            // 这里可以将Token加入黑名单（如果需要的话）
            response.put("success", true);
            response.put("message", "登出成功");
            response.put("code", 200);
        } catch (Exception e) {
            log.error("登出异常：{}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "登出失败");
            response.put("code", 500);
        }

        return response;
    }
    
    /**
     * Vue前端检查Token状态API
     * GET /api/auth/check-token
     */
    @GetMapping("/check-token")
    public Map<String, Object> checkToken(HttpServletRequest request) {
        Map<String, Object> response = new HashMap<>();

        // 从请求头中获取Token
        String authHeader = request.getHeader("Authorization");
        String token = null;
        if (authHeader != null && authHeader.startsWith("Bearer ")) {
            token = authHeader.substring(7);
        }

        boolean isValid = token != null && jwtTokenProvider.validateToken(token);

        response.put("success", true);
        response.put("data", Map.of("isValid", isValid));
        response.put("code", 200);

        if (isValid) {
            response.put("message", "Token有效");
        } else {
            response.put("message", "Token无效或已过期");
        }

        return response;
    }

    /**
     * 刷新Token API
     * POST /api/auth/refresh-token
     */
    @PostMapping("/refresh-token")
    public Map<String, Object> refreshToken(HttpServletRequest request) {
        Map<String, Object> response = new HashMap<>();

        // 从请求头中获取Token
        String authHeader = request.getHeader("Authorization");
        String token = null;
        if (authHeader != null && authHeader.startsWith("Bearer ")) {
            token = authHeader.substring(7);
        }

        if (token == null) {
            response.put("success", false);
            response.put("message", "Token不存在");
            response.put("code", 400);
            return response;
        }

        // 暂时不支持刷新Token功能，返回原Token
        String newToken = token;
        if (newToken != null) {
            response.put("success", true);
            response.put("data", Map.of("token", newToken));
            response.put("message", "Token刷新成功");
            response.put("code", 200);
        } else {
            response.put("success", false);
            response.put("message", "Token刷新失败");
            response.put("code", 400);
        }

        return response;
    }

    /**
     * Vue前端获取用户订阅的Agent列表API
     * GET /api/v1/vue-auth/subscribed-agents
     */
    @GetMapping("/subscribed-agents")
    public Map<String, Object> getUserSubscribedAgents(HttpServletRequest request) {
        Map<String, Object> response = new HashMap<>();

        // 从请求头中获取Token
        String authHeader = request.getHeader("Authorization");
        String token = null;
        if (authHeader != null && authHeader.startsWith("Bearer ")) {
            token = authHeader.substring(7);
        }

        if (token == null || !jwtTokenProvider.validateToken(token)) {
            response.put("success", false);
            response.put("message", "Token无效或已过期");
            response.put("code", 401);
            return response;
        }

        try {
            // 从Token中获取用户信息
            Long userId = jwtTokenProvider.getUserIdFromToken(token);

            // 查询用户详细信息
            User user = userMapper.selectById(userId);
            if (user == null || user.getStatus() != 1) {
                response.put("success", false);
                response.put("message", "用户不存在");
                response.put("code", 404);
                return response;
            }

            // 获取用户订阅的Agent列表
            var subscriptions = userAgentSubscriptionService.getUserActiveSubscriptions(userId);

            response.put("success", true);
            response.put("data", subscriptions);
            response.put("code", 200);

            log.info("获取用户订阅Agent列表成功，用户ID：{}，订阅数量：{}", userId, subscriptions.size());
            return response;
        } catch (Exception e) {
            log.error("获取用户订阅Agent列表失败", e);
            response.put("success", false);
            response.put("message", "获取订阅Agent列表失败");
            response.put("code", 500);
            return response;
        }
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0];
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }

    /**
     * 将UserVO转换为User实体
     */
    private User convertUserVOToUser(UserVO userVO) {
        User user = new User();
        user.setId(userVO.getId());
        user.setUsername(userVO.getUsername());
        user.setEmail(userVO.getEmail());
        user.setRealName(userVO.getRealName());
        user.setLastLoginTime(userVO.getLastLoginTime());
        return user;
    }

    /**
     * 根据邮箱查找或创建用户
     */
    private User findOrCreateUser(String email) {
        try {
            // 先查找用户
            User user = userMapper.findByEmail(email);

            if (user != null && user.getStatus() == 1) {
                log.info("用户已存在，邮箱：{}，用户ID：{}，审核状态：{}", email, user.getId(), user.getApprovalStatus());
                return user;
            }

            // 用户不存在，创建新用户
            log.info("用户不存在，开始创建新用户，邮箱：{}", email);
            return createUser(email);
        } catch (Exception e) {
            log.error("查找或创建用户失败，邮箱：{}", email, e);
            return null;
        }
    }

    /**
     * 创建新用户
     */
    private User createUser(String email) {
        try {
            User user = new User();
            user.setEmail(email);
            user.setUsername(email); // 使用邮箱作为用户名
            user.setRealName(email.substring(0, email.indexOf("@"))); // 使用邮箱前缀作为真实姓名
            user.setStatus(1); // 正常状态
            user.setForward("PORTAL"); // 来源：官网注册
            user.setApprovalStatus(1); // 自动通过审核
            user.setApprovedTime(java.time.LocalDateTime.now());
            user.setPassword(MD5.create().digestHex(PasswordUtil.generateRandomPassword()));
            userMapper.insert(user);

            log.info("创建用户成功，邮箱：{}，用户ID：{}", email, user.getId());
            return user;
        } catch (Exception e) {
            log.error("创建用户失败，邮箱：{}", email, e);
            return null;
        }
    }

    /**
     * 更新用户最后登录信息
     */
    private void updateLastLoginInfo(Long userId, String ip) {
        try {
            User user = userMapper.selectById(userId);
            if (user != null) {
                user.setLastLoginTime(java.time.LocalDateTime.now());
                userMapper.updateById(user);
                log.info("更新用户登录信息成功，用户ID：{}，IP：{}", userId, ip);
            }
        } catch (Exception e) {
            log.error("更新用户登录信息失败，用户ID：{}，错误：{}", userId, e.getMessage());
        }
    }

    /**
     * 验证邮箱格式
     */
    private boolean isValidEmail(String email) {
        if (email == null || email.trim().isEmpty()) {
            return false;
        }

        // 简单的邮箱格式验证
        String emailRegex = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$";
        return email.matches(emailRegex);
    }
}
