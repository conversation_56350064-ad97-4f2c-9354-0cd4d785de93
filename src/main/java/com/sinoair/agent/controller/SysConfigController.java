package com.sinoair.agent.controller;

import com.sinoair.agent.common.Result;
import com.sinoair.agent.entity.SysConfig;
import com.sinoair.agent.service.SysConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 系统配置控制器
 *
 * <AUTHOR> Team
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/sys-config")
@RequiredArgsConstructor
@Tag(name = "系统配置管理", description = "系统配置和字典数据管理")
public class SysConfigController {

    private final SysConfigService sysConfigService;

    @Operation(summary = "获取所有配置")
    @GetMapping
    public Result<List<SysConfig>> getAllConfigs() {
        try {
            List<SysConfig> configs = sysConfigService.list();
            return Result.success(configs);
        } catch (Exception e) {
            log.error("获取配置列表失败", e);
            return Result.error("获取配置列表失败: " + e.getMessage());
        }
    }

    @Operation(summary = "根据配置组获取配置列表")
    @GetMapping("/group/{configGroup}")
    public Result<List<SysConfig>> getConfigsByGroup(
            @Parameter(description = "配置组名") @PathVariable String configGroup) {
        try {
            List<SysConfig> configs = sysConfigService.getEnabledByGroup(configGroup);
            return Result.success(configs);
        } catch (Exception e) {
            log.error("获取配置组[{}]失败", configGroup, e);
            return Result.error("获取配置组失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取字典数据")
    @GetMapping("/dict")
    public Result<Map<String, List<SysConfig>>> getDictData() {
        try {
            Map<String, List<SysConfig>> dictData = sysConfigService.getDictData();
            return Result.success(dictData);
        } catch (Exception e) {
            log.error("获取字典数据失败", e);
            return Result.error("获取字典数据失败: " + e.getMessage());
        }
    }

    @Operation(summary = "根据配置组获取字典数据")
    @GetMapping("/dict/{configGroup}")
    public Result<List<SysConfig>> getDictDataByGroup(
            @Parameter(description = "配置组名") @PathVariable String configGroup) {
        try {
            List<SysConfig> dictData = sysConfigService.getDictDataByGroup(configGroup);
            return Result.success(dictData);
        } catch (Exception e) {
            log.error("获取字典数据[{}]失败", configGroup, e);
            return Result.error("获取字典数据失败: " + e.getMessage());
        }
    }

    @Operation(summary = "根据ID获取配置详情")
    @GetMapping("/{id}")
    public Result<SysConfig> getConfigById(
            @Parameter(description = "配置ID") @PathVariable Long id) {
        try {
            SysConfig config = sysConfigService.getById(id);
            if (config == null) {
                return Result.error("配置不存在");
            }
            return Result.success(config);
        } catch (Exception e) {
            log.error("获取配置详情失败", e);
            return Result.error("获取配置详情失败: " + e.getMessage());
        }
    }

    @Operation(summary = "创建配置")
    @PostMapping
    public Result<String> createConfig(@RequestBody SysConfig config) {
        try {
            boolean success = sysConfigService.save(config);
            if (success) {
                return Result.success("创建配置成功");
            } else {
                return Result.error("创建配置失败");
            }
        } catch (Exception e) {
            log.error("创建配置失败", e);
            return Result.error("创建配置失败: " + e.getMessage());
        }
    }

    @Operation(summary = "更新配置")
    @PutMapping("/{id}")
    public Result<String> updateConfig(
            @Parameter(description = "配置ID") @PathVariable Long id,
            @RequestBody SysConfig config) {
        try {
            config.setId(id);
            boolean success = sysConfigService.updateById(config);
            if (success) {
                return Result.success("更新配置成功");
            } else {
                return Result.error("更新配置失败");
            }
        } catch (Exception e) {
            log.error("更新配置失败", e);
            return Result.error("更新配置失败: " + e.getMessage());
        }
    }

    @Operation(summary = "删除配置")
    @DeleteMapping("/{id}")
    public Result<String> deleteConfig(
            @Parameter(description = "配置ID") @PathVariable Long id) {
        try {
            boolean success = sysConfigService.removeById(id);
            if (success) {
                return Result.success("删除配置成功");
            } else {
                return Result.error("删除配置失败");
            }
        } catch (Exception e) {
            log.error("删除配置失败", e);
            return Result.error("删除配置失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取配置值")
    @GetMapping("/value")
    public Result<String> getConfigValue(
            @Parameter(description = "配置组名") @RequestParam String configGroup,
            @Parameter(description = "配置键") @RequestParam String configKey) {
        try {
            String value = sysConfigService.getConfigValue(configGroup, configKey);
            return Result.success(value);
        } catch (Exception e) {
            log.error("获取配置值失败", e);
            return Result.error("获取配置值失败: " + e.getMessage());
        }
    }
}
