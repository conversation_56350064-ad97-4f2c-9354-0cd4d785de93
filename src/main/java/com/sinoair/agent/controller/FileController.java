package com.sinoair.agent.controller;

import com.sinoair.agent.annotation.OperationLogRecord;
import com.sinoair.agent.common.Result;
import com.sinoair.agent.dto.response.FileUploadResponse;
import com.sinoair.agent.entity.UploadedFile;
import com.sinoair.agent.service.FileService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 文件控制器
 * 
 * <AUTHOR> Team
 */
@Tag(name = "文件管理", description = "文件上传、下载、预览等文件管理功能")
@RestController
@RequestMapping("/api/v1/files")
@RequiredArgsConstructor
public class FileController {

    private final FileService fileService;

    @Operation(summary = "上传文件", description = "上传文档文件，支持图片和PDF等格式")
    @PostMapping("/upload")
    @OperationLogRecord(module = "文件管理", operationType = "UPLOAD", operationDesc = "上传文件")
    public Result<FileUploadResponse> uploadFile(
            @Parameter(description = "上传的文件") @RequestParam("file") MultipartFile file,
            @Parameter(description = "业务类型") @RequestParam(required = false) String businessType) {
        return fileService.uploadFile(file, businessType);
    }

    @Operation(summary = "获取文件信息", description = "获取文件详细信息")
    @GetMapping("/{id}/info")
    public Result<UploadedFile> getFileInfo(@Parameter(description = "文件ID") @PathVariable Long id) {
        return fileService.getFileInfo(id);
    }

    @Operation(summary = "文件预览", description = "获取文件预览信息")
    @GetMapping("/{id}/preview")
    public ResponseEntity<byte[]> previewFile(@Parameter(description = "文件ID") @PathVariable Long id) {
        Result<UploadedFile> fileInfoResult = fileService.getFileInfo(id);
        if (!fileInfoResult.isSuccess()) {
            return ResponseEntity.notFound().build();
        }

        Result<byte[]> downloadResult = fileService.downloadFile(id);
        if (!downloadResult.isSuccess()) {
            return ResponseEntity.notFound().build();
        }

        UploadedFile fileInfo = fileInfoResult.getData();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.parseMediaType(fileInfo.getMimeType()));
        headers.setContentLength(downloadResult.getData().length);
        headers.add("Content-Disposition", "inline; filename=\"" + fileInfo.getOriginalName() + "\"");

        return ResponseEntity.ok()
                .headers(headers)
                .body(downloadResult.getData());
    }

    @Operation(summary = "文件下载", description = "下载指定文件")
    @GetMapping("/{id}/download")
    public ResponseEntity<byte[]> downloadFile(@Parameter(description = "文件ID") @PathVariable Long id) {
        Result<UploadedFile> fileInfoResult = fileService.getFileInfo(id);
        if (!fileInfoResult.isSuccess()) {
            return ResponseEntity.notFound().build();
        }

        Result<byte[]> downloadResult = fileService.downloadFile(id);
        if (!downloadResult.isSuccess()) {
            return ResponseEntity.notFound().build();
        }

        UploadedFile fileInfo = fileInfoResult.getData();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        headers.setContentLength(downloadResult.getData().length);
        headers.add("Content-Disposition", "attachment; filename=\"" + fileInfo.getOriginalName() + "\"");

        return ResponseEntity.ok()
                .headers(headers)
                .body(downloadResult.getData());
    }

    @Operation(summary = "删除文件", description = "删除指定文件")
    @DeleteMapping("/{id}")
    @PreAuthorize("hasPermission('file', 'delete')")
    @OperationLogRecord(module = "文件管理", operationType = "DELETE", operationDesc = "删除文件")
    public Result<String> deleteFile(@Parameter(description = "文件ID") @PathVariable Long id) {
        return fileService.deleteFile(id);
    }

    @Operation(summary = "公开文件预览", description = "无需认证的文件预览接口，用于图片等资源的公开访问")
    @GetMapping("/public/{id}/preview")
    public ResponseEntity<byte[]> publicPreviewFile(@Parameter(description = "文件ID") @PathVariable Long id) {
        Result<UploadedFile> fileInfoResult = fileService.getFileInfo(id);
        if (!fileInfoResult.isSuccess()) {
            return ResponseEntity.notFound().build();
        }

        Result<byte[]> downloadResult = fileService.downloadFile(id);
        if (!downloadResult.isSuccess()) {
            return ResponseEntity.notFound().build();
        }

        UploadedFile fileInfo = fileInfoResult.getData();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.parseMediaType(fileInfo.getMimeType()));
        headers.setContentLength(downloadResult.getData().length);
        headers.add("Content-Disposition", "inline; filename=\"" + fileInfo.getOriginalName() + "\"");

        // 添加缓存控制
        headers.setCacheControl("public, max-age=3600");

        return ResponseEntity.ok()
                .headers(headers)
                .body(downloadResult.getData());
    }
}
