package com.sinoair.agent.controller;

import com.sinoair.agent.annotation.OperationLogRecord;
import com.sinoair.agent.common.Result;
import com.sinoair.agent.dto.response.MinioFileInfo;
import com.sinoair.agent.service.MinioService;
import io.minio.http.Method;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * MinIO文件存储控制器
 * 
 * <AUTHOR> Team
 */
@Tag(name = "MinIO文件存储", description = "MinIO对象存储相关操作")
@RestController
@RequestMapping("/api/v1/minio")
@RequiredArgsConstructor
@ConditionalOnBean(MinioService.class)
public class MinioController {

    private final MinioService minioService;

    @Operation(summary = "列出文件", description = "列出指定前缀的所有文件")
    @GetMapping("/files")
    @PreAuthorize("hasPermission('file', 'read')")
    public Result<List<MinioFileInfo>> listFiles(
            @Parameter(description = "文件前缀") @RequestParam(required = false, defaultValue = "") String prefix) {
        try {
            List<MinioFileInfo> files = minioService.listFiles(prefix);
            return Result.success("获取文件列表成功", files);
        } catch (Exception e) {
            return Result.error(500, "获取文件列表失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取文件信息", description = "获取指定对象键的文件信息")
    @GetMapping("/files/{objectKey}/info")
    @PreAuthorize("hasPermission('file', 'read')")
    public Result<MinioFileInfo> getFileInfo(
            @Parameter(description = "对象键") @PathVariable String objectKey) {
        try {
            MinioFileInfo fileInfo = minioService.getFileInfo(objectKey);
            return Result.success("获取文件信息成功", fileInfo);
        } catch (Exception e) {
            return Result.error(500, "获取文件信息失败: " + e.getMessage());
        }
    }

    @Operation(summary = "检查文件是否存在", description = "检查指定对象键的文件是否存在")
    @GetMapping("/files/{objectKey}/exists")
    @PreAuthorize("hasPermission('file', 'read')")
    public Result<Boolean> fileExists(
            @Parameter(description = "对象键") @PathVariable String objectKey) {
        try {
            boolean exists = minioService.fileExists(objectKey);
            return Result.success("检查文件存在性成功", exists);
        } catch (Exception e) {
            return Result.error(500, "检查文件存在性失败: " + e.getMessage());
        }
    }

    @Operation(summary = "生成预签名URL", description = "生成文件的预签名访问URL")
    @PostMapping("/files/{objectKey}/presigned-url")
    @PreAuthorize("hasPermission('file', 'read')")
    @OperationLogRecord(module = "MinIO管理", operationType = "GENERATE", operationDesc = "生成预签名URL")
    public Result<String> generatePresignedUrl(
            @Parameter(description = "对象键") @PathVariable String objectKey,
            @Parameter(description = "HTTP方法") @RequestParam(defaultValue = "GET") String method,
            @Parameter(description = "过期时间（小时）") @RequestParam(defaultValue = "24") int expireHours) {
        try {
            Method httpMethod = Method.valueOf(method.toUpperCase());
            String presignedUrl = minioService.generatePresignedUrl(objectKey, httpMethod, expireHours, TimeUnit.HOURS);
            return Result.success("生成预签名URL成功", presignedUrl);
        } catch (Exception e) {
            return Result.error(500, "生成预签名URL失败: " + e.getMessage());
        }
    }

    @Operation(summary = "删除文件", description = "删除指定对象键的文件")
    @DeleteMapping("/files/{objectKey}")
    @PreAuthorize("hasPermission('file', 'delete')")
    @OperationLogRecord(module = "MinIO管理", operationType = "DELETE", operationDesc = "删除MinIO文件")
    public Result<String> deleteFile(
            @Parameter(description = "对象键") @PathVariable String objectKey) {
        try {
            minioService.deleteFile(objectKey);
            return Result.success("删除文件成功");
        } catch (Exception e) {
            return Result.error(500, "删除文件失败: " + e.getMessage());
        }
    }

    @Operation(summary = "列出存储桶", description = "列出所有存储桶")
    @GetMapping("/buckets")
    @PreAuthorize("hasPermission('system', 'admin')")
    public Result<List<String>> listBuckets() {
        try {
            List<String> buckets = minioService.listBuckets();
            return Result.success("获取存储桶列表成功", buckets);
        } catch (Exception e) {
            return Result.error(500, "获取存储桶列表失败: " + e.getMessage());
        }
    }

    @Operation(summary = "创建存储桶", description = "创建新的存储桶")
    @PostMapping("/buckets/{bucketName}")
    @PreAuthorize("hasPermission('system', 'admin')")
    @OperationLogRecord(module = "MinIO管理", operationType = "CREATE", operationDesc = "创建存储桶")
    public Result<String> createBucket(
            @Parameter(description = "存储桶名称") @PathVariable String bucketName) {
        try {
            minioService.createBucketIfNotExists(bucketName);
            return Result.success("创建存储桶成功");
        } catch (Exception e) {
            return Result.error(500, "创建存储桶失败: " + e.getMessage());
        }
    }
}
