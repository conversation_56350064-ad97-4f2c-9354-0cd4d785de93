package com.sinoair.agent.controller;

import com.sinoair.agent.common.Result;
import com.sinoair.agent.security.UserPrincipal;
import com.sinoair.agent.service.PermissionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 用户权限控制器
 * 提供当前用户的权限信息
 *
 * <AUTHOR> Team
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/user")
@RequiredArgsConstructor
@Tag(name = "用户权限", description = "当前用户权限信息查询")
public class UserPermissionController {

    private final PermissionService permissionService;

    @Operation(summary = "获取当前用户权限列表")
    @GetMapping("/permissions")
    public Result<List<String>> getCurrentUserPermissions(@AuthenticationPrincipal UserPrincipal userPrincipal) {
        try {
            if (userPrincipal == null) {
                log.warn("用户未登录，返回默认权限");
                return Result.success(getDefaultPermissions());
            }

            log.debug("获取用户权限: userId={}, username={}", userPrincipal.getId(), userPrincipal.getUsername());

            // 所有用户（包括admin）都严格按照数据库权限配置
            // 获取用户的按钮权限（包含所有权限编码）
            List<String> permissions = permissionService.getButtonPermissionsByUserId(userPrincipal.getId());

            // 添加菜单权限
            try {
                List<String> menuPermissions = permissionService.getMenuPermissionsByUserId(userPrincipal.getId())
                        .stream()
                        .map(permission -> permission.getPermissionCode())
                        .filter(code -> code != null && !code.trim().isEmpty())
                        .toList();
                permissions.addAll(menuPermissions);
            } catch (Exception e) {
                log.warn("获取菜单权限失败，跳过: {}", e.getMessage());
            }

            log.debug("用户权限获取成功: userId={}, permissions={}", userPrincipal.getId(), permissions);

            return Result.success(permissions);
        } catch (Exception e) {
            log.error("获取用户权限失败: userId={}", userPrincipal != null ? userPrincipal.getId() : "null", e);

            // 返回默认权限，确保基本功能可用
            List<String> defaultPermissions = getDefaultPermissions();
            log.warn("返回默认权限: {}", defaultPermissions);
            return Result.success(defaultPermissions);
        }
    }



    /**
     * 获取默认权限（API失败时的备用）
     */
    private List<String> getDefaultPermissions() {
        return List.of(
            "DASHBOARD_VIEW", "AGENT_VIEW", "PLAYGROUND_VIEW", "TEMPLATE_VIEW",
            "business:smart-form", "RECOGNITION_VIEW"
        );
    }

    @Operation(summary = "获取当前用户信息")
    @GetMapping("/info")
    public Result<UserPrincipal> getCurrentUserInfo(@AuthenticationPrincipal UserPrincipal userPrincipal) {
        try {
            log.debug("获取用户信息: userId={}, username={}", userPrincipal.getId(), userPrincipal.getUsername());
            
            // 清除敏感信息
            UserPrincipal safeUserPrincipal = new UserPrincipal();
            safeUserPrincipal.setId(userPrincipal.getId());
            safeUserPrincipal.setUsername(userPrincipal.getUsername());
            safeUserPrincipal.setRealName(userPrincipal.getRealName());
            safeUserPrincipal.setEmail(userPrincipal.getEmail());
            safeUserPrincipal.setRoleId(userPrincipal.getRoleId());
            safeUserPrincipal.setRoleCode(userPrincipal.getRoleCode());
            safeUserPrincipal.setPermissions(userPrincipal.getPermissions());
            
            return Result.success(safeUserPrincipal);
        } catch (Exception e) {
            log.error("获取用户信息失败: userId={}", userPrincipal.getId(), e);
            return Result.error("获取用户信息失败: " + e.getMessage());
        }
    }
}
