package com.sinoair.agent.controller;

import com.sinoair.agent.annotation.OperationLogRecord;
import com.sinoair.agent.common.Result;
import com.sinoair.agent.entity.Permission;
import com.sinoair.agent.service.PermissionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 权限管理控制器
 *
 * <AUTHOR> Team
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/permissions")
@RequiredArgsConstructor
@Tag(name = "权限管理", description = "权限的增删改查和树形结构管理")
public class PermissionController {

    private final PermissionService permissionService;

    @Operation(summary = "获取所有权限（树形结构）")
    @GetMapping("/tree")
    // @PreAuthorize("hasAuthority('PERMISSION_VIEW') or hasRole('SUPER_ADMIN')")
    public Result<List<Permission>> getPermissionTree() {
        List<Permission> permissions = permissionService.getAllPermissionsTree();
        return Result.success(permissions);
    }

    @Operation(summary = "根据权限类型查询权限")
    @GetMapping("/type/{type}")
    public Result<List<Permission>> getPermissionsByType(@PathVariable Integer type) {
        List<Permission> permissions = permissionService.getPermissionsByType(type);
        return Result.success(permissions);
    }

    @Operation(summary = "根据ID获取权限详情")
    @GetMapping("/{id}")
    public Result<Permission> getPermission(@PathVariable Long id) {
        Permission permission = permissionService.getById(id);
        if (permission == null) {
            return Result.error("权限不存在");
        }
        return Result.success(permission);
    }

    @Operation(summary = "创建权限")
    @PostMapping
    @OperationLogRecord(module = "权限管理", operationType = "CREATE", operationDesc = "创建权限")
    public Result<Void> createPermission(@RequestBody Permission permission) {
        try {
            boolean success = permissionService.createPermission(permission);
            if (success) {
                return Result.success();
            } else {
                return Result.error("创建权限失败");
            }
        } catch (Exception e) {
            log.error("创建权限失败", e);
            return Result.error(e.getMessage());
        }
    }

    @Operation(summary = "更新权限")
    @PutMapping("/{id}")
    @OperationLogRecord(module = "权限管理", operationType = "UPDATE", operationDesc = "更新权限")
    public Result<Void> updatePermission(@PathVariable Long id, @RequestBody Permission permission) {
        try {
            permission.setId(id);
            boolean success = permissionService.updatePermission(permission);
            if (success) {
                return Result.success();
            } else {
                return Result.error("更新权限失败");
            }
        } catch (Exception e) {
            log.error("更新权限失败", e);
            return Result.error(e.getMessage());
        }
    }

    @Operation(summary = "删除权限")
    @DeleteMapping("/{id}")
    @OperationLogRecord(module = "权限管理", operationType = "DELETE", operationDesc = "删除权限")
    public Result<Void> deletePermission(@PathVariable Long id) {
        try {
            boolean success = permissionService.deletePermission(id);
            if (success) {
                return Result.success();
            } else {
                return Result.error("删除权限失败");
            }
        } catch (Exception e) {
            log.error("删除权限失败", e);
            return Result.error(e.getMessage());
        }
    }

    @Operation(summary = "检查权限编码是否存在")
    @GetMapping("/check-code")
    public Result<Boolean> checkPermissionCode(@RequestParam String permissionCode) {
        boolean exists = permissionService.existsByPermissionCode(permissionCode);
        return Result.success(exists);
    }

    @Operation(summary = "根据用户ID获取菜单权限")
    @GetMapping("/user/{userId}/menus")
    public Result<List<Permission>> getUserMenuPermissions(@PathVariable Long userId) {
        List<Permission> permissions = permissionService.getMenuPermissionsByUserId(userId);
        return Result.success(permissions);
    }

    @Operation(summary = "根据用户ID获取按钮权限")
    @GetMapping("/user/{userId}/buttons")
    public Result<List<String>> getUserButtonPermissions(@PathVariable Long userId) {
        List<String> permissions = permissionService.getButtonPermissionsByUserId(userId);
        return Result.success(permissions);
    }
}
