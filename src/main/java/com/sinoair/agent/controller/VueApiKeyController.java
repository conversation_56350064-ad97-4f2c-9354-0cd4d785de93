package com.sinoair.agent.controller;

import com.sinoair.agent.entity.ApiKey;
import com.sinoair.agent.entity.User;
import com.sinoair.agent.dto.response.UserVO;
import com.sinoair.agent.service.ApiKeyService;
import com.sinoair.agent.service.UserService;
import com.sinoair.agent.security.JwtTokenProvider;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Vue前端API密钥管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/dashboard/api-keys")
@CrossOrigin(originPatterns = "*", allowCredentials = "true")
public class VueApiKeyController {
    
    @Autowired
    private ApiKeyService apiKeyService;
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private JwtTokenProvider jwtTokenProvider;
    
    /**
     * 获取API密钥列表
     * GET /api/dashboard/api-keys
     */
    @GetMapping
    public ResponseEntity<Map<String, Object>> getApiKeys(HttpServletRequest request) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 从JWT Token获取用户信息
            User user = getUserFromToken(request);
            if (user == null) {
                response.put("success", false);
                response.put("message", "用户未登录");
                response.put("code", 401);
                return ResponseEntity.ok(response);
            }
            
            // 获取用户的API密钥列表
            com.baomidou.mybatisplus.core.metadata.IPage<ApiKey> page =
                    apiKeyService.getApiKeyPage(1, 100, user.getId(), null, null);
            List<ApiKey> apiKeys = page.getRecords();
            
            response.put("success", true);
            response.put("data", apiKeys);
            response.put("code", 200);
            
        } catch (Exception e) {
            log.error("获取API密钥列表失败", e);
            response.put("success", false);
            response.put("message", "获取API密钥列表失败：" + e.getMessage());
            response.put("code", 500);
        }
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 创建API密钥 (前端直接POST请求)
     * POST /api/v1/dashboard/api-keys
     */
    @PostMapping
    public ResponseEntity<Map<String, Object>> createApiKeyDirect(
            @RequestBody Map<String, Object> requestBody,
            HttpServletRequest request) {

        String keyName = (String) requestBody.get("keyName");
        String description = (String) requestBody.get("description");

        return createApiKeyInternal(keyName, description, request);
    }

    /**
     * 创建API密钥 (兼容旧接口)
     * POST /api/v1/dashboard/api-keys/create
     */
    @PostMapping("/create")
    public ResponseEntity<Map<String, Object>> createApiKey(
            @RequestParam String keyName,
            @RequestParam(required = false) String description,
            HttpServletRequest request) {

        return createApiKeyInternal(keyName, description, request);
    }

    /**
     * 创建API密钥的内部实现
     */
    private ResponseEntity<Map<String, Object>> createApiKeyInternal(
            String keyName,
            String description,
            HttpServletRequest request) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 从JWT Token获取用户信息
            User user = getUserFromToken(request);
            if (user == null) {
                response.put("success", false);
                response.put("message", "用户未登录");
                response.put("code", 401);
                return ResponseEntity.ok(response);
            }
            
            // 验证参数
            if (keyName == null || keyName.trim().isEmpty()) {
                response.put("success", false);
                response.put("message", "API密钥名称不能为空");
                response.put("code", 400);
                return ResponseEntity.ok(response);
            }
            
            // 创建API密钥对象
            ApiKey apiKey = new ApiKey();
            apiKey.setUserId(user.getId());
            apiKey.setKeyName(keyName.trim());
            apiKey.setDescription(description);

            // 调用服务创建API密钥，返回包含明文keySecret的响应对象
            com.sinoair.agent.dto.response.ApiKeyCreateResponse createResponse = apiKeyService.createApiKeyWithSecret(apiKey);

            if (createResponse != null) {
                response.put("success", true);
                response.put("message", "API密钥创建成功");
                response.put("data", createResponse);
                response.put("code", 200);

                log.info("API密钥创建成功，用户ID：{}，密钥名称：{}，密钥ID：{}",
                        user.getId(), keyName, createResponse.getKeyId());
            } else {
                response.put("success", false);
                response.put("message", "创建失败，请稍后重试");
                response.put("code", 500);
            }
            
        } catch (Exception e) {
            log.error("创建API密钥失败", e);
            response.put("success", false);
            response.put("message", "创建失败：" + e.getMessage());
            response.put("code", 500);
        }
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 获取API密钥详情
     * GET /api/dashboard/api-keys/{id}
     */
    @GetMapping("/{id}")
    public ResponseEntity<Map<String, Object>> getApiKeyDetail(
            @PathVariable Long id,
            HttpServletRequest request) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 从JWT Token获取用户信息
            User user = getUserFromToken(request);
            if (user == null) {
                response.put("success", false);
                response.put("message", "用户未登录");
                response.put("code", 401);
                return ResponseEntity.ok(response);
            }
            
            // 根据ID查询API密钥
            ApiKey apiKey = apiKeyService.getById(id);

            // 检查API密钥是否存在且属于当前用户
            if (apiKey != null && apiKey.getUserId().equals(user.getId())) {
                // 为了安全，不返回keySecret字段
                ApiKey safeApiKey = new ApiKey();
                safeApiKey.setId(apiKey.getId());
                safeApiKey.setKeyId(apiKey.getKeyId());
                safeApiKey.setKeyName(apiKey.getKeyName());
                safeApiKey.setDescription(apiKey.getDescription());
                safeApiKey.setUserId(apiKey.getUserId());
                safeApiKey.setStatus(apiKey.getStatus());
                safeApiKey.setRateLimit(apiKey.getRateLimit());
                safeApiKey.setDailyLimit(apiKey.getDailyLimit());
                safeApiKey.setMonthlyLimit(apiKey.getMonthlyLimit());
                safeApiKey.setExpiresAt(apiKey.getExpiresAt());
                safeApiKey.setLastUsedAt(apiKey.getLastUsedAt());
                safeApiKey.setLastUsedIp(apiKey.getLastUsedIp());
                safeApiKey.setCreatedTime(apiKey.getCreatedTime());
                safeApiKey.setUpdatedTime(apiKey.getUpdatedTime());

                response.put("success", true);
                response.put("data", safeApiKey);
                response.put("code", 200);
                response.put("message", "获取API密钥详情成功");

                log.info("获取API密钥详情成功，用户ID：{}，密钥ID：{}", user.getId(), id);
            } else {
                response.put("success", false);
                response.put("message", "API密钥不存在或无权限访问");
                response.put("code", 404);

                log.warn("API密钥不存在或无权限访问，用户ID：{}，密钥ID：{}", user.getId(), id);
            }
            
        } catch (Exception e) {
            log.error("获取API密钥详情失败", e);
            response.put("success", false);
            response.put("message", "获取详情失败：" + e.getMessage());
            response.put("code", 500);
        }
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 切换API密钥状态
     * POST /api/api-keys/{id}/toggle
     */
    @PostMapping("/{id}/toggle")
    public ResponseEntity<Map<String, Object>> toggleApiKeyStatus(
            @PathVariable Long id,
            HttpServletRequest request) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 从JWT Token获取用户信息
            User user = getUserFromToken(request);
            if (user == null) {
                response.put("success", false);
                response.put("message", "用户未登录");
                response.put("code", 401);
                return ResponseEntity.ok(response);
            }
            
            // 根据ID查询API密钥
            ApiKey apiKey = apiKeyService.getById(id);

            // 检查API密钥是否存在且属于当前用户
            if (apiKey != null && apiKey.getUserId().equals(user.getId())) {
                // 切换状态：1-启用，0-禁用
                int newStatus = apiKey.getStatus() == 1 ? 0 : 1;
                apiKey.setStatus(newStatus);
                apiKey.setUpdatedTime(java.time.LocalDateTime.now());

                boolean success = apiKeyService.updateById(apiKey);

                if (success) {
                    response.put("success", true);
                    response.put("message", newStatus == 1 ? "API密钥已启用" : "API密钥已禁用");
                    response.put("code", 200);
                    response.put("data", Map.of("status", newStatus));

                    log.info("API密钥状态切换成功，用户ID：{}，密钥ID：{}，新状态：{}",
                            user.getId(), id, newStatus == 1 ? "启用" : "禁用");
                } else {
                    response.put("success", false);
                    response.put("message", "状态切换失败");
                    response.put("code", 500);
                }
            } else {
                response.put("success", false);
                response.put("message", "切换失败，密钥不存在或无权限");
                response.put("code", 404);

                log.warn("API密钥不存在或无权限访问，用户ID：{}，密钥ID：{}", user.getId(), id);
            }
            
        } catch (Exception e) {
            log.error("切换API密钥状态失败", e);
            response.put("success", false);
            response.put("message", "切换失败：" + e.getMessage());
            response.put("code", 500);
        }
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 删除API密钥
     * POST /api/dashboard/api-keys/{id}/delete
     */
    @PostMapping("/{id}/delete")
    public ResponseEntity<Map<String, Object>> deleteApiKey(
            @PathVariable Long id,
            HttpServletRequest request) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 从JWT Token获取用户信息
            User user = getUserFromToken(request);
            if (user == null) {
                response.put("success", false);
                response.put("message", "用户未登录");
                response.put("code", 401);
                return ResponseEntity.ok(response);
            }
            
            // 根据ID查询API密钥
            ApiKey apiKey = apiKeyService.getById(id);

            // 检查API密钥是否存在且属于当前用户
            if (apiKey != null && apiKey.getUserId().equals(user.getId())) {
                boolean success = apiKeyService.removeById(id);

                if (success) {
                    response.put("success", true);
                    response.put("message", "API密钥删除成功");
                    response.put("code", 200);

                    log.info("API密钥删除成功，用户ID：{}，密钥ID：{}，密钥名称：{}",
                            user.getId(), id, apiKey.getKeyName());
                } else {
                    response.put("success", false);
                    response.put("message", "删除失败，请稍后重试");
                    response.put("code", 500);
                }
            } else {
                response.put("success", false);
                response.put("message", "删除失败，密钥不存在或无权限");
                response.put("code", 404);

                log.warn("API密钥不存在或无权限访问，用户ID：{}，密钥ID：{}", user.getId(), id);
            }
            
        } catch (Exception e) {
            log.error("删除API密钥失败", e);
            response.put("success", false);
            response.put("message", "删除失败：" + e.getMessage());
            response.put("code", 500);
        }
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 重新生成API密钥
     * POST /api/api-keys/{id}/regenerate
     */
    @PostMapping("/{id}/regenerate")
    public ResponseEntity<Map<String, Object>> regenerateApiKey(
            @PathVariable Long id,
            HttpServletRequest request) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 从JWT Token获取用户信息
            User user = getUserFromToken(request);
            if (user == null) {
                response.put("success", false);
                response.put("message", "用户未登录");
                response.put("code", 401);
                return ResponseEntity.ok(response);
            }
            
            // 根据ID查询API密钥
            ApiKey existingApiKey = apiKeyService.getById(id);

            // 检查API密钥是否存在且属于当前用户
            if (existingApiKey != null && existingApiKey.getUserId().equals(user.getId())) {
                // 删除旧的API密钥
                apiKeyService.removeById(id);

                // 创建新的API密钥，保留原有配置
                ApiKey newApiKey = new ApiKey();
                newApiKey.setUserId(user.getId());
                newApiKey.setKeyName(existingApiKey.getKeyName());
                newApiKey.setDescription(existingApiKey.getDescription());
                newApiKey.setRateLimit(existingApiKey.getRateLimit());
                newApiKey.setDailyLimit(existingApiKey.getDailyLimit());
                newApiKey.setMonthlyLimit(existingApiKey.getMonthlyLimit());
                newApiKey.setExpiresAt(existingApiKey.getExpiresAt());

                ApiKey regeneratedApiKey = apiKeyService.createApiKey(newApiKey);

                if (regeneratedApiKey != null) {
                    response.put("success", true);
                    response.put("message", "API密钥重新生成成功");
                    response.put("data", regeneratedApiKey);
                    response.put("code", 200);

                    log.info("API密钥重新生成成功，用户ID：{}，原密钥ID：{}，新密钥ID：{}",
                            user.getId(), id, regeneratedApiKey.getId());
                } else {
                    response.put("success", false);
                    response.put("message", "重新生成失败，请稍后重试");
                    response.put("code", 500);
                }
            } else {
                response.put("success", false);
                response.put("message", "重新生成失败，密钥不存在或无权限");
                response.put("code", 404);

                log.warn("API密钥不存在或无权限访问，用户ID：{}，密钥ID：{}", user.getId(), id);
            }
            
        } catch (Exception e) {
            log.error("重新生成API密钥失败", e);
            response.put("success", false);
            response.put("message", "重新生成失败：" + e.getMessage());
            response.put("code", 500);
        }
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 从JWT Token获取用户信息
     */
    private User getUserFromToken(HttpServletRequest request) {
        try {
            // 从请求头中获取Token
            String authHeader = request.getHeader("Authorization");
            String token = null;
            if (authHeader != null && authHeader.startsWith("Bearer ")) {
                token = authHeader.substring(7);
            }

            log.debug("获取到Authorization头: {}", authHeader != null ? "有值" : "无值");
            log.debug("提取到Token: {}", token != null ? "有值" : "无值");

            if (token == null || !jwtTokenProvider.validateToken(token)) {
                log.debug("Token无效或为空");
                return null;
            }

            // 从Token中获取用户ID
            Long userId = jwtTokenProvider.getUserIdFromToken(token);
            if (userId == null) {
                log.debug("无法从Token中获取用户ID");
                return null;
            }

            log.debug("从Token中获取到用户ID: {}", userId);

            // 查询用户信息
            UserVO userVO = userService.getUserById(userId);
            if (userVO != null) {
                return convertUserVOToUser(userVO);
            }
            log.debug("未找到用户信息");
            return null;

        } catch (Exception e) {
            log.error("从Token获取用户信息失败", e);
            return null;
        }
    }

    /**
     * 将UserVO转换为User实体
     */
    private User convertUserVOToUser(UserVO userVO) {
        User user = new User();
        user.setId(userVO.getId());
        user.setUsername(userVO.getUsername());
        user.setEmail(userVO.getEmail());
        user.setRealName(userVO.getRealName());
        user.setLastLoginTime(userVO.getLastLoginTime());
        return user;
    }
}
