package com.sinoair.agent.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sinoair.agent.common.PageResult;
import com.sinoair.agent.common.Result;
import com.sinoair.agent.entity.ApiKey;
import com.sinoair.agent.security.UserPrincipal;
import com.sinoair.agent.service.ApiKeyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.time.LocalDateTime;

/**
 * API Key管理控制器
 *
 * <AUTHOR> Team
 */
@Tag(name = "API Key管理", description = "API Key的创建、查询、更新、删除等管理功能")
@RestController
@RequestMapping("/api/v1/api-keys")
@RequiredArgsConstructor
@Slf4j
public class ApiKeyController {

    private final ApiKeyService apiKeyService;

    @Operation(summary = "分页查询API Key列表", description = "支持按用户、名称、状态等条件查询")
    @GetMapping
    @PreAuthorize("hasAuthority('api_key:list')")
    public Result<PageResult<ApiKey>> getApiKeyPage(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") int current,
            @Parameter(description = "每页数量") @RequestParam(defaultValue = "10") int size,
            @Parameter(description = "用户ID") @RequestParam(required = false) Long userId,
            @Parameter(description = "API Key名称") @RequestParam(required = false) String keyName,
            @Parameter(description = "状态") @RequestParam(required = false) Integer status) {
        
        try {
            IPage<ApiKey> page = apiKeyService.getApiKeyPage(current, size, userId, keyName, status);
            PageResult<ApiKey> pageResult = PageResult.of(page);
            return Result.success(pageResult);
        } catch (Exception e) {
            log.error("查询API Key列表失败", e);
            return Result.error("查询API Key列表失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取当前用户的API Key列表")
    @GetMapping("/my")
    public Result<PageResult<ApiKey>> getMyApiKeys(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") int current,
            @Parameter(description = "每页数量") @RequestParam(defaultValue = "10") int size,
            @Parameter(description = "API Key名称") @RequestParam(required = false) String keyName,
            @Parameter(description = "状态") @RequestParam(required = false) Integer status,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        
        try {
            IPage<ApiKey> page = apiKeyService.getApiKeyPage(current, size, userPrincipal.getId(), keyName, status);
            PageResult<ApiKey> pageResult = PageResult.of(page);
            return Result.success(pageResult);
        } catch (Exception e) {
            log.error("查询我的API Key列表失败: userId={}", userPrincipal.getId(), e);
            return Result.error("查询API Key列表失败: " + e.getMessage());
        }
    }

    @Operation(summary = "创建API Key")
    @PostMapping
    public Result<ApiKey> createApiKey(@Valid @RequestBody CreateApiKeyRequest request,
                                      @AuthenticationPrincipal UserPrincipal userPrincipal) {
        try {
            // 检查用户API Key数量限制
            int existingCount = apiKeyService.getBaseMapper().countByUserId(userPrincipal.getId());
            if (existingCount >= 10) { // 限制每个用户最多10个API Key
                return Result.error("API Key数量已达上限，每个用户最多可创建10个API Key");
            }

            ApiKey apiKey = new ApiKey();
            apiKey.setKeyName(request.getKeyName());
            apiKey.setDescription(request.getDescription());
            apiKey.setUserId(userPrincipal.getId());
            apiKey.setRateLimit(request.getRateLimit());
            apiKey.setDailyLimit(request.getDailyLimit());
            apiKey.setMonthlyLimit(request.getMonthlyLimit());
            apiKey.setAllowedIps(request.getAllowedIps());
            apiKey.setAllowedDomains(request.getAllowedDomains());
            apiKey.setExpiresAt(request.getExpiresAt());

            ApiKey createdApiKey = apiKeyService.createApiKey(apiKey);
            
            log.info("创建API Key成功: keyId={}, userId={}, keyName={}", 
                    createdApiKey.getKeyId(), userPrincipal.getId(), request.getKeyName());
            
            return Result.success("API Key创建成功", createdApiKey);
        } catch (Exception e) {
            log.error("创建API Key失败: userId={}, keyName={}", userPrincipal.getId(), request.getKeyName(), e);
            return Result.error("创建API Key失败: " + e.getMessage());
        }
    }

    @Operation(summary = "更新API Key")
    @PutMapping("/{id}")
    public Result<Void> updateApiKey(@PathVariable Long id, 
                                    @Valid @RequestBody UpdateApiKeyRequest request,
                                    @AuthenticationPrincipal UserPrincipal userPrincipal) {
        try {
            ApiKey existingApiKey = apiKeyService.getById(id);
            if (existingApiKey == null) {
                return Result.error("API Key不存在");
            }

            // 检查权限：只能修改自己的API Key，或者管理员可以修改所有
            if (!existingApiKey.getUserId().equals(userPrincipal.getId()) &&
                !hasAdminRole(userPrincipal)) {
                return Result.error("无权限修改此API Key");
            }

            existingApiKey.setKeyName(request.getKeyName());
            existingApiKey.setDescription(request.getDescription());
            existingApiKey.setStatus(request.getStatus());
            existingApiKey.setRateLimit(request.getRateLimit());
            existingApiKey.setDailyLimit(request.getDailyLimit());
            existingApiKey.setMonthlyLimit(request.getMonthlyLimit());
            existingApiKey.setAllowedIps(request.getAllowedIps());
            existingApiKey.setAllowedDomains(request.getAllowedDomains());
            existingApiKey.setExpiresAt(request.getExpiresAt());
            existingApiKey.setUpdatedTime(LocalDateTime.now());

            apiKeyService.updateById(existingApiKey);
            
            log.info("更新API Key成功: id={}, keyId={}, userId={}",
                    id, existingApiKey.getKeyId(), userPrincipal.getId());

            return Result.success("API Key更新成功");
        } catch (Exception e) {
            log.error("更新API Key失败: id={}, userId={}", id, userPrincipal.getId(), e);
            return Result.error("更新API Key失败: " + e.getMessage());
        }
    }

    @Operation(summary = "删除API Key")
    @DeleteMapping("/{id}")
    public Result<Void> deleteApiKey(@PathVariable Long id,
                                    @AuthenticationPrincipal UserPrincipal userPrincipal) {
        try {
            ApiKey existingApiKey = apiKeyService.getById(id);
            if (existingApiKey == null) {
                return Result.error("API Key不存在");
            }

            // 检查权限：只能删除自己的API Key，或者管理员可以删除所有
            if (!existingApiKey.getUserId().equals(userPrincipal.getId()) &&
                !hasAdminRole(userPrincipal)) {
                return Result.error("无权限删除此API Key");
            }

            apiKeyService.removeById(id);
            
            log.info("删除API Key成功: id={}, keyId={}, userId={}",
                    id, existingApiKey.getKeyId(), userPrincipal.getId());

            return Result.success("API Key删除成功");
        } catch (Exception e) {
            log.error("删除API Key失败: id={}, userId={}", id, userPrincipal.getId(), e);
            return Result.error("删除API Key失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取API Key详情")
    @GetMapping("/{id}")
    public Result<ApiKey> getApiKeyDetail(@PathVariable Long id,
                                         @AuthenticationPrincipal UserPrincipal userPrincipal) {
        try {
            ApiKey apiKey = apiKeyService.getById(id);
            if (apiKey == null) {
                return Result.error("API Key不存在");
            }

            // 检查权限：只能查看自己的API Key，或者管理员可以查看所有
            if (!apiKey.getUserId().equals(userPrincipal.getId()) &&
                !hasAdminRole(userPrincipal)) {
                return Result.error("无权限查看此API Key");
            }

            // 隐藏敏感信息
            apiKey.setKeySecret(null);
            
            return Result.success(apiKey);
        } catch (Exception e) {
            log.error("获取API Key详情失败: id={}, userId={}", id, userPrincipal.getId(), e);
            return Result.error("获取API Key详情失败: " + e.getMessage());
        }
    }

    // 内部类：请求对象
    public static class CreateApiKeyRequest {
        private String keyName;
        private String description;
        private Integer rateLimit;
        private Integer dailyLimit;
        private Integer monthlyLimit;
        private String allowedIps;
        private String allowedDomains;
        private LocalDateTime expiresAt;

        // getters and setters
        public String getKeyName() { return keyName; }
        public void setKeyName(String keyName) { this.keyName = keyName; }
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        public Integer getRateLimit() { return rateLimit; }
        public void setRateLimit(Integer rateLimit) { this.rateLimit = rateLimit; }
        public Integer getDailyLimit() { return dailyLimit; }
        public void setDailyLimit(Integer dailyLimit) { this.dailyLimit = dailyLimit; }
        public Integer getMonthlyLimit() { return monthlyLimit; }
        public void setMonthlyLimit(Integer monthlyLimit) { this.monthlyLimit = monthlyLimit; }
        public String getAllowedIps() { return allowedIps; }
        public void setAllowedIps(String allowedIps) { this.allowedIps = allowedIps; }
        public String getAllowedDomains() { return allowedDomains; }
        public void setAllowedDomains(String allowedDomains) { this.allowedDomains = allowedDomains; }
        public LocalDateTime getExpiresAt() { return expiresAt; }
        public void setExpiresAt(LocalDateTime expiresAt) { this.expiresAt = expiresAt; }
    }

    public static class UpdateApiKeyRequest extends CreateApiKeyRequest {
        private Integer status;

        public Integer getStatus() { return status; }
        public void setStatus(Integer status) { this.status = status; }
    }

    /**
     * 检查用户是否有管理员角色
     */
    private boolean hasAdminRole(UserPrincipal userPrincipal) {
        return userPrincipal.getAuthorities().stream()
                .anyMatch(authority -> authority.getAuthority().equals("ROLE_ADMIN") ||
                                     authority.getAuthority().equals("ROLE_SUPER_ADMIN"));
    }
}
