package com.sinoair.agent.controller;

import com.sinoair.agent.annotation.OperationLogRecord;
import com.sinoair.agent.common.Result;
import com.sinoair.agent.entity.PageBinding;
import com.sinoair.agent.service.PageBindingService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 页面绑定管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/page-bindings")
@RequiredArgsConstructor
@Tag(name = "页面绑定管理", description = "页面字段绑定配置的增删改查")
public class PageBindingController {

    private final PageBindingService pageBindingService;

    @Operation(summary = "获取页面绑定列表")
    @GetMapping
    public Result<List<PageBinding>> getBindings() {
        List<PageBinding> bindings = pageBindingService.list();
        return Result.success(bindings);
    }

    @Operation(summary = "获取所有启用的绑定配置")
    @GetMapping("/active")
    public Result<List<PageBinding>> getActiveBindings() {
        List<PageBinding> bindings = pageBindingService.getActiveBindings();
        return Result.success(bindings);
    }

    @Operation(summary = "根据URL查找匹配的绑定配置")
    @GetMapping("/match")
    public Result<List<PageBinding>> findByUrl(@RequestParam String url) {
        List<PageBinding> bindings = pageBindingService.findByUrlPattern(url);
        return Result.success(bindings);
    }

    @Operation(summary = "根据ID获取绑定配置详情")
    @GetMapping("/{id}")
    public Result<PageBinding> getBinding(@PathVariable Long id) {
        PageBinding binding = pageBindingService.getById(id);
        if (binding == null) {
            return Result.error("绑定配置不存在");
        }
        return Result.success(binding);
    }

    @Operation(summary = "创建页面绑定")
    @PostMapping
    @OperationLogRecord(module = "页面绑定管理", operationType = "CREATE", operationDesc = "创建页面绑定")
    public Result<PageBinding> createBinding(@RequestBody PageBinding binding) {
        boolean success = pageBindingService.save(binding);
        if (success) {
            return Result.success(binding);
        } else {
            return Result.error("创建失败");
        }
    }

    @Operation(summary = "更新页面绑定")
    @PutMapping("/{id}")
    @OperationLogRecord(module = "页面绑定管理", operationType = "UPDATE", operationDesc = "更新页面绑定")
    public Result<PageBinding> updateBinding(@PathVariable Long id, @RequestBody PageBinding binding) {
        PageBinding existing = pageBindingService.getById(id);
        if (existing == null) {
            return Result.error("绑定配置不存在");
        }
        
        binding.setId(id);
        boolean success = pageBindingService.updateById(binding);
        if (success) {
            return Result.success(binding);
        } else {
            return Result.error("更新失败");
        }
    }

    @Operation(summary = "删除页面绑定")
    @DeleteMapping("/{id}")
    @OperationLogRecord(module = "页面绑定管理", operationType = "DELETE", operationDesc = "删除页面绑定")
    public Result<Void> deleteBinding(@PathVariable Long id) {
        try {
            boolean success = pageBindingService.deleteBindingWithSubSteps(id);
            if (success) {
                return Result.success();
            } else {
                return Result.error("删除失败");
            }
        } catch (Exception e) {
            log.error("删除页面绑定失败", e);
            return Result.error("删除失败: " + e.getMessage());
        }
    }

    @Operation(summary = "切换页面绑定状态")
    @PutMapping("/{id}/status")
    @OperationLogRecord(module = "页面绑定管理", operationType = "UPDATE", operationDesc = "切换页面绑定状态")
    public Result<Void> toggleBindingStatus(@PathVariable Long id, @RequestBody Map<String, Object> request) {
        try {
            Integer status = (Integer) request.get("status");
            if (status == null || (status != 0 && status != 1)) {
                return Result.error("状态参数无效");
            }

            PageBinding binding = pageBindingService.getById(id);
            if (binding == null) {
                return Result.error("绑定配置不存在");
            }

            binding.setStatus(status);
            boolean success = pageBindingService.updateById(binding);

            if (success) {
                String action = status == 1 ? "启用" : "禁用";
                log.info("页面绑定状态切换成功: ID={}, 状态={}", id, action);
                return Result.success();
            } else {
                return Result.error("状态切换失败");
            }
        } catch (Exception e) {
            log.error("切换页面绑定状态失败", e);
            return Result.error("状态切换失败: " + e.getMessage());
        }
    }

    @Operation(summary = "根据模板ID获取绑定配置")
    @GetMapping("/template/{templateId}")
    public Result<List<PageBinding>> getBindingsByTemplate(@PathVariable Long templateId) {
        List<PageBinding> bindings = pageBindingService.findByTemplateId(templateId);
        return Result.success(bindings);
    }

    @Operation(summary = "获取自动填充数据（根据Agent和URL）")
    @GetMapping("/auto-fill-data")
    public Result<Map<String, Object>> getAutoFillData(
            @RequestParam String url,
            @RequestParam Long agentId) {
        try {
            Map<String, Object> result = pageBindingService.getAutoFillData(url, agentId);

            if ((Boolean) result.getOrDefault("success", false)) {
                return Result.success(result);
            } else {
                return Result.error((String) result.getOrDefault("message", "获取自动填充数据失败"));
            }
        } catch (Exception e) {
            log.error("获取自动填充数据失败", e);
            return Result.error("获取自动填充数据失败: " + e.getMessage());
        }
    }

    @Operation(summary = "插件获取自动填充数据")
    @PostMapping("/auto-fill")
    @OperationLogRecord(module = "页面绑定管理", operationType = "CALL", operationDesc = "获取自动填充数据")
    public Result<Map<String, Object>> getAutoFillData(@RequestBody Map<String, Object> request) {
        String currentUrl = (String) request.get("url");
        Long agentId = Long.valueOf(request.get("agentId").toString());

        if (currentUrl == null || currentUrl.trim().isEmpty()) {
            return Result.error("URL不能为空");
        }

        try {
            Map<String, Object> result = pageBindingService.getAutoFillData(currentUrl, agentId);
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取自动填充数据失败: url={}, agentId={}", currentUrl, agentId, e);
            return Result.error("获取自动填充数据失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取多步骤自动填充数据")
    @PostMapping("/multi-step/auto-fill")
    @OperationLogRecord(module = "页面绑定管理", operationType = "CALL", operationDesc = "获取多步骤自动填充数据")
    public Result<Map<String, Object>> getMultiStepAutoFillData(@RequestBody Map<String, Object> request) {
        String currentUrl = (String) request.get("url");
        Long agentId = Long.valueOf(request.get("agentId").toString());

        if (currentUrl == null || currentUrl.trim().isEmpty()) {
            return Result.error("URL不能为空");
        }

        try {
            Map<String, Object> result = pageBindingService.getMultiStepAutoFillData(currentUrl, agentId);
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取多步骤自动填充数据失败: url={}, agentId={}", currentUrl, agentId, e);
            return Result.error("获取多步骤自动填充数据失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取子步骤列表")
    @GetMapping("/multi-step/{parentBindingId}/sub-steps")
    public Result<List<PageBinding>> getSubSteps(@PathVariable Long parentBindingId) {
        try {
            List<PageBinding> subSteps = pageBindingService.findSubStepsByParentId(parentBindingId);
            return Result.success(subSteps);
        } catch (Exception e) {
            log.error("获取子步骤失败: parentBindingId={}", parentBindingId, e);
            return Result.error("获取子步骤失败: " + e.getMessage());
        }
    }

    @Operation(summary = "从URL加载页面内容")
    @PostMapping("/load-page")
    @OperationLogRecord(module = "页面绑定管理", operationType = "LOAD", operationDesc = "从URL加载页面内容")
    public Result<Map<String, Object>> loadPageFromUrl(@RequestBody Map<String, String> request) {
        String url = request.get("url");
        if (url == null || url.trim().isEmpty()) {
            return Result.error("URL不能为空");
        }

        try {
            log.info("开始加载页面: {}", url);

            // 验证URL格式
            java.net.URI uri;
            try {
                uri = java.net.URI.create(url);
                if (uri.getScheme() == null || (!uri.getScheme().equals("http") && !uri.getScheme().equals("https"))) {
                    throw new IllegalArgumentException("URL必须以http://或https://开头");
                }
            } catch (IllegalArgumentException e) {
                log.error("URL格式错误: {}", url, e);
                Map<String, Object> result = new HashMap<>();
                result.put("url", url);
                result.put("status", "invalid_url");
                result.put("message", "URL格式不正确，请检查URL格式");
                result.put("error", e.getMessage());
                return Result.success(result);
            }

            // 使用Java原生HTTP客户端获取页面内容，确保独立会话
            java.net.http.HttpClient client = java.net.http.HttpClient.newBuilder()
                    .followRedirects(java.net.http.HttpClient.Redirect.NORMAL)  // 使用NORMAL避免过度重定向
                    .connectTimeout(java.time.Duration.ofSeconds(15))
                    // 不设置cookieHandler，避免影响用户会话
                    .build();

            java.net.http.HttpRequest httpRequest = java.net.http.HttpRequest.newBuilder()
                    .uri(uri)
                    .header("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
                    .header("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8")
                    .header("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8")
                    // 暂时禁用压缩以排查问题
                    // .header("Accept-Encoding", "gzip, deflate")
                    .header("Cache-Control", "no-cache")
                    .header("Pragma", "no-cache")
                    .timeout(java.time.Duration.ofSeconds(30))
                    .GET()
                    .build();

            // 先尝试获取字节数据进行编码检测
            java.net.http.HttpResponse<byte[]> response = client.send(httpRequest,
                    java.net.http.HttpResponse.BodyHandlers.ofByteArray());

            log.info("页面响应状态码: {}, 最终URL: {}", response.statusCode(), response.uri());

            // 检查响应的Content-Type以确定字符编码
            String contentType = response.headers().firstValue("Content-Type").orElse("");
            log.info("响应Content-Type: {}", contentType);

            // 获取响应数据
            byte[] rawData = response.body();
            log.info("响应数据长度: {} bytes", rawData.length);

            // 打印原始数据的前几个字节来诊断问题
            if (rawData.length > 0) {
                StringBuilder hexStr = new StringBuilder();
                StringBuilder charStr = new StringBuilder();
                for (int i = 0; i < Math.min(50, rawData.length); i++) {
                    hexStr.append(String.format("%02X ", rawData[i] & 0xFF));
                    char c = (char) (rawData[i] & 0xFF);
                    charStr.append(Character.isISOControl(c) ? '.' : c);
                }
                log.info("原始数据前50字节(HEX): {}", hexStr.toString());
                log.info("原始数据前50字节(CHAR): {}", charStr.toString());
            }

            // 智能检测并转换字符编码
            String html = detectAndDecodeHtml(rawData, contentType);

            // 处理成功的响应（包括重定向后的200状态码）
            if (response.statusCode() >= 200 && response.statusCode() < 300) {
                String finalUrl = response.uri().toString();

                // 检查是否有内容
                if (html == null || html.trim().isEmpty()) {
                    Map<String, Object> result = new HashMap<>();
                    result.put("url", url);
                    result.put("finalUrl", finalUrl);
                    result.put("status", "empty");
                    result.put("message", "页面内容为空，可能需要登录或页面不存在");
                    return Result.success(result);
                }

                // 检查X-Frame-Options和CSP头
                String xFrameOptions = response.headers().firstValue("X-Frame-Options").orElse("");
                String csp = response.headers().firstValue("Content-Security-Policy").orElse("");

                boolean hasFrameRestriction = false;
                String frameRestrictionReason = "";

                if (!xFrameOptions.isEmpty()) {
                    if (xFrameOptions.equalsIgnoreCase("DENY") ||
                        xFrameOptions.equalsIgnoreCase("SAMEORIGIN")) {
                        hasFrameRestriction = true;
                        frameRestrictionReason = "X-Frame-Options: " + xFrameOptions;
                        log.info("检测到X-Frame-Options限制: {}", xFrameOptions);
                    }
                }

                if (!csp.isEmpty() && csp.toLowerCase().contains("frame-ancestors")) {
                    hasFrameRestriction = true;
                    frameRestrictionReason += (frameRestrictionReason.isEmpty() ? "" : "; ") + "CSP frame-ancestors限制";
                    log.info("检测到CSP frame-ancestors限制");
                }

                // 修复相对路径的资源链接
                html = fixRelativeUrls(html, response.uri().toString());

                // 简单的HTML清理，移除可能有问题的内容
                html = html.replaceAll("(?i)<script[^>]*>.*?</script>", "")
                          .replaceAll("(?i)on\\w+\\s*=\\s*[\"'][^\"']*[\"']", "")
                          .replaceAll("(?i)javascript:", "");
                // 保留style标签，但清理危险内容
                html = html.replaceAll("(?i)<style[^>]*>[^<]*@import[^<]*</style>", "");

                Map<String, Object> result = new HashMap<>();
                result.put("url", url);
                result.put("finalUrl", finalUrl);
                result.put("html", html);
                result.put("status", "success");
                result.put("message", "页面加载成功" + (finalUrl.equals(url) ? "" : "（已重定向到: " + finalUrl + "）"));
                result.put("redirected", !finalUrl.equals(url));
                result.put("hasFrameRestriction", hasFrameRestriction);
                result.put("frameRestrictionReason", frameRestrictionReason);

                if (hasFrameRestriction) {
                    result.put("warning", "该页面设置了iframe限制（" + frameRestrictionReason + "），可能无法在iframe中正常显示");
                    log.warn("页面 {} 设置了iframe限制: {}", finalUrl, frameRestrictionReason);
                }

                return Result.success(result);
            }
            // 处理重定向状态码（如果HttpClient没有自动处理）
            else if (response.statusCode() >= 300 && response.statusCode() < 400) {
                String location = response.headers().firstValue("Location").orElse("");

                Map<String, Object> result = new HashMap<>();
                result.put("url", url);
                result.put("status", "redirect");
                result.put("message", "页面重定向到: " + location);
                result.put("location", location);
                result.put("statusCode", response.statusCode());
                result.put("needManualLoad", true);  // 提示需要手动加载

                log.warn("页面重定向未被自动处理: {} -> {}, 建议手动加载", url, location);
                return Result.success(result);
            }
            // 处理客户端错误（4xx）
            else if (response.statusCode() >= 400 && response.statusCode() < 500) {
                Map<String, Object> result = new HashMap<>();
                result.put("url", url);
                result.put("status", "client_error");
                result.put("message", "客户端错误 HTTP " + response.statusCode() + " - 可能需要登录或权限验证");
                result.put("statusCode", response.statusCode());

                return Result.success(result);
            }
            // 处理服务器错误（5xx）
            else {
                Map<String, Object> result = new HashMap<>();
                result.put("url", url);
                result.put("status", "server_error");
                result.put("message", "服务器错误 HTTP " + response.statusCode() + " - 服务器暂时不可用");
                result.put("statusCode", response.statusCode());

                return Result.success(result);
            }

        } catch (java.net.ConnectException e) {
            log.error("连接失败: {}", url, e);
            Map<String, Object> result = new HashMap<>();
            result.put("url", url);
            result.put("status", "connection_error");
            result.put("message", "无法连接到服务器，请检查URL是否正确或网络连接");
            result.put("error", e.getMessage());
            return Result.success(result);

        } catch (java.net.http.HttpTimeoutException e) {
            log.error("请求超时: {}", url, e);
            Map<String, Object> result = new HashMap<>();
            result.put("url", url);
            result.put("status", "timeout");
            result.put("message", "请求超时，服务器响应时间过长");
            result.put("error", e.getMessage());
            return Result.success(result);

        } catch (java.net.UnknownHostException e) {
            log.error("域名解析失败: {}", url, e);
            Map<String, Object> result = new HashMap<>();
            result.put("url", url);
            result.put("status", "dns_error");
            result.put("message", "域名解析失败，请检查URL是否正确");
            result.put("error", e.getMessage());
            return Result.success(result);

        } catch (javax.net.ssl.SSLException e) {
            log.error("SSL证书错误: {}", url, e);
            Map<String, Object> result = new HashMap<>();
            result.put("url", url);
            result.put("status", "ssl_error");
            result.put("message", "SSL证书验证失败，网站可能存在安全问题");
            result.put("error", e.getMessage());
            return Result.success(result);

        } catch (Exception e) {
            log.error("加载页面失败: {}", url, e);

            // 如果直接加载失败，提供备选方案
            Map<String, Object> result = new HashMap<>();
            result.put("url", url);
            result.put("status", "fallback");
            result.put("message", "直接加载失败，建议在新窗口中打开页面进行登录");
            result.put("error", e.getMessage());
            result.put("errorType", e.getClass().getSimpleName());

            return Result.success(result);
        }
    }

    /**
     * 手动加载HTML内容
     */
    @Operation(summary = "手动加载HTML内容")
    @PostMapping("/load-manual-html")
    @OperationLogRecord(module = "页面绑定管理", operationType = "LOAD", operationDesc = "手动加载HTML内容")
    public Result<Map<String, Object>> loadManualHtml(@RequestBody Map<String, String> request) {
        String html = request.get("html");
        String url = request.get("url");

        if (html == null || html.trim().isEmpty()) {
            return Result.error("HTML内容不能为空");
        }

        try {
            log.info("手动加载HTML内容，URL: {}, HTML长度: {}", url, html.length());

            // HTML清理（保持绝对路径不变）
            String cleanedHtml = cleanHtmlContent(html);

            Map<String, Object> result = new HashMap<>();
            result.put("url", url != null ? url : "手动输入");
            result.put("html", cleanedHtml);
            result.put("status", "success");
            result.put("message", "HTML内容加载成功");
            result.put("manual", true);

            return Result.success(result);

        } catch (Exception e) {
            log.error("处理手动HTML失败", e);
            return Result.error("处理HTML内容失败: " + e.getMessage());
        }
    }

    /**
     * 修复HTML中的相对URL路径
     */
    private String fixRelativeUrls(String html, String baseUrl) {
        try {
            java.net.URI baseUri = java.net.URI.create(baseUrl);
            String baseScheme = baseUri.getScheme();
            String baseHost = baseUri.getHost();
            int basePort = baseUri.getPort();
            String baseAuthority = baseHost + (basePort != -1 && basePort != 80 && basePort != 443 ? ":" + basePort : "");
            String basePrefix = baseScheme + "://" + baseAuthority;

            // 修复CSS链接
            html = html.replaceAll("(?i)href\\s*=\\s*[\"']\\s*/([^\"']*)[\"']",
                                  "href=\"" + basePrefix + "/$1\"");
            html = html.replaceAll("(?i)href\\s*=\\s*[\"']\\s*(?!https?://)([^/\"'][^\"']*)[\"']",
                                  "href=\"" + basePrefix + "/" + baseUri.getPath().replaceAll("/[^/]*$", "/") + "$1\"");

            // 修复JS链接
            html = html.replaceAll("(?i)src\\s*=\\s*[\"']\\s*/([^\"']*)[\"']",
                                  "src=\"" + basePrefix + "/$1\"");
            html = html.replaceAll("(?i)src\\s*=\\s*[\"']\\s*(?!https?://)([^/\"'][^\"']*)[\"']",
                                  "src=\"" + basePrefix + "/" + baseUri.getPath().replaceAll("/[^/]*$", "/") + "$1\"");

            // 修复图片链接
            html = html.replaceAll("(?i)src\\s*=\\s*[\"']\\s*/([^\"']*)[\"']",
                                  "src=\"" + basePrefix + "/$1\"");

            log.debug("修复相对URL完成，基础URL: {}", basePrefix);
            return html;

        } catch (Exception e) {
            log.warn("修复相对URL失败: {}", e.getMessage());
            return html;
        }
    }

    /**
     * 根据Content-Encoding解压数据
     */
    private byte[] decompressIfNeeded(byte[] data, String contentEncoding) {
        if (contentEncoding == null || contentEncoding.isEmpty()) {
            return data;
        }

        try {
            switch (contentEncoding.toLowerCase()) {
                case "gzip":
                    return decompressGzip(data);
                case "deflate":
                    return decompressDeflate(data);
                default:
                    log.warn("不支持的压缩格式: {}", contentEncoding);
                    return data;
            }
        } catch (Exception e) {
            log.error("解压数据失败: {}", contentEncoding, e);
            return data; // 解压失败时返回原数据
        }
    }

    /**
     * 解压Gzip数据
     */
    private byte[] decompressGzip(byte[] data) throws java.io.IOException {
        try (java.io.ByteArrayInputStream bis = new java.io.ByteArrayInputStream(data);
             java.util.zip.GZIPInputStream gis = new java.util.zip.GZIPInputStream(bis);
             java.io.ByteArrayOutputStream bos = new java.io.ByteArrayOutputStream()) {

            byte[] buffer = new byte[8192];
            int len;
            while ((len = gis.read(buffer)) != -1) {
                bos.write(buffer, 0, len);
            }

            byte[] result = bos.toByteArray();
            log.info("Gzip解压成功: {} bytes -> {} bytes", data.length, result.length);
            return result;
        }
    }

    /**
     * 解压Deflate数据
     */
    private byte[] decompressDeflate(byte[] data) throws java.io.IOException {
        try (java.io.ByteArrayInputStream bis = new java.io.ByteArrayInputStream(data);
             java.util.zip.InflaterInputStream iis = new java.util.zip.InflaterInputStream(bis);
             java.io.ByteArrayOutputStream bos = new java.io.ByteArrayOutputStream()) {

            byte[] buffer = new byte[8192];
            int len;
            while ((len = iis.read(buffer)) != -1) {
                bos.write(buffer, 0, len);
            }

            byte[] result = bos.toByteArray();
            log.info("Deflate解压成功: {} bytes -> {} bytes", data.length, result.length);
            return result;
        }
    }

    /**
     * 智能检测HTML内容的字符编码并转换为正确的字符串
     */
    private String detectAndDecodeHtml(byte[] htmlBytes, String contentType) {
        try {
            // 1. 首先尝试从Content-Type头中提取编码
            String charset = extractCharsetFromContentType(contentType);
            if (charset != null) {
                try {
                    String html = new String(htmlBytes, charset);
                    log.info("使用Content-Type中的编码 {} 解码成功", charset);
                    return html;
                } catch (Exception e) {
                    log.warn("使用Content-Type编码 {} 解码失败: {}", charset, e.getMessage());
                }
            }

            // 2. 尝试从HTML内容中检测编码（查找meta标签）
            String tempHtml = new String(htmlBytes, java.nio.charset.StandardCharsets.UTF_8);
            charset = extractCharsetFromHtml(tempHtml);
            if (charset != null && !charset.equalsIgnoreCase("utf-8")) {
                try {
                    String html = new String(htmlBytes, charset);
                    log.info("使用HTML meta标签中的编码 {} 解码成功", charset);
                    return html;
                } catch (Exception e) {
                    log.warn("使用HTML meta编码 {} 解码失败: {}", charset, e.getMessage());
                }
            }

            // 3. 尝试常见的中文编码（按优先级排序）
            String[] commonCharsets = {"GBK", "UTF-8", "GB2312", "Big5", "ISO-8859-1"};
            String bestHtml = null;
            String bestCharset = null;
            double bestScore = -1;

            for (String testCharset : commonCharsets) {
                try {
                    String html = new String(htmlBytes, testCharset);

                    // 计算文本质量分数
                    double score = calculateTextQualityScore(html);
                    log.debug("编码 {} 的质量分数: {:.2f}", testCharset, score);

                    if (score > bestScore) {
                        bestScore = score;
                        bestHtml = html;
                        bestCharset = testCharset;
                    }

                    // 如果分数很高，直接使用
                    if (score > 0.8) {
                        log.info("使用编码 {} 解码成功（自动检测，分数: {:.2f}）", testCharset, score);
                        return html;
                    }
                } catch (Exception e) {
                    log.debug("编码 {} 解码失败: {}", testCharset, e.getMessage());
                }
            }

            // 使用最佳编码结果
            if (bestHtml != null && bestScore > 0.3) {
                log.info("使用最佳编码 {} 解码（分数: {:.2f}）", bestCharset, bestScore);
                return bestHtml;
            }

            // 4. 如果都失败了，使用UTF-8作为默认编码
            log.warn("无法确定正确编码，使用UTF-8作为默认编码");
            return new String(htmlBytes, java.nio.charset.StandardCharsets.UTF_8);

        } catch (Exception e) {
            log.error("编码检测失败", e);
            return new String(htmlBytes, java.nio.charset.StandardCharsets.UTF_8);
        }
    }

    /**
     * 从Content-Type头中提取字符编码
     */
    private String extractCharsetFromContentType(String contentType) {
        if (contentType == null || contentType.isEmpty()) {
            return null;
        }

        // 查找charset参数
        String[] parts = contentType.toLowerCase().split(";");
        for (String part : parts) {
            part = part.trim();
            if (part.startsWith("charset=")) {
                String charset = part.substring(8).trim();
                // 移除可能的引号
                charset = charset.replaceAll("[\"']", "");
                return charset;
            }
        }
        return null;
    }

    /**
     * 从HTML内容中提取字符编码（查找meta标签）
     */
    private String extractCharsetFromHtml(String html) {
        if (html == null || html.isEmpty()) {
            return null;
        }

        // 查找 <meta charset="..."> 标签
        java.util.regex.Pattern charsetPattern = java.util.regex.Pattern.compile(
            "<meta[^>]+charset\\s*=\\s*[\"']?([^\"'>\\s]+)[\"']?[^>]*>",
            java.util.regex.Pattern.CASE_INSENSITIVE
        );
        java.util.regex.Matcher matcher = charsetPattern.matcher(html);
        if (matcher.find()) {
            return matcher.group(1);
        }

        // 查找 <meta http-equiv="Content-Type" content="..."> 标签
        java.util.regex.Pattern contentTypePattern = java.util.regex.Pattern.compile(
            "<meta[^>]+http-equiv\\s*=\\s*[\"']?content-type[\"']?[^>]+content\\s*=\\s*[\"']([^\"']+)[\"'][^>]*>",
            java.util.regex.Pattern.CASE_INSENSITIVE
        );
        matcher = contentTypePattern.matcher(html);
        if (matcher.find()) {
            return extractCharsetFromContentType(matcher.group(1));
        }

        return null;
    }

    /**
     * 简单检测文本是否为有效的中文文本
     */
    private boolean isValidChineseText(String text) {
        if (text == null || text.length() < 50) {
            return true; // 短文本直接通过
        }

        // 统计各种字符的比例
        int chineseCount = 0;
        int englishCount = 0;
        int htmlTagCount = 0;
        int invalidCount = 0;
        int totalCount = Math.min(text.length(), 2000); // 检查前2000个字符

        for (int i = 0; i < totalCount; i++) {
            char c = text.charAt(i);
            if (c >= 0x4E00 && c <= 0x9FFF) {
                // 中文字符
                chineseCount++;
            } else if ((c >= 'a' && c <= 'z') || (c >= 'A' && c <= 'Z')) {
                // 英文字符
                englishCount++;
            } else if (c == '<' || c == '>' || c == '&' || c == '"' || c == '\'' || c == '=' || c == '/') {
                // HTML标签字符
                htmlTagCount++;
            } else if (c == '?' || c == '�' || c == '□' || c == '▯') {
                // 明显的乱码字符
                invalidCount++;
            }
        }

        // 计算乱码比例
        double invalidRatio = (double) invalidCount / totalCount;
        double validRatio = (double) (chineseCount + englishCount + htmlTagCount) / totalCount;

        log.debug("文本质量检测: 总字符={}, 中文={}, 英文={}, HTML={}, 乱码={}, 乱码比例={:.2f}%, 有效比例={:.2f}%",
                totalCount, chineseCount, englishCount, htmlTagCount, invalidCount,
                invalidRatio * 100, validRatio * 100);

        // 如果乱码字符少于5%且有效字符多于70%，认为编码正确
        return invalidRatio < 0.05 && validRatio > 0.7;
    }

    /**
     * 计算文本质量分数（0-1之间，越高越好）
     */
    private double calculateTextQualityScore(String text) {
        if (text == null || text.length() < 50) {
            return 0.5; // 短文本给中等分数
        }

        int chineseCount = 0;
        int englishCount = 0;
        int htmlTagCount = 0;
        int invalidCount = 0;
        int totalCount = Math.min(text.length(), 2000);

        for (int i = 0; i < totalCount; i++) {
            char c = text.charAt(i);
            if (c >= 0x4E00 && c <= 0x9FFF) {
                chineseCount++;
            } else if ((c >= 'a' && c <= 'z') || (c >= 'A' && c <= 'Z') || (c >= '0' && c <= '9')) {
                englishCount++;
            } else if (c == '<' || c == '>' || c == '&' || c == '"' || c == '\'' || c == '=' || c == '/' || c == '-' || c == '_') {
                htmlTagCount++;
            } else if (c == '?' || c == '�' || c == '□' || c == '▯' || (c >= 0xFFFD && c <= 0xFFFF)) {
                invalidCount++;
            }
        }

        // 计算各种比例
        double chineseRatio = (double) chineseCount / totalCount;
        double englishRatio = (double) englishCount / totalCount;
        double htmlRatio = (double) htmlTagCount / totalCount;
        double invalidRatio = (double) invalidCount / totalCount;

        // 计算质量分数
        double score = 0.0;

        // 中文内容加分
        if (chineseRatio > 0.1) {
            score += chineseRatio * 0.4;
        }

        // 英文内容加分
        if (englishRatio > 0.1) {
            score += englishRatio * 0.3;
        }

        // HTML标签适量加分
        if (htmlRatio > 0.05 && htmlRatio < 0.3) {
            score += 0.2;
        }

        // 乱码严重扣分
        score -= invalidRatio * 2.0;

        // 确保分数在0-1之间
        return Math.max(0.0, Math.min(1.0, score));
    }

    /**
     * 清理HTML内容，移除安全风险元素，但保持有效的绝对路径
     */
    private String cleanHtmlContent(String html) {
        if (html == null || html.trim().isEmpty()) {
            return html;
        }

        return html
                // 移除脚本和样式标签
                .replaceAll("(?i)<script[^>]*>.*?</script>", "")
                .replaceAll("(?i)<style[^>]*>.*?</style>", "")
                // 移除事件处理器
                .replaceAll("(?i)on\\w+\\s*=\\s*[\"'][^\"']*[\"']", "")
                .replaceAll("(?i)javascript:", "")
                // 不进行任何URL转换，保持原始路径
                // 因为Chrome插件已经将相对路径转换为绝对路径了
                ;
    }
}
