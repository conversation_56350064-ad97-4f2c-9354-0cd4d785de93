package com.sinoair.agent.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sinoair.agent.dto.response.AgentSquareDTO;
import com.sinoair.agent.dto.response.AgentSupplementVO;
import com.sinoair.agent.entity.Agent;
import com.sinoair.agent.entity.AgentCategory;
import com.sinoair.agent.entity.BusinessType;
import com.sinoair.agent.entity.User;
import com.sinoair.agent.dto.response.UserVO;
import com.sinoair.agent.mapper.BusinessTypeMapper;
import com.sinoair.agent.mapper.FormFillRecordMapper;
import com.sinoair.agent.service.AgentCategoryService;
import com.sinoair.agent.service.AgentService;
import com.sinoair.agent.service.AgentSupplementService;
import com.sinoair.agent.service.UserAgentSubscriptionService;
import com.sinoair.agent.service.UserService;
import com.sinoair.agent.security.JwtTokenProvider;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Vue Agent广场API控制器
 * 简化版本，主要提供基础功能框架
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/agent")
@CrossOrigin(originPatterns = "*", allowCredentials = "true")
public class VueAgentController {
    
    @Autowired
    private AgentService agentService;

    @Autowired
    private UserAgentSubscriptionService subscriptionService;
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private JwtTokenProvider jwtTokenProvider;

    @Autowired
    private AgentCategoryService agentCategoryService;

    @Autowired
    private BusinessTypeMapper businessTypeMapper;

    @Autowired
    private AgentSupplementService agentSupplementService;

    @Autowired
    private FormFillRecordMapper formFillRecordMapper;
    
    /**
     * 获取Agent列表（分页）
     */
    @GetMapping("/list")
    public Map<String, Object> getAgentList(
            HttpServletRequest request,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "12") int size,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String category) {
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 获取当前用户（可选，未登录也可以查看）
            User currentUser = getCurrentUser(request);

            // 查询Agent列表
            Page<Agent> queryPage = new Page<>(page, size);
            IPage<Agent> agentPage = agentService.getPublicAgentList(queryPage, keyword, category);

            // 转换为DTO并添加订阅状态
            List<AgentSquareDTO> agentDTOs = new ArrayList<>();
            for (Agent agent : agentPage.getRecords()) {
                AgentSquareDTO dto = convertToAgentSquareDTO(agent);

                // 如果用户已登录，检查订阅状态
                if (currentUser != null) {
                    boolean isSubscribed = subscriptionService.isUserSubscribed(currentUser.getId(), agent.getId());
                    dto.setIsSubscribed(isSubscribed);
                } else {
                    dto.setIsSubscribed(false);
                }
                // 获取订阅数量
                int subscriptionCount = subscriptionService.getAgentSubscriptionCount(agent.getId());
                dto.setSubscriptionCount(subscriptionCount);
                agentDTOs.add(dto);
            }

            log.info("Agent列表查询，页码：{}，大小：{}，关键词：{}，分类：{}，用户ID：{}，查询到{}条记录",
                    page, size, keyword, category, currentUser != null ? currentUser.getId() : "未登录", agentPage.getTotal());

            result.put("success", true);
            result.put("data", agentDTOs);
            result.put("total", agentPage.getTotal());
            result.put("current", agentPage.getCurrent());
            result.put("size", agentPage.getSize());
            result.put("pages", agentPage.getPages());
            
        } catch (Exception e) {
            log.error("获取Agent列表失败", e);
            result.put("success", false);
            result.put("message", "获取Agent列表失败，请稍后重试");
        }
        
        return result;
    }
    
    /**
     * 获取Agent分类列表
     */
    @GetMapping("/categories")
    public Map<String, Object> getCategories() {
        Map<String, Object> result = new HashMap<>();

        try {
            // 查询所有启用的Agent分类
            List<AgentCategory> categories = agentService.getAllCategories();

            log.info("查询到{}个Agent分类", categories.size());

            result.put("success", true);
            result.put("data", categories);

        } catch (Exception e) {
            log.error("获取Agent分类失败", e);
            result.put("success", false);
            result.put("message", "获取分类失败，请稍后重试");
        }

        return result;
    }
    
    /**
     * 获取Agent详情
     */
    @GetMapping("/{id}")
    public AgentSquareDTO getAgentDetail(HttpServletRequest request, @PathVariable Long id) {
        try {
            // 查询Agent基本信息
            Agent agent = agentService.getAgentById(id);
            if (agent == null) {
                throw new RuntimeException("Agent不存在");
            }

            // 获取当前用户（可选）
            User currentUser = getCurrentUser(request);

            // 转换为AgentSquareDTO
            AgentSquareDTO agentDTO = convertToAgentSquareDTO(agent);

            // 检查订阅状态
            boolean isSubscribed = false;
            if (currentUser != null) {
                isSubscribed = subscriptionService.isUserSubscribed(currentUser.getId(), id);
            }
            agentDTO.setIsSubscribed(isSubscribed);

            // 获取订阅数量
            int subscriptionCount = subscriptionService.getAgentSubscriptionCount(id);
            agentDTO.setSubscriptionCount(subscriptionCount);

            log.info("Agent详情查询成功，ID：{}，名称：{}，用户ID：{}，订阅状态：{}",
                    id, agent.getAgentName(), currentUser != null ? currentUser.getId() : "未登录", isSubscribed);
            log.debug("返回的AgentDTO - screenshotUrls: {}, agentIntroduction: {}",
                    agentDTO.getScreenshotUrls(), agentDTO.getAgentIntroduction());

            // 直接返回AgentSquareDTO，不包装在result对象中
            return agentDTO;

        } catch (Exception e) {
            log.error("获取Agent详情失败，ID：{}", id, e);
            throw new RuntimeException("获取Agent详情失败，请稍后重试", e);
        }
    }
    
    /**
     * 切换订阅状态
     */
    @PostMapping("/{agentId}/toggle-subscription")
    public Map<String, Object> toggleSubscription(HttpServletRequest request, @PathVariable Long agentId) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 检查用户是否已登录
            User currentUser = getCurrentUser(request);
            if (currentUser == null) {
                result.put("success", false);
                result.put("message", "请先登录");
                return result;
            }

            // 切换订阅状态
            boolean success = subscriptionService.toggleSubscription(currentUser.getId(), agentId);
            
            if (success) {
                // 获取最新的订阅状态和数量
                boolean isSubscribed = subscriptionService.isUserSubscribed(currentUser.getId(), agentId);
                int subscriptionCount = subscriptionService.getAgentSubscriptionCount(agentId);
                
                result.put("success", true);
                result.put("isSubscribed", isSubscribed);
                result.put("subscriptionCount", subscriptionCount);
                result.put("message", isSubscribed ? "订阅成功" : "取消订阅成功");
                
                log.info("用户{}{}Agent{}成功", currentUser.getId(), isSubscribed ? "订阅" : "取消订阅", agentId);
            } else {
                result.put("success", false);
                result.put("message", "操作失败，请稍后重试");
            }
            
        } catch (Exception e) {
            log.error("切换订阅状态失败，Agent ID：{}", agentId, e);
            result.put("success", false);
            result.put("message", "操作失败，请稍后重试");
        }

        return result;
    }
    
    /**
     * 获取当前用户（从JWT Token）
     */
    private User getCurrentUser(HttpServletRequest request) {
        try {
            String token = request.getHeader("Authorization");
            if (token != null && token.startsWith("Bearer ")) {
                token = token.substring(7);
                if (jwtTokenProvider.validateToken(token)) {
                    Long userId = jwtTokenProvider.getUserIdFromToken(token);
                    if (userId != null) {
                        UserVO userVO = userService.getUserById(userId);
                        if (userVO != null) {
                            return convertUserVOToUser(userVO);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.debug("获取当前用户失败", e);
        }
        return null;
    }

    /**
     * 将Agent转换为AgentSquareDTO
     */
    private AgentSquareDTO convertToAgentSquareDTO(Agent agent) {
        AgentSquareDTO dto = new AgentSquareDTO();
        dto.setId(agent.getId());
        dto.setAgentName(agent.getAgentName());
        dto.setAgentCode(agent.getAgentCode());
        dto.setDescription(agent.getDescription());
        dto.setCategoryId(agent.getCategoryId());
        dto.setBusinessTypeId(agent.getBusinessTypeId());
        dto.setAgentType(agent.getAgentType());
        dto.setVersion(agent.getVersion());
        dto.setStatus(agent.getStatus());
        dto.setPublishedTime(agent.getPublishedTime());
        dto.setCreatedTime(agent.getCreatedTime());
        dto.setIsSubscribed(false); // 默认未订阅，后续会更新

        // 获取分类名称
        if (agent.getCategoryId() != null) {
            try {
                AgentCategory category = agentCategoryService.getById(agent.getCategoryId());
                if (category != null) {
                    dto.setCategoryName(category.getCategoryName());
                    dto.setCategoryCode(category.getCategoryCode());
                }
            } catch (Exception e) {
                log.warn("获取Agent分类信息失败，Agent ID：{}，分类ID：{}", agent.getId(), agent.getCategoryId(), e);
            }
        }

        // 获取业务类型名称
        if (agent.getBusinessTypeId() != null) {
            try {
                BusinessType businessType = businessTypeMapper.selectById(agent.getBusinessTypeId());
                if (businessType != null) {
                    dto.setBusinessTypeName(businessType.getTypeName());
                    dto.setBusinessTypeCode(businessType.getTypeCode());
                }
            } catch (Exception e) {
                log.warn("获取业务类型信息失败，Agent ID：{}，业务类型ID：{}", agent.getId(), agent.getBusinessTypeId(), e);
            }
        }

        // 获取补充信息
        try {
            log.debug("开始获取Agent补充信息，Agent ID：{}", agent.getId());
            AgentSupplementVO supplementVO = agentSupplementService.getSupplementInfo(agent.getId());
            log.debug("获取到补充信息：{}", supplementVO != null ? "有数据" : "无数据");
            if (supplementVO != null) {
                dto.setAgentIntroduction(supplementVO.getAgentIntroduction());
                dto.setUsageScenarios(supplementVO.getUsageScenarios());
                dto.setPainPointsSolved(supplementVO.getPainPointsSolved());

                // 处理截图URL列表
                if (supplementVO.getScreenshotUrls() != null && !supplementVO.getScreenshotUrls().isEmpty()) {
                    // 将List<String>转换为JSON字符串
                    try {
                        dto.setScreenshotUrls(String.join(",", supplementVO.getScreenshotUrls()));
                    } catch (Exception e) {
                        log.warn("处理截图URL列表失败，Agent ID：{}", agent.getId(), e);
                        dto.setScreenshotUrls("");
                    }
                } else {
                    dto.setScreenshotUrls("");
                }

                // 处理版本信息
                if (supplementVO.getVersionInfo() != null) {
                    try {
                        // 将Map转换为JSON字符串，这里简化处理
                        dto.setVersionInfo(supplementVO.getVersionInfo().toString());
                    } catch (Exception e) {
                        log.warn("处理版本信息失败，Agent ID：{}", agent.getId(), e);
                        dto.setVersionInfo("");
                    }
                } else {
                    dto.setVersionInfo("");
                }

                // 处理统计信息
                if (supplementVO.getUsageStatistics() != null) {
                    dto.setUsageStatistics(supplementVO.getUsageStatistics().toString());
                } else {
                    dto.setUsageStatistics("");
                }

                if (supplementVO.getCallHistorySummary() != null) {
                    dto.setCallHistorySummary(supplementVO.getCallHistorySummary().toString());
                } else {
                    dto.setCallHistorySummary("");
                }
            } else {
                // 如果没有补充信息，设置默认值
                log.debug("没有补充信息，设置默认值，Agent ID：{}", agent.getId());
                dto.setAgentIntroduction("");
                dto.setUsageScenarios("");
                dto.setPainPointsSolved("");
                dto.setScreenshotUrls("");
                dto.setVersionInfo("");
                dto.setUsageStatistics("");
                dto.setCallHistorySummary("");
            }
        } catch (Exception e) {
            log.warn("获取Agent补充信息失败，Agent ID：{}", agent.getId(), e);
            // 设置默认值
            dto.setAgentIntroduction("");
            dto.setUsageScenarios("");
            dto.setPainPointsSolved("");
            dto.setScreenshotUrls("");
            dto.setVersionInfo("");
            dto.setUsageStatistics("");
            dto.setCallHistorySummary("");
        }

        // 获取好评差评数据
        try {
            log.debug("开始获取Agent好评差评数据，Agent ID：{}", agent.getId());
            Map<String, Object> feedbackData = formFillRecordMapper.countFeedbackByAgentId(agent.getId());
            if (feedbackData != null) {
                Object goodCountObj = feedbackData.get("goodCount");
                Object badCountObj = feedbackData.get("badCount");

                Integer goodCount = goodCountObj != null ? ((Number) goodCountObj).intValue() : 0;
                Integer badCount = badCountObj != null ? ((Number) badCountObj).intValue() : 0;

                dto.setGoodRating(goodCount);
                dto.setBadRating(badCount);

                log.debug("获取到好评差评数据，Agent ID：{}，好评：{}，差评：{}", agent.getId(), goodCount, badCount);
            } else {
                dto.setGoodRating(0);
                dto.setBadRating(0);
                log.debug("没有好评差评数据，Agent ID：{}", agent.getId());
            }
        } catch (Exception e) {
            log.warn("获取Agent好评差评数据失败，Agent ID：{}", agent.getId(), e);
            dto.setGoodRating(0);
            dto.setBadRating(0);
        }

        return dto;
    }

    /**
     * 将UserVO转换为User实体
     */
    private User convertUserVOToUser(UserVO userVO) {
        User user = new User();
        user.setId(userVO.getId());
        user.setUsername(userVO.getUsername());
        user.setEmail(userVO.getEmail());
        user.setRealName(userVO.getRealName());
        user.setLastLoginTime(userVO.getLastLoginTime());
        return user;
    }
}
