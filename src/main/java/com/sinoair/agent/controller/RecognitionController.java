package com.sinoair.agent.controller;

import com.sinoair.agent.annotation.OperationLogRecord;
import com.sinoair.agent.common.Result;
import com.sinoair.agent.dto.request.RecognitionRequest;
import com.sinoair.agent.dto.response.RecognitionResult;
import com.sinoair.agent.entity.RecognitionRecord;
import com.sinoair.agent.service.RecognitionService;

import java.util.Map;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 识别控制器
 *
 * <AUTHOR> Team
 */
@Tag(name = "识别处理", description = "文档识别、结果查询等识别相关功能")
@RestController
@RequestMapping("/api/v1/recognition")
@RequiredArgsConstructor
public class RecognitionController {

    private final RecognitionService recognitionService;

    @Operation(summary = "提交识别任务", description = "提交文档识别任务，返回任务ID")
    @PostMapping("/analyze")
    @OperationLogRecord(module = "文档识别", operationType = "ANALYZE", operationDesc = "提交文档识别任务-文本")
    public Result<RecognitionService.RecognitionTaskResponse> analyzeDocument(
            @Valid @RequestBody RecognitionRequest request) {
        return recognitionService.submitRecognitionTask(request);
    }

    @Operation(summary = "提交识别任务(文件上传)", description = "通过文件上传提交文档识别任务")
    @PostMapping(value = "/analyze", consumes = "multipart/form-data")
    @OperationLogRecord(module = "文档识别", operationType = "ANALYZE", operationDesc = "提交文档识别任务-文件")
    public Result<RecognitionService.RecognitionTaskResponse> analyzeDocumentWithFile(
            @Parameter(description = "上传的文件") @RequestParam("file") MultipartFile file,
            @Parameter(description = "大模型平台") @RequestParam("agentType") Integer agentType,
            @Parameter(description = "Agent编码") @RequestParam("agentCode") String agentCode,
            @Parameter(description = "模型") @RequestParam(required = false, name = "model") String model,
            @Parameter(description = "识别参数") @RequestParam(required = false) String parameters,
            @Parameter(description = "自定义提示词模板") @RequestParam(required = false) String promptTemplate) {
        return recognitionService.submitRecognitionTaskWithFile(file, agentCode,agentType, model, parameters, promptTemplate);
    }

   @Operation(summary = "提交识别任务(文件上传)", description = "通过文件上传提交文档识别任务")
    @PostMapping(value = "/analyzeByCode", consumes = "multipart/form-data")
    @OperationLogRecord(module = "文档识别", operationType = "ANALYZE", operationDesc = "提交文档识别任务-文件")
    public Result<RecognitionResult> analyzeDocumentWithFileByCode(
            @Parameter(description = "上传的文件") @RequestParam("file") MultipartFile file,
            @Parameter(description = "Agent编码") @RequestParam("agentCode") String agentCode) {
        return recognitionService.submitRecognitionTaskWithFileByCode(file, agentCode);
    }


    @Operation(summary = "查询识别进度", description = "查询识别任务的执行进度")
    @GetMapping("/records/{recordId}/progress")
    public Result<String> getRecognitionProgress(
            @Parameter(description = "识别记录ID") @PathVariable Long recordId) {
        // TODO: 实现进度查询逻辑
        return Result.success("进度查询功能待实现");
    }

    @Operation(summary = "查询识别结果", description = "根据记录ID查询识别结果")
    @GetMapping("/records/{recordId}")
    public Result<RecognitionRecord> getRecognitionResult(
            @Parameter(description = "识别记录ID") @PathVariable Long recordId) {
        return recognitionService.getRecognitionResult(recordId);
    }

    @Operation(summary = "查询识别记录列表", description = "分页查询识别记录列表")
    @GetMapping("/records")
    public Result<String> getRecognitionRecords(
            @Parameter(description = "Agent ID") @RequestParam(required = false) Long agentId,
            @Parameter(description = "状态") @RequestParam(required = false) Integer status,
            @Parameter(description = "开始时间") @RequestParam(required = false) String startTime,
            @Parameter(description = "结束时间") @RequestParam(required = false) String endTime,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") int size) {
        // TODO: 实现记录列表查询
        return Result.success("记录列表查询功能待实现");
    }

    @Operation(summary = "批量识别", description = "批量提交多个文件的识别任务")
    @PostMapping("/batch-analyze")
    @OperationLogRecord(module = "文档识别", operationType = "ANALYZE", operationDesc = "批量提交文档识别任务")
    public Result<String> batchAnalyze(@Valid @RequestBody String request) {
        // TODO: 实现批量识别功能
        return Result.success("批量识别功能待实现");
    }
}
