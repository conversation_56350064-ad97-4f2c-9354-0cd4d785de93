package com.sinoair.agent.controller;

import com.sinoair.agent.annotation.OperationLogRecord;
import com.sinoair.agent.common.Result;
import com.sinoair.agent.dto.request.AgentCallRequest;
import com.sinoair.agent.dto.response.AgentCallResponse;
import com.sinoair.agent.service.AgentCallService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * Agent调用控制器 - 供外部系统调用
 * 
 * <AUTHOR> Team
 */
@Tag(name = "Agent调用接口", description = "供外部系统调用的Agent接口，支持文件识别和数据处理")
@RestController
@RequestMapping("/api/v1/agent-call")
@RequiredArgsConstructor
public class AgentCallController {

    private final AgentCallService agentCallService;

    @Operation(summary = "通过Agent编码调用识别", description = "使用Agent编码和文件进行文档识别")
    @PostMapping("/recognize/{agentCode}")
    @OperationLogRecord(module = "Agent调用", operationType = "CALL", operationDesc = "调用Agent进行文档识别")
    public Result<AgentCallResponse> recognizeByAgentCode(
            @Parameter(description = "Agent编码") @PathVariable String agentCode,
            @Parameter(description = "上传的文件") @RequestParam("file") MultipartFile file,
            @Parameter(description = "业务参数") @RequestParam(required = false) String businessParams,
            @Parameter(description = "回调URL") @RequestParam(required = false) String callbackUrl) {
        
        AgentCallRequest request = AgentCallRequest.builder()
                .agentCode(agentCode)
                .file(file)
                .businessParams(businessParams)
                .callbackUrl(callbackUrl)
                .build();
                
        return agentCallService.recognizeDocument(request);
    }

    @Operation(summary = "批量文档识别", description = "批量上传文件进行识别")
    @PostMapping("/batch-recognize/{agentCode}")
    @OperationLogRecord(module = "Agent调用", operationType = "CALL", operationDesc = "批量调用Agent进行文档识别")
    public Result<AgentCallResponse> batchRecognize(
            @Parameter(description = "Agent编码") @PathVariable String agentCode,
            @Parameter(description = "上传的文件列表") @RequestParam("files") MultipartFile[] files,
            @Parameter(description = "业务参数") @RequestParam(required = false) String businessParams) {
        
        return agentCallService.batchRecognize(agentCode, files, businessParams);
    }

    @Operation(summary = "获取识别结果", description = "根据任务ID获取识别结果")
    @GetMapping("/result/{taskId}")
    public Result<AgentCallResponse> getRecognitionResult(
            @Parameter(description = "任务ID") @PathVariable String taskId) {
        return agentCallService.getRecognitionResult(taskId);
    }

    @Operation(summary = "获取可用Agent列表", description = "获取所有已发布的Agent列表")
    @GetMapping("/agents")
    public Result<?> getAvailableAgents() {
        return agentCallService.getAvailableAgents();
    }

    @Operation(summary = "Agent健康检查", description = "检查指定Agent是否可用")
    @GetMapping("/health/{agentCode}")
    public Result<?> checkAgentHealth(@Parameter(description = "Agent编码") @PathVariable String agentCode) {
        return agentCallService.checkAgentHealth(agentCode);
    }

    @Operation(summary = "文本识别", description = "直接传入文本内容进行识别")
    @PostMapping("/recognize-text/{agentCode}")
    @OperationLogRecord(module = "Agent调用", operationType = "CALL", operationDesc = "调用Agent进行文本识别")
    public Result<AgentCallResponse> recognizeText(
            @Parameter(description = "Agent编码") @PathVariable String agentCode,
            @Valid @RequestBody AgentCallRequest.TextRequest request) {
        return agentCallService.recognizeText(agentCode, request);
    }
}
