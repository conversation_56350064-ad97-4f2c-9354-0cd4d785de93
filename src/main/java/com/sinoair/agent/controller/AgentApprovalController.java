package com.sinoair.agent.controller;

import com.sinoair.agent.annotation.OperationLogRecord;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sinoair.agent.common.Result;
import com.sinoair.agent.dto.request.AgentApprovalDTO;
import com.sinoair.agent.dto.request.AgentApprovalQueryDTO;
import com.sinoair.agent.dto.response.AgentApprovalDetailVO;
import com.sinoair.agent.dto.response.AgentApprovalVO;
import com.sinoair.agent.entity.AgentApprovalRecord;
import com.sinoair.agent.security.UserPrincipal;
import com.sinoair.agent.service.AgentApprovalService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;


import java.util.List;
import java.util.Map;

/**
 * Agent审批控制器
 *
 * <AUTHOR> Team
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/agent-approval")
@RequiredArgsConstructor
@Tag(name = "Agent审批管理", description = "Agent审批相关接口")
public class AgentApprovalController {

    private final AgentApprovalService approvalService;

    @GetMapping("/list")
    @Operation(summary = "获取Agent审批列表", description = "分页查询Agent审批列表")
    @PreAuthorize("hasAuthority('AGENT_APPROVAL_VIEW')")
    public Result<IPage<AgentApprovalVO>> getApprovalList(AgentApprovalQueryDTO queryDTO,
                                                          @AuthenticationPrincipal UserPrincipal userPrincipal) {
        try {
            log.info("收到审批列表查询请求: {}", queryDTO);
            IPage<AgentApprovalVO> result = approvalService.getApprovalList(queryDTO, userPrincipal);
            log.info("查询结果: 总数={}, 当前页数据量={}", result.getTotal(), result.getRecords().size());
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取Agent审批列表失败", e);
            return Result.error("获取审批列表失败: " + e.getMessage());
        }
    }

    @GetMapping("/detail/{agentId}")
    @Operation(summary = "获取Agent审批详情", description = "获取指定Agent的审批详情信息")
    @PreAuthorize("hasAuthority('AGENT_APPROVAL_VIEW')")
    public Result<AgentApprovalDetailVO> getApprovalDetail(
            @Parameter(description = "Agent ID") @PathVariable Long agentId) {
        try {
            AgentApprovalDetailVO result = approvalService.getApprovalDetail(agentId);
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取Agent审批详情失败: agentId={}", agentId, e);
            return Result.error("获取审批详情失败: " + e.getMessage());
        }
    }

    @PostMapping("/approve")
    @Operation(summary = "提交审批结果", description = "管理员提交Agent审批结果")
    @PreAuthorize("hasAuthority('AGENT_APPROVAL_MANAGE')")
    @OperationLogRecord(module = "Agent审批", operationType = "APPROVE", operationDesc = "提交Agent审批结果")
    public Result<String> submitApproval(@Valid @RequestBody AgentApprovalDTO approvalDTO) {
        try {
            // 获取当前用户信息
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String username = authentication.getName();

            // TODO: 从用户服务获取用户ID和真实姓名
            Long approverId = 1L; // 临时使用固定值
            String approverName = "管理员"; // 临时使用固定值

            boolean success = approvalService.submitApproval(approvalDTO, approverId, approverName);

            if (success) {
                return Result.success("审批成功");
            } else {
                return Result.error("审批失败");
            }
        } catch (Exception e) {
            log.error("提交审批结果失败", e);
            return Result.error("审批操作失败: " + e.getMessage());
        }
    }

    @PostMapping("/submit/{agentId}")
    @Operation(summary = "申请发布Agent", description = "客户申请发布Agent")
    @PreAuthorize("hasAuthority('AGENT_MANAGE')")
    @OperationLogRecord(module = "Agent审批", operationType = "SUBMIT", operationDesc = "申请发布Agent")
    public Result<String> submitForApproval(
            @Parameter(description = "Agent ID") @PathVariable Long agentId,
            @RequestBody(required = false) Map<String, Object> request) {
        try {

            UserPrincipal currentUser = getCurrentUser();
            Long userId = currentUser.getId();

            // 获取版本更新说明
            String changeLog = request != null ? (String) request.get("changeLog") : null;

            // 获取版本信息
            Object versionIdObj = request != null ? request.get("versionId") : null;
            String versionNumber = request != null ? (String) request.get("versionNumber") : null;

            boolean success;
            if (versionIdObj != null && versionNumber != null) {
                // 带版本信息的申请发布
                Long versionId = null;
                if (versionIdObj instanceof Number) {
                    versionId = ((Number) versionIdObj).longValue();
                } else if (versionIdObj instanceof String) {
                    versionId = Long.parseLong((String) versionIdObj);
                }

                if (versionId != null) {
                    success = approvalService.submitForApprovalWithVersion(agentId, userId, changeLog, versionId, versionNumber);
                } else {
                    success = approvalService.submitForApproval(agentId, userId, changeLog);
                }
            } else {
                // 原有的申请发布方式
                success = approvalService.submitForApproval(agentId, userId, changeLog);
            }

            if (success) {
                return Result.success("提交审批成功");
            } else {
                return Result.error("提交审批失败");
            }
        } catch (Exception e) {
            log.error("申请发布Agent失败: agentId={}", agentId, e);
            return Result.error("提交审批失败: " + e.getMessage());
        }
    }

    @GetMapping("/history/{agentId}")
    @Operation(summary = "获取Agent审批历史", description = "获取指定Agent的审批历史记录")
    @PreAuthorize("hasAuthority('AGENT_APPROVAL_VIEW')")
    public Result<List<AgentApprovalRecord>> getApprovalHistory(
            @Parameter(description = "Agent ID") @PathVariable Long agentId) {
        try {
            List<AgentApprovalRecord> result = approvalService.getApprovalHistory(agentId);
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取Agent审批历史失败: agentId={}", agentId, e);
            return Result.error("获取审批历史失败: " + e.getMessage());
        }
    }

    @GetMapping("/stats")
    @Operation(summary = "获取审批状态统计", description = "获取各审批状态的统计数据")
    @PreAuthorize("hasAuthority('AGENT_APPROVAL_VIEW')")
    public Result<List<Map<String, Object>>> getApprovalStats() {
        try {
            List<Map<String, Object>> result = approvalService.getApprovalStatusStats();
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取审批状态统计失败", e);
            return Result.error("获取统计数据失败: " + e.getMessage());
        }
    }

    @GetMapping("/versions/{agentId}")
    @Operation(summary = "获取Agent版本历史", description = "获取指定Agent的版本历史（仅显示审批通过和审批中的版本）")
    @PreAuthorize("hasAuthority('AGENT_APPROVAL_VIEW')")
    public Result<List<Map<String, Object>>> getAgentVersionHistory(
            @Parameter(description = "Agent ID") @PathVariable Long agentId) {
        try {
            List<Map<String, Object>> result = approvalService.getAgentVersionHistory(agentId);
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取Agent版本历史失败: agentId={}", agentId, e);
            return Result.error("获取版本历史失败: " + e.getMessage());
        }
    }

    /**
     * 获取当前用户
     */
    private UserPrincipal getCurrentUser() {
        return (UserPrincipal) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
    }
}
