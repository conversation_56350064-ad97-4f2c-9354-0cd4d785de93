package com.sinoair.agent.controller;

import com.sinoair.agent.annotation.OperationLogRecord;
import com.sinoair.agent.common.Result;
import com.sinoair.agent.entity.SysMessage;
import com.sinoair.agent.service.SysMessageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;
import com.sinoair.agent.security.UserPrincipal;

import java.util.List;

/**
 * 站内消息控制器
 *
 * <AUTHOR> Team
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/messages")
@RequiredArgsConstructor
@Tag(name = "站内消息管理", description = "站内消息相关接口")
public class SysMessageController {

    private final SysMessageService messageService;

    @GetMapping("/unread-count")
    @Operation(summary = "获取未读消息数量", description = "获取当前用户的未读消息数量")
    public Result<Integer> getUnreadCount(@AuthenticationPrincipal UserPrincipal userPrincipal) {
        try {
            if (userPrincipal == null) {
                return Result.error("用户未登录");
            }

            Long userId = userPrincipal.getId();
            int count = messageService.getUnreadCount(userId);
            return Result.success(count);
        } catch (Exception e) {
            log.error("获取未读消息数量失败", e);
            return Result.error("获取未读消息数量失败: " + e.getMessage());
        }
    }

    @GetMapping("/list")
    @Operation(summary = "获取消息列表", description = "获取当前用户的消息列表")
    public Result<List<SysMessage>> getMessageList(
            @Parameter(description = "限制数量") @RequestParam(defaultValue = "20") Integer limit,
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        try {
            if (userPrincipal == null) {
                return Result.error("用户未登录");
            }

            Long userId = userPrincipal.getId();
            List<SysMessage> messages = messageService.getUserMessages(userId, limit);
            return Result.success(messages);
        } catch (Exception e) {
            log.error("获取消息列表失败", e);
            return Result.error("获取消息列表失败: " + e.getMessage());
        }
    }

    @PostMapping("/mark-read")
    @Operation(summary = "标记消息为已读", description = "批量标记消息为已读")
    @OperationLogRecord(module = "系统消息", operationType = "UPDATE", operationDesc = "标记消息为已读")
    public Result<String> markAsRead(@RequestBody List<Long> messageIds,
                                   @AuthenticationPrincipal UserPrincipal userPrincipal) {
        try {
            if (userPrincipal == null) {
                return Result.error("用户未登录");
            }

            Long userId = userPrincipal.getId();
            boolean success = messageService.markAsRead(messageIds, userId);

            if (success) {
                return Result.success("标记成功");
            } else {
                return Result.error("标记失败");
            }
        } catch (Exception e) {
            log.error("标记消息已读失败", e);
            return Result.error("标记消息已读失败: " + e.getMessage());
        }
    }

    @PostMapping("/mark-all-read")
    @Operation(summary = "标记所有消息为已读", description = "标记当前用户的所有消息为已读")
    @OperationLogRecord(module = "系统消息", operationType = "UPDATE", operationDesc = "标记所有消息为已读")
    public Result<String> markAllAsRead(@AuthenticationPrincipal UserPrincipal userPrincipal) {
        try {
            if (userPrincipal == null) {
                return Result.error("用户未登录");
            }

            Long userId = userPrincipal.getId();
            boolean success = messageService.markAllAsRead(userId);

            if (success) {
                return Result.success("标记成功");
            } else {
                return Result.error("标记失败");
            }
        } catch (Exception e) {
            log.error("标记所有消息已读失败", e);
            return Result.error("标记所有消息已读失败: " + e.getMessage());
        }
    }
}
