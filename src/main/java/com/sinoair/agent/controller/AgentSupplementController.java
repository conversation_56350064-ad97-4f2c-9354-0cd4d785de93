package com.sinoair.agent.controller;

import com.sinoair.agent.annotation.OperationLogRecord;
import com.sinoair.agent.common.Result;
import com.sinoair.agent.dto.request.AgentSupplementDTO;
import com.sinoair.agent.dto.response.AgentSupplementVO;
import com.sinoair.agent.entity.AgentSupplementInfo;
import com.sinoair.agent.service.AgentSupplementService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

/**
 * Agent补充资料控制器
 *
 * <AUTHOR> Team
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/agent-supplement")
@RequiredArgsConstructor
@Tag(name = "Agent补充资料管理", description = "Agent补充资料相关接口")
public class AgentSupplementController {

    private final AgentSupplementService supplementService;

    @GetMapping("/{agentId}")
    @Operation(summary = "获取Agent补充资料", description = "根据Agent ID获取补充资料信息")
    @PreAuthorize("hasAuthority('AGENT_VIEW') or hasAuthority('AGENT_MANAGE')")
    public Result<AgentSupplementVO> getSupplementInfo(
            @Parameter(description = "Agent ID", required = true)
            @PathVariable Long agentId) {
        try {
            log.info("获取Agent补充资料: agentId={}", agentId);
            AgentSupplementVO result = supplementService.getSupplementInfo(agentId);
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取Agent补充资料失败", e);
            return Result.error("获取补充资料失败: " + e.getMessage());
        }
    }

    @GetMapping("/{agentId}/statistics")
    @Operation(summary = "获取Agent统计信息", description = "获取Agent的版本信息、使用统计等")
    @PreAuthorize("hasAuthority('AGENT_VIEW') or hasAuthority('AGENT_MANAGE')")
    public Result<AgentSupplementVO> getAgentStatistics(
            @Parameter(description = "Agent ID", required = true)
            @PathVariable Long agentId) {
        try {
            log.info("获取Agent统计信息: agentId={}", agentId);
            AgentSupplementVO result = supplementService.getAgentStatistics(agentId);
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取Agent统计信息失败", e);
            return Result.error("获取统计信息失败: " + e.getMessage());
        }
    }

    @PostMapping("/save")
    @Operation(summary = "保存Agent补充资料", description = "保存或更新Agent补充资料")
    @PreAuthorize("hasAuthority('AGENT_MANAGE')")
    @OperationLogRecord(module = "Agent补充资料", operationType = "SAVE", operationDesc = "保存Agent补充资料")
    public Result<String> saveSupplement(@Valid @RequestBody AgentSupplementDTO supplementDTO) {
        try {
            // 获取当前用户信息
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String username = authentication.getName();
            
            // TODO: 从用户服务获取用户ID
            Long userId = 1L; // 临时使用固定值

            log.info("保存Agent补充资料: agentId={}, userId={}", supplementDTO.getAgentId(), userId);
            boolean success = supplementService.saveOrUpdateSupplement(supplementDTO, userId);
            
            if (success) {
                return Result.success("保存成功");
            } else {
                return Result.error("保存失败");
            }
        } catch (Exception e) {
            log.error("保存Agent补充资料失败", e);
            return Result.error("保存失败: " + e.getMessage());
        }
    }

    @PostMapping("/submit")
    @Operation(summary = "提交Agent补充资料", description = "提交Agent补充资料，状态变更为已提交")
    @PreAuthorize("hasAuthority('AGENT_MANAGE')")
    @OperationLogRecord(module = "Agent补充资料", operationType = "SUBMIT", operationDesc = "提交Agent补充资料")
    public Result<String> submitSupplementByBody(@Valid @RequestBody AgentSupplementDTO supplementDTO) {
        try {
            // 获取当前用户信息
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String username = authentication.getName();

            // TODO: 从用户服务获取用户ID
            Long userId = 1L; // 临时使用固定值

            log.info("提交Agent补充资料: agentId={}, userId={}", supplementDTO.getAgentId(), userId);

            // 先保存补充资料
            supplementDTO.setStatus(AgentSupplementInfo.STATUS_SUBMITTED);
            boolean saveSuccess = supplementService.saveOrUpdateSupplement(supplementDTO, userId);
            if (!saveSuccess) {
                return Result.error("保存补充资料失败");
            }

            // 再提交（如果有版本信息，则提交特定版本）
            boolean submitSuccess;
            if (supplementDTO.getVersionId() != null && supplementDTO.getVersionNumber() != null) {
                // 提交特定版本的审核
                submitSuccess = supplementService.submitSupplementWithVersion(
                    supplementDTO.getAgentId(),
                    userId,
                    supplementDTO.getVersionId(),
                    supplementDTO.getVersionNumber()
                );
                log.info("提交特定版本审核: agentId={}, versionId={}, versionNumber={}",
                    supplementDTO.getAgentId(), supplementDTO.getVersionId(), supplementDTO.getVersionNumber());
            } else {
                // 提交当前版本的审核
                submitSuccess = supplementService.submitSupplement(supplementDTO.getAgentId(), userId);
            }

            if (submitSuccess) {
                return Result.success("提交成功");
            } else {
                return Result.error("提交失败");
            }
        } catch (Exception e) {
            log.error("提交Agent补充资料失败", e);
            return Result.error("提交失败: " + e.getMessage());
        }
    }

    @PostMapping("/{agentId}/submit")
    @Operation(summary = "提交Agent补充资料", description = "提交Agent补充资料，状态变更为已提交")
    @PreAuthorize("hasAuthority('AGENT_MANAGE')")
    @OperationLogRecord(module = "Agent补充资料", operationType = "SUBMIT", operationDesc = "提交Agent补充资料")
    public Result<String> submitSupplement(
            @Parameter(description = "Agent ID", required = true)
            @PathVariable Long agentId) {
        try {
            // 获取当前用户信息
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String username = authentication.getName();

            // TODO: 从用户服务获取用户ID
            Long userId = 1L; // 临时使用固定值

            log.info("提交Agent补充资料: agentId={}, userId={}", agentId, userId);
            boolean success = supplementService.submitSupplement(agentId, userId);

            if (success) {
                return Result.success("提交成功");
            } else {
                return Result.error("提交失败");
            }
        } catch (Exception e) {
            log.error("提交Agent补充资料失败", e);
            return Result.error("提交失败: " + e.getMessage());
        }
    }
}
