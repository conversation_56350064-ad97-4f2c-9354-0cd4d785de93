package com.sinoair.agent.controller;

import com.sinoair.agent.annotation.OperationLogRecord;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sinoair.agent.common.Result;
import com.sinoair.agent.dto.request.RolePermissionAssignDTO;
import com.sinoair.agent.entity.Role;
import com.sinoair.agent.service.RoleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;


import java.util.List;
import java.util.Map;

/**
 * 角色管理控制器
 *
 * <AUTHOR> Team
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/roles")
@RequiredArgsConstructor
@Tag(name = "角色管理", description = "角色的增删改查和权限分配")
public class RoleController {

    private final RoleService roleService;

    @Operation(summary = "分页查询角色列表")
    @GetMapping
    public Result<IPage<Role>> getRoles(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String keyword) {

        IPage<Role> result = roleService.getPageList(page, size, keyword);
        return Result.success(result);
    }

    @Operation(summary = "获取所有可用角色")
    @GetMapping("/active")
    public Result<List<Role>> getActiveRoles() {
        List<Role> roles = roleService.getAllActiveRoles();
        return Result.success(roles);
    }

    @Operation(summary = "根据ID获取角色详情")
    @GetMapping("/{id}")
    public Result<Role> getRole(@PathVariable Long id) {
        Role role = roleService.getById(id);
        if (role == null) {
            return Result.error("角色不存在");
        }
        return Result.success(role);
    }

    @Operation(summary = "创建角色")
    @PostMapping
    @OperationLogRecord(module = "角色管理", operationType = "CREATE", operationDesc = "创建角色")
    public Result<Void> createRole(@RequestBody Role role) {
        try {
            boolean success = roleService.createRole(role);
            if (success) {
                return Result.success();
            } else {
                return Result.error("创建角色失败");
            }
        } catch (Exception e) {
            log.error("创建角色失败", e);
            return Result.error(e.getMessage());
        }
    }

    @Operation(summary = "更新角色")
    @PutMapping("/{id}")
    @OperationLogRecord(module = "角色管理", operationType = "UPDATE", operationDesc = "更新角色")
    public Result<Void> updateRole(@PathVariable Long id, @RequestBody Role role) {
        try {
            role.setId(id);
            boolean success = roleService.updateRole(role);
            if (success) {
                return Result.success();
            } else {
                return Result.error("更新角色失败");
            }
        } catch (Exception e) {
            log.error("更新角色失败", e);
            return Result.error(e.getMessage());
        }
    }

    @Operation(summary = "删除角色")
    @DeleteMapping("/{id}")
    @OperationLogRecord(module = "角色管理", operationType = "DELETE", operationDesc = "删除角色")
    public Result<Void> deleteRole(@PathVariable Long id) {
        try {
            boolean success = roleService.deleteRole(id);
            if (success) {
                return Result.success();
            } else {
                return Result.error("删除角色失败");
            }
        } catch (Exception e) {
            log.error("删除角色失败", e);
            return Result.error(e.getMessage());
        }
    }

    @Operation(summary = "批量删除角色")
    @DeleteMapping("/batch")
    @OperationLogRecord(module = "角色管理", operationType = "DELETE", operationDesc = "批量删除角色")
    public Result<Void> deleteRoles(@RequestBody List<Long> roleIds) {
        try {
            boolean success = roleService.deleteRoles(roleIds);
            if (success) {
                return Result.success();
            } else {
                return Result.error("批量删除角色失败");
            }
        } catch (Exception e) {
            log.error("批量删除角色失败", e);
            return Result.error(e.getMessage());
        }
    }

    @Operation(summary = "为角色分配权限")
    @PostMapping("/{id}/permissions")
    @OperationLogRecord(module = "角色管理", operationType = "ASSIGN", operationDesc = "为角色分配权限")
    public Result<Void> assignPermissions(@PathVariable Long id, @Valid @RequestBody RolePermissionAssignDTO request) {
        try {
            boolean success = roleService.assignPermissions(id, request.getPermissionIds());
            if (success) {
                return Result.success();
            } else {
                return Result.error("分配权限失败");
            }
        } catch (Exception e) {
            log.error("分配权限失败", e);
            return Result.error(e.getMessage());
        }
    }

    @Operation(summary = "获取角色及其权限信息")
    @GetMapping("/{id}/permissions")
    public Result<Role> getRoleWithPermissions(@PathVariable Long id) {
        Role role = roleService.getRoleWithPermissions(id);
        if (role == null) {
            return Result.error("角色不存在");
        }
        return Result.success(role);
    }

    @Operation(summary = "更新角色状态")
    @PutMapping("/{id}/status")
    @OperationLogRecord(module = "角色管理", operationType = "UPDATE", operationDesc = "更新角色状态")
    public Result<Void> updateRoleStatus(@PathVariable Long id, @RequestBody Map<String, Integer> request) {
        try {
            Integer status = request.get("status");
            boolean success = roleService.updateRoleStatus(id, status);
            if (success) {
                return Result.success();
            } else {
                return Result.error("更新角色状态失败");
            }
        } catch (Exception e) {
            log.error("更新角色状态失败", e);
            return Result.error(e.getMessage());
        }
    }

    @Operation(summary = "检查角色编码是否存在")
    @GetMapping("/check-code")
    public Result<Boolean> checkRoleCode(@RequestParam String roleCode) {
        boolean exists = roleService.existsByRoleCode(roleCode);
        return Result.success(exists);
    }

    @Operation(summary = "检查角色名称是否存在")
    @GetMapping("/check-name")
    public Result<Boolean> checkRoleName(@RequestParam String roleName) {
        boolean exists = roleService.existsByRoleName(roleName);
        return Result.success(exists);
    }
}
