package com.sinoair.agent.controller;

import com.sinoair.agent.annotation.OperationLogRecord;
import com.sinoair.agent.common.Result;
import com.sinoair.agent.dto.request.CreateUserRequest;
import com.sinoair.agent.dto.request.UpdateUserRequest;
import com.sinoair.agent.dto.request.UserRoleAssignDTO;
import com.sinoair.agent.dto.request.UserApprovalDTO;
import com.sinoair.agent.dto.request.ChangePasswordRequest;
import com.sinoair.agent.dto.request.UpdateProfileRequest;
import com.sinoair.agent.dto.response.UserVO;
import com.sinoair.agent.entity.User;
import com.sinoair.agent.security.UserPrincipal;
import com.sinoair.agent.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 用户管理控制器
 * 
 * <AUTHOR> Team
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/users")
@RequiredArgsConstructor
@Tag(name = "用户管理", description = "用户的增删改查、状态管理等功能")
public class UserController {

    private final UserService userService;

    @Operation(summary = "获取当前用户信息")
    @GetMapping("/current")
    public Result<UserPrincipal> getCurrentUser(@AuthenticationPrincipal UserPrincipal userPrincipal) {
        try {
            if (userPrincipal == null) {
                return Result.error("用户未登录");
            }

            log.debug("获取当前用户信息: userId={}, username={}", userPrincipal.getId(), userPrincipal.getUsername());

            // 清除敏感信息
            UserPrincipal safeUserPrincipal = new UserPrincipal();
            safeUserPrincipal.setId(userPrincipal.getId());
            safeUserPrincipal.setUsername(userPrincipal.getUsername());
            safeUserPrincipal.setRealName(userPrincipal.getRealName());
            safeUserPrincipal.setEmail(userPrincipal.getEmail());
            safeUserPrincipal.setRoleId(userPrincipal.getRoleId());
            safeUserPrincipal.setRoleCode(userPrincipal.getRoleCode());
            safeUserPrincipal.setPermissions(userPrincipal.getPermissions());

            return Result.success(safeUserPrincipal);
        } catch (Exception e) {
            log.error("获取当前用户信息失败: userId={}", userPrincipal != null ? userPrincipal.getId() : "null", e);
            return Result.error("获取用户信息失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取用户列表")
    @GetMapping
    // @PreAuthorize("hasAuthority('USER_VIEW') or hasRole('SUPER_ADMIN')")
    public Result<List<UserVO>> getUsers(
            @Parameter(description = "角色ID") @RequestParam(required = false) Long roleId,
            @Parameter(description = "状态") @RequestParam(required = false) Integer status,
            @Parameter(description = "关键词") @RequestParam(required = false) String keyword) {

        List<UserVO> users = userService.getUserList(roleId, status, keyword);
        return Result.success(users);
    }

    @Operation(summary = "根据ID获取用户详情")
    @GetMapping("/{id}")
    // @PreAuthorize("hasAuthority('user:view') or hasRole('ADMIN')")
    public Result<UserVO> getUser(@PathVariable Long id) {
        UserVO user = userService.getUserById(id);
        if (user == null) {
            return Result.error("用户不存在");
        }
        return Result.success(user);
    }

    @Operation(summary = "创建用户")
    @PostMapping
    @OperationLogRecord(module = "用户管理", operationType = "CREATE", operationDesc = "创建新用户")
    public Result<UserVO> createUser(@Valid @RequestBody CreateUserRequest request) {
        return userService.createUser(request);
    }

    @Operation(summary = "更新用户")
    @PutMapping("/{id}")
    @OperationLogRecord(module = "用户管理", operationType = "UPDATE", operationDesc = "更新用户信息")
    public Result<UserVO> updateUser(@PathVariable Long id, @Valid @RequestBody UpdateUserRequest request) {
        return userService.updateUser(id, request);
    }

    @Operation(summary = "删除用户")
    @DeleteMapping("/{id}")
    @OperationLogRecord(module = "用户管理", operationType = "DELETE", operationDesc = "删除用户")
    public Result<Void> deleteUser(@PathVariable Long id) {
        boolean success = userService.deleteUser(id);
        if (success) {
            return Result.success();
        } else {
            return Result.error("删除失败");
        }
    }

    @Operation(summary = "切换用户状态")
    @PostMapping("/{id}/toggle-status")
    public Result<Void> toggleUserStatus(@PathVariable Long id) {
        boolean success = userService.toggleUserStatus(id);
        if (success) {
            return Result.success();
        } else {
            return Result.error("状态切换失败");
        }
    }

    @Operation(summary = "重置用户密码")
    @PostMapping("/{id}/reset-password")
    public Result<Map<String, String>> resetPassword(@PathVariable Long id) {
        return userService.resetPassword(id);
    }

    @Operation(summary = "导出用户列表")
    @GetMapping("/export")
    public Result<String> exportUsers(
            @Parameter(description = "角色ID") @RequestParam(required = false) Long roleId,
            @Parameter(description = "状态") @RequestParam(required = false) Integer status) {
        
        String downloadUrl = userService.exportUsers(roleId, status);
        return Result.success(downloadUrl);
    }

    @Operation(summary = "获取用户统计信息")
    @GetMapping("/statistics")
    public Result<Map<String, Object>> getUserStatistics() {
        Map<String, Object> statistics = userService.getUserStatistics();
        return Result.success(statistics);
    }

    @Operation(summary = "为用户分配角色")
    @PostMapping("/{id}/roles")
    public Result<Void> assignRolesToUser(@PathVariable Long id, @Valid @RequestBody UserRoleAssignDTO request) {
        try {
            boolean success = userService.assignRolesToUser(id, request.getRoleIds());
            if (success) {
                return Result.success();
            } else {
                return Result.error("分配角色失败");
            }
        } catch (Exception e) {
            log.error("分配角色失败", e);
            return Result.error(e.getMessage());
        }
    }

    @Operation(summary = "获取用户的角色列表")
    @GetMapping("/{id}/roles")
    public Result<List<Long>> getUserRoles(@PathVariable Long id) {
        List<Long> roleIds = userService.getUserRoleIds(id);
        return Result.success(roleIds);
    }

    @Operation(summary = "获取用户的权限列表")
    @GetMapping("/{id}/permissions")
    public Result<List<String>> getUserPermissions(@PathVariable Long id) {
        List<String> permissions = userService.getUserPermissions(id);
        return Result.success(permissions);
    }

    @Operation(summary = "根据用户来源获取用户列表")
    @GetMapping("/by-forward")
    public Result<List<UserVO>> getUserListByForward(
            @Parameter(description = "用户来源") @RequestParam(required = false) String forward,
            @Parameter(description = "审核状态") @RequestParam(required = false) Integer approvalStatus,
            @Parameter(description = "关键词") @RequestParam(required = false) String keyword) {
        List<UserVO> users = userService.getUserListByForward(forward, approvalStatus, keyword);
        return Result.success(users);
    }

    @Operation(summary = "审核用户")
    @PostMapping("/approve")
    @OperationLogRecord(module = "用户管理", operationType = "APPROVE", operationDesc = "审核用户")
    public Result<Void> approveUser(@Valid @RequestBody UserApprovalDTO request) {
        return userService.approveUser(request.getUserId(), request.getApprovalStatus(), request.getApprovalRemark());
    }

    @Operation(summary = "获取待审核用户数量")
    @GetMapping("/pending-approval-count")
    public Result<Long> getPendingApprovalCount() {
        Long count = userService.getPendingApprovalCount();
        return Result.success(count);
    }

    @Operation(summary = "修改密码")
    @PostMapping("/change-password")
    @OperationLogRecord(module = "用户管理", operationType = "UPDATE", operationDesc = "修改密码")
    public Result<Void> changePassword(@Valid @RequestBody ChangePasswordRequest request,
                                      @AuthenticationPrincipal UserPrincipal userPrincipal) {
        if (userPrincipal == null) {
            return Result.error("用户未登录");
        }
        return userService.changePassword(userPrincipal.getId(), request);
    }

    @Operation(summary = "更新个人资料")
    @PostMapping("/update-profile")
    @OperationLogRecord(module = "用户管理", operationType = "UPDATE", operationDesc = "更新个人资料")
    public Result<UserVO> updateProfile(@Valid @RequestBody UpdateProfileRequest request,
                                       @AuthenticationPrincipal UserPrincipal userPrincipal) {
        if (userPrincipal == null) {
            return Result.error("用户未登录");
        }
        return userService.updateProfile(userPrincipal.getId(), request);
    }
}
