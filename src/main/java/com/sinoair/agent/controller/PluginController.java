package com.sinoair.agent.controller;

import com.sinoair.agent.annotation.OperationLogRecord;
import com.sinoair.agent.common.Result;
import com.sinoair.agent.security.UserPrincipal;
import com.sinoair.agent.service.PermissionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 插件管理控制器
 *
 * <AUTHOR> Team
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/plugin")
@RequiredArgsConstructor
@Tag(name = "插件管理", description = "浏览器插件权限验证和配置管理")
public class PluginController {

    private final PermissionService permissionService;

    @Operation(summary = "验证插件权限")
    @PostMapping("/verify-permission")
    @OperationLogRecord(module = "插件管理", operationType = "VERIFY", operationDesc = "验证插件权限")
    public Result<Map<String, Boolean>> verifyPermission(
            @AuthenticationPrincipal UserPrincipal userPrincipal,
            @RequestBody Map<String, Object> request) {
        try {
            String permission = (String) request.get("permission");
            
            if (userPrincipal == null) {
                return Result.error("用户未登录");
            }
            
            log.debug("验证插件权限: userId={}, permission={}", userPrincipal.getId(), permission);
            
            boolean hasPermission = permissionService.hasPermission(userPrincipal.getId(), permission);
            
            Map<String, Boolean> result = new HashMap<>();
            result.put("hasPermission", hasPermission);
            
            log.debug("插件权限验证结果: userId={}, permission={}, hasPermission={}", 
                     userPrincipal.getId(), permission, hasPermission);
            
            return Result.success(result);
        } catch (Exception e) {
            log.error("验证插件权限失败", e);
            return Result.error("权限验证失败: " + e.getMessage());
        }
    }

    @Operation(summary = "批量验证插件权限")
    @PostMapping("/verify-permissions")
    @OperationLogRecord(module = "插件管理", operationType = "VERIFY", operationDesc = "批量验证插件权限")
    public Result<Map<String, Boolean>> verifyPermissions(
            @AuthenticationPrincipal UserPrincipal userPrincipal,
            @RequestBody Map<String, Object> request) {
        try {
            @SuppressWarnings("unchecked")
            List<String> permissions = (List<String>) request.get("permissions");
            
            if (userPrincipal == null) {
                return Result.error("用户未登录");
            }
            
            log.debug("批量验证插件权限: userId={}, permissions={}", userPrincipal.getId(), permissions);
            
            Map<String, Boolean> result = new HashMap<>();
            
            for (String permission : permissions) {
                boolean hasPermission = permissionService.hasPermission(userPrincipal.getId(), permission);
                result.put(permission, hasPermission);
            }
            
            log.debug("批量插件权限验证结果: userId={}, result={}", userPrincipal.getId(), result);
            
            return Result.success(result);
        } catch (Exception e) {
            log.error("批量验证插件权限失败", e);
            return Result.error("权限验证失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取用户插件权限列表")
    @GetMapping("/permissions")
    public Result<List<String>> getUserPluginPermissions(@AuthenticationPrincipal UserPrincipal userPrincipal) {
        try {
            if (userPrincipal == null) {
                return Result.error("用户未登录");
            }
            
            log.debug("获取用户插件权限: userId={}", userPrincipal.getId());
            
            // 获取用户的所有权限
            List<String> allPermissions = permissionService.getButtonPermissionsByUserId(userPrincipal.getId());
            
            // 过滤出插件相关权限
            List<String> pluginPermissions = allPermissions.stream()
                    .filter(permission -> permission.startsWith("plugin:"))
                    .toList();
            
            log.debug("用户插件权限获取成功: userId={}, permissions={}", userPrincipal.getId(), pluginPermissions);
            
            return Result.success(pluginPermissions);
        } catch (Exception e) {
            log.error("获取用户插件权限失败: userId={}", userPrincipal.getId(), e);
            return Result.error("获取插件权限失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取插件配置信息")
    @GetMapping("/config")
    public Result<Map<String, Object>> getPluginConfig(@AuthenticationPrincipal UserPrincipal userPrincipal) {
        try {
            if (userPrincipal == null) {
                return Result.error("用户未登录");
            }
            
            // 检查是否有查看插件配置的权限
            if (!permissionService.hasPermission(userPrincipal.getId(), "plugin:view")) {
                return Result.error("无权限访问插件配置");
            }
            
            Map<String, Object> config = new HashMap<>();
            config.put("version", "1.0.0");
            config.put("apiBaseUrl", "http://localhost:8080");
            config.put("features", Map.of(
                "htmlExtract", permissionService.hasPermission(userPrincipal.getId(), "plugin:html:extract"),
                "formFill", permissionService.hasPermission(userPrincipal.getId(), "plugin:form:fill"),
                "dataRecognize", permissionService.hasPermission(userPrincipal.getId(), "plugin:data:recognize"),
                "configManage", permissionService.hasPermission(userPrincipal.getId(), "plugin:config:manage"),
                "debug", permissionService.hasPermission(userPrincipal.getId(), "plugin:debug"),
                "logView", permissionService.hasPermission(userPrincipal.getId(), "plugin:log:view"),
                "systemInfo", permissionService.hasPermission(userPrincipal.getId(), "plugin:system:info")
            ));
            
            return Result.success(config);
        } catch (Exception e) {
            log.error("获取插件配置失败: userId={}", userPrincipal.getId(), e);
            return Result.error("获取插件配置失败: " + e.getMessage());
        }
    }

    @Operation(summary = "插件用户认证")
    @PostMapping("/auth")
    @OperationLogRecord(module = "插件管理", operationType = "AUTH", operationDesc = "插件用户认证")
    public Result<Map<String, Object>> authenticatePlugin(@RequestBody Map<String, Object> request) {
        try {
            String token = (String) request.get("token");
            
            if (token == null || token.trim().isEmpty()) {
                return Result.error("Token不能为空");
            }
            
            // 这里应该验证token的有效性，暂时简化处理
            // 实际实现中应该解析JWT token并验证
            
            Map<String, Object> result = new HashMap<>();
            result.put("authenticated", true);
            result.put("message", "认证成功");
            
            return Result.success(result);
        } catch (Exception e) {
            log.error("插件用户认证失败", e);
            return Result.error("认证失败: " + e.getMessage());
        }
    }
}
