package com.sinoair.agent.controller;

import com.sinoair.agent.common.Result;
import com.sinoair.agent.service.OcrService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * OCR控制器
 * 提供图片文字识别功能的API接口
 * 
 * <AUTHOR> Team
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/ocr")
@RequiredArgsConstructor
@Tag(name = "OCR管理", description = "图片文字识别相关接口")
public class OcrController {

    private final OcrService ocrService;

    /**
     * 图片文字识别
     */
    @PostMapping("/recognize")
    @Operation(summary = "图片文字识别", description = "上传图片文件，识别其中的文字内容")
    public Result<String> recognizeImage(
            @Parameter(description = "图片文件", required = true)
            @RequestParam("file") MultipartFile file) {
        
        try {
            log.info("收到OCR识别请求: fileName={}, size={}", file.getOriginalFilename(), file.getSize());

            // 验证文件
            if (file.isEmpty()) {
                return Result.error("请选择要识别的图片文件");
            }

            // 获取文件数据
            byte[] fileData = file.getBytes();
            String fileName = file.getOriginalFilename();

            // 执行OCR识别
            Result<String> result = ocrService.recognizeText(fileData, fileName);

            log.info("OCR识别完成: fileName={}, success={}", fileName, result.isSuccess());
            return result;

        } catch (Exception e) {
            log.error("OCR识别异常: fileName={}", file.getOriginalFilename(), e);
            return Result.error("OCR识别异常: " + e.getMessage());
        }
    }

    /**
     * 检查OCR服务状态
     */
    @GetMapping("/status")
    @Operation(summary = "检查OCR服务状态", description = "检查OCR服务是否可用")
    public Result<Boolean> checkStatus() {
        try {
            boolean available = ocrService.isAvailable();
            String message = available ? "OCR服务可用" : "OCR服务不可用";
            
            log.info("OCR服务状态检查: available={}", available);
            return Result.success(message, available);
            
        } catch (Exception e) {
            log.error("OCR服务状态检查异常", e);
            return Result.error("OCR服务状态检查异常: " + e.getMessage());
        }
    }
}
