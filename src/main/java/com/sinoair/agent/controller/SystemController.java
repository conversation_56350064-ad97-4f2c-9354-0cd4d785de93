package com.sinoair.agent.controller;

import com.sinoair.agent.common.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 系统管理控制器
 * 
 * <AUTHOR> Team
 */
@Tag(name = "系统管理", description = "系统健康检查、配置管理等系统功能")
@RestController
@RequestMapping("/api/v1/system")
@RequiredArgsConstructor
public class SystemController {

    @Operation(summary = "系统健康检查", description = "检查系统各组件的运行状态")
    @GetMapping("/health")
    public Result<Map<String, Object>> healthCheck() {
        Map<String, Object> health = new HashMap<>();
        health.put("status", "UP");
        
        Map<String, String> components = new HashMap<>();
        components.put("database", "UP");
        components.put("redis", "UP");
        components.put("fileStorage", "UP");
        components.put("llmService", "UP");
        
        health.put("components", components);
        health.put("timestamp", LocalDateTime.now());
        
        return Result.success(health);
    }

    @Operation(summary = "获取系统信息", description = "获取系统版本、环境等基本信息")
    @GetMapping("/info")
    public Result<Map<String, Object>> getSystemInfo() {
        Map<String, Object> info = new HashMap<>();
        info.put("name", "SinoairAgent Platform");
        info.put("version", "1.0.0");
        info.put("description", "智能文档识别与自动填写平台");
        info.put("buildTime", "2024-01-01T00:00:00Z");
        info.put("javaVersion", System.getProperty("java.version"));
        info.put("osName", System.getProperty("os.name"));
        
        return Result.success(info);
    }
}
