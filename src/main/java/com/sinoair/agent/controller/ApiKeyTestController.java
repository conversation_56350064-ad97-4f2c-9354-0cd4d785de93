package com.sinoair.agent.controller;

import com.sinoair.agent.common.Result;
import com.sinoair.agent.entity.ApiKey;
import com.sinoair.agent.security.ApiKeyAuthenticationToken;
import com.sinoair.agent.service.ApiKeyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * API Key测试控制器
 * 用于调试API Key认证问题
 * 
 * <AUTHOR> Team
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/public/test")
@RequiredArgsConstructor
@Tag(name = "API Key测试", description = "用于测试API Key认证功能的调试接口")
public class ApiKeyTestController {

    private final ApiKeyService apiKeyService;

    @Operation(summary = "测试API Key认证", description = "简单的测试接口，验证API Key认证是否正常工作",
               security = @SecurityRequirement(name = "ApiKey"))
    @GetMapping("/auth")
    public Result<Map<String, Object>> testAuth(
            @AuthenticationPrincipal ApiKeyAuthenticationToken authentication) {
        
        try {
            if (authentication == null) {
                return Result.error(10001, "Authentication is null - API Key认证失败");
            }
            
            ApiKey apiKey = authentication.getApiKey();
            if (apiKey == null) {
                return Result.error(10002, "ApiKey object is null");
            }
            
            Map<String, Object> result = new HashMap<>();
            result.put("keyId", apiKey.getKeyId());
            result.put("keyName", apiKey.getKeyName());
            result.put("userId", apiKey.getUserId());
            result.put("status", apiKey.getStatus());
            result.put("rateLimit", apiKey.getRateLimit());
            result.put("message", "API Key认证成功！");
            
            log.info("API Key认证测试成功: keyId={}", apiKey.getKeyId());
            return Result.success("API Key认证测试成功", result);
            
        } catch (Exception e) {
            log.error("API Key认证测试失败", e);
            return Result.error("API Key认证测试失败: " + e.getMessage());
        }
    }

    @Operation(summary = "验证特定API Key", description = "验证指定的API Key是否有效")
    @GetMapping("/validate")
    public Result<Map<String, Object>> validateApiKey(
            @RequestParam String keyId,
            @RequestParam String keySecret) {
        
        try {
            log.info("验证API Key: keyId={}, keySecret length={}", keyId, keySecret.length());
            
            ApiKey apiKey = apiKeyService.validateApiKey(keyId, keySecret);
            
            Map<String, Object> result = new HashMap<>();
            if (apiKey != null) {
                result.put("valid", true);
                result.put("keyId", apiKey.getKeyId());
                result.put("keyName", apiKey.getKeyName());
                result.put("status", apiKey.getStatus());
                result.put("userId", apiKey.getUserId());
                result.put("message", "API Key有效");
            } else {
                result.put("valid", false);
                result.put("message", "API Key无效或不存在");
            }
            
            return Result.success(result);
            
        } catch (Exception e) {
            log.error("验证API Key失败: keyId={}", keyId, e);
            return Result.error("验证API Key失败: " + e.getMessage());
        }
    }

    @Operation(summary = "列出所有测试API Key", description = "显示数据库中所有的测试API Key（不显示密钥）")
    @GetMapping("/list")
    public Result<List<Map<String, Object>>> listTestApiKeys() {
        
        try {
            // 这里我们直接查询数据库中的API Key
            List<ApiKey> apiKeys = apiKeyService.list();
            
            List<Map<String, Object>> result = apiKeys.stream()
                .filter(key -> key.getKeyId().startsWith("ak_test") || key.getKeyId().startsWith("ak_demo"))
                .map(key -> {
                    Map<String, Object> map = new HashMap<>();
                    map.put("keyId", key.getKeyId());
                    map.put("keyName", key.getKeyName());
                    map.put("status", key.getStatus());
                    map.put("userId", key.getUserId());
                    map.put("rateLimit", key.getRateLimit());
                    map.put("dailyLimit", key.getDailyLimit());
                    map.put("expiresAt", key.getExpiresAt());
                    map.put("createdTime", key.getCreatedTime());
                    return map;
                })
                .toList();
            
            return Result.success("查询成功", result);
            
        } catch (Exception e) {
            log.error("查询API Key列表失败", e);
            return Result.error("查询API Key列表失败: " + e.getMessage());
        }
    }
}
