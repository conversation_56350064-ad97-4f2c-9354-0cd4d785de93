package com.sinoair.agent.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sinoair.agent.common.PageResult;
import com.sinoair.agent.common.Result;
import com.sinoair.agent.entity.Agent;
import com.sinoair.agent.security.ApiKeyAuthenticationToken;
import com.sinoair.agent.service.AgentService;
import com.sinoair.agent.service.FileParseService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 公开API控制器 - 使用API Key认证
 *
 * <AUTHOR> Team
 */
@Tag(name = "公开API", description = "使用API Key认证的公开接口")
@RestController
@RequestMapping("/api/v1/public")
@RequiredArgsConstructor
@Slf4j
@SecurityRequirement(name = "ApiKey")
public class PublicApiController {

    private final AgentService agentService;
    private final FileParseService fileParseService;

    @Operation(summary = "获取Agent列表", description = "获取所有可用的Agent列表，用于获取AgentId供文件解析接口使用",
               security = @SecurityRequirement(name = "ApiKey"))
    @GetMapping("/agents")
    public Result<Map<String, Object>> getAgents(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") int page,
            @Parameter(description = "每页数量") @RequestParam(defaultValue = "10") int size,
            @Parameter(description = "指定Agent ID") @RequestParam(required = false) String agentId) {

        try {
            // 从SecurityContext获取认证信息
            Authentication auth = SecurityContextHolder.getContext().getAuthentication();
            log.debug("Authentication from SecurityContext: {}", auth);

            if (auth == null || !(auth instanceof ApiKeyAuthenticationToken)) {
                log.warn("No valid API Key authentication found in SecurityContext");
                return Result.error(10001, "Missing API Key authentication. Please provide X-API-Key header with format: keyId.keySecret");
            }

            ApiKeyAuthenticationToken authentication = (ApiKeyAuthenticationToken) auth;
            log.info("公开API - 获取Agent列表: keyId={}, page={}, size={}",
                    authentication.getKeyId(), page, size);

            IPage<Agent> agentPage = agentService.getPublicAgentList(page, size, agentId);

            // 构建响应数据，符合API文档格式
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("total", agentPage.getTotal());
            responseData.put("page", agentPage.getCurrent());
            responseData.put("size", agentPage.getSize());
            responseData.put("agents", agentPage.getRecords().stream().map(agent -> {
                Map<String, Object> agentInfo = new HashMap<>();
                agentInfo.put("agentCode", agent.getAgentCode());
                agentInfo.put("name", agent.getAgentName());
                agentInfo.put("description", agent.getDescription());
                agentInfo.put("supportedFileTypes", getSupportedFileTypes(agent));
                agentInfo.put("maxFileSize", "5MB");
                return agentInfo;
            }).collect(Collectors.toList()));

            return Result.success(responseData);
        } catch (Exception e) {
            log.error("获取Agent列表失败", e);
            return Result.error("获取Agent列表失败: " + e.getMessage());
        }
    }

    @Operation(summary = "文件解析接口", description = "根据指定的AgentId上传单个文件，调用对应的Agent解析文件内容并返回JSON格式的结构化数据。当前仅支持同步处理",
               security = @SecurityRequirement(name = "ApiKey"))
    @PostMapping("/files/parse")
    public Result<Object> parseFile(
            @Parameter(description = "Agent标识符") @RequestParam String agentCode,
            @Parameter(description = "上传的文件") @RequestParam("file") MultipartFile file) {

        try {
            // 从SecurityContext获取认证信息
            Authentication auth = SecurityContextHolder.getContext().getAuthentication();
            if (auth == null || !(auth instanceof ApiKeyAuthenticationToken)) {
                return Result.error(10001, "Missing API Key authentication. Please provide X-API-Key header with format: keyId.keySecret");
            }

            ApiKeyAuthenticationToken authentication = (ApiKeyAuthenticationToken) auth;
            log.info("公开API - 文件解析: keyId={}, agentId={}, fileName={}",
                    authentication.getKeyId(), agentCode, file.getOriginalFilename());

            // 验证文件
            if (file.isEmpty()) {
                return Result.error(10001, "文件不能为空");
            }

            // 验证文件大小 (5MB限制)
            if (file.getSize() > 5 * 1024 * 1024) {
                return Result.error(10005, "文件大小超出限制，最大支持5MB");
            }

            // 验证文件类型
            String fileName = file.getOriginalFilename();
            if (fileName == null || !isValidFileType(fileName)) {
                return Result.error(10004, "不支持的文件格式，仅支持PDF和图片文件");
            }

            // 验证Agent是否存在且已发布
            Agent agent = agentService.getPublishedAgentByCode(agentCode);
            if (agent == null) {
                return Result.error(10003, "Agent不存在或未发布");
            }

            // 调用文件解析服务（仅同步模式）
            Object result = fileParseService.parseFileForPublicApi(agent, file, false, null, "normal", authentication.getUserId());

            return Result.success("文件解析成功", result);

        } catch (Exception e) {
            log.error("文件解析失败: agentId={}, fileName={}",
                    agentCode, file.getOriginalFilename(), e);
            return Result.error(30001, "文件解析服务异常: " + e.getMessage());
        }
    }

    // 暂时屏蔽异步状态查询接口，当前仅支持同步处理
    /*
    @Operation(summary = "解析状态查询接口", description = "查询异步文件解析任务的状态和结果",
               security = @SecurityRequirement(name = "ApiKey"))
    @GetMapping("/files/parse/status/{taskId}")
    public Result<Object> getParseStatus(
            @Parameter(description = "任务ID") @PathVariable String taskId) {

        try {
            // 从SecurityContext获取认证信息
            Authentication auth = SecurityContextHolder.getContext().getAuthentication();
            if (auth == null || !(auth instanceof ApiKeyAuthenticationToken)) {
                return Result.error(10001, "Missing API Key authentication. Please provide X-API-Key header with format: keyId.keySecret");
            }

            ApiKeyAuthenticationToken authentication = (ApiKeyAuthenticationToken) auth;
            log.info("公开API - 查询解析状态: keyId={}, taskId={}",
                    authentication.getKeyId(), taskId);

            Object result = fileParseService.getParseStatus(taskId, authentication.getUserId());
            if (result == null) {
                return Result.error("任务不存在或已过期");
            }

            return Result.success(result);
        } catch (Exception e) {
            log.error("查询解析状态失败: taskId={}", taskId, e);
            return Result.error("查询解析状态失败: " + e.getMessage());
        }
    }
    */

    /**
     * 获取Agent支持的文件类型
     */
    private String[] getSupportedFileTypes(Agent agent) {
        // 根据Agent类型返回支持的文件类型
        // 这里简化处理，实际应该根据Agent配置来确定
        return new String[]{"pdf", "jpg", "jpeg", "png", "gif"};
    }

    /**
     * 验证文件类型
     */
    private boolean isValidFileType(String fileName) {
        String extension = fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();
        return extension.matches("pdf|jpg|jpeg|png|gif");
    }
}
