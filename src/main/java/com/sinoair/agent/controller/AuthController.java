package com.sinoair.agent.controller;

import com.sinoair.agent.annotation.OperationLogRecord;
import com.sinoair.agent.common.Result;
import com.sinoair.agent.dto.request.LoginRequest;
import com.sinoair.agent.dto.request.RegisterRequest;
import com.sinoair.agent.dto.response.LoginResponse;
import com.sinoair.agent.security.UserPrincipal;
import com.sinoair.agent.service.AuthService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

/**
 * 认证控制器
 * 
 * <AUTHOR> Team
 */
@Tag(name = "认证管理", description = "用户登录、登出、Token刷新等认证相关接口")
@RestController
@RequestMapping("/api/v1/auth")
@RequiredArgsConstructor
public class AuthController {

    private final AuthService authService;

    @Operation(summary = "用户登录", description = "用户名密码登录，返回JWT Token")
    @PostMapping("/login")
    @OperationLogRecord(module = "用户认证", operationType = "LOGIN", operationDesc = "用户登录")
    public Result<LoginResponse> login(@Valid @RequestBody LoginRequest request) {
        return authService.login(request);
    }

    @Operation(summary = "用户注册", description = "客户注册账号")
    @PostMapping("/register")
    public Result<Void> register(@Valid @RequestBody RegisterRequest request) {
        return authService.register(request);
    }

    @Operation(summary = "验证Token", description = "验证当前Token是否有效")
    @PostMapping("/verify")
    public Result<Object> verifyToken(@AuthenticationPrincipal UserPrincipal userPrincipal) {
        if (userPrincipal != null) {
            return Result.success("Token有效", userPrincipal);
        } else {
            return Result.error("Token无效");
        }
    }

    @Operation(summary = "用户登出", description = "清除用户认证信息")
    @PostMapping("/logout")
    @OperationLogRecord(module = "用户认证", operationType = "LOGOUT", operationDesc = "用户登出")
    public Result<Void> logout() {
        return authService.logout();
    }

    @Operation(summary = "刷新Token", description = "使用刷新Token获取新的访问Token")
    @PostMapping("/refresh")
    @OperationLogRecord(module = "用户认证", operationType = "REFRESH", operationDesc = "刷新Token")
    public Result<LoginResponse> refreshToken(@RequestHeader("Authorization") String refreshToken) {
        // 移除 "Bearer " 前缀
        if (refreshToken.startsWith("Bearer ")) {
            refreshToken = refreshToken.substring(7);
        }
        return authService.refreshToken(refreshToken);
    }

    @Operation(summary = "验证Token", description = "验证当前Token是否有效")
    @GetMapping("/validate")
    public Result<Void> validateToken() {
        // 如果能到达这里，说明JWT过滤器已经验证了token的有效性
        return Result.success("Token有效", null);
    }
}
