package com.sinoair.agent.service;

import com.sinoair.agent.config.MinioProperties;
import io.minio.BucketExistsArgs;
import io.minio.MinioClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * MinIO健康检查服务
 * 
 * <AUTHOR> Team
 */
@Slf4j
@Service
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "app.minio", name = "enabled", havingValue = "true", matchIfMissing = true)
public class MinioHealthService implements HealthIndicator {

    private final MinioClient minioClient;
    private final MinioProperties minioProperties;

    @Override
    public Health health() {
        try {
            // 检查MinIO连接
            boolean isConnected = checkConnection();
            
            if (isConnected) {
                Map<String, Object> details = new HashMap<>();
                details.put("endpoint", minioProperties.getEndpoint());
                details.put("bucketName", minioProperties.getBucketName());
                details.put("region", minioProperties.getRegion());
                details.put("status", "Connected");
                
                // 检查存储桶是否存在
                boolean bucketExists = checkBucketExists();
                details.put("bucketExists", bucketExists);
                
                if (bucketExists) {
                    return Health.up()
                            .withDetails(details)
                            .build();
                } else {
                    details.put("error", "Bucket does not exist");
                    return Health.down()
                            .withDetails(details)
                            .build();
                }
            } else {
                return Health.down()
                        .withDetail("endpoint", minioProperties.getEndpoint())
                        .withDetail("status", "Connection failed")
                        .build();
            }
            
        } catch (Exception e) {
            log.error("MinIO健康检查失败", e);
            return Health.down()
                    .withDetail("endpoint", minioProperties.getEndpoint())
                    .withDetail("error", e.getMessage())
                    .withDetail("status", "Error")
                    .build();
        }
    }

    /**
     * 检查MinIO连接
     */
    private boolean checkConnection() {
        try {
            // 尝试列出存储桶来测试连接
            minioClient.listBuckets();
            return true;
        } catch (Exception e) {
            log.warn("MinIO连接检查失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 检查存储桶是否存在
     */
    private boolean checkBucketExists() {
        try {
            return minioClient.bucketExists(
                    BucketExistsArgs.builder()
                            .bucket(minioProperties.getBucketName())
                            .build()
            );
        } catch (Exception e) {
            log.warn("检查存储桶存在性失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 获取MinIO服务信息
     */
    public Map<String, Object> getMinioInfo() {
        Map<String, Object> info = new HashMap<>();
        
        try {
            info.put("endpoint", minioProperties.getEndpoint());
            info.put("bucketName", minioProperties.getBucketName());
            info.put("region", minioProperties.getRegion());
            info.put("enabled", minioProperties.isEnabled());
            info.put("connectTimeout", minioProperties.getConnectTimeout());
            info.put("writeTimeout", minioProperties.getWriteTimeout());
            info.put("readTimeout", minioProperties.getReadTimeout());
            
            // 检查连接状态
            boolean isConnected = checkConnection();
            info.put("connected", isConnected);
            
            if (isConnected) {
                // 检查存储桶
                boolean bucketExists = checkBucketExists();
                info.put("bucketExists", bucketExists);
                
                // 获取存储桶列表
                try {
                    var buckets = minioClient.listBuckets();
                    info.put("totalBuckets", buckets.size());
                    info.put("buckets", buckets.stream().map(b -> b.name()).toList());
                } catch (Exception e) {
                    info.put("bucketsError", e.getMessage());
                }
            }
            
        } catch (Exception e) {
            info.put("error", e.getMessage());
            log.error("获取MinIO信息失败", e);
        }
        
        return info;
    }

    /**
     * 测试MinIO操作
     */
    public Map<String, Object> testMinioOperations() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 测试连接
            long startTime = System.currentTimeMillis();
            boolean connected = checkConnection();
            long connectionTime = System.currentTimeMillis() - startTime;
            
            result.put("connectionTest", connected);
            result.put("connectionTime", connectionTime + "ms");
            
            if (connected) {
                // 测试存储桶操作
                startTime = System.currentTimeMillis();
                boolean bucketExists = checkBucketExists();
                long bucketCheckTime = System.currentTimeMillis() - startTime;
                
                result.put("bucketExistsTest", bucketExists);
                result.put("bucketCheckTime", bucketCheckTime + "ms");
                
                // 测试列出对象（限制数量）
                try {
                    startTime = System.currentTimeMillis();
                    var objects = minioClient.listObjects(
                            io.minio.ListObjectsArgs.builder()
                                    .bucket(minioProperties.getBucketName())
                                    .maxKeys(10)
                                    .build()
                    );
                    
                    int objectCount = 0;
                    for (var obj : objects) {
                        objectCount++;
                        if (objectCount >= 10) break; // 限制检查数量
                    }
                    
                    long listTime = System.currentTimeMillis() - startTime;
                    result.put("listObjectsTest", true);
                    result.put("listObjectsTime", listTime + "ms");
                    result.put("sampleObjectCount", objectCount);
                    
                } catch (Exception e) {
                    result.put("listObjectsTest", false);
                    result.put("listObjectsError", e.getMessage());
                }
            }
            
            result.put("overallStatus", "SUCCESS");
            
        } catch (Exception e) {
            result.put("overallStatus", "FAILED");
            result.put("error", e.getMessage());
            log.error("MinIO操作测试失败", e);
        }
        
        return result;
    }
}
