package com.sinoair.agent.service;

import com.sinoair.agent.entity.SysConfig;

import java.util.List;
import java.util.Map;

/**
 * 系统配置服务接口
 *
 * <AUTHOR> Team
 */
public interface SysConfigService {

    /**
     * 获取所有配置
     */
    List<SysConfig> list();

    /**
     * 根据ID获取配置
     */
    SysConfig getById(Long id);

    /**
     * 保存配置
     */
    boolean save(SysConfig entity);

    /**
     * 更新配置
     */
    boolean updateById(SysConfig entity);

    /**
     * 删除配置
     */
    boolean removeById(Long id);

    /**
     * 根据配置组查询配置列表
     */
    List<SysConfig> getByGroup(String configGroup);

    /**
     * 根据配置组查询有效的配置列表
     */
    List<SysConfig> getEnabledByGroup(String configGroup);

    /**
     * 根据配置组和配置键查询配置
     */
    SysConfig getByGroupAndKey(String configGroup, String configKey);

    /**
     * 获取配置值
     */
    String getConfigValue(String configGroup, String configKey);

    /**
     * 获取配置值（带默认值）
     */
    String getConfigValue(String configGroup, String configKey, String defaultValue);

    /**
     * 获取配置的布尔值
     */
    Boolean getBooleanValue(String configGroup, String configKey);

    /**
     * 获取配置的整数值
     */
    Integer getIntegerValue(String configGroup, String configKey);

    /**
     * 获取字典数据（按组分组）
     */
    Map<String, List<SysConfig>> getDictData();

    /**
     * 获取指定组的字典数据
     */
    List<SysConfig> getDictDataByGroup(String configGroup);
}
