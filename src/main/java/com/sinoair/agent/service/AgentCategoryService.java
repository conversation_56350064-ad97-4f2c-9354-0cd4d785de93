package com.sinoair.agent.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sinoair.agent.entity.AgentCategory;

import java.util.List;

/**
 * Agent分类服务接口
 * 
 * <AUTHOR> Team
 */
public interface AgentCategoryService extends IService<AgentCategory> {

    /**
     * 查询所有可用分类
     */
    List<AgentCategory> getAllActiveCategories();

    /**
     * 查询分类树形结构
     */
    List<AgentCategory> getCategoryTree();

    /**
     * 根据分类代码查询分类
     */
    AgentCategory getByCategoryCode(String categoryCode);

    /**
     * 根据父级ID查询子分类
     */
    List<AgentCategory> getByParentId(Long parentId);

    /**
     * 查询所有根分类
     */
    List<AgentCategory> getRootCategories();

    /**
     * 创建分类
     */
    boolean createCategory(AgentCategory category);

    /**
     * 更新分类
     */
    boolean updateCategory(AgentCategory category);

    /**
     * 删除分类
     */
    boolean deleteCategory(Long categoryId);

    /**
     * 检查分类是否有子分类
     */
    boolean hasChildren(Long categoryId);

    /**
     * 统计分类数量
     */
    Long getCategoryCount();

    /**
     * 统计各分类下的Agent数量
     */
    List<java.util.Map<String, Object>> countAgentsByCategory();
}
