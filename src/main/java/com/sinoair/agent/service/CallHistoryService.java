package com.sinoair.agent.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sinoair.agent.entity.CallHistory;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 调用历史记录服务接口
 */
public interface CallHistoryService {

    /**
     * 分页查询用户的调用历史记录（基于真实数据）
     */
    IPage<CallHistory> getCallHistoryByUserId(
            Long userId,
            Page<CallHistory> page,
            String agentName,
            String callType,
            LocalDateTime startTime,
            LocalDateTime endTime
    );

    /**
     * 根据ID获取调用历史详情（基于真实数据）
     */
    CallHistory getCallHistoryById(Long id, Long userId);

    /**
     * 获取用户成功调用次数统计
     */
    Long getSuccessCountByUserId(Long userId);

    /**
     * 获取用户失败调用次数统计
     */
    Long getFailedCountByUserId(Long userId);

    /**
     * 记录API调用历史
     */
    void recordApiCall(CallHistory callHistory);

    /**
     * 记录插件调用历史
     */
    void recordPluginCall(CallHistory callHistory);

    /**
     * 获取用户最近的调用历史记录
     * @param userId 用户ID
     * @param limit 限制条数
     * @return 最近的调用历史记录列表
     */
    List<CallHistory> getRecentCallHistory(Long userId, int limit);

    /**
     * 获取用户本月调用数量
     * @param userId 用户ID
     * @return 本月调用数量
     */
    Long getMonthlyCallCount(Long userId);

    /**
     * 获取用户本月成功调用数量
     * @param userId 用户ID
     * @return 本月成功调用数量
     */
    Long getMonthlySuccessCount(Long userId);

    /**
     * 计算用户的调用成功率
     * @param userId 用户ID
     * @return 成功率（百分比，保留一位小数）
     */
    Double getSuccessRate(Long userId);

    /**
     * 获取用户今日调用数量
     * @param userId 用户ID
     * @return 今日调用数量
     */
    Long getTodayCallCount(Long userId);
}
