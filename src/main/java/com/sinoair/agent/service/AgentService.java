package com.sinoair.agent.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sinoair.agent.common.PageResult;
import com.sinoair.agent.common.Result;
import com.sinoair.agent.common.ResultCode;
import com.sinoair.agent.dto.request.CreateAgentRequest;
import com.sinoair.agent.dto.response.AgentVO;
import com.sinoair.agent.entity.Agent;
import com.sinoair.agent.entity.AgentVersion;
import com.sinoair.agent.mapper.AgentMapper;
import com.sinoair.agent.mapper.AgentVersionMapper;
import com.sinoair.agent.security.UserPrincipal;
import com.sinoair.agent.util.AgentCodeGenerator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Agent服务
 * 
 * <AUTHOR> Team
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AgentService {

    private final AgentMapper agentMapper;
    private final AgentVersionMapper agentVersionMapper;
    private final AgentVersionService agentVersionService;

    /**
     * 创建Agent
     */
    @Transactional
    public Result<AgentVO> createAgent(CreateAgentRequest request) {
        try {
            // 检查Agent名称是否已存在
            Agent existingAgentByName = agentMapper.findByAgentName(request.getAgentName());
            if (existingAgentByName != null) {
                return Result.error(ResultCode.AGENT_NAME_EXISTS, "Agent名称已存在，请使用其他名称");
            }

            // 如果agentCode为空，自动生成UUID
            String agentCode = request.getAgentCode();
            if (agentCode == null || agentCode.trim().isEmpty()) {
                agentCode = generateUniqueAgentCode();
                request.setAgentCode(agentCode);
            } else {
                // 检查Agent编码是否已存在
                Agent existingAgent = agentMapper.findByAgentCode(agentCode);
                if (existingAgent != null) {
                    return Result.error(ResultCode.AGENT_CODE_EXISTS, "Agent编码已存在");
                }
            }

            // 获取当前用户
            UserPrincipal currentUser = getCurrentUser();

            // 创建Agent实体
            Agent agent = new Agent();
            BeanUtils.copyProperties(request, agent);
            agent.setStatus(Agent.STATUS_DRAFT); // 默认为草稿状态
            agent.setCreatorId(currentUser.getId());

            // 保存Agent
            agentMapper.insert(agent);

            // 创建初始版本
            Result<com.sinoair.agent.entity.AgentVersion> versionResult = agentVersionService.createInitialVersion(agent.getId());
            if (!versionResult.isSuccess()) {
                log.error("创建初始版本失败: agentId={}, error={}", agent.getId(), versionResult.getMessage());
                // 如果版本创建失败，回滚Agent创建（由于@Transactional注解，抛出异常会自动回滚）
                throw new RuntimeException("创建初始版本失败: " + versionResult.getMessage());
            }

            // 转换为VO
            AgentVO agentVO = convertToVO(agent);

            log.info("创建Agent成功: agentName={}, agentCode={}, creatorId={}, 初始版本已创建",
                request.getAgentName(), agentCode, currentUser.getId());
            return Result.success("创建Agent成功", agentVO);

        } catch (Exception e) {
            log.error("创建Agent失败: agentName={}, agentCode={}",
                request.getAgentName(), request.getAgentCode(), e);
            return Result.error("创建Agent失败: " + e.getMessage());
        }
    }

    /**
     * 获取Agent详情
     */
    public Result<AgentVO> getAgent(Long id) {
        try {
            Agent agent = agentMapper.selectById(id);
            if (agent == null) {
                return Result.error(ResultCode.AGENT_NOT_FOUND, "Agent不存在");
            }

            AgentVO agentVO = convertToVO(agent);

            return Result.success("获取Agent详情成功", agentVO);

        } catch (Exception e) {
            log.error("获取Agent详情失败: id={}", id, e);
            return Result.error("获取Agent详情失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID获取Agent实体（内部使用）
     */
    public Agent getAgentById(Long id) {
        return agentMapper.selectById(id);
    }

    /**
     * 更新Agent
     */
    @Transactional
    public Result<AgentVO> updateAgent(Long id, CreateAgentRequest request) {
        try {
            Agent agent = agentMapper.selectById(id);
            if (agent == null) {
                return Result.error(ResultCode.AGENT_NOT_FOUND, "Agent不存在");
            }

            // 检查Agent名称是否被其他Agent使用
            if (!agent.getAgentName().equals(request.getAgentName())) {
                Agent existingAgentByName = agentMapper.findByAgentName(request.getAgentName());
                if (existingAgentByName != null) {
                    return Result.error(ResultCode.AGENT_NAME_EXISTS, "Agent名称已存在，请使用其他名称");
                }
            }

            // 检查Agent编码是否被其他Agent使用
            if (!agent.getAgentCode().equals(request.getAgentCode())) {
                Agent existingAgent = agentMapper.findByAgentCode(request.getAgentCode());
                if (existingAgent != null) {
                    return Result.error(ResultCode.AGENT_CODE_EXISTS, "Agent编码已存在");
                }
            }

            // 更新Agent信息
            BeanUtils.copyProperties(request, agent);
            agentMapper.updateById(agent);

            AgentVO agentVO = convertToVO(agent);

            log.info("更新Agent成功: id={}, agentCode={}", id, request.getAgentCode());
            return Result.success("更新Agent成功", agentVO);

        } catch (Exception e) {
            log.error("更新Agent失败: id={}", id, e);
            return Result.error("更新Agent失败: " + e.getMessage());
        }
    }

    /**
     * 删除Agent
     */
    @Transactional
    public Result<String> deleteAgent(Long id) {
        try {
            Agent agent = agentMapper.selectById(id);
            if (agent == null) {
                return Result.error(ResultCode.AGENT_NOT_FOUND, "Agent不存在");
            }

            // 检查Agent状态，已发布的Agent不能删除
            if (agent.getStatus() == Agent.STATUS_PUBLISHED) {
                return Result.error(ResultCode.AGENT_STATUS_ERROR, "已发布的Agent不能删除");
            }

            // 使用MyBatis-Plus的逻辑删除
            agentMapper.deleteById(id);

            log.info("删除Agent成功: id={}, agentCode={}", id, agent.getAgentCode());
            return Result.success("删除Agent成功");

        } catch (Exception e) {
            log.error("删除Agent失败: id={}", id, e);
            return Result.error("删除Agent失败: " + e.getMessage());
        }
    }



    /**
     * 分页查询Agent列表
     */
    public Result<PageResult<AgentVO>> getAgentList(Long categoryId, Long businessTypeId,
                                                   Integer status, String keyword,
                                                   int page, int size, UserPrincipal userPrincipal) {
        try {
            Page<Agent> agentPage = new Page<>(page, size);

            // 根据用户角色决定是否过滤创建者
            Long creatorId = null;
            if (userPrincipal != null) {
                // 检查是否是超级管理员
                boolean isSuperAdmin = userPrincipal.getPermissions().contains("ROLE_ADMIN");
                if (!isSuperAdmin) {
                    // 普通用户只能查看自己创建的Agent
                    creatorId = userPrincipal.getId();
                }
                log.debug("用户权限检查: userId={}, roleCode={}, isSuperAdmin={}, permissions={}",
                         userPrincipal.getId(), userPrincipal.getRoleCode(), isSuperAdmin, userPrincipal.getPermissions());
            }

            IPage<Agent> result = agentMapper.selectPageWithDetails(agentPage, keyword, categoryId, null, status, creatorId);

            List<AgentVO> agentVOList = result.getRecords().stream()
                    .map(this::convertToVO)
                    .toList();

            PageResult<AgentVO> pageResult = PageResult.of(
                agentVOList, result.getTotal(), (long) page, (long) size);

            return Result.success(pageResult);

        } catch (Exception e) {
            log.error("查询Agent列表失败", e);
            return Result.error("查询Agent列表失败: " + e.getMessage());
        }
    }

    /**
     * 转换Agent实体为VO
     */
    private AgentVO convertToVO(Agent agent) {
        AgentVO vo = new AgentVO();
        BeanUtils.copyProperties(agent, vo);

        // 设置扩展字段
        vo.setAgentTypeName(vo.getAgentTypeName());
        vo.setStatusName(vo.getStatusName());

        // 获取发布版本信息
        try {
            AgentVersion publishedVersion = agentVersionMapper.findLatestPublishedByAgentId(agent.getId());
            if (publishedVersion != null) {
                agent.setPublishedVersion(publishedVersion.getVersionNumber());
                vo.setPublishedVersion(publishedVersion.getVersionNumber());
            }
        } catch (Exception e) {
            log.warn("获取Agent发布版本失败: agentId={}", agent.getId(), e);
        }

        // 获取当前版本信息（is_current=1）
        try {
            AgentVersion currentVersion = agentVersionMapper.findCurrentByAgentId(agent.getId());
            if (currentVersion != null) {
                vo.setLatestVersion(currentVersion.getVersionNumber());
            } else {
                // 如果没有当前版本记录，使用Agent表中的版本号
                vo.setLatestVersion(agent.getVersion());
            }
        } catch (Exception e) {
            log.warn("获取Agent当前版本失败: agentId={}", agent.getId(), e);
            // 如果获取失败，使用Agent表中的版本号作为备选
            vo.setLatestVersion(agent.getVersion());
        }

        // 设置审批状态名称
        if (agent.getApprovalStatus() != null) {
            String approvalStatusName = switch (agent.getApprovalStatus()) {
                case 0 -> "未提交";
                case 1 -> "审批中";
                case 2 -> "审批通过";
                case 3 -> "审批不通过";
                default -> "未知";
            };
            agent.setApprovalStatusName(approvalStatusName);
            vo.setApprovalStatusName(approvalStatusName);
        }

        return vo;
    }

    /**
     * 发布Agent
     */
    @Transactional
    public Result<String> publishAgent(Long id) {
        try {
            Agent agent = agentMapper.selectById(id);
            if (agent == null) {
                return Result.error(ResultCode.AGENT_NOT_FOUND, "Agent不存在");
            }

            // 检查Agent状态，只有草稿和测试中的Agent可以发布
            if (agent.getStatus() != Agent.STATUS_DRAFT && agent.getStatus() != Agent.STATUS_TESTING) {
                return Result.error(ResultCode.AGENT_STATUS_ERROR, "只有草稿或测试中的Agent可以发布");
            }

            // 更新状态为已发布
            agent.setStatus(Agent.STATUS_PUBLISHED);
            agent.setPublishedTime(LocalDateTime.now());
            agentMapper.updateById(agent);

            log.info("发布Agent成功: id={}, agentCode={}", id, agent.getAgentCode());
            return Result.success("Agent发布成功");

        } catch (Exception e) {
            log.error("发布Agent失败: id={}", id, e);
            return Result.error("发布Agent失败: " + e.getMessage());
        }
    }

    /**
     * 下线Agent
     */
    @Transactional
    public Result<String> unpublishAgent(Long id) {
        try {
            Agent agent = agentMapper.selectById(id);
            if (agent == null) {
                return Result.error(ResultCode.AGENT_NOT_FOUND, "Agent不存在");
            }

            // 检查Agent状态，只有已发布的Agent可以下线
            if (agent.getStatus() != Agent.STATUS_PUBLISHED) {
                return Result.error(ResultCode.AGENT_STATUS_ERROR, "只有已发布的Agent可以下线");
            }

            // 更新状态为已下线
            agent.setStatus(Agent.STATUS_OFFLINE);
            agentMapper.updateById(agent);

            log.info("下线Agent成功: id={}, agentCode={}", id, agent.getAgentCode());
            return Result.success("Agent下线成功");

        } catch (Exception e) {
            log.error("下线Agent失败: id={}", id, e);
            return Result.error("下线Agent失败: " + e.getMessage());
        }
    }

    /**
     * 克隆Agent
     */
    @Transactional
    public Result<AgentVO> cloneAgent(Long id, String newName) {
        try {
            Agent originalAgent = agentMapper.selectById(id);
            if (originalAgent == null) {
                return Result.error(ResultCode.AGENT_NOT_FOUND, "Agent不存在");
            }

            UserPrincipal currentUser = getCurrentUser();

            // 创建新的Agent
            Agent clonedAgent = new Agent();
            BeanUtils.copyProperties(originalAgent, clonedAgent);

            // 设置新的属性
            clonedAgent.setId(null); // 清空ID，让数据库自动生成
            clonedAgent.setAgentName(newName);
            clonedAgent.setAgentCode(generateUniqueAgentCode(originalAgent.getAgentCode()));
            clonedAgent.setStatus(Agent.STATUS_DRAFT); // 克隆的Agent默认为草稿状态
            clonedAgent.setCreatorId(currentUser.getId());
            clonedAgent.setPublishedTime(null); // 清空发布时间

            agentMapper.insert(clonedAgent);

            // 创建初始版本
            Result<com.sinoair.agent.entity.AgentVersion> versionResult = agentVersionService.createInitialVersion(clonedAgent.getId());
            if (!versionResult.isSuccess()) {
                log.error("创建克隆Agent初始版本失败: agentId={}, error={}", clonedAgent.getId(), versionResult.getMessage());
                // 如果版本创建失败，回滚Agent创建（由于@Transactional注解，抛出异常会自动回滚）
                throw new RuntimeException("创建初始版本失败: " + versionResult.getMessage());
            }

            AgentVO agentVO = convertToVO(clonedAgent);

            log.info("克隆Agent成功: originalId={}, newId={}, newAgentCode={}, 初始版本已创建",
                    id, clonedAgent.getId(), clonedAgent.getAgentCode());
            return Result.success("Agent克隆成功", agentVO);

        } catch (Exception e) {
            log.error("克隆Agent失败: id={}", id, e);
            return Result.error("克隆Agent失败: " + e.getMessage());
        }
    }

    /**
     * 生成唯一的Agent编码（克隆时使用，统一使用6位随机编码）
     */
    private String generateUniqueAgentCode(String originalCode) {
        // 克隆时也使用统一的6位随机编码生成逻辑
        return generateUniqueAgentCode();
    }

    /**
     * 生成唯一的Agent编码（6位随机数字和字母）
     */
    private String generateUniqueAgentCode() {
        String agentCode;
        int maxAttempts = 100; // 最大尝试次数，防止无限循环
        int attempts = 0;

        do {
            agentCode = generateRandomCode();
            attempts++;

            // 如果尝试次数过多，抛出异常
            if (attempts >= maxAttempts) {
                log.error("生成唯一Agent编码失败，已尝试{}次", attempts);
                throw new RuntimeException("生成唯一Agent编码失败，请稍后重试");
            }

            // 记录重试情况
            if (attempts > 1) {
                log.debug("Agent编码{}已存在，正在重新生成，第{}次尝试", agentCode, attempts);
            }
        } while (agentMapper.findByAgentCode(agentCode) != null);

        log.debug("成功生成唯一Agent编码: {}，尝试次数: {}", agentCode, attempts);
        return agentCode;
    }

    /**
     * 生成6位随机编码（数字+大写字母）
     */
    private String generateRandomCode() {
        return AgentCodeGenerator.generateRandomCode();
    }

    /**
     * 获取公开API的Agent列表
     */
    public IPage<Agent> getPublicAgentList(int page, int size, String agentId) {
        Page<Agent> agentPage = new Page<>(page, size);
        QueryWrapper<Agent> queryWrapper = new QueryWrapper<>();

        // 只返回已发布的Agent
        queryWrapper.eq("status", Agent.STATUS_PUBLISHED);

        // 如果指定了agentId，则按agentCode查询
        if (agentId != null && !agentId.trim().isEmpty()) {
            queryWrapper.eq("agent_code", agentId);
        }

        queryWrapper.orderByDesc("created_time");

        return agentMapper.selectPage(agentPage, queryWrapper);
    }

    /**
     * 根据Agent编码获取Agent
     */
    public Agent getByAgentCode(String agentCode) {
        return agentMapper.findByAgentCode(agentCode);
    }

    /**
     * 检查Agent名称是否存在
     */
    public Result<Boolean> checkAgentNameExists(String agentName, Long excludeId) {
        try {
            Agent existingAgent = agentMapper.findByAgentName(agentName);
            boolean exists = existingAgent != null && (excludeId == null || !existingAgent.getId().equals(excludeId));
            return Result.success(exists);
        } catch (Exception e) {
            log.error("检查Agent名称是否存在失败: agentName={}, excludeId={}", agentName, excludeId, e);
            return Result.error("检查Agent名称失败: " + e.getMessage());
        }
    }

    /**
     * 获取当前用户
     */
    private UserPrincipal getCurrentUser() {
        return (UserPrincipal) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
    }
}
