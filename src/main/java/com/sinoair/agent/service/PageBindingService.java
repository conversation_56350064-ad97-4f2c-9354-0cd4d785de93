package com.sinoair.agent.service;

import com.sinoair.agent.entity.PageBinding;

import java.util.List;
import java.util.Map;

/**
 * 页面绑定服务接口
 */
public interface PageBindingService {

    /**
     * 获取所有绑定配置
     */
    List<PageBinding> list();

    /**
     * 根据ID获取绑定配置
     */
    PageBinding getById(Long id);

    /**
     * 保存绑定配置
     */
    boolean save(PageBinding entity);

    /**
     * 更新绑定配置
     */
    boolean updateById(PageBinding entity);

    /**
     * 删除绑定配置
     */
    boolean removeById(Long id);

    /**
     * 根据URL模式查找匹配的绑定配置
     */
    List<PageBinding> findByUrlPattern(String url);

    /**
     * 根据模板ID查找绑定配置
     */
    List<PageBinding> findByTemplateId(Long templateId);

    /**
     * 获取所有启用的绑定配置
     */
    List<PageBinding> getActiveBindings();

    /**
     * 获取插件自动填充数据
     * @param currentUrl 当前页面URL
     * @param agentId Agent ID
     * @return 自动填充数据
     */
    Map<String, Object> getAutoFillData(String currentUrl, Long agentId);

    /**
     * 查找多步骤绑定配置
     * @param currentUrl 当前页面URL
     * @param agentId Agent ID
     * @return 多步骤绑定配置
     */
    Map<String, Object> getMultiStepAutoFillData(String currentUrl, Long agentId);

    /**
     * 根据父绑定ID查找子步骤
     * @param parentBindingId 父绑定ID
     * @return 子步骤列表
     */
    List<PageBinding> findSubStepsByParentId(Long parentBindingId);

    /**
     * 保存多步骤绑定配置
     * @param mainBinding 主绑定配置
     * @param subSteps 子步骤列表
     * @return 是否保存成功
     */
    boolean saveMultiStepBinding(PageBinding mainBinding, List<PageBinding> subSteps);

    /**
     * 删除绑定配置（包括子步骤）
     * @param id 绑定配置ID
     * @return 是否删除成功
     */
    boolean deleteBindingWithSubSteps(Long id);
}
