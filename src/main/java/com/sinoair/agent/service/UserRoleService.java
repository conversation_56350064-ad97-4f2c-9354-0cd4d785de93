package com.sinoair.agent.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sinoair.agent.entity.UserRole;

import java.util.List;

/**
 * 用户角色关联服务接口
 *
 * <AUTHOR> Team
 */
public interface UserRoleService extends IService<UserRole> {

    /**
     * 根据用户ID删除用户角色关联
     */
    boolean deleteByUserId(Long userId);

    /**
     * 根据角色ID删除用户角色关联
     */
    boolean deleteByRoleId(Long roleId);

    /**
     * 根据用户ID查询角色ID列表
     */
    List<Long> getRoleIdsByUserId(Long userId);

    /**
     * 根据角色ID查询用户ID列表
     */
    List<Long> getUserIdsByRoleId(Long roleId);

    /**
     * 为用户分配角色
     */
    boolean assignRolesToUser(Long userId, List<Long> roleIds);

    /**
     * 批量保存用户角色关联
     */
    boolean saveBatch(List<UserRole> userRoles);
}
