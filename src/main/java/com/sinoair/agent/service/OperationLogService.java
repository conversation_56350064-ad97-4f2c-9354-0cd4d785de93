package com.sinoair.agent.service;

import com.sinoair.agent.entity.OperationLog;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 操作日志服务接口
 *
 * <AUTHOR> Team
 */
public interface OperationLogService {

    /**
     * 保存操作日志
     */
    void saveLog(OperationLog operationLog);

    /**
     * 异步保存操作日志
     */
    void saveLogAsync(OperationLog operationLog);

    /**
     * 根据ID查询日志
     */
    OperationLog findById(String id);

    /**
     * 分页查询操作日志
     */
    Page<OperationLog> findLogs(Pageable pageable);

    /**
     * 根据用户ID分页查询日志
     */
    Page<OperationLog> findLogsByUserId(Long userId, Pageable pageable);

    /**
     * 根据用户名分页查询日志
     */
    Page<OperationLog> findLogsByUsername(String username, Pageable pageable);

    /**
     * 根据模块分页查询日志
     */
    Page<OperationLog> findLogsByModule(String module, Pageable pageable);

    /**
     * 根据操作类型分页查询日志
     */
    Page<OperationLog> findLogsByOperationType(String operationType, Pageable pageable);

    /**
     * 根据状态分页查询日志
     */
    Page<OperationLog> findLogsByStatus(String status, Pageable pageable);

    /**
     * 根据时间范围分页查询日志
     */
    Page<OperationLog> findLogsByTimeRange(LocalDateTime startTime, LocalDateTime endTime, Pageable pageable);

    /**
     * 复合条件查询日志
     */
    Page<OperationLog> findLogsByConditions(Long userId, String username, String module, 
                                           String operationType, String status, 
                                           LocalDateTime startTime, LocalDateTime endTime, 
                                           Pageable pageable);

    /**
     * 获取最近的操作日志
     */
    List<OperationLog> getRecentLogs(int limit);

    /**
     * 获取指定用户最近的操作日志
     */
    List<OperationLog> getRecentLogsByUserId(Long userId, int limit);

    /**
     * 获取操作统计信息
     */
    Map<String, Object> getOperationStatistics();

    /**
     * 获取指定时间范围内的操作统计
     */
    Map<String, Object> getOperationStatistics(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 获取用户操作统计
     */
    Map<String, Object> getUserOperationStatistics(Long userId);

    /**
     * 获取模块操作统计
     */
    Map<String, Object> getModuleOperationStatistics();

    /**
     * 获取操作类型统计
     */
    Map<String, Object> getOperationTypeStatistics();

    /**
     * 获取每日操作统计（最近30天）
     */
    List<Map<String, Object>> getDailyOperationStatistics(int days);

    /**
     * 获取慢操作日志
     */
    Page<OperationLog> getSlowOperations(Long thresholdMs, Pageable pageable);

    /**
     * 清理过期日志
     */
    void cleanExpiredLogs(int retentionDays);

    /**
     * 导出日志数据
     */
    String exportLogs(Long userId, String module, String operationType, 
                     LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 批量删除日志
     */
    void deleteLogs(List<String> logIds);

    /**
     * 根据条件删除日志
     */
    void deleteLogsByConditions(LocalDateTime beforeTime);
}
