package com.sinoair.agent.service;

import com.sinoair.agent.common.Result;
import com.sinoair.agent.common.ResultCode;
import com.sinoair.agent.dto.request.RecognitionRequest;
import com.sinoair.agent.dto.response.RecognitionResult;
import com.sinoair.agent.dto.response.FileUploadResponse;
import com.sinoair.agent.entity.*;
import com.sinoair.agent.mapper.AgentMapper;
import com.sinoair.agent.mapper.RecognitionRecordMapper;
import com.sinoair.agent.mapper.UploadedFileMapper;
import com.sinoair.agent.security.UserPrincipal;
import com.sinoair.agent.service.llm.LLMService;
import com.sinoair.agent.util.LLMUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * 识别服务
 *
 * <AUTHOR> Team
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RecognitionService {

    private final AgentMapper agentMapper;
    private final UploadedFileMapper fileMapper;
    private final RecognitionRecordMapper recordMapper;
    private final LLMService llmService;
    private final ObjectMapper objectMapper;
    private final FileService fileService;

    private final AgentDebugHistoryService debugHistoryService;
    private final AgentVersionService agentVersionService;

    /**
     * 提交识别任务
     */
    @Transactional
    public Result<RecognitionTaskResponse> submitRecognitionTask(RecognitionRequest request) {
        try {
            // 验证输入参数
            if (request.getAgentId() == null && (request.getAgentCode() == null || request.getAgentCode().trim().isEmpty())) {
                return Result.error("Agent ID或Agent编码必须提供其中之一");
            }

            if (request.getFileId() == null && (request.getInputText() == null || request.getInputText().trim().isEmpty())) {
                return Result.error("文件ID或输入文本必须提供其中之一");
            }
            if (request.getModel() == null || request.getModel().trim().isEmpty()) {
                return Result.error("模型名称必须提供");
            }

            if (request.getPromptTemplate() == null || request.getPromptTemplate().trim().isEmpty()) {
                return Result.error("提示词模板必须提供");
            }

            // 获取Agent
            Agent agent = null;
            if (request.getAgentId() != null) {
                agent = agentMapper.selectById(request.getAgentId());
            } else if (request.getAgentCode() != null) {
                agent = agentMapper.findByAgentCode(request.getAgentCode());
            }

            if (agent == null) {
                return Result.error(ResultCode.AGENT_NOT_FOUND, "Agent不存在");
            }


            // 验证文件（如果是文件模式）
            if (request.getFileId() != null && !fileMapper.existsByIdAndNotDeleted(request.getFileId())) {
                return Result.error(ResultCode.FILE_NOT_FOUND, "文件不存在");
            }

            // 获取当前用户
            UserPrincipal currentUser = getCurrentUser();

            // 创建识别记录
            RecognitionRecord record = new RecognitionRecord();
            record.setAgentId(agent.getId());
            // 如果是纯文本的模式 则将文件ID设为-1
            record.setFileId(request.getFileId() == null ? -1 : request.getFileId());

            record.setBusinessTypeId(request.getBusinessTypeId() != null ? request.getBusinessTypeId() : agent.getBusinessTypeId());
            record.setInputParams(LLMUtils.getSystemPrompt(request.getPromptTemplate(), agent.getJsonTemplate()));
            record.setStatus(RecognitionRecord.STATUS_PROCESSING);
            record.setUserId(currentUser.getId());
            record.setSessionId(UUID.randomUUID().toString());
            record.setCreatedTime(LocalDateTime.now());

            recordMapper.insert(record);

            RecognitionRecord savedRecord = record;
            // 执行识别任务
            executeRecognitionAsync(agent, request, savedRecord);

            // 构建响应
            RecognitionTaskResponse response = RecognitionTaskResponse.builder()
                    .recordId(savedRecord.getId())
                    .status(RecognitionRecord.STATUS_PROCESSING == savedRecord.getStatus() ? "处理中" :
                            (RecognitionRecord.STATUS_SUCCESS == savedRecord.getStatus() ? "成功" : "失败"))
                    .estimatedTime(savedRecord.getProcessingTime())
                    .result(formatJsonResult(savedRecord.getRecognitionResult()))
                    .queuePosition(1)
                    .progressUrl("/api/v1/recognition/records/" + savedRecord.getId() + "/progress")
                    .build();


            saveDebugHistory(request, agent, savedRecord, currentUser);

            log.info("识别任务已提交: recordId={}, agentId={}, fileId={}, hasText={}",
                    savedRecord.getId(), agent.getId(), request.getFileId(), request.getInputText() != null);

            return Result.success("识别任务已提交", response);

        } catch (Exception e) {
            log.error("提交识别任务失败", e);
            return Result.error("提交识别任务失败: " + e.getMessage());
        }
    }

    private void saveDebugHistory(RecognitionRequest request, Agent agent, RecognitionRecord savedRecord, UserPrincipal currentUser) {
        log.info("保存调试记录");
        // 保存调试记录
        AgentDebugHistory debugHistory = new AgentDebugHistory();
        debugHistory.setAgentId(agent.getId());
        // 保存版本号
        Result<List<AgentVersion>> versionList = agentVersionService.getVersionList(agent.getId());
        versionList.getData().stream().filter(v -> v.getIsCurrent() == 1).findFirst().ifPresent(v -> {
            debugHistory.setVersionId(v.getId());
        });

        debugHistory.setModelName(request.getModel());
        debugHistory.setModelConfig(convertParamsToJson(request.getParams()));

        // 构建输入数据JSON，包含类型和内容信息
        String inputDataJson = buildInputDataJson(request);
        debugHistory.setInputData(inputDataJson);

        debugHistory.setOutputData(savedRecord.getRecognitionResult());
        debugHistory.setStatus(savedRecord.getStatus());
        debugHistory.setErrorMessage(savedRecord.getErrorMessage());
        debugHistory.setResponseTime(savedRecord.getProcessingTime());
        // debugHistory.setTokensUsed(savedRecord.getTokensUsed());
        // debugHistory.setCost(savedRecord.getCost());
        debugHistory.setCreatorId(currentUser.getId());
        debugHistory.setCreatedTime(LocalDateTime.now());
        debugHistory.setSuccess(savedRecord.getStatus() == RecognitionRecord.STATUS_SUCCESS);
        // debugHistory.setAgentName(agent.getName());
        debugHistory.setUserName(currentUser.getRealName());

        debugHistoryService.saveDebugHistory(debugHistory);
    }

    /**
     * 构建输入数据JSON
     */
    private String buildInputDataJson(RecognitionRequest request) {
        try {
            Map<String, Object> inputData = new HashMap<>();

            // -1 是纯文本模式
            if (request.getFileId() != null && request.getFileId() < 0 && request.getInputText() != null && !request.getInputText().trim().isEmpty()) {
                // 文本模式
                inputData.put("type", "text");
                inputData.put("content", request.getInputText());
                inputData.put("length", request.getInputText().length());
            } else if (request.getFileId() != null && request.getFileId() > 0) {

                // 正常的ID是文件模式
                inputData.put("type", "file");
                inputData.put("fileId", request.getFileId());

                // 获取文件详细信息
                Result<UploadedFile> fileResult = fileService.getFileInfo(request.getFileId());
                if (fileResult.isSuccess()) {
                    UploadedFile file = fileResult.getData();
                    inputData.put("fileName", file.getOriginalName());
                    inputData.put("fileSize", file.getFileSize());
                    inputData.put("fileType", file.getFileType());
                    inputData.put("mimeType", file.getMimeType());
                    inputData.put("previewUrl", "/api/v1/files/" + file.getId() + "/preview");
                    inputData.put("downloadUrl", "/api/v1/files/" + file.getId() + "/download");

                    // 根据MIME类型确定展示类型
                    String displayType = determineDisplayType(file.getMimeType(), file.getFileType());
                    inputData.put("displayType", displayType);
                }
            } else {
                // 空输入
                inputData.put("type", "empty");
                inputData.put("content", "");
            }

            return objectMapper.writeValueAsString(inputData);
        } catch (Exception e) {
            log.error("构建输入数据JSON失败", e);
            // 降级处理
            if (request.getInputText() != null) {
                return request.getInputText();
            } else {
                return "文件ID: " + request.getFileId();
            }
        }
    }

    /**
     * 根据MIME类型确定展示类型
     */
    private String determineDisplayType(String mimeType, String fileType) {
        if (mimeType == null) {
            mimeType = "";
        }

        // 图片类型
        if (mimeType.startsWith("image/")) {
            return "image";
        }

        // PDF类型
        if (mimeType.equals("application/pdf") || "pdf".equalsIgnoreCase(fileType)) {
            return "pdf";
        }

        // 文本类型
        if (mimeType.startsWith("text/") ||
                "txt".equalsIgnoreCase(fileType) ||
                "csv".equalsIgnoreCase(fileType)) {
            return "text";
        }

        // Word文档
        if (mimeType.contains("word") ||
                "doc".equalsIgnoreCase(fileType) ||
                "docx".equalsIgnoreCase(fileType)) {
            return "document";
        }

        // Excel文档
        if (mimeType.contains("excel") || mimeType.contains("spreadsheet") ||
                "xls".equalsIgnoreCase(fileType) ||
                "xlsx".equalsIgnoreCase(fileType)) {
            return "document";
        }

        // 默认为文档类型
        return "document";
    }

    /**
     * 查询识别结果
     */
    public Result<RecognitionRecord> getRecognitionResult(Long recordId) {
        try {
            RecognitionRecord record = recordMapper.selectById(recordId);
            if (record == null) {
                return Result.error(ResultCode.RECOGNITION_RESULT_NOT_FOUND, "识别记录不存在");
            }

            // 格式化识别结果JSON，防止前端解析失败
            if (record.getRecognitionResult() != null) {
                record.setRecognitionResult(formatJsonResult(record.getRecognitionResult()));
            }

            return Result.success(record);

        } catch (Exception e) {
            log.error("查询识别结果失败: recordId={}", recordId, e);
            return Result.error("查询识别结果失败: " + e.getMessage());
        }
    }


    /**
     * 异步执行识别任务
     */
    private RecognitionRecord executeRecognitionAsync(Agent agent, RecognitionRequest request, RecognitionRecord record) {
        // 这里应该使用异步任务执行，简化处理直接在当前线程执行
        try {
            // 把页面的传参赋值给 agent
            if(request.getAgentType()!=null){
                agent.setAgentType(request.getAgentType());
            }
            // 调用LLM服务执行识别
            RecognitionResult result = llmService.recognize(agent, request);




            // 更新识别记录
            record.setRecognitionResult(result.getResult());
            record.setConfidenceScore(result.getConfidence());
            record.setProcessingTime(result.getProcessingTime());
            record.setStatus(result.isSuccess() ? RecognitionRecord.STATUS_SUCCESS : RecognitionRecord.STATUS_FAILED);
            record.setErrorMessage(result.getErrorMessage());
            record.setCompletedTime(LocalDateTime.now());

            recordMapper.updateById(record);

            log.info("识别任务完成: recordId={}, success={}", record.getId(), result.isSuccess());

        } catch (Exception e) {
            log.error("执行识别任务失败: recordId={}", record.getId(), e);

            // 更新失败状态
            record.setStatus(RecognitionRecord.STATUS_FAILED);
            record.setErrorMessage("识别执行失败: " + e.getMessage());
            record.setCompletedTime(LocalDateTime.now());
            recordMapper.updateById(record);
        }
        return record;
    }

    /**
     * 转换参数为JSON字符串
     */
    private String convertParamsToJson(RecognitionRequest.RecognitionParams params) {
        if (params == null) {
            return "{}";
        }
        // 简化处理，实际应该使用JSON序列化
        return String.format("{\"temperature\":%s,\"maxTokens\":%s,\"enableCache\":%s}",
                params.getTemperature(), params.getMaxTokens(), params.getEnableCache());
    }

    /**
     * 格式化JSON结果，防止页面解析失败
     */
    private String formatJsonResult(String jsonResult) {
        if (jsonResult == null || jsonResult.trim().isEmpty()) {
            return null;
        }

        try {
            // 尝试解析JSON以验证格式
            Object parsed = objectMapper.readValue(jsonResult, Object.class);
            // 重新序列化以确保格式正确
            return objectMapper.writeValueAsString(parsed);
        } catch (Exception e) {
            log.warn("JSON格式验证失败，返回原始字符串: {}", e.getMessage());
            // 如果不是有效的JSON，返回null避免前端解析错误
            return null;
        }
    }


    public Result<RecognitionResult> submitRecognitionTaskWithFileByCode(MultipartFile file, String agentCode) {
        try {
            // 验证Agent
            Agent agent = agentMapper.findByAgentCode(agentCode);
            if (agent == null) {
                return Result.error(ResultCode.AGENT_NOT_FOUND, "Agent不存在: " + agentCode);
            }


            // 上传文件
            Result<FileUploadResponse> uploadResult = fileService.uploadFile(file, "recognition");
            if (!uploadResult.isSuccess()) {
                return Result.error(uploadResult.getCode(), uploadResult.getMessage());
            }

            // 调用原有的识别方法
            UploadedFile uploadFile = new UploadedFile();
            Long fileId = uploadResult.getData().getFileId();
            uploadFile.setId(fileId);
            RecognitionResult recognize = llmService.recognize(agent, uploadFile,getCurrentUser().getId());


            return Result.success("识别任务完成", recognize);

        } catch (Exception e) {
            log.error("提交文件识别任务失败", e);
            return Result.error("文件识别任务失败: " + e.getMessage());
        }
    }

    /**
     * 提交识别任务(文件上传)
     */
    @Transactional
    public Result<RecognitionTaskResponse> submitRecognitionTaskWithFile(MultipartFile file, String agentCode,
                                                                         Integer agentType,
                                                                         String model, String parameters, String promptTemplate) {
        try {
            // 验证Agent
            Agent agent = agentMapper.findByAgentCode(agentCode);
            if (agent == null) {
                return Result.error(ResultCode.AGENT_NOT_FOUND, "Agent不存在: " + agentCode);
            }


            // 上传文件
            Result<FileUploadResponse> uploadResult = fileService.uploadFile(file, "recognition");
            if (!uploadResult.isSuccess()) {
                return Result.error(uploadResult.getCode(), uploadResult.getMessage());
            }

            RecognitionRequest.RecognitionParams params = null;
            if (parameters != null && !parameters.trim().isEmpty()) {
                try {
                    params = objectMapper.readValue(parameters, RecognitionRequest.RecognitionParams.class);
                } catch (Exception e) {
                    log.warn("解析识别参数失败，使用默认参数: {}", e.getMessage());
                }
            }

            // 构建识别请求
            RecognitionRequest request = new RecognitionRequest();
            request.setAgentId(agent.getId());
            request.setFileId(uploadResult.getData().getFileId());
            request.setBusinessTypeId(agent.getBusinessTypeId());
            request.setModel(model);
            request.setParams(params);
            // 设置平台类型
            if (agentType != null) {
                request.setAgentType(agentType);
            }
            // 设置自定义提示词模板
            if (promptTemplate != null && !promptTemplate.trim().isEmpty()) {
                request.setPromptTemplate(promptTemplate);
            }

            // 调用原有的识别方法
            return submitRecognitionTask(request);

        } catch (Exception e) {
            log.error("提交文件识别任务失败", e);
            return Result.error("提交文件识别任务失败: " + e.getMessage());
        }
    }

    /**
     * 获取当前用户
     */
    private UserPrincipal getCurrentUser() {
        return (UserPrincipal) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
    }


    /**
     * 识别任务响应
     */
    @lombok.Data
    @lombok.Builder
    public static class RecognitionTaskResponse {
        private Long recordId;
        private String status;
        private String result;
        private Integer estimatedTime;
        private Integer queuePosition;
        private String progressUrl;
    }
}
