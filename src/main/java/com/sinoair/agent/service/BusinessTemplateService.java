package com.sinoair.agent.service;

import com.sinoair.agent.entity.BusinessTemplate;

import java.util.List;

/**
 * 业务模板服务接口
 */
public interface BusinessTemplateService {

    /**
     * 获取所有模板
     */
    List<BusinessTemplate> list();

    /**
     * 根据ID获取模板
     */
    BusinessTemplate getById(Long id);

    /**
     * 保存模板
     */
    boolean save(BusinessTemplate entity);

    /**
     * 更新模板
     */
    boolean updateById(BusinessTemplate entity);

    /**
     * 删除模板
     */
    boolean removeById(Long id);

    /**
     * 根据分类获取模板列表
     */
    List<BusinessTemplate> getByCategory(String category);

    /**
     * 根据模板编码获取模板
     */
    BusinessTemplate getByCode(String templateCode);

    /**
     * 获取所有启用的模板
     */
    List<BusinessTemplate> getActiveTemplates();
}
