package com.sinoair.agent.service;

import com.sinoair.agent.common.Result;
import com.sinoair.agent.config.OcrConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.sourceforge.tess4j.ITesseract;
import net.sourceforge.tess4j.Tesseract;
import net.sourceforge.tess4j.TesseractException;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;

/**
 * OCR服务类
 * 使用Tesseract进行图片文字识别
 * 
 * <AUTHOR> Team
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OcrService {

    private final OcrConfig ocrConfig;

    /**
     * 识别图片中的文字
     * 
     * @param imageData 图片数据
     * @param fileName 文件名
     * @return 识别结果
     */
    public Result<String> recognizeText(byte[] imageData, String fileName) {
        try {
            log.info("开始OCR识别: fileName={}, size={}", fileName, imageData.length);

            // 验证文件大小
            if (imageData.length > ocrConfig.getMaxFileSize() * 1024 * 1024) {
                return Result.error("图片文件过大，最大支持" + ocrConfig.getMaxFileSize() + "MB");
            }

            // 验证文件格式
            if (!isSupportedFormat(fileName)) {
                return Result.error("不支持的图片格式，支持的格式：" + String.join(", ", ocrConfig.getSupportedFormats()));
            }

            // 读取图片
            BufferedImage image = ImageIO.read(new ByteArrayInputStream(imageData));
            if (image == null) {
                return Result.error("无法读取图片文件，请检查文件是否损坏");
            }

            // 图片预处理
            if (ocrConfig.getTesseract().getPreprocessing().getEnabled()) {
                image = preprocessImage(image);
            }

            // 配置Tesseract
            ITesseract tesseract = configureTesseract();

            // 执行OCR识别
            String recognizedText = tesseract.doOCR(image);
            
            if (recognizedText == null || recognizedText.trim().isEmpty()) {
                return Result.error("未能识别出文字内容，请检查图片质量");
            }

            // 清理识别结果
            recognizedText = cleanRecognizedText(recognizedText);

            log.info("OCR识别成功: fileName={}, 识别文字长度={}", fileName, recognizedText.length());
            return Result.success("OCR识别成功", recognizedText);

        } catch (TesseractException e) {
            log.error("Tesseract OCR识别失败: fileName={}", fileName, e);
            return Result.error("OCR识别失败: " + e.getMessage());
        } catch (IOException e) {
            log.error("图片读取失败: fileName={}", fileName, e);
            return Result.error("图片读取失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("OCR识别异常: fileName={}", fileName, e);
            return Result.error("OCR识别异常: " + e.getMessage());
        }
    }

    /**
     * 检查是否为支持的图片格式
     */
    private boolean isSupportedFormat(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return false;
        }
        
        String extension = getFileExtension(fileName).toLowerCase();
        return ocrConfig.getSupportedFormats().contains(extension);
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String fileName) {
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex == -1 || lastDotIndex == fileName.length() - 1) {
            return "";
        }
        return fileName.substring(lastDotIndex + 1);
    }

    /**
     * 配置Tesseract
     */
    private ITesseract configureTesseract() {
        ITesseract tesseract = new Tesseract();
        
        OcrConfig.TesseractConfig config = ocrConfig.getTesseract();
        
        // 设置数据路径
        if (config.getDataPath() != null && !config.getDataPath().isEmpty()) {
            tesseract.setDatapath(config.getDataPath());
        }
        
        // 设置语言
        tesseract.setLanguage(config.getLanguage());
        
        // 设置OCR引擎模式
        tesseract.setOcrEngineMode(config.getOem());
        
        // 设置页面分割模式
        tesseract.setPageSegMode(config.getPsm());
        
        log.debug("Tesseract配置: language={}, oem={}, psm={}", 
                config.getLanguage(), config.getOem(), config.getPsm());
        
        return tesseract;
    }

    /**
     * 图片预处理
     */
    private BufferedImage preprocessImage(BufferedImage originalImage) {
        OcrConfig.PreprocessingConfig config = ocrConfig.getTesseract().getPreprocessing();
        BufferedImage processedImage = originalImage;
        
        try {
            // 缩放图片
            if (config.getScaleFactor() != null && config.getScaleFactor() != 1.0) {
                processedImage = scaleImage(processedImage, config.getScaleFactor());
            }
            
            // 灰度处理
            if (config.getGrayscale()) {
                processedImage = convertToGrayscale(processedImage);
            }
            
            // 降噪处理（简单的中值滤波）
            if (config.getDenoise()) {
                processedImage = denoiseImage(processedImage);
            }
            
            log.debug("图片预处理完成: 原始尺寸={}x{}, 处理后尺寸={}x{}", 
                    originalImage.getWidth(), originalImage.getHeight(),
                    processedImage.getWidth(), processedImage.getHeight());
            
        } catch (Exception e) {
            log.warn("图片预处理失败，使用原始图片: {}", e.getMessage());
            return originalImage;
        }
        
        return processedImage;
    }

    /**
     * 缩放图片
     */
    private BufferedImage scaleImage(BufferedImage originalImage, double scaleFactor) {
        int newWidth = (int) (originalImage.getWidth() * scaleFactor);
        int newHeight = (int) (originalImage.getHeight() * scaleFactor);
        
        BufferedImage scaledImage = new BufferedImage(newWidth, newHeight, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = scaledImage.createGraphics();
        g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BICUBIC);
        g2d.drawImage(originalImage, 0, 0, newWidth, newHeight, null);
        g2d.dispose();
        
        return scaledImage;
    }

    /**
     * 转换为灰度图
     */
    private BufferedImage convertToGrayscale(BufferedImage originalImage) {
        BufferedImage grayscaleImage = new BufferedImage(
                originalImage.getWidth(), originalImage.getHeight(), BufferedImage.TYPE_BYTE_GRAY);
        Graphics2D g2d = grayscaleImage.createGraphics();
        g2d.drawImage(originalImage, 0, 0, null);
        g2d.dispose();
        return grayscaleImage;
    }

    /**
     * 简单降噪处理
     */
    private BufferedImage denoiseImage(BufferedImage originalImage) {
        // 这里实现简单的降噪算法，实际项目中可以使用更复杂的算法
        return originalImage;
    }

    /**
     * 清理识别结果
     */
    private String cleanRecognizedText(String text) {
        if (text == null) {
            return "";
        }
        
        // 移除多余的空白字符
        text = text.replaceAll("\\s+", " ");
        
        // 移除首尾空白
        text = text.trim();
        
        return text;
    }

    /**
     * 检查OCR服务是否可用
     */
    public boolean isAvailable() {
        try {
            ITesseract tesseract = configureTesseract();
            // 创建一个简单的测试图片
            BufferedImage testImage = new BufferedImage(100, 50, BufferedImage.TYPE_INT_RGB);
            Graphics2D g2d = testImage.createGraphics();
            g2d.setColor(Color.WHITE);
            g2d.fillRect(0, 0, 100, 50);
            g2d.setColor(Color.BLACK);
            g2d.drawString("TEST", 10, 30);
            g2d.dispose();
            
            tesseract.doOCR(testImage);
            return true;
        } catch (Exception e) {
            log.warn("OCR服务不可用: {}", e.getMessage());
            return false;
        }
    }
}
