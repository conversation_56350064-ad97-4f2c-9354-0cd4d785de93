package com.sinoair.agent.service;

import com.sinoair.agent.common.Result;
import com.sinoair.agent.common.ResultCode;
import com.sinoair.agent.config.MinioProperties;
import com.sinoair.agent.dto.response.FileUploadResponse;
import com.sinoair.agent.dto.response.MinioUploadResponse;
import com.sinoair.agent.dto.response.UserVO;
import com.sinoair.agent.entity.ApiKey;
import com.sinoair.agent.entity.UploadedFile;
import com.sinoair.agent.entity.User;
import com.sinoair.agent.mapper.UploadedFileMapper;
import com.sinoair.agent.security.ApiKeyAuthenticationToken;
import com.sinoair.agent.security.UserPrincipal;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.DigestUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

/**
 * 文件服务
 * 
 * <AUTHOR> Team
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FileService {

    private final UploadedFileMapper fileMapper;
    private final MinioProperties minioProperties;
    private final UserService userService;

    @Autowired(required = false)
    private MinioService minioService;

    @Value("${app.file.upload-path}")
    private String uploadPath;

    @Value("${app.file.max-size}")
    private long maxFileSize;

    @Value("${app.file.allowed-types}")
    private String allowedTypes;

    /**
     * 上传文件
     */
    @Transactional
    public Result<FileUploadResponse> uploadFile(MultipartFile file, String businessType) {
        try {
            // 验证文件
            Result<Void> validation = validateFile(file);
            if (!validation.isSuccess()) {
                return Result.error(validation.getCode(), validation.getMessage());
            }

            // 获取当前用户
            UserPrincipal currentUser = getCurrentUser();
            if(currentUser == null){
                currentUser = new UserPrincipal();
                currentUser.setId(1L);
                currentUser.setUsername("admin");
                currentUser.setRealName("管理员");
            }

            // 生成文件名
            String originalFilename = file.getOriginalFilename();
            String fileExtension = getFileExtension(originalFilename);
            String storedName = generateStoredName(fileExtension);

            // 计算MD5
            String md5Hash = DigestUtils.md5DigestAsHex(file.getBytes());

            UploadedFile uploadedFile = new UploadedFile();
            uploadedFile.setOriginalName(originalFilename);
            uploadedFile.setStoredName(storedName);
            uploadedFile.setFileSize(file.getSize());
            uploadedFile.setFileType(fileExtension);
            uploadedFile.setMimeType(file.getContentType());
            uploadedFile.setMd5Hash(md5Hash);
            uploadedFile.setUploaderId(currentUser.getId());
            uploadedFile.setUploadTime(LocalDateTime.now());
            uploadedFile.setStatus(1);

            // 根据配置选择存储方式
            if (minioProperties.isEnabled() && minioService != null) {
                // 使用MinIO存储
                return uploadToMinio(file, uploadedFile, businessType);
            } else {
                // 使用本地存储
                return uploadToLocal(file, uploadedFile);
            }

        } catch (Exception e) {
            log.error("文件上传失败: {}", file.getOriginalFilename(), e);
            return Result.error(ResultCode.FILE_UPLOAD_FAILED, "文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 验证文件
     */
    private Result<Void> validateFile(MultipartFile file) {
        // 检查文件是否为空
        if (file.isEmpty()) {
            return Result.error(ResultCode.FILE_EMPTY, "文件不能为空");
        }

        // 检查文件大小
        if (file.getSize() > maxFileSize) {
            return Result.error(ResultCode.FILE_SIZE_EXCEEDED, 
                    String.format("文件大小超过限制，最大允许 %d MB", maxFileSize / 1024 / 1024));
        }

        // 检查文件类型
        String fileExtension = getFileExtension(file.getOriginalFilename());
        List<String> allowedTypeList = Arrays.asList(allowedTypes.split(","));
        if (!allowedTypeList.contains(fileExtension.toLowerCase())) {
            return Result.error(ResultCode.FILE_TYPE_NOT_ALLOWED, 
                    "不支持的文件类型，支持的类型: " + allowedTypes);
        }

        return Result.success();
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filename) {
        if (filename == null || filename.lastIndexOf('.') == -1) {
            return "";
        }
        return filename.substring(filename.lastIndexOf('.') + 1);
    }

    /**
     * 生成存储文件名
     */
    private String generateStoredName(String fileExtension) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String uuid = UUID.randomUUID().toString().replace("-", "").substring(0, 8);
        return timestamp + "_" + uuid + "." + fileExtension;
    }

    /**
     * 上传到MinIO
     */
    private Result<FileUploadResponse> uploadToMinio(MultipartFile file, UploadedFile uploadedFile, String businessType) {
        try {
            // 生成对象键
            String objectKey = generateObjectKey(uploadedFile.getStoredName(), businessType);

            // 上传到MinIO
            MinioUploadResponse minioResponse = minioService.uploadFile(file, objectKey);

            // 更新文件记录
            uploadedFile.setStorageType("MINIO");
            uploadedFile.setBucketName(minioResponse.getBucketName());
            uploadedFile.setObjectKey(minioResponse.getObjectKey());
            uploadedFile.setEtag(minioResponse.getEtag());
            uploadedFile.setFilePath(minioResponse.getObjectKey()); // 存储对象键作为路径

            fileMapper.insert(uploadedFile);

            // 构建响应
            FileUploadResponse response = FileUploadResponse.builder()
                    .fileId(uploadedFile.getId())
                    .originalName(uploadedFile.getOriginalName())
                    .storedName(uploadedFile.getStoredName())
                    .fileSize(uploadedFile.getFileSize())
                    .fileType(uploadedFile.getFileType())
                    .uploadTime(uploadedFile.getUploadTime())
                    .previewUrl("/api/v1/files/" + uploadedFile.getId() + "/preview")
                    .downloadUrl("/api/v1/files/" + uploadedFile.getId() + "/download")
                    .build();

            log.info("文件上传到MinIO成功: originalName={}, objectKey={}, size={}",
                    uploadedFile.getOriginalName(), objectKey, uploadedFile.getFileSize());

            return Result.success("文件上传成功", response);

        } catch (Exception e) {
            log.error("MinIO文件上传失败，尝试降级到本地存储: {}", uploadedFile.getOriginalName(), e);

            // 降级到本地存储
            try {
                log.info("降级到本地存储: {}", uploadedFile.getOriginalName());
                return uploadToLocal(file, uploadedFile);
            } catch (Exception localException) {
                log.error("本地存储也失败: {}", uploadedFile.getOriginalName(), localException);
                return Result.error(ResultCode.FILE_UPLOAD_FAILED,
                    "文件上传失败 - MinIO: " + e.getMessage() + ", 本地存储: " + localException.getMessage());
            }
        }
    }

    /**
     * 上传到本地
     */
    private Result<FileUploadResponse> uploadToLocal(MultipartFile file, UploadedFile uploadedFile) {
        try {
            // 创建上传目录
            Path uploadDir = Paths.get(uploadPath);
            if (!Files.exists(uploadDir)) {
                Files.createDirectories(uploadDir);
            }

            // 保存文件
            Path filePath = uploadDir.resolve(uploadedFile.getStoredName());
            Files.copy(file.getInputStream(), filePath);

            // 更新文件记录
            uploadedFile.setStorageType("LOCAL");
            uploadedFile.setFilePath(filePath.toString());

            fileMapper.insert(uploadedFile);

            // 构建响应
            FileUploadResponse response = FileUploadResponse.builder()
                    .fileId(uploadedFile.getId())
                    .originalName(uploadedFile.getOriginalName())
                    .storedName(uploadedFile.getStoredName())
                    .fileSize(uploadedFile.getFileSize())
                    .fileType(uploadedFile.getFileType())
                    .uploadTime(uploadedFile.getUploadTime())
                    .previewUrl("/api/v1/files/" + uploadedFile.getId() + "/preview")
                    .downloadUrl("/api/v1/files/" + uploadedFile.getId() + "/download")
                    .build();

            log.info("文件上传到本地成功: originalName={}, storedName={}, size={}",
                    uploadedFile.getOriginalName(), uploadedFile.getStoredName(), uploadedFile.getFileSize());

            return Result.success("文件上传成功", response);

        } catch (IOException e) {
            log.error("本地文件上传失败: {}", uploadedFile.getOriginalName(), e);
            return Result.error(ResultCode.FILE_UPLOAD_FAILED, "本地文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 生成对象键
     */
    private String generateObjectKey(String storedName, String businessType) {
        String prefix = businessType != null ? businessType + "/" : "files/";
        String datePath = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));
        return prefix + datePath + "/" + storedName;
    }

    /**
     * 下载文件
     */
    public Result<byte[]> downloadFile(Long fileId) {
        try {
            UploadedFile uploadedFile = fileMapper.selectById(fileId);
            if (uploadedFile == null) {
                return Result.error(ResultCode.FILE_NOT_FOUND, "文件不存在");
            }

            if ("MINIO".equals(uploadedFile.getStorageType()) && minioService != null) {
                // 从MinIO下载
                try (InputStream inputStream = minioService.downloadFile(uploadedFile.getObjectKey())) {
                    byte[] fileBytes = inputStream.readAllBytes();
                    log.info("从MinIO下载文件成功: fileId={}, objectKey={}", fileId, uploadedFile.getObjectKey());
                    return Result.success("文件下载成功", fileBytes);
                }
            } else {
                // 从本地下载
                Path filePath = Paths.get(uploadedFile.getFilePath());
                if (!Files.exists(filePath)) {
                    return Result.error(ResultCode.FILE_NOT_FOUND, "文件不存在");
                }
                byte[] fileBytes = Files.readAllBytes(filePath);
                log.info("从本地下载文件成功: fileId={}, filePath={}", fileId, uploadedFile.getFilePath());
                return Result.success("文件下载成功", fileBytes);
            }

        } catch (Exception e) {
            log.error("文件下载失败: fileId={}", fileId, e);
            return Result.error(ResultCode.FILE_DOWNLOAD_FAILED, "文件下载失败: " + e.getMessage());
        }
    }

    /**
     * 删除文件
     */
    @Transactional
    public Result<String> deleteFile(Long fileId) {
        try {
            UploadedFile uploadedFile = fileMapper.selectById(fileId);
            if (uploadedFile == null) {
                return Result.error(ResultCode.FILE_NOT_FOUND, "文件不存在");
            }

            if ("MINIO".equals(uploadedFile.getStorageType()) && minioService != null) {
                // 从MinIO删除
                minioService.deleteFile(uploadedFile.getObjectKey());
                log.info("从MinIO删除文件成功: fileId={}, objectKey={}", fileId, uploadedFile.getObjectKey());
            } else {
                // 从本地删除
                Path filePath = Paths.get(uploadedFile.getFilePath());
                if (Files.exists(filePath)) {
                    Files.delete(filePath);
                    log.info("从本地删除文件成功: fileId={}, filePath={}", fileId, uploadedFile.getFilePath());
                }
            }

            // 删除数据库记录（逻辑删除）
            fileMapper.deleteById(fileId);

            return Result.success("文件删除成功");

        } catch (Exception e) {
            log.error("文件删除失败: fileId={}", fileId, e);
            return Result.error(ResultCode.FILE_DELETE_FAILED, "文件删除失败: " + e.getMessage());
        }
    }

    /**
     * 获取文件信息
     */
    public Result<UploadedFile> getFileInfo(Long fileId) {
        try {
            UploadedFile uploadedFile = fileMapper.selectById(fileId);
            if (uploadedFile == null) {
                return Result.error(ResultCode.FILE_NOT_FOUND, "文件不存在");
            }

            // 设置预览和下载URL
            uploadedFile.setPreviewUrl("/api/v1/files/" + fileId + "/preview");
            uploadedFile.setDownloadUrl("/api/v1/files/" + fileId + "/download");

            return Result.success("获取文件信息成功", uploadedFile);

        } catch (Exception e) {
            log.error("获取文件信息失败: fileId={}", fileId, e);
            return Result.error(ResultCode.INTERNAL_SERVER_ERROR, "获取文件信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取当前用户
     */
    private UserPrincipal getCurrentUser() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            Object principal = authentication.getPrincipal();

            // 处理普通用户认证
            if (principal instanceof UserPrincipal) {
                return (UserPrincipal) principal;
            }

            // 处理API Key认证 - 需要根据userId查询用户信息
            if (authentication instanceof ApiKeyAuthenticationToken) {
                ApiKeyAuthenticationToken apiKeyAuth = (ApiKeyAuthenticationToken) authentication;
                ApiKey apiKey = apiKeyAuth.getApiKey();
                if (apiKey != null && apiKey.getUserId() != null) {
                    // 根据userId查询用户信息并创建UserPrincipal
                    UserVO userVO = userService.getUserById(apiKey.getUserId());
                    if (userVO != null) {
                        // 创建一个简化的UserPrincipal用于文件上传
                        return new UserPrincipal(
                            userVO.getId(),
                            userVO.getUsername(),
                            userVO.getRealName(),
                            userVO.getEmail(),
                            null, // roleId
                            userVO.getRoleCode(),
                            List.of("ROLE_API_USER"), // 简单的权限
                            null // password
                        );
                    }
                }
            }

            log.warn("无法获取当前用户信息，认证类型: {}", authentication.getClass().getSimpleName());
            return null;
        } catch (Exception e) {
            log.error("获取当前用户失败", e);
            return null;
        }
    }
}
