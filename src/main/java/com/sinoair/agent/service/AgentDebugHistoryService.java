package com.sinoair.agent.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sinoair.agent.common.Result;
import com.sinoair.agent.entity.AgentDebugHistory;

import java.util.List;
import java.util.Map;

/**
 * Agent调试历史服务接口
 * 
 * <AUTHOR> Team
 */
public interface AgentDebugHistoryService {

    /**
     * 保存调试历史记录
     */
    Result<AgentDebugHistory> saveDebugHistory(AgentDebugHistory debugHistory);

    /**
     * 根据Agent ID查询调试历史
     */
    Result<List<AgentDebugHistory>> getDebugHistoryByAgentId(Long agentId);

    /**
     * 分页查询调试历史
     */
    Result<IPage<AgentDebugHistory>> getDebugHistoryPage(Long agentId, Long userId, Boolean success, int page, int size);

    /**
     * 根据ID查询调试历史详情
     */
    Result<AgentDebugHistory> getDebugHistoryById(Long id);

    /**
     * 获取Agent调试统计信息
     */
    Result<Map<String, Object>> getDebugStatistics(Long agentId);

    /**
     * 获取最近的调试记录
     */
    Result<List<AgentDebugHistory>> getRecentDebugHistory(Long agentId, Integer limit);

    /**
     * 删除调试历史记录
     */
    Result<String> deleteDebugHistory(Long id);

    /**
     * 批量删除调试历史记录
     */
    Result<String> batchDeleteDebugHistory(List<Long> ids);

    /**
     * 清理过期的调试记录
     */
    Result<Integer> cleanupOldRecords(Integer days);
}
