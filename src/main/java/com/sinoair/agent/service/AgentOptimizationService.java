package com.sinoair.agent.service;

import com.sinoair.agent.entity.AgentOptimization;

import java.util.List;

/**
 * Agent优化参数服务接口
 */
public interface AgentOptimizationService {

    /**
     * 获取所有优化配置
     */
    List<AgentOptimization> list();

    /**
     * 根据ID获取优化配置
     */
    AgentOptimization getById(Long id);

    /**
     * 保存优化配置
     */
    boolean save(AgentOptimization entity);

    /**
     * 更新优化配置
     */
    boolean updateById(AgentOptimization entity);

    /**
     * 删除优化配置
     */
    boolean removeById(Long id);

    /**
     * 根据Agent ID获取优化配置列表
     */
    List<AgentOptimization> findByAgentId(Long agentId);

    /**
     * 根据Agent ID获取当前激活的优化配置
     */
    AgentOptimization getActiveByAgentId(Long agentId);

    /**
     * 激活指定的优化配置
     */
    boolean activateOptimization(Long optimizationId);

    /**
     * 统计Agent的优化配置数量
     */
    long countByAgentId(Long agentId);
}
