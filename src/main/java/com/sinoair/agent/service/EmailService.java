package com.sinoair.agent.service;

/**
 * 邮件服务接口
 */
public interface EmailService {
    
    /**
     * 发送验证码邮件
     */
    boolean sendVerificationCode(String email, String code);
    
    /**
     * 生成验证码
     */
    String generateVerificationCode();
    
    /**
     * 验证验证码
     */
    boolean verifyCode(String email, String code);
    
    /**
     * 存储验证码到Redis
     */
    void storeVerificationCode(String email, String code);
    
    /**
     * 检查发送频率限制
     */
    boolean checkSendFrequency(String email);
}
