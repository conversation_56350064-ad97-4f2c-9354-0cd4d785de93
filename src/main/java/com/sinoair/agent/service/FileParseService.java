package com.sinoair.agent.service;

import cn.hutool.core.util.IdUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sinoair.agent.common.Result;
import com.sinoair.agent.dto.response.FileUploadResponse;
import com.sinoair.agent.dto.response.RecognitionResult;
import com.sinoair.agent.entity.Agent;
import com.sinoair.agent.entity.UploadedFile;
import com.sinoair.agent.service.llm.LLMService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 文件解析服务
 *
 * <AUTHOR> Team
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FileParseService {

    private final RedisTemplate<String, Object> redisTemplate;
    private final LLMService llmService;
    private final FileService fileService;
    private final ObjectMapper objectMapper;

    private static final String TASK_KEY_PREFIX = "file_parse_task:";

    /**
     * 为公开API解析文件
     */
    public Object parseFileForPublicApi(Agent agent, MultipartFile file, boolean async, String callbackUrl, String priority, Long userId) {
        String taskId = generateTaskId();

        if (async) {
            // 异步处理
            return handleAsyncParsing(agent, file, taskId, callbackUrl, priority, userId);
        } else {
            // 同步处理（不推荐用于大文件）
            if (file.getSize() > 1024 * 1024 * 5) { // 5MB
                log.warn("大文件使用同步模式可能导致超时: fileName={}, size={}", file.getOriginalFilename(), file.getSize());
            }
            return handleSyncParsing(agent, file, taskId, userId);
        }
    }

    /**
     * 处理同步解析
     */
    private Object handleSyncParsing(Agent agent, MultipartFile file, String taskId, Long userId) {
        try {
            log.info("开始同步文件解析: taskId={}, agentCode={}, fileName={}",
                    taskId, agent.getAgentCode(), file.getOriginalFilename());

            long startTime = System.currentTimeMillis();

            // 调用真实的Agent进行文件解析
            Map<String, Object> parsedContent = callAgentForParsing(agent, file, userId);

            long endTime = System.currentTimeMillis();
            String processingTime = String.format("%.1fs", (endTime - startTime) / 1000.0);

            // 构建响应数据
            Map<String, Object> response = new HashMap<>();
            response.put("taskId", taskId);
            response.put("agentId", agent.getAgentCode());
            response.put("agentName", agent.getAgentName());
            response.put("fileName", file.getOriginalFilename());
            response.put("fileSize", formatFileSize(file.getSize()));
            response.put("parsedContent", parsedContent);
            response.put("processingTime", processingTime);
            response.put("timestamp", LocalDateTime.now());

            log.info("同步文件解析完成: taskId={}, processingTime={}", taskId, processingTime);
            return response;

        } catch (Exception e) {
            log.error("同步文件解析失败: taskId={}", taskId, e);
            throw new RuntimeException("文件解析失败: " + e.getMessage());
        }
    }

    /**
     * 处理异步解析
     */
    private Object handleAsyncParsing(Agent agent, MultipartFile file, String taskId, String callbackUrl, String priority, Long userId) {
        try {
            log.info("开始异步文件解析: taskId={}, agentCode={}, fileName={}", 
                    taskId, agent.getAgentCode(), file.getOriginalFilename());

            // 将任务信息存储到Redis
            Map<String, Object> taskInfo = new HashMap<>();
            taskInfo.put("taskId", taskId);
            taskInfo.put("agentId", agent.getAgentCode());
            taskInfo.put("fileName", file.getOriginalFilename());
            taskInfo.put("fileSize", file.getSize());
            taskInfo.put("userId", userId);
            taskInfo.put("status", "processing");
            taskInfo.put("progress", 0);
            taskInfo.put("priority", priority);
            taskInfo.put("callbackUrl", callbackUrl);
            taskInfo.put("startTime", LocalDateTime.now());

            redisTemplate.opsForValue().set(TASK_KEY_PREFIX + taskId, taskInfo, 7, TimeUnit.DAYS);

            // 异步处理文件解析（这里简化处理，实际应该使用线程池或消息队列）
            processAsyncTask(taskId, agent, file, callbackUrl, priority, userId);

            // 返回任务信息
            Map<String, Object> response = new HashMap<>();
            response.put("taskId", taskId);
            response.put("status", "processing");
            response.put("estimatedTime", "30s");
            response.put("statusUrl", "/api/v1/public/files/parse/status/" + taskId);

            return response;

        } catch (Exception e) {
            log.error("异步文件解析启动失败: taskId={}", taskId, e);
            throw new RuntimeException("文件解析启动失败: " + e.getMessage());
        }
    }

    /**
     * 异步处理任务
     */
    private void processAsyncTask(String taskId, Agent agent, MultipartFile file, String callbackUrl, String priority, Long userId) {
        // 使用新线程模拟异步处理
        new Thread(() -> {
            try {
                // 模拟处理时间
                Thread.sleep(5000);

                // 更新任务进度
                updateTaskProgress(taskId, 50, "正在解析文件内容...");
                Thread.sleep(3000);

                updateTaskProgress(taskId, 80, "正在生成结构化数据...");
                Thread.sleep(2000);

                // 调用真实的Agent进行文件解析
                long startTime = System.currentTimeMillis();
                Map<String, Object> parsedContent = callAgentForParsing(agent, file, userId);
                long endTime = System.currentTimeMillis();
                String processingTime = String.format("%.1fs", (endTime - startTime) / 1000.0);

                // 构建结果对象
                Map<String, Object> result = new HashMap<>();
                result.put("taskId", taskId);
                result.put("agentId", agent.getAgentCode());
                result.put("agentName", agent.getAgentName());
                result.put("fileName", file.getOriginalFilename());
                result.put("fileSize", formatFileSize(file.getSize()));
                result.put("parsedContent", parsedContent);
                result.put("processingTime", processingTime);
                result.put("timestamp", LocalDateTime.now());

                // 更新任务完成状态
                Map<String, Object> taskInfo = (Map<String, Object>) redisTemplate.opsForValue().get(TASK_KEY_PREFIX + taskId);
                if (taskInfo != null) {
                    taskInfo.put("status", "completed");
                    taskInfo.put("progress", 100);
                    taskInfo.put("completedTime", LocalDateTime.now());
                    taskInfo.put("result", result);
                    redisTemplate.opsForValue().set(TASK_KEY_PREFIX + taskId, taskInfo, 7, TimeUnit.DAYS);
                }

                log.info("异步文件解析完成: taskId={}", taskId);

                // 发送回调通知
                if (callbackUrl != null && !callbackUrl.trim().isEmpty()) {
                    sendCallback(callbackUrl, result);
                }

            } catch (Exception e) {
                log.error("异步文件解析失败: taskId={}", taskId, e);
                
                // 更新任务失败状态
                Map<String, Object> taskInfo = (Map<String, Object>) redisTemplate.opsForValue().get(TASK_KEY_PREFIX + taskId);
                if (taskInfo != null) {
                    taskInfo.put("status", "failed");
                    taskInfo.put("error", e.getMessage());
                    taskInfo.put("completedTime", LocalDateTime.now());
                    redisTemplate.opsForValue().set(TASK_KEY_PREFIX + taskId, taskInfo, 7, TimeUnit.DAYS);
                }
            }
        }).start();
    }

    /**
     * 更新任务进度
     */
    private void updateTaskProgress(String taskId, int progress, String message) {
        Map<String, Object> taskInfo = (Map<String, Object>) redisTemplate.opsForValue().get(TASK_KEY_PREFIX + taskId);
        if (taskInfo != null) {
            taskInfo.put("progress", progress);
            taskInfo.put("message", message);
            redisTemplate.opsForValue().set(TASK_KEY_PREFIX + taskId, taskInfo, 7, TimeUnit.DAYS);
        }
    }

    /**
     * 获取解析状态
     */
    public Object getParseStatus(String taskId, Long userId) {
        Map<String, Object> taskInfo = (Map<String, Object>) redisTemplate.opsForValue().get(TASK_KEY_PREFIX + taskId);
        
        if (taskInfo == null) {
            return null;
        }

        // 检查用户权限（简化处理）
        Long taskUserId = (Long) taskInfo.get("userId");
        if (!userId.equals(taskUserId)) {
            return null;
        }

        return taskInfo;
    }

    /**
     * 调用Agent进行文件解析
     */
    private Map<String, Object> callAgentForParsing(Agent agent, MultipartFile file, Long userId) {
        try {
            log.info("调用Agent进行文件解析: agentCode={}, fileName={}, fileSize={}",
                    agent.getAgentCode(), file.getOriginalFilename(), file.getSize());

            // 直接调用LLMService进行同步文件识别
            String recognitionResult = callAgentCallService(agent, file, userId);

            // 解析Agent返回的结果
            Map<String, Object> parsedContent = parseAgentResult(recognitionResult, agent);

            return parsedContent;

        } catch (Exception e) {
            log.error("调用Agent解析文件失败: agentCode={}, fileName={}",
                    agent.getAgentCode(), file.getOriginalFilename(), e);

            // 如果Agent调用失败，返回错误信息
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("error", true);
            errorResult.put("errorMessage", "Agent解析失败: " + e.getMessage());
            errorResult.put("agentCode", agent.getAgentCode());
            errorResult.put("fileName", file.getOriginalFilename());
            return errorResult;
        }
    }

    /**
     * 调用LLMService进行文件识别
     */
    private String callAgentCallService(Agent agent, MultipartFile file, Long userId) {
        try {
            log.info("调用LLMService进行文件识别: agentCode={}, fileName={}, fileSize={}",
                    agent.getAgentCode(), file.getOriginalFilename(), file.getSize());

            // 先上传文件
            Result<FileUploadResponse> uploadResult = fileService.uploadFile(file, "file_parse");
            if (!uploadResult.isSuccess()) {
                throw new RuntimeException("文件上传失败: " + uploadResult.getMessage());
            }

            // 创建UploadedFile对象
            UploadedFile uploadedFile = new UploadedFile();
            uploadedFile.setId(uploadResult.getData().getFileId());

            // 调用LLMService进行识别
            RecognitionResult result = llmService.recognize(agent, uploadedFile, userId);

            if (result.isSuccess()) {
                // 返回识别结果
                String resultStr = result.getResult();
                if (resultStr == null || resultStr.trim().isEmpty()) {
                    return "{}";
                }
                return resultStr;
            } else {
                String errorMsg = result.getErrorMessage() != null ? result.getErrorMessage() : "LLMService识别失败";
                throw new RuntimeException("LLMService识别失败: " + errorMsg);
            }

        } catch (Exception e) {
            log.error("调用LLMService失败: agentCode={}", agent.getAgentCode(), e);
            throw new RuntimeException("LLMService调用失败", e);
        }
    }


    /**
     * 解析Agent返回的结果
     */
    private Map<String, Object> parseAgentResult(String agentResult, Agent agent) {
        try {
            if (agentResult == null || agentResult.trim().isEmpty()) {
                log.warn("Agent返回结果为空: agentCode={}", agent.getAgentCode());
                Map<String, Object> emptyResult = new HashMap<>();
                emptyResult.put("warning", "Agent返回结果为空");
                emptyResult.put("agentCode", agent.getAgentCode());
                return emptyResult;
            }

            // 尝试将Agent返回的JSON字符串解析为Map
            Map<String, Object> parsedResult = objectMapper.readValue(agentResult, Map.class);

            // 添加一些元数据
            parsedResult.put("agentCode", agent.getAgentCode());
            parsedResult.put("agentName", agent.getAgentName());
            parsedResult.put("processedAt", LocalDateTime.now());

            return parsedResult;

        } catch (Exception e) {
            log.error("解析Agent结果失败: agentCode={}, result={}", agent.getAgentCode(), agentResult, e);

            // 如果解析失败，尝试将结果作为纯文本处理
            Map<String, Object> fallbackResult = new HashMap<>();
            fallbackResult.put("textResult", agentResult);
            fallbackResult.put("parseError", e.getMessage());
            fallbackResult.put("agentCode", agent.getAgentCode());
            fallbackResult.put("agentName", agent.getAgentName());
            fallbackResult.put("note", "Agent返回的结果无法解析为JSON，以文本形式返回");

            return fallbackResult;
        }
    }

    /**
     * 格式化文件大小
     */
    private String formatFileSize(long size) {
        if (size < 1024) {
            return size + "B";
        } else if (size < 1024 * 1024) {
            return String.format("%.1fKB", size / 1024.0);
        } else {
            return String.format("%.1fMB", size / (1024.0 * 1024.0));
        }
    }

    /**
     * 发送回调通知
     */
    private void sendCallback(String callbackUrl, Map<String, Object> result) {
        try {
            log.info("开始发送回调通知: callbackUrl={}, taskId={}", callbackUrl, result.get("taskId"));

            // 创建HTTP客户端
            HttpClient httpClient = HttpClient.newBuilder()
                    .connectTimeout(Duration.ofSeconds(10))
                    .build();

            // 构建回调数据
            Map<String, Object> callbackData = new HashMap<>();
            callbackData.put("event", "parse_completed");
            callbackData.put("taskId", result.get("taskId"));
            callbackData.put("status", "completed");
            callbackData.put("timestamp", LocalDateTime.now());
            callbackData.put("result", result);

            // 转换为JSON
            String jsonBody = objectMapper.writeValueAsString(callbackData);

            // 创建HTTP请求
            HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create(callbackUrl))
                    .header("Content-Type", "application/json")
                    .header("User-Agent", "SinoairAgent-Callback/1.0")
                    .timeout(Duration.ofSeconds(30))
                    .POST(HttpRequest.BodyPublishers.ofString(jsonBody))
                    .build();

            // 发送请求
            HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());

            if (response.statusCode() >= 200 && response.statusCode() < 300) {
                log.info("回调通知发送成功: callbackUrl={}, statusCode={}", callbackUrl, response.statusCode());
            } else {
                log.warn("回调通知响应异常: callbackUrl={}, statusCode={}, response={}",
                        callbackUrl, response.statusCode(), response.body());
            }

        } catch (Exception e) {
            log.error("发送回调通知失败: callbackUrl={}, taskId={}", callbackUrl, result.get("taskId"), e);
        }
    }

    /**
     * 生成任务ID
     */
    private String generateTaskId() {
        return "task_" + System.currentTimeMillis() + "_" + IdUtil.fastSimpleUUID().substring(0, 8);
    }
}
