package com.sinoair.agent.service;

import com.sinoair.agent.common.Result;
import com.sinoair.agent.entity.UploadedFile;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.apache.poi.hwpf.HWPFDocument;
import org.apache.poi.hwpf.extractor.WordExtractor;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.extractor.XWPFWordExtractor;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;

/**
 * 文档解析服务
 * 支持PDF、Word、Excel、文本文件、图片文件的内容提取
 *
 * <AUTHOR> Team
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DocumentParserService {

    private final FileService fileService;
    private final OcrService ocrService;

    /**
     * 解析文档内容
     * 
     * @param fileId 文件ID
     * @return 解析后的文本内容
     */
    public Result<String> parseDocument(Long fileId) {
        try {
            // 获取文件信息
            Result<UploadedFile> fileInfoResult = fileService.getFileInfo(fileId);
            if (!fileInfoResult.isSuccess()) {
                return Result.error(fileInfoResult.getCode(), fileInfoResult.getMessage());
            }

            UploadedFile fileInfo = fileInfoResult.getData();
            
            // 获取文件内容
            Result<byte[]> fileContentResult = fileService.downloadFile(fileId);
            if (!fileContentResult.isSuccess()) {
                return Result.error(fileContentResult.getCode(), fileContentResult.getMessage());
            }

            byte[] fileContent = fileContentResult.getData();
            String mimeType = fileInfo.getMimeType();
            String fileName = fileInfo.getOriginalName();

            log.info("开始解析文档: fileId={}, fileName={}, mimeType={}, size={}", 
                    fileId, fileName, mimeType, fileContent.length);

            // 根据文件类型解析内容
            String parsedContent = parseByMimeType(fileContent, mimeType, fileName);
            
            if (parsedContent == null || parsedContent.trim().isEmpty()) {
                return Result.error("文档解析失败：无法提取文本内容");
            }

            log.info("文档解析成功: fileId={}, 提取文本长度={}", fileId, parsedContent.length());
            return Result.success("文档解析成功", parsedContent);

        } catch (Exception e) {
            log.error("文档解析失败: fileId={}", fileId, e);
            return Result.error("文档解析失败: " + e.getMessage());
        }
    }

    /**
     * 根据MIME类型解析文档
     */
    private String parseByMimeType(byte[] fileContent, String mimeType, String fileName) throws IOException {
        if (mimeType == null) {
            mimeType = guessMimeTypeFromFileName(fileName);
        }

        try (InputStream inputStream = new ByteArrayInputStream(fileContent)) {
            switch (mimeType.toLowerCase()) {
                case "application/pdf":
                    return parsePdf(inputStream);
                    
                case "application/msword":
                    return parseDocFile(inputStream);
                    
                case "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
                    return parseDocxFile(inputStream);
                    
                case "application/vnd.ms-excel":
                    return parseXlsFile(inputStream);
                    
                case "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":
                    return parseXlsxFile(inputStream);
                    
                case "text/plain":
                case "text/csv":
                    return parseTextFile(fileContent);

                // 图片文件类型
                case "image/jpeg":
                case "image/jpg":
                case "image/png":
                case "image/bmp":
                case "image/tiff":
                case "image/gif":
                case "image/webp":
                    return parseImageFile(fileContent, fileName);

                default:
                    // 检查是否为图片文件（通过文件扩展名）
                    if (isImageFile(fileName)) {
                        return parseImageFile(fileContent, fileName);
                    }
                    // 尝试作为文本文件解析
                    log.warn("未知文件类型: {}, 尝试作为文本文件解析", mimeType);
                    return parseTextFile(fileContent);
            }
        }
    }

    /**
     * 解析PDF文件
     */
    private String parsePdf(InputStream inputStream) throws IOException {
        try (PDDocument document = PDDocument.load(inputStream)) {
            PDFTextStripper stripper = new PDFTextStripper();
            String text = stripper.getText(document);
            log.debug("PDF解析完成，页数: {}, 文本长度: {}", document.getNumberOfPages(), text.length());
            return text;
        }
    }

    /**
     * 解析DOC文件
     */
    private String parseDocFile(InputStream inputStream) throws IOException {
        try (HWPFDocument document = new HWPFDocument(inputStream);
             WordExtractor extractor = new WordExtractor(document)) {
            String text = extractor.getText();
            log.debug("DOC文件解析完成，文本长度: {}", text.length());
            return text;
        }
    }

    /**
     * 解析DOCX文件
     */
    private String parseDocxFile(InputStream inputStream) throws IOException {
        try (XWPFDocument document = new XWPFDocument(inputStream);
             XWPFWordExtractor extractor = new XWPFWordExtractor(document)) {
            String text = extractor.getText();
            log.debug("DOCX文件解析完成，文本长度: {}", text.length());
            return text;
        }
    }

    /**
     * 解析XLS文件
     */
    private String parseXlsFile(InputStream inputStream) throws IOException {
        try (HSSFWorkbook workbook = new HSSFWorkbook(inputStream)) {
            return extractExcelContent(workbook);
        }
    }

    /**
     * 解析XLSX文件
     */
    private String parseXlsxFile(InputStream inputStream) throws IOException {
        try (XSSFWorkbook workbook = new XSSFWorkbook(inputStream)) {
            return extractExcelContent(workbook);
        }
    }

    /**
     * 提取Excel内容
     */
    private String extractExcelContent(Workbook workbook) {
        StringBuilder content = new StringBuilder();
        
        for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
            Sheet sheet = workbook.getSheetAt(i);
            content.append("工作表: ").append(sheet.getSheetName()).append("\n");
            
            for (Row row : sheet) {
                for (Cell cell : row) {
                    switch (cell.getCellType()) {
                        case STRING:
                            content.append(cell.getStringCellValue()).append("\t");
                            break;
                        case NUMERIC:
                            content.append(cell.getNumericCellValue()).append("\t");
                            break;
                        case BOOLEAN:
                            content.append(cell.getBooleanCellValue()).append("\t");
                            break;
                        case FORMULA:
                            content.append(cell.getCellFormula()).append("\t");
                            break;
                        default:
                            content.append("").append("\t");
                            break;
                    }
                }
                content.append("\n");
            }
            content.append("\n");
        }
        
        log.debug("Excel文件解析完成，工作表数: {}, 文本长度: {}", 
                workbook.getNumberOfSheets(), content.length());
        return content.toString();
    }

    /**
     * 解析文本文件
     */
    private String parseTextFile(byte[] fileContent) {
        // 尝试不同的编码
        String[] encodings = {"UTF-8", "GBK", "GB2312", "ISO-8859-1"};

        for (String encoding : encodings) {
            try {
                String text = new String(fileContent, encoding);
                // 检查是否包含乱码（简单检测）
                if (!containsGarbledText(text)) {
                    log.debug("文本文件解析完成，编码: {}, 文本长度: {}", encoding, text.length());
                    return text;
                }
            } catch (Exception e) {
                log.debug("使用编码 {} 解析失败: {}", encoding, e.getMessage());
            }
        }

        // 如果所有编码都失败，使用UTF-8作为默认
        String text = new String(fileContent, StandardCharsets.UTF_8);
        log.debug("文本文件解析完成（默认UTF-8），文本长度: {}", text.length());
        return text;
    }

    /**
     * 解析图片文件（使用OCR）
     */
    private String parseImageFile(byte[] fileContent, String fileName) {
        try {
            log.info("开始解析图片文件: fileName={}, size={}", fileName, fileContent.length);

            Result<String> ocrResult = ocrService.recognizeText(fileContent, fileName);

            if (ocrResult.isSuccess()) {
                String recognizedText = ocrResult.getData();
                log.debug("图片OCR解析完成，文本长度: {}", recognizedText.length());
                return recognizedText;
            } else {
                log.error("图片OCR解析失败: {}", ocrResult.getMessage());
                return "图片OCR解析失败: " + ocrResult.getMessage();
            }

        } catch (Exception e) {
            log.error("图片文件解析异常: fileName={}", fileName, e);
            return "图片文件解析异常: " + e.getMessage();
        }
    }

    /**
     * 根据文件名猜测MIME类型
     */
    private String guessMimeTypeFromFileName(String fileName) {
        if (fileName == null) {
            return "application/octet-stream";
        }

        String lowerFileName = fileName.toLowerCase();
        if (lowerFileName.endsWith(".pdf")) {
            return "application/pdf";
        } else if (lowerFileName.endsWith(".doc")) {
            return "application/msword";
        } else if (lowerFileName.endsWith(".docx")) {
            return "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
        } else if (lowerFileName.endsWith(".xls")) {
            return "application/vnd.ms-excel";
        } else if (lowerFileName.endsWith(".xlsx")) {
            return "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
        } else if (lowerFileName.endsWith(".txt")) {
            return "text/plain";
        } else if (lowerFileName.endsWith(".csv")) {
            return "text/csv";
        } else if (lowerFileName.endsWith(".jpg") || lowerFileName.endsWith(".jpeg")) {
            return "image/jpeg";
        } else if (lowerFileName.endsWith(".png")) {
            return "image/png";
        } else if (lowerFileName.endsWith(".bmp")) {
            return "image/bmp";
        } else if (lowerFileName.endsWith(".tiff") || lowerFileName.endsWith(".tif")) {
            return "image/tiff";
        } else if (lowerFileName.endsWith(".gif")) {
            return "image/gif";
        } else if (lowerFileName.endsWith(".webp")) {
            return "image/webp";
        } else {
            return "application/octet-stream";
        }
    }

    /**
     * 检测文本是否包含乱码
     */
    private boolean containsGarbledText(String text) {
        // 简单的乱码检测：检查是否包含大量的问号或特殊字符
        if (text.length() == 0) {
            return false;
        }
        
        int questionMarkCount = 0;
        int specialCharCount = 0;
        
        for (char c : text.toCharArray()) {
            if (c == '?') {
                questionMarkCount++;
            } else if (c < 32 && c != '\n' && c != '\r' && c != '\t') {
                specialCharCount++;
            }
        }
        
        // 如果问号或特殊字符超过10%，认为是乱码
        double questionMarkRatio = (double) questionMarkCount / text.length();
        double specialCharRatio = (double) specialCharCount / text.length();
        
        return questionMarkRatio > 0.1 || specialCharRatio > 0.1;
    }

    /**
     * 检查是否为图片文件
     */
    private boolean isImageFile(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return false;
        }

        String lowerFileName = fileName.toLowerCase();
        return lowerFileName.endsWith(".jpg") || lowerFileName.endsWith(".jpeg") ||
               lowerFileName.endsWith(".png") || lowerFileName.endsWith(".bmp") ||
               lowerFileName.endsWith(".tiff") || lowerFileName.endsWith(".tif") ||
               lowerFileName.endsWith(".gif") || lowerFileName.endsWith(".webp");
    }
}
