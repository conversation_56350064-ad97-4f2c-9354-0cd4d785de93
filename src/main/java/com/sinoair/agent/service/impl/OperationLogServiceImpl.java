package com.sinoair.agent.service.impl;

import com.sinoair.agent.entity.OperationLog;
import com.sinoair.agent.repository.OperationLogRepository;
import com.sinoair.agent.service.OperationLogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 操作日志服务实现类
 *
 * <AUTHOR> Team
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OperationLogServiceImpl implements OperationLogService {

    private final OperationLogRepository operationLogRepository;
    private final MongoTemplate mongoTemplate;

    @Override
    public void saveLog(OperationLog operationLog) {
        try {
            if (operationLog.getCreatedTime() == null) {
                operationLog.setCreatedTime(LocalDateTime.now());
            }
            if (operationLog.getOperationTime() == null) {
                operationLog.setOperationTime(LocalDateTime.now());
            }
            operationLogRepository.save(operationLog);
            log.debug("操作日志保存成功: {}", operationLog.getId());
        } catch (Exception e) {
            log.error("保存操作日志失败", e);
        }
    }

    @Override
    @Async("taskExecutor")
    public void saveLogAsync(OperationLog operationLog) {
        saveLog(operationLog);
    }

    @Override
    public OperationLog findById(String id) {
        return operationLogRepository.findById(id).orElse(null);
    }

    @Override
    public Page<OperationLog> findLogs(Pageable pageable) {
        return operationLogRepository.findAll(pageable);
    }

    @Override
    public Page<OperationLog> findLogsByUserId(Long userId, Pageable pageable) {
        return operationLogRepository.findByUserIdOrderByOperationTimeDesc(userId, pageable);
    }

    @Override
    public Page<OperationLog> findLogsByUsername(String username, Pageable pageable) {
        return operationLogRepository.findByUsernameOrderByOperationTimeDesc(username, pageable);
    }

    @Override
    public Page<OperationLog> findLogsByModule(String module, Pageable pageable) {
        return operationLogRepository.findByModuleOrderByOperationTimeDesc(module, pageable);
    }

    @Override
    public Page<OperationLog> findLogsByOperationType(String operationType, Pageable pageable) {
        return operationLogRepository.findByOperationTypeOrderByOperationTimeDesc(operationType, pageable);
    }

    @Override
    public Page<OperationLog> findLogsByStatus(String status, Pageable pageable) {
        return operationLogRepository.findByStatusOrderByOperationTimeDesc(status, pageable);
    }

    @Override
    public Page<OperationLog> findLogsByTimeRange(LocalDateTime startTime, LocalDateTime endTime, Pageable pageable) {
        return operationLogRepository.findByOperationTimeBetweenOrderByOperationTimeDesc(startTime, endTime, pageable);
    }

    @Override
    public Page<OperationLog> findLogsByConditions(Long userId, String username, String module, 
                                                  String operationType, String status, 
                                                  LocalDateTime startTime, LocalDateTime endTime, 
                                                  Pageable pageable) {
        return operationLogRepository.findByConditions(userId, username, module, operationType, 
                                                      status, startTime, endTime, pageable);
    }

    @Override
    public List<OperationLog> getRecentLogs(int limit) {
        if (limit <= 0 || limit > 100) {
            limit = 10;
        }
        return operationLogRepository.getRecentLogs(limit);
    }

    @Override
    public List<OperationLog> getRecentLogsByUserId(Long userId, int limit) {
        if (limit <= 0 || limit > 100) {
            limit = 10;
        }
        return operationLogRepository.getRecentLogsByUserId(userId, limit);
    }

    @Override
    public Map<String, Object> getOperationStatistics() {
        return operationLogRepository.getOperationStatistics();
    }

    @Override
    public Map<String, Object> getOperationStatistics(LocalDateTime startTime, LocalDateTime endTime) {
        return operationLogRepository.getOperationStatistics(startTime, endTime);
    }

    @Override
    public Map<String, Object> getUserOperationStatistics(Long userId) {
        return operationLogRepository.getUserOperationStatistics(userId);
    }

    @Override
    public Map<String, Object> getModuleOperationStatistics() {
        return operationLogRepository.getModuleOperationStatistics();
    }

    @Override
    public Map<String, Object> getOperationTypeStatistics() {
        return operationLogRepository.getOperationTypeStatistics();
    }

    @Override
    public List<Map<String, Object>> getDailyOperationStatistics(int days) {
        return operationLogRepository.getDailyOperationStatistics(days);
    }

    @Override
    public Page<OperationLog> getSlowOperations(Long thresholdMs, Pageable pageable) {
        return operationLogRepository.getSlowOperations(thresholdMs, pageable);
    }

    @Override
    public void cleanExpiredLogs(int retentionDays) {
        try {
            long deletedCount = operationLogRepository.cleanExpiredLogs(retentionDays);
            log.info("清理过期日志完成，删除了 {} 条记录", deletedCount);
        } catch (Exception e) {
            log.error("清理过期日志失败", e);
        }
    }

    @Override
    public void deleteLogs(List<String> logIds) {
        // 禁用网页删除日志功能，仅记录尝试删除的行为
        log.warn("尝试通过网页删除日志被拒绝，日志ID数量: {}", logIds != null ? logIds.size() : 0);
        throw new UnsupportedOperationException("为了数据安全，不允许通过网页删除操作日志");
    }

    @Override
    public void deleteLogsByConditions(LocalDateTime beforeTime) {
        // 禁用网页删除日志功能，仅记录尝试删除的行为
        log.warn("尝试通过网页按条件删除日志被拒绝，删除时间点: {}", beforeTime);
        throw new UnsupportedOperationException("为了数据安全，不允许通过网页删除操作日志");
    }

    @Override
    public String exportLogs(Long userId, String module, String operationType,
                           LocalDateTime startTime, LocalDateTime endTime) {
        try {
            // 使用Repository的安全查询方法
            Page<OperationLog> logsPage = operationLogRepository.findByConditions(
                userId, null, module, operationType, null, startTime, endTime,
                org.springframework.data.domain.PageRequest.of(0, 10000)); // 限制最大导出数量

            List<OperationLog> logs = logsPage.getContent();

            // 生成CSV格式数据
            StringBuilder csv = new StringBuilder();
            csv.append("ID,用户ID,用户名,真实姓名,模块,操作类型,操作描述,请求方法,请求URL,响应状态码,响应消息,执行时间(ms),客户端IP,用户代理,状态,操作时间,创建时间\n");

            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            for (OperationLog log : logs) {
                csv.append(String.format("\"%s\",\"%s\",\"%s\",\"%s\",\"%s\",\"%s\",\"%s\",\"%s\",\"%s\",\"%s\",\"%s\",\"%s\",\"%s\",\"%s\",\"%s\",\"%s\",\"%s\"\n",
                    escapeCsvValue(log.getId()),
                    escapeCsvValue(String.valueOf(log.getUserId())),
                    escapeCsvValue(log.getUsername()),
                    escapeCsvValue(log.getRealName()),
                    escapeCsvValue(log.getModule()),
                    escapeCsvValue(log.getOperationType()),
                    escapeCsvValue(log.getOperationDesc()),
                    escapeCsvValue(log.getRequestMethod()),
                    escapeCsvValue(log.getRequestUrl()),
                    escapeCsvValue(String.valueOf(log.getResponseCode())),
                    escapeCsvValue(log.getResponseMessage()),
                    escapeCsvValue(String.valueOf(log.getExecutionTime())),
                    escapeCsvValue(log.getClientIp()),
                    escapeCsvValue(log.getUserAgent()),
                    escapeCsvValue(log.getStatus()),
                    log.getOperationTime() != null ? log.getOperationTime().format(formatter) : "",
                    log.getCreatedTime() != null ? log.getCreatedTime().format(formatter) : ""
                ));
            }

            log.info("导出操作日志完成，导出记录数: {}", logs.size());
            return csv.toString();
        } catch (Exception e) {
            log.error("导出操作日志失败", e);
            throw new RuntimeException("导出日志失败: " + e.getMessage());
        }
    }

    /**
     * 转义CSV值，处理特殊字符
     */
    private String escapeCsvValue(String value) {
        if (value == null) {
            return "";
        }
        // 替换双引号为两个双引号，并移除换行符
        return value.replace("\"", "\"\"").replace("\n", " ").replace("\r", " ");
    }


}
