package com.sinoair.agent.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sinoair.agent.common.Result;
import com.sinoair.agent.entity.AgentDebugHistory;
import com.sinoair.agent.mapper.AgentDebugHistoryMapper;
import com.sinoair.agent.service.AgentDebugHistoryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Agent调试历史服务实现类
 * 
 * <AUTHOR> Team
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AgentDebugHistoryServiceImpl implements AgentDebugHistoryService {

    private final AgentDebugHistoryMapper debugHistoryMapper;
    private final ObjectMapper objectMapper;

    @Override
    @Transactional
    public Result<AgentDebugHistory> saveDebugHistory(AgentDebugHistory debugHistory) {
        try {
            if (debugHistory.getCreatedTime() == null) {
                debugHistory.setCreatedTime(LocalDateTime.now());
            }
            
            debugHistoryMapper.insert(debugHistory);
            log.info("保存调试历史记录成功: agentId={}, creatorId={}",
                    debugHistory.getAgentId(), debugHistory.getCreatorId());
            
            return Result.success("保存调试历史记录成功", debugHistory);
        } catch (Exception e) {
            log.error("保存调试历史记录失败: agentId={}", debugHistory.getAgentId(), e);
            return Result.error("保存调试历史记录失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<AgentDebugHistory>> getDebugHistoryByAgentId(Long agentId) {
        try {
            List<AgentDebugHistory> histories = debugHistoryMapper.findByAgentId(agentId);

            // 格式化每条记录的输出数据JSON
            if (histories != null) {
                histories.forEach(record -> {
                    if (record.getOutputData() != null) {
                        record.setOutputData(formatJsonResult(record.getOutputData()));
                    }
                });
            }

            return Result.success(histories);
        } catch (Exception e) {
            log.error("查询调试历史失败: agentId={}", agentId, e);
            return Result.error("查询调试历史失败: " + e.getMessage());
        }
    }

    @Override
    public Result<IPage<AgentDebugHistory>> getDebugHistoryPage(Long agentId, Long userId, Boolean success, int page, int size) {
        try {
            Page<AgentDebugHistory> pageParam = new Page<>(page, size);
            IPage<AgentDebugHistory> result = debugHistoryMapper.selectPageWithDetails(pageParam, agentId, userId, success);

            // 格式化每条记录的输出数据JSON
            if (result.getRecords() != null) {
                result.getRecords().forEach(record -> {
                    if (record.getOutputData() != null) {
                        record.setOutputData(formatJsonResult(record.getOutputData()));
                    }
                });
            }

            return Result.success(result);
        } catch (Exception e) {
            log.error("分页查询调试历史失败: agentId={}, page={}, size={}", agentId, page, size, e);
            return Result.error("分页查询调试历史失败: " + e.getMessage());
        }
    }

    @Override
    public Result<AgentDebugHistory> getDebugHistoryById(Long id) {
        try {
            AgentDebugHistory debugHistory = debugHistoryMapper.selectById(id);
            if (debugHistory == null) {
                return Result.error("调试历史记录不存在");
            }

            // 格式化输出数据JSON，防止前端解析失败
            if (debugHistory.getOutputData() != null) {
                debugHistory.setOutputData(formatJsonResult(debugHistory.getOutputData()));
            }

            return Result.success(debugHistory);
        } catch (Exception e) {
            log.error("查询调试历史详情失败: id={}", id, e);
            return Result.error("查询调试历史详情失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Map<String, Object>> getDebugStatistics(Long agentId) {
        try {
            Map<String, Object> statistics = new HashMap<>();
            
            // 总调试次数
            Integer totalCount = debugHistoryMapper.countByAgentId(agentId);
            statistics.put("totalCount", totalCount != null ? totalCount : 0);
            
            // 成功次数
            Integer successCount = debugHistoryMapper.countSuccessByAgentId(agentId);
            statistics.put("successCount", successCount != null ? successCount : 0);
            
            // 失败次数
            Integer failureCount = debugHistoryMapper.countFailureByAgentId(agentId);
            statistics.put("failureCount", failureCount != null ? failureCount : 0);
            
            // 成功率
            double successRate = totalCount > 0 ? (double) successCount / totalCount * 100 : 0;
            statistics.put("successRate", Math.round(successRate * 100.0) / 100.0);
            
            // 平均执行时间
            Double avgTime = debugHistoryMapper.getAverageExecutionTime(agentId);
            statistics.put("averageExecutionTime", avgTime != null ? Math.round(avgTime) : 0);
            
            return Result.success(statistics);
        } catch (Exception e) {
            log.error("获取调试统计信息失败: agentId={}", agentId, e);
            return Result.error("获取调试统计信息失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<AgentDebugHistory>> getRecentDebugHistory(Long agentId, Integer limit) {
        try {
            if (limit == null || limit <= 0) {
                limit = 10;
            }
            List<AgentDebugHistory> histories = debugHistoryMapper.findRecentByAgentId(agentId, limit);

            // 格式化每条记录的输出数据JSON
            if (histories != null) {
                histories.forEach(record -> {
                    if (record.getOutputData() != null) {
                        record.setOutputData(formatJsonResult(record.getOutputData()));
                    }
                });
            }

            return Result.success(histories);
        } catch (Exception e) {
            log.error("查询最近调试记录失败: agentId={}, limit={}", agentId, limit, e);
            return Result.error("查询最近调试记录失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<String> deleteDebugHistory(Long id) {
        try {
            AgentDebugHistory debugHistory = debugHistoryMapper.selectById(id);
            if (debugHistory == null) {
                return Result.error("调试历史记录不存在");
            }

            debugHistoryMapper.deleteById(id);

            log.info("删除调试历史记录成功: id={}", id);
            return Result.success("删除调试历史记录成功");
        } catch (Exception e) {
            log.error("删除调试历史记录失败: id={}", id, e);
            return Result.error("删除调试历史记录失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<String> batchDeleteDebugHistory(List<Long> ids) {
        try {
            if (ids == null || ids.isEmpty()) {
                return Result.error("请选择要删除的记录");
            }
            
            for (Long id : ids) {
                AgentDebugHistory debugHistory = debugHistoryMapper.selectById(id);
                if (debugHistory != null) {
                    debugHistoryMapper.deleteById(id);
                }
            }
            
            log.info("批量删除调试历史记录成功: count={}", ids.size());
            return Result.success("批量删除调试历史记录成功");
        } catch (Exception e) {
            log.error("批量删除调试历史记录失败: ids={}", ids, e);
            return Result.error("批量删除调试历史记录失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<Integer> cleanupOldRecords(Integer days) {
        try {
            if (days == null || days <= 0) {
                days = 30; // 默认清理30天前的记录
            }
            
            int deletedCount = debugHistoryMapper.cleanupOldRecords(days);
            log.info("清理过期调试记录成功: days={}, deletedCount={}", days, deletedCount);
            
            return Result.success("清理过期调试记录成功", deletedCount);
        } catch (Exception e) {
            log.error("清理过期调试记录失败: days={}", days, e);
            return Result.error("清理过期调试记录失败: " + e.getMessage());
        }
    }

    /**
     * 格式化JSON结果，防止页面解析失败
     */
    private String formatJsonResult(String jsonResult) {
        if (jsonResult == null || jsonResult.trim().isEmpty()) {
            return null;
        }

        try {
            // 尝试解析JSON以验证格式
            Object parsed = objectMapper.readValue(jsonResult, Object.class);
            // 重新序列化以确保格式正确
            return objectMapper.writeValueAsString(parsed);
        } catch (Exception e) {
            log.warn("JSON格式验证失败，返回原始字符串: {}", e.getMessage());
            // 如果不是有效的JSON，返回null避免前端解析错误
            return null;
        }
    }
}
