package com.sinoair.agent.service.impl;

import com.sinoair.agent.service.EmailService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.stereotype.Service;

import java.util.Random;
import java.util.concurrent.TimeUnit;

/**
 * 邮件服务实现类
 * 当 mail.enabled=true 时使用此实现
 */
@Slf4j
@Service
@ConditionalOnProperty(name = "mail.enabled", havingValue = "true", matchIfMissing = true)
public class EmailServiceImpl implements EmailService {

    @Autowired(required = false)
    private JavaMailSender mailSender;

    @Autowired
    private StringRedisTemplate redisTemplate;
    
    private static final String VERIFICATION_CODE_PREFIX = "verification_code:";
    private static final String SEND_FREQUENCY_PREFIX = "send_frequency:";
    private static final int CODE_EXPIRE_MINUTES = 5;
    private static final int SEND_FREQUENCY_SECONDS = 60;
    
    @Override
    public boolean sendVerificationCode(String email, String code) {
        // 检查邮件发送器是否可用
        if (mailSender == null) {
            log.warn("邮件发送器不可用，跳过发送验证码到邮箱：{}，验证码：{}", email, code);
            return false;
        }

        try {
            SimpleMailMessage message = new SimpleMailMessage();
            message.setFrom("<EMAIL>"); // 设置发件人地址
            message.setTo(email);
            message.setSubject("SINOAIR-AGENT 登录验证码");
            message.setText("您的SINOAIR-AGENT登录验证码是：" + code + "\n\n验证码有效期为5分钟，请及时使用。\n\n如果这不是您的操作，请忽略此邮件。");

            mailSender.send(message);
            log.info("验证码邮件发送成功，邮箱：{}", email);
            return true;
        } catch (Exception e) {
            log.error("验证码邮件发送失败，邮箱：{}，错误：{}", email, e.getMessage());
            return false;
        }
    }
    
    @Override
    public String generateVerificationCode() {
        Random random = new Random();
        return String.format("%06d", random.nextInt(1000000));
    }
    
    @Override
    public boolean verifyCode(String email, String code) {
        String key = VERIFICATION_CODE_PREFIX + email;
        String storedCode = redisTemplate.opsForValue().get(key);
        
        if (storedCode != null && storedCode.equals(code)) {
            // 验证成功后删除验证码
            redisTemplate.delete(key);
            return true;
        }
        return false;
    }
    
    @Override
    public void storeVerificationCode(String email, String code) {
        String key = VERIFICATION_CODE_PREFIX + email;
        redisTemplate.opsForValue().set(key, code, CODE_EXPIRE_MINUTES, TimeUnit.MINUTES);
    }
    
    @Override
    public boolean checkSendFrequency(String email) {
        String key = SEND_FREQUENCY_PREFIX + email;
        String lastSendTime = redisTemplate.opsForValue().get(key);
        
        if (lastSendTime == null) {
            // 记录发送时间
            redisTemplate.opsForValue().set(key, String.valueOf(System.currentTimeMillis()), 
                    SEND_FREQUENCY_SECONDS, TimeUnit.SECONDS);
            return true;
        }
        return false;
    }
}
