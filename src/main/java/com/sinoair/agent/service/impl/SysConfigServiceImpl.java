package com.sinoair.agent.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sinoair.agent.entity.SysConfig;
import com.sinoair.agent.mapper.SysConfigMapper;
import com.sinoair.agent.service.SysConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 系统配置服务实现类
 *
 * <AUTHOR> Team
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SysConfigServiceImpl implements SysConfigService {

    private final SysConfigMapper sysConfigMapper;

    @Override
    public List<SysConfig> list() {
        return sysConfigMapper.selectList(
            new LambdaQueryWrapper<SysConfig>()
                .eq(SysConfig::getDeleted, 0)
                .orderByAsc(SysConfig::getConfigGroup, SysConfig::getConfigKey)
        );
    }

    @Override
    public SysConfig getById(Long id) {
        return sysConfigMapper.selectById(id);
    }

    @Override
    public boolean save(SysConfig entity) {
        try {
            if (entity.getCreatedTime() == null) {
                entity.setCreatedTime(LocalDateTime.now());
            }
            entity.setUpdatedTime(LocalDateTime.now());
            if (entity.getIsEnabled() == null) {
                entity.setIsEnabled(SysConfig.STATUS_ENABLED);
            }
            sysConfigMapper.insert(entity);
            return true;
        } catch (Exception e) {
            log.error("保存系统配置失败", e);
            return false;
        }
    }

    @Override
    public boolean updateById(SysConfig entity) {
        try {
            entity.setUpdatedTime(LocalDateTime.now());
            sysConfigMapper.updateById(entity);
            return true;
        } catch (Exception e) {
            log.error("更新系统配置失败", e);
            return false;
        }
    }

    @Override
    public boolean removeById(Long id) {
        try {
            // 逻辑删除
            SysConfig config = new SysConfig();
            config.setId(id);
            config.setDeleted(1);
            config.setUpdatedTime(LocalDateTime.now());
            sysConfigMapper.updateById(config);
            return true;
        } catch (Exception e) {
            log.error("删除系统配置失败", e);
            return false;
        }
    }

    @Override
    public List<SysConfig> getByGroup(String configGroup) {
        return sysConfigMapper.selectByGroup(configGroup);
    }

    @Override
    public List<SysConfig> getEnabledByGroup(String configGroup) {
        return sysConfigMapper.selectEnabledByGroup(configGroup);
    }

    @Override
    public SysConfig getByGroupAndKey(String configGroup, String configKey) {
        return sysConfigMapper.selectByGroupAndKey(configGroup, configKey);
    }

    @Override
    public String getConfigValue(String configGroup, String configKey) {
        SysConfig config = getByGroupAndKey(configGroup, configKey);
        return config != null ? config.getConfigValue() : null;
    }

    @Override
    public String getConfigValue(String configGroup, String configKey, String defaultValue) {
        String value = getConfigValue(configGroup, configKey);
        return value != null ? value : defaultValue;
    }

    @Override
    public Boolean getBooleanValue(String configGroup, String configKey) {
        SysConfig config = getByGroupAndKey(configGroup, configKey);
        return config != null ? config.getBooleanValue() : null;
    }

    @Override
    public Integer getIntegerValue(String configGroup, String configKey) {
        SysConfig config = getByGroupAndKey(configGroup, configKey);
        return config != null ? config.getIntegerValue() : null;
    }

    @Override
    public Map<String, List<SysConfig>> getDictData() {
        List<SysConfig> allConfigs = sysConfigMapper.selectAllEnabled();
        return allConfigs.stream()
            .collect(Collectors.groupingBy(SysConfig::getConfigGroup));
    }

    @Override
    public List<SysConfig> getDictDataByGroup(String configGroup) {
        return getEnabledByGroup(configGroup);
    }
}
