package com.sinoair.agent.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinoair.agent.entity.AgentCategory;
import com.sinoair.agent.mapper.AgentCategoryMapper;
import com.sinoair.agent.service.AgentCategoryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Agent分类服务实现类
 * 
 * <AUTHOR> Team
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AgentCategoryServiceImpl extends ServiceImpl<AgentCategoryMapper, AgentCategory> implements AgentCategoryService {

    private final AgentCategoryMapper agentCategoryMapper;

    @Override
    public List<AgentCategory> getAllActiveCategories() {
        try {
            List<AgentCategory> categories = agentCategoryMapper.findAllActive();
            log.debug("查询到 {} 个可用分类", categories.size());
            return categories;
        } catch (Exception e) {
            log.error("查询可用分类失败", e);
            throw new RuntimeException("查询可用分类失败: " + e.getMessage());
        }
    }

    @Override
    public List<AgentCategory> getCategoryTree() {
        try {
            List<AgentCategory> categories = agentCategoryMapper.findCategoryTree();
            log.debug("查询到分类树形结构，共 {} 个节点", categories.size());
            return categories;
        } catch (Exception e) {
            log.error("查询分类树形结构失败", e);
            throw new RuntimeException("查询分类树形结构失败: " + e.getMessage());
        }
    }

    @Override
    public AgentCategory getByCategoryCode(String categoryCode) {
        try {
            AgentCategory category = agentCategoryMapper.findByCategoryCode(categoryCode);
            log.debug("根据分类代码 {} 查询到分类: {}", categoryCode, category != null ? category.getCategoryName() : "未找到");
            return category;
        } catch (Exception e) {
            log.error("根据分类代码查询分类失败: categoryCode={}", categoryCode, e);
            throw new RuntimeException("根据分类代码查询分类失败: " + e.getMessage());
        }
    }

    @Override
    public List<AgentCategory> getByParentId(Long parentId) {
        try {
            List<AgentCategory> categories = agentCategoryMapper.findByParentId(parentId);
            log.debug("根据父级ID {} 查询到 {} 个子分类", parentId, categories.size());
            return categories;
        } catch (Exception e) {
            log.error("根据父级ID查询子分类失败: parentId={}", parentId, e);
            throw new RuntimeException("根据父级ID查询子分类失败: " + e.getMessage());
        }
    }

    @Override
    public List<AgentCategory> getRootCategories() {
        try {
            List<AgentCategory> categories = agentCategoryMapper.findRootCategories();
            log.debug("查询到 {} 个根分类", categories.size());
            return categories;
        } catch (Exception e) {
            log.error("查询根分类失败", e);
            throw new RuntimeException("查询根分类失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createCategory(AgentCategory category) {
        try {
            // 检查分类代码是否已存在
            AgentCategory existingCategory = agentCategoryMapper.findByCategoryCode(category.getCategoryCode());
            if (existingCategory != null) {
                throw new RuntimeException("分类代码已存在: " + category.getCategoryCode());
            }

            // 设置默认值
            if (category.getStatus() == null) {
                category.setStatus(1);
            }
            if (category.getParentId() == null) {
                category.setParentId(0L);
            }
            if (category.getLevel() == null) {
                category.setLevel(1);
            }
            if (category.getSortOrder() == null) {
                category.setSortOrder(0);
            }

            category.setCreatedTime(LocalDateTime.now());
            category.setUpdatedTime(LocalDateTime.now());

            int result = agentCategoryMapper.insert(category);
            log.info("创建分类成功: categoryName={}, categoryCode={}", category.getCategoryName(), category.getCategoryCode());
            return result > 0;
        } catch (Exception e) {
            log.error("创建分类失败: categoryName={}, categoryCode={}", category.getCategoryName(), category.getCategoryCode(), e);
            throw new RuntimeException("创建分类失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateCategory(AgentCategory category) {
        try {
            category.setUpdatedTime(LocalDateTime.now());
            int result = agentCategoryMapper.updateById(category);
            log.info("更新分类成功: id={}, categoryName={}", category.getId(), category.getCategoryName());
            return result > 0;
        } catch (Exception e) {
            log.error("更新分类失败: id={}, categoryName={}", category.getId(), category.getCategoryName(), e);
            throw new RuntimeException("更新分类失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteCategory(Long categoryId) {
        try {
            // 检查是否有子分类
            Boolean hasChildren = agentCategoryMapper.hasChildren(categoryId);
            if (hasChildren != null && hasChildren) {
                throw new RuntimeException("该分类下存在子分类，无法删除");
            }

            // 软删除
            AgentCategory category = agentCategoryMapper.selectById(categoryId);
            if (category == null) {
                throw new RuntimeException("分类不存在");
            }

            category.setDeleted(1);
            category.setUpdatedTime(LocalDateTime.now());
            int result = agentCategoryMapper.updateById(category);
            
            log.info("删除分类成功: id={}, categoryName={}", categoryId, category.getCategoryName());
            return result > 0;
        } catch (Exception e) {
            log.error("删除分类失败: categoryId={}", categoryId, e);
            throw new RuntimeException("删除分类失败: " + e.getMessage());
        }
    }

    @Override
    public boolean hasChildren(Long categoryId) {
        try {
            Boolean result = agentCategoryMapper.hasChildren(categoryId);
            return result != null && result;
        } catch (Exception e) {
            log.error("检查分类是否有子分类失败: categoryId={}", categoryId, e);
            return false;
        }
    }

    @Override
    public Long getCategoryCount() {
        try {
            Long count = agentCategoryMapper.countCategories();
            log.debug("统计分类数量: {}", count);
            return count;
        } catch (Exception e) {
            log.error("统计分类数量失败", e);
            return 0L;
        }
    }

    @Override
    public List<java.util.Map<String, Object>> countAgentsByCategory() {
        try {
            List<java.util.Map<String, Object>> result = agentCategoryMapper.countAgentsByCategory();
            log.debug("统计各分类下的Agent数量完成");
            return result;
        } catch (Exception e) {
            log.error("统计各分类下的Agent数量失败", e);
            throw new RuntimeException("统计各分类下的Agent数量失败: " + e.getMessage());
        }
    }
}
