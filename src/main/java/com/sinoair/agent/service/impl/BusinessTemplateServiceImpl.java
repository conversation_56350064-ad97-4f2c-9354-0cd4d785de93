package com.sinoair.agent.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sinoair.agent.entity.BusinessTemplate;
import com.sinoair.agent.mapper.BusinessTemplateMapper;
import com.sinoair.agent.service.BusinessTemplateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 业务模板服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BusinessTemplateServiceImpl implements BusinessTemplateService {

    private final BusinessTemplateMapper businessTemplateMapper;

    @Override
    public List<BusinessTemplate> list() {
        return businessTemplateMapper.selectList(new QueryWrapper<BusinessTemplate>().eq("deleted", 0));
    }

    @Override
    public BusinessTemplate getById(Long id) {
        return businessTemplateMapper.selectById(id);
    }

    @Override
    public boolean save(BusinessTemplate entity) {
        try {
            businessTemplateMapper.insert(entity);
            return true;
        } catch (Exception e) {
            log.error("保存业务模板失败", e);
            return false;
        }
    }

    @Override
    public boolean updateById(BusinessTemplate entity) {
        try {
            businessTemplateMapper.updateById(entity);
            return true;
        } catch (Exception e) {
            log.error("更新业务模板失败", e);
            return false;
        }
    }

    @Override
    public boolean removeById(Long id) {
        try {
            businessTemplateMapper.deleteById(id);
            return true;
        } catch (Exception e) {
            log.error("删除业务模板失败", e);
            return false;
        }
    }

    @Override
    public List<BusinessTemplate> getByCategory(String category) {
        return businessTemplateMapper.findByCategory(category);
    }

    @Override
    public BusinessTemplate getByCode(String templateCode) {
        BusinessTemplate template = businessTemplateMapper.findByTemplateCode(templateCode);
        return (template != null && template.getStatus() == 1) ? template : null;
    }

    @Override
    public List<BusinessTemplate> getActiveTemplates() {
        return businessTemplateMapper.findAllActive();
    }
}
