package com.sinoair.agent.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sinoair.agent.dto.request.AgentSupplementDTO;
import com.sinoair.agent.dto.response.AgentSupplementVO;
import com.sinoair.agent.entity.Agent;
import com.sinoair.agent.entity.AgentDebugHistory;
import com.sinoair.agent.entity.AgentSupplementInfo;
import com.sinoair.agent.entity.AgentVersion;
import com.sinoair.agent.entity.RecognitionRecord;
import com.sinoair.agent.mapper.AgentMapper;
import com.sinoair.agent.mapper.AgentSupplementInfoMapper;
import com.sinoair.agent.mapper.AgentVersionMapper;
import com.sinoair.agent.mapper.AgentDebugHistoryMapper;
import com.sinoair.agent.mapper.RecognitionRecordMapper;
import com.sinoair.agent.service.AgentSupplementService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;

/**
 * Agent补充资料服务实现类
 *
 * <AUTHOR> Team
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AgentSupplementServiceImpl implements AgentSupplementService {

    private final AgentSupplementInfoMapper supplementMapper;
    private final AgentMapper agentMapper;
    private final AgentVersionMapper versionMapper;
    private final AgentDebugHistoryMapper debugHistoryMapper;
    private final RecognitionRecordMapper recognitionRecordMapper;
    private final ObjectMapper objectMapper;

    @Override
    public AgentSupplementVO getSupplementInfo(Long agentId) {
        // 获取补充资料
        AgentSupplementInfo supplementInfo = supplementMapper.selectByAgentId(agentId);
        
        if (supplementInfo == null) {
            // 如果没有补充资料，创建一个空的返回基本信息
            Agent agent = agentMapper.selectById(agentId);
            if (agent == null) {
                return null;
            }
            
            AgentSupplementVO vo = new AgentSupplementVO();
            vo.setAgentId(agentId);
            vo.setAgentName(agent.getAgentName());
            vo.setAgentCode(agent.getAgentCode());
            vo.setAgentDescription(agent.getDescription());
            vo.setStatus(AgentSupplementInfo.STATUS_DRAFT);
            vo.setStatusName("草稿");
            return vo;
        }

        // 获取Agent基本信息
        Agent agent = agentMapper.selectById(supplementInfo.getAgentId());
        if (agent == null) {
            log.error("Agent不存在: {}", supplementInfo.getAgentId());
            return null;
        }

        // 转换为VO
        AgentSupplementVO vo = new AgentSupplementVO();
        BeanUtils.copyProperties(supplementInfo, vo);

        // 设置Agent基本信息
        vo.setAgentName(agent.getAgentName());
        vo.setAgentCode(agent.getAgentCode());
        vo.setAgentDescription(agent.getDescription());

        // 设置状态名称
        vo.setStatusName(supplementInfo.getStatus() == AgentSupplementInfo.STATUS_DRAFT ? "草稿" : "已提交");
        
        // 解析JSON字段
        try {
            if (supplementInfo.getScreenshotUrls() != null) {
                vo.setScreenshotUrls(objectMapper.readValue(supplementInfo.getScreenshotUrls(), 
                    new TypeReference<List<String>>() {}));
            }
            if (supplementInfo.getVersionInfo() != null) {
                vo.setVersionInfo(objectMapper.readValue(supplementInfo.getVersionInfo(), 
                    new TypeReference<Map<String, Object>>() {}));
            }
            if (supplementInfo.getUsageStatistics() != null) {
                vo.setUsageStatistics(objectMapper.readValue(supplementInfo.getUsageStatistics(), 
                    new TypeReference<Map<String, Object>>() {}));
            }
            if (supplementInfo.getCallHistorySummary() != null) {
                vo.setCallHistorySummary(objectMapper.readValue(supplementInfo.getCallHistorySummary(), 
                    new TypeReference<Map<String, Object>>() {}));
            }
        } catch (Exception e) {
            log.error("解析JSON字段失败", e);
        }
        
        return vo;
    }

    @Override
    @Transactional
    public boolean saveOrUpdateSupplement(AgentSupplementDTO supplementDTO, Long userId) {
        try {
            // 检查Agent是否存在
            Agent agent = agentMapper.selectById(supplementDTO.getAgentId());
            if (agent == null) {
                log.error("Agent不存在: {}", supplementDTO.getAgentId());
                return false;
            }
            
            // 查找现有的补充资料
            AgentSupplementInfo existingInfo = supplementMapper.selectByAgentId(supplementDTO.getAgentId());
            
            AgentSupplementInfo supplementInfo;
            if (existingInfo != null) {
                // 更新现有记录
                supplementInfo = existingInfo;
            } else {
                // 创建新记录
                supplementInfo = new AgentSupplementInfo();
                supplementInfo.setAgentId(supplementDTO.getAgentId());
                supplementInfo.setCreatedTime(LocalDateTime.now());
            }
            
            // 设置基本信息
            supplementInfo.setAgentIntroduction(supplementDTO.getAgentIntroduction());
            supplementInfo.setUsageScenarios(supplementDTO.getUsageScenarios());
            supplementInfo.setPainPointsSolved(supplementDTO.getPainPointsSolved());
            supplementInfo.setStatus(supplementDTO.getStatus() != null ? supplementDTO.getStatus() : AgentSupplementInfo.STATUS_DRAFT);
            supplementInfo.setUpdatedTime(LocalDateTime.now());
            
            // 处理截图URL列表
            if (supplementDTO.getScreenshotUrls() != null) {
                supplementInfo.setScreenshotUrls(objectMapper.writeValueAsString(supplementDTO.getScreenshotUrls()));
            }
            
            // 自动生成版本信息、使用统计和调用历史
            generateStatistics(supplementInfo);
            
            // 保存或更新
            if (existingInfo != null) {
                supplementMapper.updateById(supplementInfo);
            } else {
                supplementMapper.insert(supplementInfo);
            }
            
            return true;
        } catch (Exception e) {
            log.error("保存补充资料失败", e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean submitSupplement(Long agentId, Long userId) {
        try {
            AgentSupplementInfo supplementInfo = supplementMapper.selectByAgentId(agentId);
            if (supplementInfo == null) {
                log.error("补充资料不存在: {}", agentId);
                return false;
            }

            // 更新补充资料状态为已提交
            supplementInfo.setStatus(AgentSupplementInfo.STATUS_SUBMITTED);
            supplementInfo.setUpdatedTime(LocalDateTime.now());
            supplementMapper.updateById(supplementInfo);

            // 更新Agent状态为审批中
            Agent agent = agentMapper.selectById(agentId);
            if (agent != null) {
                agent.setApprovalStatus(1); // 1-审批中
                agent.setSubmitTime(LocalDateTime.now());
                agent.setUpdatedTime(LocalDateTime.now());
                agentMapper.updateById(agent);
                log.info("Agent状态已更新为审批中: agentId={}", agentId);
            }

            return true;
        } catch (Exception e) {
            log.error("提交补充资料失败", e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean submitSupplementWithVersion(Long agentId, Long userId, Long versionId, String versionNumber) {
        try {
            AgentSupplementInfo supplementInfo = supplementMapper.selectByAgentId(agentId);
            if (supplementInfo == null) {
                log.error("补充资料不存在: {}", agentId);
                return false;
            }

            // 更新补充资料状态为已提交
            supplementInfo.setStatus(AgentSupplementInfo.STATUS_SUBMITTED);
            supplementInfo.setUpdatedTime(LocalDateTime.now());
            supplementMapper.updateById(supplementInfo);

            // 更新Agent状态为审批中
            Agent agent = agentMapper.selectById(agentId);
            if (agent != null) {
                agent.setApprovalStatus(1); // 1-审批中
                agent.setSubmitTime(LocalDateTime.now());
                agent.setUpdatedTime(LocalDateTime.now());
                agentMapper.updateById(agent);
                log.info("Agent状态已更新为审批中: agentId={}", agentId);
            }

            // 更新指定版本的审批状态为审批中
            if (versionId != null) {
                LambdaUpdateWrapper<AgentVersion> versionUpdateWrapper = new LambdaUpdateWrapper<>();
                versionUpdateWrapper.eq(AgentVersion::getId, versionId)
                        .set(AgentVersion::getApprovalStatus, 1) // 1-审批中
                        .set(AgentVersion::getUpdatedTime, LocalDateTime.now());
                versionMapper.update(null, versionUpdateWrapper);
                log.info("版本审批状态已更新为审批中: versionId={}, versionNumber={}", versionId, versionNumber);
            }

            return true;
        } catch (Exception e) {
            log.error("提交补充资料失败", e);
            return false;
        }
    }

    @Override
    public AgentSupplementVO getAgentStatistics(Long agentId) {
        AgentSupplementVO vo = getSupplementInfo(agentId);
        if (vo == null) {
            return null;
        }
        
        // 重新生成最新的统计信息
        try {
            AgentSupplementInfo tempInfo = new AgentSupplementInfo();
            tempInfo.setAgentId(agentId);
            generateStatistics(tempInfo);
            
            // 更新VO中的统计信息
            if (tempInfo.getVersionInfo() != null) {
                vo.setVersionInfo(objectMapper.readValue(tempInfo.getVersionInfo(), 
                    new TypeReference<Map<String, Object>>() {}));
            }
            if (tempInfo.getUsageStatistics() != null) {
                vo.setUsageStatistics(objectMapper.readValue(tempInfo.getUsageStatistics(), 
                    new TypeReference<Map<String, Object>>() {}));
            }
            if (tempInfo.getCallHistorySummary() != null) {
                vo.setCallHistorySummary(objectMapper.readValue(tempInfo.getCallHistorySummary(), 
                    new TypeReference<Map<String, Object>>() {}));
            }
        } catch (Exception e) {
            log.error("生成统计信息失败", e);
        }
        
        return vo;
    }

    /**
     * 生成统计信息
     */
    private void generateStatistics(AgentSupplementInfo supplementInfo) {
        try {
            Long agentId = supplementInfo.getAgentId();
            
            // 1. 生成版本信息
            generateVersionInfo(supplementInfo, agentId);
            
            // 2. 生成使用统计
            generateUsageStatistics(supplementInfo, agentId);
            
            // 3. 生成调用历史汇总
            generateCallHistorySummary(supplementInfo, agentId);
            
        } catch (Exception e) {
            log.error("生成统计信息失败", e);
        }
    }

    /**
     * 生成版本信息
     */
    private void generateVersionInfo(AgentSupplementInfo supplementInfo, Long agentId) {
        try {
            Agent agent = agentMapper.selectById(agentId);
            List<AgentVersion> versions = versionMapper.selectList(
                new LambdaQueryWrapper<AgentVersion>()
                    .eq(AgentVersion::getAgentId, agentId)
                    .orderByDesc(AgentVersion::getCreatedTime)
                    .last("LIMIT 5")
            );
            
            Map<String, Object> versionInfo = new HashMap<>();
            versionInfo.put("currentVersion", agent != null ? agent.getVersion() : "1.0.0");
            versionInfo.put("totalVersions", versions.size());
            versionInfo.put("latestVersions", versions);
            versionInfo.put("lastUpdateTime", agent != null ? agent.getUpdatedTime() : null);
            
            supplementInfo.setVersionInfo(objectMapper.writeValueAsString(versionInfo));
        } catch (Exception e) {
            log.error("生成版本信息失败", e);
        }
    }

    /**
     * 生成使用统计
     */
    private void generateUsageStatistics(AgentSupplementInfo supplementInfo, Long agentId) {
        try {
            // 查询调试历史统计
            Long debugCount = debugHistoryMapper.selectCount(
                new LambdaQueryWrapper<AgentDebugHistory>()
                    .eq(AgentDebugHistory::getAgentId, agentId)
            );

            // 查询识别记录统计
            Long recognitionCount = recognitionRecordMapper.selectCount(
                new LambdaQueryWrapper<RecognitionRecord>()
                    .eq(RecognitionRecord::getAgentId, agentId)
            );
            
            Map<String, Object> usageStats = new HashMap<>();
            usageStats.put("totalDebugCount", debugCount != null ? debugCount : 0);
            usageStats.put("totalRecognitionCount", recognitionCount != null ? recognitionCount : 0);
            usageStats.put("totalUsageCount", (debugCount != null ? debugCount : 0) + (recognitionCount != null ? recognitionCount : 0));
            usageStats.put("subscriptionCount", 0); // 暂时使用固定值
            usageStats.put("avgResponseTime", 2.5); // 暂时使用固定值
            usageStats.put("successRate", 95.8); // 暂时使用固定值
            
            supplementInfo.setUsageStatistics(objectMapper.writeValueAsString(usageStats));
        } catch (Exception e) {
            log.error("生成使用统计失败", e);
        }
    }

    /**
     * 生成调用历史汇总
     */
    private void generateCallHistorySummary(AgentSupplementInfo supplementInfo, Long agentId) {
        try {
            Map<String, Object> callHistory = new HashMap<>();
            callHistory.put("last7Days", 0);
            callHistory.put("last30Days", 0);
            callHistory.put("totalCalls", 0);
            callHistory.put("peakUsageTime", "14:00-16:00");
            callHistory.put("mostUsedFeature", "文档识别");
            
            supplementInfo.setCallHistorySummary(objectMapper.writeValueAsString(callHistory));
        } catch (Exception e) {
            log.error("生成调用历史汇总失败", e);
        }
    }
}
