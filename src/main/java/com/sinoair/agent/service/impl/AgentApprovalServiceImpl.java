package com.sinoair.agent.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sinoair.agent.dto.request.AgentApprovalDTO;
import com.sinoair.agent.dto.request.AgentApprovalQueryDTO;
import com.sinoair.agent.dto.response.AgentApprovalDetailVO;
import com.sinoair.agent.dto.response.AgentApprovalVO;
import com.sinoair.agent.entity.Agent;
import com.sinoair.agent.entity.AgentApprovalRecord;
import com.sinoair.agent.entity.AgentCategory;
import com.sinoair.agent.entity.AgentSupplementInfo;
import com.sinoair.agent.entity.AgentVersion;
import com.sinoair.agent.entity.RecognitionRecord;
import com.sinoair.agent.entity.User;
import com.sinoair.agent.security.UserPrincipal;
import com.sinoair.agent.mapper.AgentApprovalRecordMapper;
import com.sinoair.agent.mapper.AgentCategoryMapper;
import com.sinoair.agent.mapper.AgentDebugHistoryMapper;
import com.sinoair.agent.mapper.AgentMapper;
import com.sinoair.agent.mapper.AgentSupplementInfoMapper;
import com.sinoair.agent.mapper.AgentVersionMapper;
import com.sinoair.agent.mapper.RecognitionRecordMapper;
import com.sinoair.agent.mapper.UserMapper;
import com.sinoair.agent.service.AgentApprovalService;
import com.sinoair.agent.service.SysMessageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Agent审批服务实现类
 *
 * <AUTHOR> Team
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AgentApprovalServiceImpl implements AgentApprovalService {

    private final AgentApprovalRecordMapper approvalRecordMapper;
    private final AgentMapper agentMapper;
    private final AgentVersionMapper agentVersionMapper;
    private final AgentDebugHistoryMapper debugHistoryMapper;
    private final RecognitionRecordMapper recognitionRecordMapper;
    private final AgentSupplementInfoMapper supplementMapper;
    private final AgentCategoryMapper agentCategoryMapper;
    private final UserMapper userMapper;
    private final SysMessageService messageService;
    private final ObjectMapper objectMapper;

    @Override
    public IPage<AgentApprovalVO> getApprovalList(AgentApprovalQueryDTO queryDTO, UserPrincipal userPrincipal) {
        // 根据用户角色决定是否过滤创建者
        if (userPrincipal != null) {
            // 检查是否是超级管理员
            boolean isSuperAdmin = userPrincipal.getPermissions().contains("ROLE_ADMIN");
            if (!isSuperAdmin) {
                // 普通用户只能查看自己提交的Agent审批
                queryDTO.setCreatorId(userPrincipal.getId());
            }
            log.debug("用户权限检查: userId={}, roleCode={}, isSuperAdmin={}, permissions={}",
                     userPrincipal.getId(), userPrincipal.getRoleCode(), isSuperAdmin, userPrincipal.getPermissions());
        }

        Page<AgentApprovalVO> page = new Page<>(queryDTO.getCurrent(), queryDTO.getSize());
        return approvalRecordMapper.selectApprovalList(page, queryDTO);
    }

    @Override
    public AgentApprovalDetailVO getApprovalDetail(Long agentId) {
        // 获取Agent基本信息
        Agent agent = agentMapper.selectById(agentId);
        if (agent == null) {
            throw new RuntimeException("Agent不存在");
        }

        AgentApprovalDetailVO detailVO = new AgentApprovalDetailVO();

        // 获取待审批的版本信息
        AgentVersion pendingVersion = null;
        LambdaQueryWrapper<AgentVersion> versionQuery = new LambdaQueryWrapper<>();
        versionQuery.eq(AgentVersion::getAgentId, agentId)
                .eq(AgentVersion::getApprovalStatus, 1) // 1-审批中
                .orderByDesc(AgentVersion::getCreatedTime)
                .last("LIMIT 1");
        pendingVersion = agentVersionMapper.selectOne(versionQuery);

        // 如果没有待审批版本，获取当前版本
        if (pendingVersion == null) {
            LambdaQueryWrapper<AgentVersion> currentVersionQuery = new LambdaQueryWrapper<>();
            currentVersionQuery.eq(AgentVersion::getAgentId, agentId)
                    .eq(AgentVersion::getIsCurrent, 1)
                    .orderByDesc(AgentVersion::getCreatedTime)
                    .last("LIMIT 1");
            pendingVersion = agentVersionMapper.selectOne(currentVersionQuery);
        }

        // 基本信息
        AgentApprovalDetailVO.BasicInfo basicInfo = new AgentApprovalDetailVO.BasicInfo();
        basicInfo.setId(agent.getId());
        basicInfo.setAgentName(agent.getAgentName());
        basicInfo.setAgentCode(agent.getAgentCode());
        basicInfo.setDescription(agent.getDescription());
        basicInfo.setCreateTime(agent.getCreatedTime());
        basicInfo.setVersion(agent.getVersion());

        // 设置待审批版本信息
        if (pendingVersion != null) {
            basicInfo.setPendingVersionId(pendingVersion.getId());
            basicInfo.setPendingVersionNumber(pendingVersion.getVersionNumber());
            basicInfo.setPendingVersionChangeLog(pendingVersion.getChangeLog());
            basicInfo.setPendingVersionCreateTime(pendingVersion.getCreatedTime());
            basicInfo.setUpgradeContent(pendingVersion.getChangeLog());
        } else {
            basicInfo.setUpgradeContent("优化识别准确率，增加新的字段支持"); // 默认内容
        }

        // 获取分类名称
        if (agent.getCategoryId() != null) {
            try {
                AgentCategory category = agentCategoryMapper.selectById(agent.getCategoryId());
                if (category != null) {
                    basicInfo.setCategoryName(category.getCategoryName());
                }
            } catch (Exception e) {
                log.warn("获取分类信息失败: categoryId={}", agent.getCategoryId(), e);
            }
        }

        // 获取创建者名称
        if (agent.getCreatorId() != null) {
            try {
                User creator = userMapper.selectById(agent.getCreatorId());
                if (creator != null) {
                    basicInfo.setCreatorName(creator.getRealName() != null ? creator.getRealName() : creator.getUsername());
                }
            } catch (Exception e) {
                log.warn("获取创建者信息失败: creatorId={}", agent.getCreatorId(), e);
            }
        }

        detailVO.setBasicInfo(basicInfo);

        // 配置信息
        AgentApprovalDetailVO.ConfigInfo configInfo = new AgentApprovalDetailVO.ConfigInfo();
        configInfo.setPromptTemplate(agent.getPromptTemplate());
        configInfo.setJsonTemplate(agent.getJsonTemplate());
        configInfo.setModelConfig(agent.getConfig());
        // TODO: 解析模型名称
        detailVO.setConfigInfo(configInfo);

        // 使用统计
        AgentApprovalDetailVO.UsageStats usageStats = buildUsageStats(agentId);
        detailVO.setUsageStats(usageStats);

        // 使用场景 - 从补充资料获取数据
        AgentApprovalDetailVO.UsageScenarios usageScenarios = buildUsageScenarios(agentId, agent);
        detailVO.setUsageScenarios(usageScenarios);

        // API接口信息
        AgentApprovalDetailVO.ApiInterface apiInterface = new AgentApprovalDetailVO.ApiInterface();
        apiInterface.setUrl("/api/v1/agent-call/recognize/" + agent.getAgentCode());
        apiInterface.setRequestParams("文件参数: file\n业务参数: businessParams (可选)");
        apiInterface.setResponseFormat("JSON格式，根据Agent配置的模板返回");
        apiInterface.setRequestExample("curl -X POST " + apiInterface.getUrl() + " -F \"file=@document.pdf\"");
        apiInterface.setResponseExample("{\"result\": \"识别结果\"}");
        detailVO.setApiInterface(apiInterface);

        // Chrome插件信息
        AgentApprovalDetailVO.ChromePlugin chromePlugin = new AgentApprovalDetailVO.ChromePlugin();
        chromePlugin.setSupportedSites(Arrays.asList("www.example.com", "order.sample.com"));
        chromePlugin.setFeatures("文档识别、表单自动填写");
        chromePlugin.setScreenshots(Arrays.asList());
        detailVO.setChromePlugin(chromePlugin);

        // 调用历史
        List<AgentApprovalDetailVO.CallHistory> callHistory = buildCallHistory(agentId);
        detailVO.setCallHistory(callHistory);

        return detailVO;
    }

    @Override
    @Transactional
    public boolean submitApproval(AgentApprovalDTO approvalDTO, Long approverId, String approverName) {
        try {
            // 获取Agent信息
            Agent agent = agentMapper.selectById(approvalDTO.getAgentId());
            if (agent == null) {
                throw new RuntimeException("Agent不存在");
            }

            // 获取要审批的版本信息
            AgentVersion targetVersion = null;
            if (approvalDTO.getAgentVersionId() != null) {
                targetVersion = agentVersionMapper.selectById(approvalDTO.getAgentVersionId());
                if (targetVersion == null || !targetVersion.getAgentId().equals(approvalDTO.getAgentId())) {
                    throw new RuntimeException("版本信息不存在或不匹配");
                }
            } else {
                // 如果没有指定版本ID，获取审批中的版本
                LambdaQueryWrapper<AgentVersion> versionQuery = new LambdaQueryWrapper<>();
                versionQuery.eq(AgentVersion::getAgentId, approvalDTO.getAgentId())
                        .eq(AgentVersion::getApprovalStatus, 1) // 1-审批中
                        .orderByDesc(AgentVersion::getCreatedTime)
                        .last("LIMIT 1");
                targetVersion = agentVersionMapper.selectOne(versionQuery);

                // 如果没有审批中的版本，获取当前版本作为备选
                if (targetVersion == null) {
                    versionQuery = new LambdaQueryWrapper<>();
                    versionQuery.eq(AgentVersion::getAgentId, approvalDTO.getAgentId())
                            .eq(AgentVersion::getIsCurrent, 1)
                            .orderByDesc(AgentVersion::getCreatedTime)
                            .last("LIMIT 1");
                    targetVersion = agentVersionMapper.selectOne(versionQuery);
                }
            }

            // 创建审批记录
            AgentApprovalRecord record = new AgentApprovalRecord();
            record.setAgentId(approvalDTO.getAgentId());
            record.setAgentVersionId(targetVersion != null ? targetVersion.getId() : null);
            record.setApprovalStatus(approvalDTO.getApprovalResult()); // 1-审批中，2-通过，3-不通过
            record.setApprovalOpinion(approvalDTO.getApprovalComment());
            record.setApproverId(approverId);
            record.setApproverName(approverName);
            record.setApprovalTime(LocalDateTime.now());
            record.setCreatedTime(LocalDateTime.now());
            record.setUpdatedTime(LocalDateTime.now());

            approvalRecordMapper.insert(record);

            // 更新Agent状态
            LambdaUpdateWrapper<Agent> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(Agent::getId, approvalDTO.getAgentId())
                    .set(Agent::getApprovalStatus, record.getApprovalStatus())
                    .set(Agent::getLastApprovalId, record.getId())
                    .set(Agent::getUpdatedTime, LocalDateTime.now());

            // 如果审批通过，更新Agent和版本状态
            if (approvalDTO.getApprovalResult() == 2) { // 2-审批通过
                updateWrapper.set(Agent::getStatus, 3) // 3-已发布
                        .set(Agent::getPublishedTime, LocalDateTime.now());

                // 更新版本状态为已发布
                if (targetVersion != null) {
                    // 将审批通过的版本数据同步到Agent主表
                    updateWrapper.set(Agent::getAgentName, targetVersion.getAgentName())
                            .set(Agent::getAgentCode, targetVersion.getAgentCode())
                            .set(Agent::getDescription, targetVersion.getDescription())
                            .set(Agent::getCategoryId, targetVersion.getCategoryId())
                            .set(Agent::getBusinessTypeId, targetVersion.getBusinessTypeId())
                            .set(Agent::getAgentType, targetVersion.getAgentType())
                            .set(Agent::getConfig, targetVersion.getConfig())
                            .set(Agent::getPromptTemplate, targetVersion.getPromptTemplate())
                            .set(Agent::getJsonTemplate, targetVersion.getJsonTemplate())
                            .set(Agent::getTemplateId, targetVersion.getTemplateId())
                            .set(Agent::getVersion, targetVersion.getVersionNumber());

                    // 先将该Agent的所有版本的is_current设置为0
                    LambdaUpdateWrapper<AgentVersion> resetCurrentWrapper = new LambdaUpdateWrapper<>();
                    resetCurrentWrapper.eq(AgentVersion::getAgentId, approvalDTO.getAgentId())
                            .set(AgentVersion::getIsCurrent, 0)
                            .set(AgentVersion::getUpdatedTime, LocalDateTime.now());
                    agentVersionMapper.update(null, resetCurrentWrapper);

                    // 更新审批通过的版本状态，并设置为当前版本
                    LambdaUpdateWrapper<AgentVersion> versionUpdateWrapper = new LambdaUpdateWrapper<>();
                    versionUpdateWrapper.eq(AgentVersion::getId, targetVersion.getId())
                            .set(AgentVersion::getApprovalStatus, 2) // 2-审批通过
                            .set(AgentVersion::getStatus, 3) // 3-已发布
                            .set(AgentVersion::getIsCurrent, 1) // 设置为当前版本
                            .set(AgentVersion::getPublishedTime, LocalDateTime.now())
                            .set(AgentVersion::getUpdatedTime, LocalDateTime.now());
                    agentVersionMapper.update(null, versionUpdateWrapper);

                    log.info("审批通过，已将版本数据同步到Agent主表并设置为当前版本: agentId={}, versionId={}, versionNumber={}",
                            approvalDTO.getAgentId(), targetVersion.getId(), targetVersion.getVersionNumber());
                }
            } else if (approvalDTO.getApprovalResult() == 3) { // 3-审批不通过
                // 审批不通过，Agent状态回到草稿状态，可以重新修改和提交
                updateWrapper.set(Agent::getStatus, 1); // 1-草稿状态，允许重新编辑

                // 更新版本状态为审批不通过
                if (targetVersion != null) {
                    LambdaUpdateWrapper<AgentVersion> versionUpdateWrapper = new LambdaUpdateWrapper<>();
                    versionUpdateWrapper.eq(AgentVersion::getId, targetVersion.getId())
                            .set(AgentVersion::getApprovalStatus, 3) // 3-审批不通过
                            .set(AgentVersion::getUpdatedTime, LocalDateTime.now());
                    agentVersionMapper.update(null, versionUpdateWrapper);
                }
            }

            agentMapper.update(null, updateWrapper);

            // 发送通知消息
            String statusText = approvalDTO.getApprovalResult() == 2 ? "审批通过" : "审批不通过";
            String versionInfo = targetVersion != null ? "（版本：" + targetVersion.getVersionNumber() + "）" : "";
            messageService.sendApprovalNotification(
                    agent.getCreatorId(),
                    agent.getAgentName() + versionInfo,
                    approvalDTO.getApprovalResult() == 2, // 2表示通过
                    approvalDTO.getApprovalComment(),
                    agent.getId()
            );

            log.info("Agent审批完成: agentId={}, versionId={}, version={}, approvalResult={}, approver={}",
                    approvalDTO.getAgentId(),
                    targetVersion != null ? targetVersion.getId() : null,
                    targetVersion != null ? targetVersion.getVersionNumber() : null,
                    approvalDTO.getApprovalResult(),
                    approverName);

            return true;
        } catch (Exception e) {
            log.error("Agent审批失败", e);
            throw new RuntimeException("审批操作失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public boolean submitForApproval(Long agentId, Long userId, String changeLog) {
        try {
            // 获取Agent信息
            Agent agent = agentMapper.selectById(agentId);
            if (agent == null) {
                throw new RuntimeException("Agent不存在");
            }

            // 更新Agent状态为审批中
            LambdaUpdateWrapper<Agent> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(Agent::getId, agentId)
                    .set(Agent::getApprovalStatus, 1) // 1-审批中
                    .set(Agent::getSubmitTime, LocalDateTime.now())
                    .set(Agent::getUpdatedTime, LocalDateTime.now());

            agentMapper.update(null, updateWrapper);

            // 如果有版本更新说明，创建新版本记录
            if (changeLog != null && !changeLog.trim().isEmpty()) {
                // 这里可以调用AgentVersionService创建新版本
                // 暂时先记录在审批记录中
            }

            // 创建审批记录
            AgentApprovalRecord record = new AgentApprovalRecord();
            record.setAgentId(agentId);
            record.setApprovalStatus(1); // 1-审批中
            record.setApprovalOpinion(changeLog); // 将版本更新说明暂存在审批意见中
            record.setCreatedTime(LocalDateTime.now());
            record.setUpdatedTime(LocalDateTime.now());
            // TODO: 默认管理员=1
            record.setApproverId(1l);
            record.setApproverName("管理员");

            approvalRecordMapper.insert(record);

            // 发送站内消息给管理员
            // TODO: 获取所有管理员用户ID
            List<Long> adminUserIds = List.of(1L); // 临时使用固定值
            for (Long adminUserId : adminUserIds) {
                messageService.sendMessage(
                        adminUserId,
                        "Agent审批申请",
                        String.format("用户申请发布Agent：%s，请及时处理。", agent.getAgentName()),
                        2, // 审批通知
                        agentId,
                        "AGENT_APPROVAL"
                );
            }

            log.info("Agent提交审批: agentId={}, userId={}, changeLog={}", agentId, userId, changeLog);

            return true;
        } catch (Exception e) {
            log.error("Agent提交审批失败", e);
            throw new RuntimeException("提交审批失败: " + e.getMessage());
        }
    }

    /**
     * 申请发布Agent（带版本信息）
     */
    @Transactional
    public boolean submitForApprovalWithVersion(Long agentId, Long userId, String changeLog, Long versionId, String versionNumber) {
        try {
            // 获取Agent信息
            Agent agent = agentMapper.selectById(agentId);
            if (agent == null) {
                throw new RuntimeException("Agent不存在");
            }

            // 获取版本信息
            AgentVersion version = agentVersionMapper.selectById(versionId);
            if (version == null || !version.getAgentId().equals(agentId)) {
                throw new RuntimeException("版本信息不存在或不匹配");
            }

            // 更新Agent状态为审批中
            LambdaUpdateWrapper<Agent> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(Agent::getId, agentId)
                    .set(Agent::getApprovalStatus, 1) // 1-审批中
                    .set(Agent::getSubmitTime, LocalDateTime.now())
                    .set(Agent::getUpdatedTime, LocalDateTime.now());

            agentMapper.update(null, updateWrapper);

            // 更新版本状态为审批中
            LambdaUpdateWrapper<AgentVersion> versionUpdateWrapper = new LambdaUpdateWrapper<>();
            versionUpdateWrapper.eq(AgentVersion::getId, versionId)
                    .set(AgentVersion::getApprovalStatus, 1) // 1-审批中
                    .set(AgentVersion::getUpdatedTime, LocalDateTime.now());

            agentVersionMapper.update(null, versionUpdateWrapper);

            // 创建审批记录
            AgentApprovalRecord record = new AgentApprovalRecord();
            record.setAgentId(agentId);
            record.setAgentVersionId(versionId); // 关联版本ID
            record.setApprovalStatus(1); // 1-审批中
            record.setApprovalOpinion(changeLog); // 将版本更新说明暂存在审批意见中
            record.setCreatedTime(LocalDateTime.now());
            record.setUpdatedTime(LocalDateTime.now());
            // TODO: 默认管理员=1
            record.setApproverId(1l);
            record.setApproverName("管理员");

            approvalRecordMapper.insert(record);

            // 发送站内消息给管理员
            // TODO: 获取所有管理员用户ID
            List<Long> adminUserIds = List.of(1L); // 临时使用固定值
            for (Long adminUserId : adminUserIds) {
                messageService.sendMessage(
                        adminUserId,
                        "Agent审批申请",
                        String.format("用户申请发布Agent：%s（版本：%s），请及时处理。", agent.getAgentName(), versionNumber),
                        2, // 审批通知
                        agentId,
                        "AGENT_APPROVAL"
                );
            }

            log.info("Agent提交审批: agentId={}, versionId={}, versionNumber={}, userId={}, changeLog={}",
                    agentId, versionId, versionNumber, userId, changeLog);

            return true;
        } catch (Exception e) {
            log.error("Agent提交审批失败", e);
            throw new RuntimeException("提交审批失败: " + e.getMessage());
        }
    }

    @Override
    public List<AgentApprovalRecord> getApprovalHistory(Long agentId) {
        return approvalRecordMapper.selectHistoryByAgentId(agentId, null);
    }

    @Override
    public List<Map<String, Object>> getApprovalStatusStats() {
        return approvalRecordMapper.selectApprovalStatusStats();
    }

    @Override
    public AgentApprovalRecord getApprovalStatus(Long agentId, Long versionId) {
        List<AgentApprovalRecord> agentApprovalRecords = approvalRecordMapper.selectHistoryByAgentId(agentId, versionId);
        if (agentApprovalRecords.size() > 0) {
            return agentApprovalRecords.get(0);
        }
        return null;
    }

    @Override
    public List<Map<String, Object>> getAgentVersionHistory(Long agentId) {
        try {
            // 获取Agent的版本历史（显示所有版本，但优先显示审批通过和审批中的版本）
            LambdaQueryWrapper<AgentVersion> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(AgentVersion::getAgentId, agentId)
                    .orderByDesc(AgentVersion::getCreatedTime);

            List<AgentVersion> allVersions = agentVersionMapper.selectList(queryWrapper);

            // 过滤出需要显示的版本：审批通过、审批中、或者是当前版本
            List<AgentVersion> versions = allVersions.stream()
                    .filter(version ->
                        version.getApprovalStatus() != null &&
                        (version.getApprovalStatus() == 1 || version.getApprovalStatus() == 2) || // 审批中或审批通过
                        version.getIsCurrent() == 1 // 或者是当前版本
                    )
                    .collect(java.util.stream.Collectors.toList());

            List<Map<String, Object>> result = new ArrayList<>();
            for (AgentVersion version : versions) {
                Map<String, Object> versionInfo = new HashMap<>();
                versionInfo.put("id", version.getId());
                versionInfo.put("versionNumber", version.getVersionNumber());
                versionInfo.put("changeLog", version.getChangeLog());
                versionInfo.put("createdTime", version.getCreatedTime());
                versionInfo.put("publishedTime", version.getPublishedTime());
                versionInfo.put("approvalStatus", version.getApprovalStatus());
                versionInfo.put("status", version.getStatus());
                versionInfo.put("isCurrent", version.getIsCurrent());

                // 获取审批状态名称
                String approvalStatusName;
                switch (version.getApprovalStatus()) {
                    case 0:
                        approvalStatusName = "未提交";
                        break;
                    case 1:
                        approvalStatusName = "审批中";
                        break;
                    case 2:
                        approvalStatusName = "审批通过";
                        break;
                    case 3:
                        approvalStatusName = "审批不通过";
                        break;
                    default:
                        approvalStatusName = "未知";
                        break;
                }
                versionInfo.put("approvalStatusName", approvalStatusName);
                version.setApprovalStatusName(approvalStatusName); // 同时设置到实体对象中

                // 获取版本状态名称
                String statusName;
                switch (version.getStatus()) {
                    case 1:
                        statusName = "草稿";
                        break;
                    case 2:
                        statusName = "测试中";
                        break;
                    case 3:
                        statusName = "已发布";
                        break;
                    case 4:
                        statusName = "已下线";
                        break;
                    default:
                        statusName = "未知";
                        break;
                }
                versionInfo.put("statusName", statusName);

                // 获取创建者姓名
                if (version.getCreatorId() != null) {
                    try {
                        User creator = userMapper.selectById(version.getCreatorId());
                        if (creator != null) {
                            versionInfo.put("creatorName", creator.getRealName() != null ? creator.getRealName() : creator.getUsername());
                        } else {
                            versionInfo.put("creatorName", "未知用户");
                        }
                    } catch (Exception e) {
                        log.warn("获取版本创建者信息失败: creatorId={}", version.getCreatorId(), e);
                        versionInfo.put("creatorName", "未知用户");
                    }
                } else {
                    versionInfo.put("creatorName", "系统");
                }

                // 获取审批信息
                if (version.getApprovalStatus() != null && version.getApprovalStatus() > 1) {
                    try {
                        // 查询该版本的审批记录
                        LambdaQueryWrapper<AgentApprovalRecord> approvalQuery = new LambdaQueryWrapper<>();
                        approvalQuery.eq(AgentApprovalRecord::getAgentId, agentId)
                                .eq(AgentApprovalRecord::getAgentVersionId, version.getId())
                                .orderByDesc(AgentApprovalRecord::getCreatedTime)
                                .last("LIMIT 1");
                        AgentApprovalRecord approvalRecord = approvalRecordMapper.selectOne(approvalQuery);

                        if (approvalRecord != null) {
                            versionInfo.put("approvalTime", approvalRecord.getApprovalTime());
                            versionInfo.put("approverName", approvalRecord.getApproverName());
                        }
                    } catch (Exception e) {
                        log.warn("获取版本审批信息失败: versionId={}", version.getId(), e);
                    }
                }

                result.add(versionInfo);
            }

            return result;
        } catch (Exception e) {
            log.error("获取Agent版本历史失败: agentId={}", agentId, e);
            throw new RuntimeException("获取版本历史失败: " + e.getMessage());
        }
    }

    /**
     * 构建使用统计信息
     */
    private AgentApprovalDetailVO.UsageStats buildUsageStats(Long agentId) {
        AgentApprovalDetailVO.UsageStats usageStats = new AgentApprovalDetailVO.UsageStats();

        // 统计调用次数
        LambdaQueryWrapper<RecognitionRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RecognitionRecord::getAgentId, agentId);
        long totalCalls = recognitionRecordMapper.selectCount(queryWrapper);

        // 统计成功次数
        queryWrapper.eq(RecognitionRecord::getStatus, 1);
        long successCalls = recognitionRecordMapper.selectCount(queryWrapper);

        usageStats.setTotalCalls(totalCalls);
        usageStats.setSuccessRate(totalCalls > 0 ? (double) successCalls / totalCalls * 100 : 0.0);
        usageStats.setAvgResponseTime(2.3); // 模拟数据
        usageStats.setSubscriptionCount(120); // 模拟数据
        usageStats.setWeeklyTrend(Arrays.asList(120, 130, 125, 140, 150, 145, 160)); // 模拟数据

        return usageStats;
    }

    /**
     * 构建使用场景信息
     */
    private AgentApprovalDetailVO.UsageScenarios buildUsageScenarios(Long agentId, Agent agent) {
        AgentApprovalDetailVO.UsageScenarios usageScenarios = new AgentApprovalDetailVO.UsageScenarios();

        // 获取补充资料
        AgentSupplementInfo supplementInfo = supplementMapper.selectByAgentId(agentId);

        if (supplementInfo != null) {
            // 使用补充资料中的富文本数据
            usageScenarios.setAgentIntroduction(supplementInfo.getAgentIntroduction());
            usageScenarios.setUsageScenariosRichText(supplementInfo.getUsageScenarios());
            usageScenarios.setPainPointsSolvedRichText(supplementInfo.getPainPointsSolved());

            // 解析截图URL列表
            if (StringUtils.hasText(supplementInfo.getScreenshotUrls())) {
                try {
                    List<String> screenshots = objectMapper.readValue(
                            supplementInfo.getScreenshotUrls(),
                            new TypeReference<List<String>>() {
                            }
                    );
                    usageScenarios.setScreenshots(screenshots);
                } catch (Exception e) {
                    log.warn("解析截图URL失败: {}", e.getMessage());
                    usageScenarios.setScreenshots(Collections.emptyList());
                }
            } else {
                usageScenarios.setScreenshots(Collections.emptyList());
            }
        } else {
            // 如果没有补充资料，使用默认数据
            usageScenarios.setAgentIntroduction("");
            usageScenarios.setUsageScenariosRichText("");
            usageScenarios.setPainPointsSolvedRichText("");
            usageScenarios.setScreenshots(Collections.emptyList());
        }

        // 保留原有的基本信息
        usageScenarios.setDescription(agent.getDescription());
        usageScenarios.setBusinessTypes(Arrays.asList("国际空运", "国内空运"));
        usageScenarios.setTargetUsers(Arrays.asList("货代操作人员", "客服人员"));

        return usageScenarios;
    }

    /**
     * 构建调用历史
     */
    private List<AgentApprovalDetailVO.CallHistory> buildCallHistory(Long agentId) {
        // 查询最近10次调用记录
        LambdaQueryWrapper<RecognitionRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RecognitionRecord::getAgentId, agentId)
                .orderByDesc(RecognitionRecord::getCreatedTime)
                .last("LIMIT 10");

        List<RecognitionRecord> records = recognitionRecordMapper.selectList(queryWrapper);

        return records.stream().map(record -> {
            AgentApprovalDetailVO.CallHistory callHistory = new AgentApprovalDetailVO.CallHistory();
            callHistory.setId(record.getId());
            callHistory.setCallTime(record.getCreatedTime());
            callHistory.setParams("{\"file\":\"" + record.getFileName() + "\"}");
            callHistory.setResponse(record.getRecognitionResult());
            callHistory.setResponseTime(record.getProcessingTime());
            callHistory.setStatus(record.getStatus() == 1 ? "成功" : "失败");
            return callHistory;
        }).collect(java.util.stream.Collectors.toList());
    }
}
