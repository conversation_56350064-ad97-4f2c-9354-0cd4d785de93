package com.sinoair.agent.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinoair.agent.entity.Role;
import com.sinoair.agent.entity.RolePermission;
import com.sinoair.agent.mapper.RoleMapper;
import com.sinoair.agent.service.RolePermissionService;
import com.sinoair.agent.service.RoleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 角色服务实现类
 *
 * <AUTHOR> Team
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RoleServiceImpl extends ServiceImpl<RoleMapper, Role> implements RoleService {

    private final RoleMapper roleMapper;
    private final RolePermissionService rolePermissionService;

    @Override
    public IPage<Role> getPageList(int page, int size, String keyword) {
        Page<Role> pageParam = new Page<>(page, size);
        IPage<Role> result = roleMapper.selectPageWithKeyword(pageParam, keyword);

        // 查询每个角色关联的用户数量
        result.getRecords().forEach(role -> {
            Integer userCount = roleMapper.countUsersByRoleId(role.getId());
            role.setUserCount(userCount);
        });

        return result;
    }

    @Override
    public List<Role> getRolesByUserId(Long userId) {
        return roleMapper.selectRolesByUserId(userId);
    }

    @Override
    public List<Role> getAllActiveRoles() {
        return roleMapper.findAllActive();
    }

    @Override
    public Role getRoleByCode(String roleCode) {
        return roleMapper.findByRoleCode(roleCode);
    }

    @Override
    public Role getRoleByName(String roleName) {
        return roleMapper.findByRoleName(roleName);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createRole(Role role) {
        // 检查角色编码是否已存在
        if (existsByRoleCode(role.getRoleCode())) {
            throw new RuntimeException("角色编码已存在");
        }

        // 检查角色名称是否已存在
        if (existsByRoleName(role.getRoleName())) {
            throw new RuntimeException("角色名称已存在");
        }

        if (role.getStatus() == null) {
            role.setStatus(1);
        }
        if (role.getSortOrder() == null) {
            role.setSortOrder(0);
        }

        role.setCreatedTime(LocalDateTime.now());
        role.setUpdatedTime(LocalDateTime.now());

        return save(role);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateRole(Role role) {
        // 检查角色是否存在
        Role existingRole = getById(role.getId());
        if (existingRole == null) {
            throw new RuntimeException("角色不存在");
        }

        // 检查角色编码是否已被其他角色使用
        Role roleByCode = getRoleByCode(role.getRoleCode());
        if (roleByCode != null && !roleByCode.getId().equals(role.getId())) {
            throw new RuntimeException("角色编码已被其他角色使用");
        }

        // 检查角色名称是否已被其他角色使用
        Role roleByName = getRoleByName(role.getRoleName());
        if (roleByName != null && !roleByName.getId().equals(role.getId())) {
            throw new RuntimeException("角色名称已被其他角色使用");
        }

        role.setUpdatedTime(LocalDateTime.now());
        return updateById(role);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteRole(Long roleId) {
        // 检查角色是否存在
        Role role = getById(roleId);
        if (role == null) {
            throw new RuntimeException("角色不存在");
        }

        // 检查是否有用户关联此角色
        Integer userCount = roleMapper.countUsersByRoleId(roleId);
        if (userCount > 0) {
            throw new RuntimeException("该角色下还有用户，无法删除");
        }

        // 删除角色权限关联
        rolePermissionService.deleteByRoleId(roleId);

        // 删除角色
        return removeById(roleId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteRoles(List<Long> roleIds) {
        for (Long roleId : roleIds) {
            deleteRole(roleId);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean assignPermissions(Long roleId, List<Long> permissionIds) {
        // 检查角色是否存在
        Role role = getById(roleId);
        if (role == null) {
            throw new RuntimeException("角色不存在");
        }

        // 删除原有的角色权限关联
        rolePermissionService.deleteByRoleId(roleId);

        // 添加新的角色权限关联
        if (permissionIds != null && !permissionIds.isEmpty()) {
            List<RolePermission> rolePermissions = permissionIds.stream()
                    .map(permissionId -> {
                        RolePermission rp = new RolePermission();
                        rp.setRoleId(roleId);
                        rp.setPermissionId(permissionId);
                        rp.setCreatedTime(LocalDateTime.now());
                        return rp;
                    })
                    .collect(Collectors.toList());

            return rolePermissionService.saveBatch(rolePermissions);
        }

        return true;
    }

    @Override
    public List<Role> getRolesWithPermissions() {
        return roleMapper.selectRolesWithPermissions();
    }

    @Override
    public Role getRoleWithPermissions(Long roleId) {
        return roleMapper.selectRoleWithPermissions(roleId);
    }

    @Override
    public boolean updateRoleStatus(Long roleId, Integer status) {
        Role role = new Role();
        role.setId(roleId);
        role.setStatus(status);
        role.setUpdatedTime(LocalDateTime.now());
        return updateById(role);
    }

    @Override
    public boolean existsByRoleCode(String roleCode) {
        if (!StringUtils.hasText(roleCode)) {
            return false;
        }
        return getRoleByCode(roleCode) != null;
    }

    @Override
    public boolean existsByRoleName(String roleName) {
        if (!StringUtils.hasText(roleName)) {
            return false;
        }
        return getRoleByName(roleName) != null;
    }

    @Override
    public Long getRoleCount() {
        return roleMapper.countRoles();
    }

    @Override
    public Long getActiveRoleCount() {
        return roleMapper.countActiveRoles();
    }
}
