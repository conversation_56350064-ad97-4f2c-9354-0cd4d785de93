package com.sinoair.agent.service.impl;

import com.sinoair.agent.entity.Role;
import com.sinoair.agent.entity.User;
import com.sinoair.agent.mapper.RoleMapper;
import com.sinoair.agent.mapper.UserMapper;
import com.sinoair.agent.security.UserPrincipal;
import com.sinoair.agent.service.PermissionService;
import com.sinoair.agent.service.UserRoleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Spring Security用户详情服务实现
 * 
 * <AUTHOR> Team
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserDetailsServiceImpl implements UserDetailsService {

    private final UserMapper userMapper;
    private final RoleMapper roleMapper;
    private final UserRoleService userRoleService;
    private final PermissionService permissionService;

    @Override
    @Transactional(readOnly = true)
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        log.debug("加载用户详情: username={}", username);

        User user = userMapper.findByUsernameAndStatus(username, 1);
        if (user == null) {
            log.error("用户不存在或已被禁用: {}", username);
            throw new UsernameNotFoundException("用户不存在或已被禁用: " + username);
        }

        log.debug("找到用户: id={}, username={}, status={}",
                user.getId(), user.getUsername(), user.getStatus());

        // 获取用户的角色列表
        List<Long> roleIds = userRoleService.getRoleIdsByUserId(user.getId());
        if (roleIds.isEmpty()) {
            log.error("用户没有分配角色: userId={}", user.getId());
            throw new UsernameNotFoundException("用户没有分配角色: " + user.getId());
        }

        // 获取第一个角色作为主角色（兼容旧系统）
        Role role = roleMapper.selectById(roleIds.get(0));
        if (role == null) {
            log.error("用户主角色不存在: roleId={}", roleIds.get(0));
            throw new UsernameNotFoundException("用户主角色不存在: " + roleIds.get(0));
        }

        log.debug("找到主角色: id={}, roleCode={}, roleName={}",
                role.getId(), role.getRoleCode(), role.getRoleName());

        // 获取用户的所有权限
        List<String> permissions = permissionService.getButtonPermissionsByUserId(user.getId());

        // 添加角色权限
        permissions.add("ROLE_" + role.getRoleCode());

        log.debug("获取权限完成: permissions={}", permissions);

        UserPrincipal userPrincipal = UserPrincipal.create(user, role.getId(), role.getRoleCode(), permissions);
        log.debug("创建UserPrincipal成功: username={}, permissions={}", userPrincipal.getUsername(), permissions);

        return userPrincipal;
    }


}
