package com.sinoair.agent.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sinoair.agent.entity.SysMessage;
import com.sinoair.agent.mapper.SysMessageMapper;
import com.sinoair.agent.service.SysMessageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
 * 站内消息服务实现类
 *
 * <AUTHOR> Team
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SysMessageServiceImpl implements SysMessageService {

    private final SysMessageMapper messageMapper;

    @Override
    public boolean sendMessage(Long userId, String title, String content, Integer messageType, Long relatedId, String relatedType) {
        try {
            SysMessage message = new SysMessage();
            message.setUserId(userId);
            message.setTitle(title);
            message.setContent(content);
            message.setMessageType(messageType);
            message.setRelatedId(relatedId);
            message.setRelatedType(relatedType);
            message.setIsRead(0);
            message.setCreatedTime(LocalDateTime.now());
            message.setUpdatedTime(LocalDateTime.now());

            messageMapper.insert(message);

            log.info("发送消息成功: userId={}, title={}", userId, title);
            return true;
        } catch (Exception e) {
            log.error("发送消息失败", e);
            return false;
        }
    }

    @Override
    public boolean sendApprovalNotification(Long userId, String agentName, boolean approved, String opinion, Long agentId) {
        String title = String.format("Agent审批通知 - %s", agentName);
        String content = String.format(
                "您的Agent \"%s\" 审批%s。\n\n审批意见：%s",
                agentName,
                approved ? "已通过" : "未通过",
                opinion != null ? opinion : "无"
        );

        return sendMessage(userId, title, content, 2, agentId, "AGENT_APPROVAL");
    }

    @Override
    public int getUnreadCount(Long userId) {
        return messageMapper.selectUnreadCountByUserId(userId);
    }

    @Override
    public List<SysMessage> getUserMessages(Long userId, Integer limit) {
        return messageMapper.selectByUserId(userId, limit);
    }

    @Override
    public boolean markAsRead(List<Long> messageIds, Long userId) {
        try {
            messageMapper.batchMarkAsRead(messageIds, userId);
            return true;
        } catch (Exception e) {
            log.error("标记消息已读失败", e);
            return false;
        }
    }

    @Override
    public boolean markAllAsRead(Long userId) {
        try {
            messageMapper.batchMarkAsRead(null, userId);
            return true;
        } catch (Exception e) {
            log.error("标记所有消息已读失败", e);
            return false;
        }
    }
}
