package com.sinoair.agent.service.impl;

import com.sinoair.agent.service.EmailService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Random;
import java.util.concurrent.TimeUnit;

/**
 * 开发环境邮件服务实现类
 * 当 mail.enabled=false 时使用此实现
 */
@Slf4j
@Service
@ConditionalOnProperty(name = "mail.enabled", havingValue = "false", matchIfMissing = false)
public class DevEmailServiceImpl implements EmailService {

    @Autowired(required = false)
    private StringRedisTemplate redisTemplate;
    
    private static final String VERIFICATION_CODE_PREFIX = "verification_code:";
    private static final String SEND_FREQUENCY_PREFIX = "send_frequency:";
    private static final int CODE_EXPIRE_MINUTES = 5;
    private static final int SEND_FREQUENCY_SECONDS = 60;
    
    // 开发环境固定验证码（方便测试）
    private static final String DEV_CODE = "123456";
    
    @Override
    public boolean sendVerificationCode(String email, String code) {
        log.info("=== 开发环境模拟发送验证码 ===");
        log.info("收件人：{}", email);
        log.info("验证码：{}", code);
        log.info("提示：开发环境下，您可以使用固定验证码 {} 进行登录", DEV_CODE);
        log.info("================================");
        return true;
    }
    
    @Override
    public String generateVerificationCode() {
        // 开发环境返回固定验证码，方便测试
        return DEV_CODE;
    }
    
    @Override
    public boolean verifyCode(String email, String code) {
        // 开发环境允许使用固定验证码
        if (DEV_CODE.equals(code)) {
            log.info("开发环境：使用固定验证码 {} 验证成功", DEV_CODE);
            return true;
        }

        // 如果Redis可用，检查存储的验证码
        if (redisTemplate != null) {
            try {
                String key = VERIFICATION_CODE_PREFIX + email;
                String storedCode = redisTemplate.opsForValue().get(key);

                if (storedCode != null && storedCode.equals(code)) {
                    // 验证成功后删除验证码
                    redisTemplate.delete(key);
                    return true;
                }
            } catch (Exception e) {
                log.warn("Redis不可用，跳过验证码检查：{}", e.getMessage());
            }
        }

        return false;
    }
    
    @Override
    public void storeVerificationCode(String email, String code) {
        if (redisTemplate != null) {
            try {
                String key = VERIFICATION_CODE_PREFIX + email;
                redisTemplate.opsForValue().set(key, code, CODE_EXPIRE_MINUTES, TimeUnit.MINUTES);
                log.debug("验证码已存储到Redis，邮箱：{}，验证码：{}", email, code);
            } catch (Exception e) {
                log.warn("Redis不可用，跳过验证码存储：{}", e.getMessage());
            }
        } else {
            log.debug("Redis不可用，开发环境跳过验证码存储，邮箱：{}，验证码：{}", email, code);
        }
    }
    
    @Override
    public boolean checkSendFrequency(String email) {
        if (redisTemplate != null) {
            try {
                String key = SEND_FREQUENCY_PREFIX + email;
                String lastSendTime = redisTemplate.opsForValue().get(key);

                if (lastSendTime == null) {
                    // 记录发送时间
                    redisTemplate.opsForValue().set(key, String.valueOf(System.currentTimeMillis()),
                            SEND_FREQUENCY_SECONDS, TimeUnit.SECONDS);
                    return true;
                }
                return false;
            } catch (Exception e) {
                log.warn("Redis不可用，跳过发送频率检查：{}", e.getMessage());
                return true; // Redis不可用时允许发送
            }
        } else {
            log.debug("Redis不可用，开发环境跳过发送频率检查");
            return true; // 开发环境允许发送
        }
    }
}
