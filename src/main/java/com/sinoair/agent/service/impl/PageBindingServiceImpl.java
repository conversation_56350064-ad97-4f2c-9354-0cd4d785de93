package com.sinoair.agent.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sinoair.agent.common.Result;
import com.sinoair.agent.dto.response.AgentVO;
import com.sinoair.agent.entity.Agent;
import com.sinoair.agent.entity.BusinessTemplate;
import com.sinoair.agent.entity.PageBinding;
import com.sinoair.agent.dto.response.AgentVO;
import com.sinoair.agent.mapper.PageBindingMapper;
import com.sinoair.agent.service.AgentService;
import com.sinoair.agent.service.BusinessTemplateService;
import com.sinoair.agent.service.PageBindingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 页面绑定服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PageBindingServiceImpl implements PageBindingService {

    private final PageBindingMapper pageBindingMapper;
    private final AgentService agentService;
    private final BusinessTemplateService businessTemplateService;
    private final ObjectMapper objectMapper;

    @Override
    public List<PageBinding> list() {
        // 查询所有主绑定（包含模板信息）
        List<PageBinding> mainBindings = pageBindingMapper.selectAllWithTemplate();

        // 为每个主绑定查询子步骤
        for (PageBinding mainBinding : mainBindings) {
            if (mainBinding.getIsMultiStep() != null && mainBinding.getIsMultiStep() == 1) {
                List<PageBinding> subSteps = findSubStepsByParentId(mainBinding.getId());
                log.info("主绑定ID: {}, 名称: {}, 模板: {}, 子步骤数量: {}",
                    mainBinding.getId(), mainBinding.getBindingName(),
                    mainBinding.getTemplateName(), subSteps.size());
                mainBinding.setSubSteps(subSteps);
            } else {
                log.info("单步骤绑定ID: {}, 名称: {}, 模板: {}",
                    mainBinding.getId(), mainBinding.getBindingName(), mainBinding.getTemplateName());
            }
        }

        return mainBindings;
    }

    @Override
    public PageBinding getById(Long id) {
        return pageBindingMapper.selectById(id);
    }

    @Override
    public boolean save(PageBinding entity) {
        try {
            pageBindingMapper.insert(entity);
            return true;
        } catch (Exception e) {
            log.error("保存页面绑定失败", e);
            return false;
        }
    }

    @Override
    public boolean updateById(PageBinding entity) {
        try {
            pageBindingMapper.updateById(entity);
            return true;
        } catch (Exception e) {
            log.error("更新页面绑定失败", e);
            return false;
        }
    }

    @Override
    public boolean removeById(Long id) {
        try {
            pageBindingMapper.deleteById(id);
            return true;
        } catch (Exception e) {
            log.error("删除页面绑定失败", e);
            return false;
        }
    }

    @Override
    public List<PageBinding> findByUrlPattern(String url) {
        List<PageBinding> mainBindings = pageBindingMapper.findByUrl(url);

        // 为每个多步骤主绑定查询子步骤
        for (PageBinding mainBinding : mainBindings) {
            if (mainBinding.getIsMultiStep() != null && mainBinding.getIsMultiStep() == 1) {
                List<PageBinding> subSteps = findSubStepsByParentId(mainBinding.getId());
                log.info("URL匹配 - 主绑定ID: {}, 名称: {}, 子步骤数量: {}",
                    mainBinding.getId(), mainBinding.getBindingName(), subSteps.size());
                mainBinding.setSubSteps(subSteps);
            }
        }

        return mainBindings;
    }

    @Override
    public List<PageBinding> findByTemplateId(Long templateId) {
        return pageBindingMapper.findByTemplateId(templateId);
    }

    @Override
    public List<PageBinding> getActiveBindings() {
        QueryWrapper<PageBinding> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", 1).eq("deleted", 0).orderByDesc("created_time");
        return pageBindingMapper.selectList(queryWrapper);
    }

    @Override
    public Map<String, Object> getAutoFillData(String currentUrl, Long agentId) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 1. 根据agentId获取Agent信息
            Result<AgentVO> agentResult = agentService.getAgent(agentId);
            if (agentResult.getCode() != 200 || agentResult.getData() == null) {
                result.put("success", false);
                result.put("message", "Agent不存在");
                return result;
            }

            AgentVO agentVO = agentResult.getData();

            // 2. 获取Agent关联的业务模板
            BusinessTemplate template = businessTemplateService.getById(agentVO.getTemplateId());
            if (template == null) {
                result.put("success", false);
                result.put("message", "业务模板不存在");
                return result;
            }

            // 3. 根据URL查找匹配的绑定配置
            List<PageBinding> matchingBindings = findByUrlPattern(currentUrl);

            // 4. 过滤出与当前模板匹配的绑定
            List<PageBinding> templateBindings = matchingBindings.stream()
                    .filter(binding -> binding.getTemplateId().equals(template.getId()))
                    .collect(Collectors.toList());

            if (templateBindings.isEmpty()) {
                result.put("success", false);
                result.put("message", "未找到匹配的页面绑定配置");
                result.put("availableBindings", matchingBindings.size());
                result.put("templateId", template.getId());
                return result;
            }

            // 5. 如果有多个匹配的绑定，返回让用户选择
            if (templateBindings.size() > 1) {
                result.put("success", true);
                result.put("multipleBindings", true);
                result.put("message", "找到多个匹配的绑定配置，请选择一个");
                result.put("bindings", templateBindings.stream().map(binding -> {
                    Map<String, Object> bindingInfo = new HashMap<>();
                    bindingInfo.put("id", binding.getId());
                    bindingInfo.put("bindingName", binding.getBindingName());
                    bindingInfo.put("urlPattern", binding.getUrlPattern());
                    bindingInfo.put("targetUrl", binding.getTargetUrl());
                    bindingInfo.put("templateId", binding.getTemplateId());
                    bindingInfo.put("templateName", binding.getTemplateName());
                    bindingInfo.put("isMultiStep", binding.getIsMultiStep());
                    bindingInfo.put("createdTime", binding.getCreatedTime());

                    // 添加完整的绑定配置信息
                    bindingInfo.put("bindingConfig", binding.getBindingConfig());
                    bindingInfo.put("nextAction", binding.getNextAction());
                    bindingInfo.put("waitTime", binding.getWaitTime());
                    bindingInfo.put("stepName", binding.getStepName());
                    bindingInfo.put("stepOrder", binding.getStepOrder());
                    bindingInfo.put("isFinalStep", binding.getIsFinalStep());

                    // 如果是多步骤绑定，添加子步骤信息
                    if (binding.getIsMultiStep() != null && binding.getIsMultiStep() == 1 && binding.getSubSteps() != null) {
                        bindingInfo.put("subSteps", binding.getSubSteps());
                    }
                    return bindingInfo;
                }).collect(Collectors.toList()));
                return result;
            }

            // 6. 使用唯一匹配的绑定配置
            PageBinding selectedBinding = templateBindings.get(0);

            // 7. 解析绑定配置
            Map<String, Object> bindingConfig = objectMapper.readValue(
                selectedBinding.getBindingConfig(),
                new TypeReference<Map<String, Object>>() {}
            );

            // 8. 解析业务模板的JSON结构
            Map<String, Object> templateJson = objectMapper.readValue(
                template.getJsonTemplate(),
                new TypeReference<Map<String, Object>>() {}
            );

            // 9. 构建自动填充数据
            result.put("success", true);
            result.put("message", "找到匹配的绑定配置");

            // 构建绑定信息，包含完整的多步骤信息
            Map<String, Object> bindingInfo = new HashMap<>();
            bindingInfo.put("id", selectedBinding.getId());
            bindingInfo.put("bindingName", selectedBinding.getBindingName());
            bindingInfo.put("urlPattern", selectedBinding.getUrlPattern());
            bindingInfo.put("targetUrl", selectedBinding.getTargetUrl());
            bindingInfo.put("templateId", selectedBinding.getTemplateId());
            bindingInfo.put("templateName", selectedBinding.getTemplateName());
            bindingInfo.put("isMultiStep", selectedBinding.getIsMultiStep());
            bindingInfo.put("bindingConfig", selectedBinding.getBindingConfig());
            bindingInfo.put("nextAction", selectedBinding.getNextAction());
            bindingInfo.put("waitTime", selectedBinding.getWaitTime());
            bindingInfo.put("stepName", selectedBinding.getStepName());
            bindingInfo.put("stepOrder", selectedBinding.getStepOrder());
            bindingInfo.put("isFinalStep", selectedBinding.getIsFinalStep());

            // 如果是多步骤绑定，添加子步骤信息
            if (selectedBinding.getIsMultiStep() != null && selectedBinding.getIsMultiStep() == 1) {
                List<PageBinding> subSteps = findSubStepsByParentId(selectedBinding.getId());
                log.info("获取多步骤绑定的子步骤: bindingId={}, 子步骤数量={}",
                    selectedBinding.getId(), subSteps.size());
                bindingInfo.put("subSteps", subSteps);
            }

            result.put("binding", bindingInfo);
            result.put("template", Map.of(
                "id", template.getId(),
                "name", template.getTemplateName(),
                "fields", templateJson
            ));
            result.put("bindingConfig", bindingConfig);
            result.put("agent", Map.of(
                "id", agentVO.getId(),
                "name", agentVO.getAgentName()
            ));

            log.info("成功获取自动填充数据: url={}, agentId={}, bindingId={}",
                currentUrl, agentId, selectedBinding.getId());

        } catch (Exception e) {
            log.error("获取自动填充数据失败", e);
            result.put("success", false);
            result.put("message", "处理失败: " + e.getMessage());
        }

        return result;
    }

    @Override
    public Map<String, Object> getMultiStepAutoFillData(String currentUrl, Long agentId) {
        Map<String, Object> result = new HashMap<>();

        try {
            log.info("检查多步骤自动填充数据，URL: {}, Agent ID: {}", currentUrl, agentId);

            // 1. 根据Agent ID获取业务模板
            Result<AgentVO> agentResult = agentService.getAgent(agentId);
            if (!agentResult.isSuccess() || agentResult.getData() == null || agentResult.getData().getTemplateId() == null) {
                result.put("success", false);
                result.put("message", "Agent不存在或未配置业务模板");
                return result;
            }

            AgentVO agentVO = agentResult.getData();
            // 创建Agent实体用于后续处理
            Agent agent = new Agent();
            agent.setId(agentVO.getId());
            agent.setTemplateId(agentVO.getTemplateId());
            agent.setAgentName(agentVO.getAgentName());
            agent.setAgentCode(agentVO.getAgentCode());

            BusinessTemplate template = businessTemplateService.getById(agent.getTemplateId());
            if (template == null) {
                result.put("success", false);
                result.put("message", "业务模板不存在");
                return result;
            }

            // 2. 查找多步骤绑定配置
            List<PageBinding> multiStepBindings = pageBindingMapper.findMultiStepBindings(currentUrl);

            // 3. 过滤出与当前模板匹配的多步骤绑定
            List<PageBinding> templateMultiStepBindings = multiStepBindings.stream()
                    .filter(binding -> binding.getTemplateId().equals(template.getId()))
                    .collect(Collectors.toList());

            if (templateMultiStepBindings.isEmpty()) {
                // 如果没有多步骤绑定，尝试查找单步骤绑定
                return getAutoFillData(currentUrl, agentId);
            }

            // 4. 获取第一个匹配的多步骤绑定
            PageBinding mainBinding = templateMultiStepBindings.get(0);

            // 5. 获取所有子步骤
            List<PageBinding> subSteps = pageBindingMapper.findSubStepsByParentId(mainBinding.getId());
            mainBinding.setSubSteps(subSteps);

            result.put("success", true);
            result.put("isMultiStep", true);
            result.put("binding", mainBinding);
            result.put("template", template);
            result.put("agent", agent);
            result.put("totalSteps", subSteps.size() + 1); // 包含主步骤
            result.put("message", "找到多步骤绑定配置");

            log.info("找到多步骤绑定配置: {}, 总步骤数: {}", mainBinding.getBindingName(), subSteps.size() + 1);

            return result;
        } catch (Exception e) {
            log.error("获取多步骤自动填充数据失败", e);
            result.put("success", false);
            result.put("message", "获取多步骤自动填充数据失败: " + e.getMessage());
            return result;
        }
    }

    @Override
    public List<PageBinding> findSubStepsByParentId(Long parentBindingId) {
        return pageBindingMapper.findSubStepsByParentId(parentBindingId);
    }

    @Override
    @Transactional
    public boolean saveMultiStepBinding(PageBinding mainBinding, List<PageBinding> subSteps) {
        try {
            // 1. 设置主绑定为多步骤
            mainBinding.setIsMultiStep(1);
            mainBinding.setParentBindingId(null);
            mainBinding.setStepOrder(1);
            mainBinding.setIsFinalStep(subSteps.isEmpty() ? 1 : 0);

            // 2. 保存主绑定
            pageBindingMapper.insert(mainBinding);
            Long mainBindingId = mainBinding.getId();

            // 3. 保存子步骤
            for (int i = 0; i < subSteps.size(); i++) {
                PageBinding subStep = subSteps.get(i);
                subStep.setIsMultiStep(1);
                subStep.setParentBindingId(mainBindingId);
                subStep.setStepOrder(i + 2); // 从第2步开始
                subStep.setIsFinalStep(i == subSteps.size() - 1 ? 1 : 0);

                // 确保必要字段有值
                if (subStep.getTargetUrl() == null) {
                    subStep.setTargetUrl(mainBinding.getTargetUrl() != null ? mainBinding.getTargetUrl() : "");
                }
                if (subStep.getTemplateId() == null) {
                    subStep.setTemplateId(mainBinding.getTemplateId());
                }
                if (subStep.getUrlPattern() == null) {
                    subStep.setUrlPattern(mainBinding.getUrlPattern());
                }

                pageBindingMapper.insert(subStep);
            }

            log.info("保存多步骤绑定成功，主绑定ID: {}, 子步骤数: {}", mainBindingId, subSteps.size());
            return true;
        } catch (Exception e) {
            log.error("保存多步骤绑定失败", e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean deleteBindingWithSubSteps(Long id) {
        try {
            // 获取主绑定信息
            PageBinding mainBinding = pageBindingMapper.selectById(id);
            if (mainBinding == null) {
                log.warn("要删除的绑定不存在: {}", id);
                return false;
            }

            log.info("开始删除绑定配置: ID={}, 名称={}, 是否多步骤={}",
                id, mainBinding.getBindingName(), mainBinding.getIsMultiStep());

            // 如果是多步骤绑定，先删除所有子步骤
            if (mainBinding.getIsMultiStep() != null && mainBinding.getIsMultiStep() == 1) {
                List<PageBinding> subSteps = findSubStepsByParentId(id);
                if (!subSteps.isEmpty()) {
                    log.info("删除 {} 个子步骤", subSteps.size());
                    for (PageBinding subStep : subSteps) {
                        pageBindingMapper.deleteById(subStep.getId());
                        log.info("已删除子步骤: ID={}, 名称={}", subStep.getId(), subStep.getBindingName());
                    }
                }
            }

            // 删除主绑定
            int result = pageBindingMapper.deleteById(id);
            boolean success = result > 0;

            if (success) {
                log.info("绑定配置删除成功: ID={}", id);
            } else {
                log.error("绑定配置删除失败: ID={}", id);
            }

            return success;
        } catch (Exception e) {
            log.error("删除绑定配置时发生异常: ID={}", id, e);
            throw new RuntimeException("删除绑定配置失败", e);
        }
    }
}
