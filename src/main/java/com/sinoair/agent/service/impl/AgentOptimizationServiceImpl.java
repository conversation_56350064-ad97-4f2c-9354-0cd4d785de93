package com.sinoair.agent.service.impl;

import com.sinoair.agent.entity.AgentOptimization;
import com.sinoair.agent.mapper.AgentOptimizationMapper;
import com.sinoair.agent.service.AgentOptimizationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Agent优化参数服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AgentOptimizationServiceImpl implements AgentOptimizationService {

    private final AgentOptimizationMapper agentOptimizationMapper;

    @Override
    public List<AgentOptimization> list() {
        return agentOptimizationMapper.selectList(null);
    }

    @Override
    public AgentOptimization getById(Long id) {
        return agentOptimizationMapper.selectById(id);
    }

    @Override
    public boolean save(AgentOptimization entity) {
        try {
            if (entity.getCreatedTime() == null) {
                entity.setCreatedTime(LocalDateTime.now());
            }
            entity.setUpdatedTime(LocalDateTime.now());
            agentOptimizationMapper.insert(entity);
            return true;
        } catch (Exception e) {
            log.error("保存Agent优化配置失败", e);
            return false;
        }
    }

    @Override
    public boolean updateById(AgentOptimization entity) {
        try {
            entity.setUpdatedTime(LocalDateTime.now());
            agentOptimizationMapper.updateById(entity);
            return true;
        } catch (Exception e) {
            log.error("更新Agent优化配置失败", e);
            return false;
        }
    }

    @Override
    public boolean removeById(Long id) {
        try {
            agentOptimizationMapper.deleteById(id);
            return true;
        } catch (Exception e) {
            log.error("删除Agent优化配置失败", e);
            return false;
        }
    }

    @Override
    public List<AgentOptimization> findByAgentId(Long agentId) {
        return agentOptimizationMapper.findByAgentIdOrderByCreatedTimeDesc(agentId);
    }

    @Override
    public AgentOptimization getActiveByAgentId(Long agentId) {
        return agentOptimizationMapper.findActiveByAgentId(agentId);
    }

    @Override
    @Transactional
    public boolean activateOptimization(Long optimizationId) {
        try {
            AgentOptimization optimization = getById(optimizationId);
            if (optimization == null) {
                return false;
            }
            
            // 先将该Agent的所有优化配置设为非激活状态
            List<AgentOptimization> allOptimizations = findByAgentId(optimization.getAgentId());
            for (AgentOptimization opt : allOptimizations) {
                opt.setIsActive(0);
                agentOptimizationMapper.updateById(opt);
            }

            // 激活指定的优化配置
            optimization.setIsActive(1);
            agentOptimizationMapper.updateById(optimization);
            
            return true;
        } catch (Exception e) {
            log.error("激活Agent优化配置失败", e);
            return false;
        }
    }

    @Override
    public long countByAgentId(Long agentId) {
        return agentOptimizationMapper.countByAgentId(agentId);
    }
}
