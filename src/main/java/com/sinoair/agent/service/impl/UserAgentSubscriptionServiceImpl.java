package com.sinoair.agent.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinoair.agent.dto.response.UserSubscriptionDTO;
import com.sinoair.agent.entity.Agent;
import com.sinoair.agent.entity.AgentCategory;
import com.sinoair.agent.entity.BusinessType;
import com.sinoair.agent.entity.UserAgentSubscription;
import com.sinoair.agent.mapper.BusinessTypeMapper;
import com.sinoair.agent.mapper.UserAgentSubscriptionMapper;
import com.sinoair.agent.service.AgentCategoryService;
import com.sinoair.agent.service.AgentService;
import com.sinoair.agent.service.UserAgentSubscriptionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 用户Agent订阅服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserAgentSubscriptionServiceImpl extends ServiceImpl<UserAgentSubscriptionMapper, UserAgentSubscription>
        implements UserAgentSubscriptionService {

    private final AgentService agentService;
    private final AgentCategoryService agentCategoryService;
    private final BusinessTypeMapper businessTypeMapper;

    @Override
    @Transactional
    public boolean subscribeAgent(Long userId, Long agentId) {
        try {
            // 检查是否已经存在订阅记录
            UserAgentSubscription existingSubscription = baseMapper.getUserAgentSubscription(userId, agentId);
            
            if (existingSubscription != null) {
                if (existingSubscription.isSubscribed()) {
                    log.info("用户已订阅该Agent，用户ID：{}，Agent ID：{}", userId, agentId);
                    return true;
                } else {
                    // 重新订阅
                    existingSubscription.setSubscribed();
                    existingSubscription.setUpdatedTime(LocalDateTime.now());
                    return updateById(existingSubscription);
                }
            } else {
                // 创建新的订阅记录
                UserAgentSubscription subscription = new UserAgentSubscription()
                        .setUserId(userId)
                        .setAgentId(agentId)
                        .setSubscribed();
                return save(subscription);
            }
        } catch (Exception e) {
            log.error("订阅Agent失败，用户ID：{}，Agent ID：{}", userId, agentId, e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean unsubscribeAgent(Long userId, Long agentId) {
        try {
            UserAgentSubscription subscription = baseMapper.getUserAgentSubscription(userId, agentId);
            if (subscription != null && subscription.isSubscribed()) {
                subscription.setUnsubscribed();
                subscription.setUpdatedTime(LocalDateTime.now());
                return updateById(subscription);
            }
            return true;
        } catch (Exception e) {
            log.error("取消订阅Agent失败，用户ID：{}，Agent ID：{}", userId, agentId, e);
            return false;
        }
    }

    @Override
    public boolean isUserSubscribed(Long userId, Long agentId) {
        try {
            int count = baseMapper.countUserAgentSubscription(userId, agentId);
            return count > 0;
        } catch (Exception e) {
            log.error("检查用户订阅状态失败，用户ID：{}，Agent ID：{}", userId, agentId, e);
            return false;
        }
    }

    @Override
    public List<Long> getUserSubscribedAgentIds(Long userId) {
        try {
            return baseMapper.getUserSubscribedAgentIds(userId);
        } catch (Exception e) {
            log.error("获取用户订阅的Agent ID列表失败，用户ID：{}", userId, e);
            return new ArrayList<>();
        }
    }

    @Override
    public int getAgentSubscriptionCount(Long agentId) {
        try {
            return baseMapper.getAgentSubscriptionCount(agentId);
        } catch (Exception e) {
            log.error("获取Agent订阅数量失败，Agent ID：{}", agentId, e);
            return 0;
        }
    }

    @Override
    @Transactional
    public boolean toggleSubscription(Long userId, Long agentId) {
        try {
            if (isUserSubscribed(userId, agentId)) {
                return unsubscribeAgent(userId, agentId);
            } else {
                return subscribeAgent(userId, agentId);
            }
        } catch (Exception e) {
            log.error("切换订阅状态失败，用户ID：{}，Agent ID：{}", userId, agentId, e);
            return false;
        }
    }

    @Override
    public List<UserSubscriptionDTO> getUserSubscriptions(Long userId) {
        try {
            // 获取用户订阅的Agent ID列表
            List<Long> agentIds = getUserSubscribedAgentIds(userId);
            List<UserSubscriptionDTO> subscriptions = new ArrayList<>();

            for (Long agentId : agentIds) {
                try {
                    // 获取Agent详细信息
                    Agent agent = agentService.getAgentById(agentId);
                    if (agent != null) {
                        UserSubscriptionDTO dto = new UserSubscriptionDTO();
                        dto.setAgentId(agentId);
                        dto.setAgentName(agent.getAgentName());
                        dto.setAgentCode(agent.getAgentCode());
                        dto.setAgentDescription(agent.getDescription());
                        dto.setCategoryId(agent.getCategoryId());
                        dto.setAgentStatus(agent.getStatus());
                        dto.setAgentType(agent.getAgentType());
                        dto.setAgentVersion(agent.getVersion());
                        dto.setBusinessTypeId(agent.getBusinessTypeId());
                        dto.setSubscriptionTime(LocalDateTime.now()); // 实际应该从订阅记录中获取
                        dto.setSubscriptionStatus(1); // 1-已订阅

                        // 获取分类名称
                        if (agent.getCategoryId() != null) {
                            try {
                                AgentCategory category = agentCategoryService.getById(agent.getCategoryId());
                                if (category != null) {
                                    dto.setCategoryName(category.getCategoryName());
                                    dto.setCategoryCode(category.getCategoryCode());
                                }
                            } catch (Exception e) {
                                log.warn("获取Agent分类信息失败，分类ID：{}", agent.getCategoryId(), e);
                            }
                        }

                        // 获取业务类型名称
                        if (agent.getBusinessTypeId() != null) {
                            try {
                                BusinessType businessType = businessTypeMapper.selectById(agent.getBusinessTypeId());
                                if (businessType != null) {
                                    dto.setBusinessTypeName(businessType.getTypeName());
                                    dto.setBusinessTypeCode(businessType.getTypeCode());
                                }
                            } catch (Exception e) {
                                log.warn("获取业务类型信息失败，业务类型ID：{}", agent.getBusinessTypeId(), e);
                            }
                        }

                        subscriptions.add(dto);
                    }
                } catch (Exception e) {
                    log.warn("获取Agent信息失败，Agent ID：{}", agentId, e);
                }
            }

            log.info("获取用户订阅列表成功，用户ID：{}，订阅数量：{}", userId, subscriptions.size());
            return subscriptions;
        } catch (Exception e) {
            log.error("获取用户订阅列表失败，用户ID：{}", userId, e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<UserSubscriptionDTO> getUserActiveSubscriptions(Long userId) {
        try {
            // 获取所有订阅，然后过滤出可用的Agent
            List<UserSubscriptionDTO> allSubscriptions = getUserSubscriptions(userId);
            List<UserSubscriptionDTO> activeSubscriptions = new ArrayList<>();

            for (UserSubscriptionDTO subscription : allSubscriptions) {
                // 只返回已发布状态的Agent
                if (subscription.getAgentStatus() != null && subscription.getAgentStatus() == Agent.STATUS_PUBLISHED) {
                    activeSubscriptions.add(subscription);
                }
            }

            log.info("获取用户有效订阅列表成功，用户ID：{}，有效订阅数量：{}", userId, activeSubscriptions.size());
            return activeSubscriptions;
        } catch (Exception e) {
            log.error("获取用户有效订阅列表失败，用户ID：{}", userId, e);
            return new ArrayList<>();
        }
    }

    @Override
    public int getUserSubscriptionCount(Long userId) {
        try {
            QueryWrapper<UserAgentSubscription> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("user_id", userId)
                       .eq("subscription_status", UserAgentSubscription.STATUS_SUBSCRIBED)
                       .eq("deleted", 0);
            return Math.toIntExact(count(queryWrapper));
        } catch (Exception e) {
            log.error("获取用户订阅数量失败，用户ID：{}", userId, e);
            return 0;
        }
    }

    @Override
    public int getUserActiveSubscriptionCount(Long userId) {
        try {
            // 简化实现，直接返回订阅数量
            // 实际实现需要联表查询Agent状态
            return getUserSubscriptionCount(userId);
        } catch (Exception e) {
            log.error("获取用户有效订阅数量失败，用户ID：{}", userId, e);
            return 0;
        }
    }
}
