package com.sinoair.agent.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sinoair.agent.entity.Agent;
import com.sinoair.agent.entity.CallHistory;
import com.sinoair.agent.entity.RecognitionRecord;
import com.sinoair.agent.entity.UploadedFile;
import com.sinoair.agent.mapper.RecognitionRecordMapper;
import com.sinoair.agent.service.AgentService;
import com.sinoair.agent.service.CallHistoryService;
import com.sinoair.agent.service.FileService;
import com.sinoair.agent.common.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 调用历史记录服务实现类
 * 简化实现版本，主要提供基础功能框架
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CallHistoryServiceImpl implements CallHistoryService {

    private final RecognitionRecordMapper recognitionRecordMapper;
    private final AgentService agentService;
    private final FileService fileService;

    @Override
    public IPage<CallHistory> getCallHistoryByUserId(Long userId, Page<CallHistory> page,
                                                   String agentName, String callType,
                                                   LocalDateTime startTime, LocalDateTime endTime) {
        try {
            log.info("查询用户{}的调用历史记录，页码：{}，大小：{}，Agent名称：{}，调用类型：{}",
                    userId, page.getCurrent(), page.getSize(), agentName, callType);

            // 基于RecognitionRecord查询调用历史
            Page<RecognitionRecord> recordPage = new Page<>(page.getCurrent(), page.getSize());

            // 构建查询条件
            QueryWrapper<RecognitionRecord> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("user_id", userId)
                       .eq("deleted", 0)
                       .orderByDesc("created_time");

            // 如果有Agent名称过滤，需要关联查询
            if (agentName != null && !agentName.trim().isEmpty()) {
                // 这里简化处理，实际应该使用联表查询
                log.debug("Agent名称过滤：{}", agentName);
            }

            // 时间范围过滤
            if (startTime != null) {
                queryWrapper.ge("created_time", startTime);
            }
            if (endTime != null) {
                queryWrapper.le("created_time", endTime);
            }

            IPage<RecognitionRecord> recordResult = recognitionRecordMapper.selectPage(recordPage, queryWrapper);

            // 转换为CallHistory
            List<CallHistory> callHistories = new ArrayList<>();
            for (RecognitionRecord record : recordResult.getRecords()) {
                CallHistory callHistory = convertToCallHistory(record);
                callHistories.add(callHistory);
            }

            // 构建返回结果
            Page<CallHistory> resultPage = new Page<>(page.getCurrent(), page.getSize());
            resultPage.setRecords(callHistories);
            resultPage.setTotal(recordResult.getTotal());
            resultPage.setPages(recordResult.getPages());

            log.info("查询到用户{}的调用历史记录{}条", userId, callHistories.size());
            return resultPage;
        } catch (Exception e) {
            log.error("查询用户调用历史失败，用户ID：{}", userId, e);
            Page<CallHistory> errorPage = new Page<>(page.getCurrent(), page.getSize());
            errorPage.setRecords(new ArrayList<>());
            errorPage.setTotal(0);
            return errorPage;
        }
    }

    @Override
    public CallHistory getCallHistoryById(Long id, Long userId) {
        try {
            log.info("查询调用历史详情，ID：{}，用户ID：{}", id, userId);

            // 构建查询条件：根据ID和用户ID查询，确保用户只能查看自己的记录
            QueryWrapper<RecognitionRecord> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("id", id)
                       .eq("user_id", userId)
                       .eq("deleted", 0);

            RecognitionRecord record = recognitionRecordMapper.selectOne(queryWrapper);

            if (record == null) {
                log.warn("未找到调用历史记录，ID：{}，用户ID：{}", id, userId);
                return null;
            }

            // 转换为CallHistory对象
            CallHistory callHistory = convertToCallHistory(record);

            log.info("查询到调用历史详情：Agent名称={}，调用类型={}，状态={}",
                    callHistory.getAgentName(), callHistory.getCallType(), callHistory.getCallStatus());

            return callHistory;
        } catch (Exception e) {
            log.error("查询调用历史详情失败，ID：{}，用户ID：{}", id, userId, e);
            return null;
        }
    }

    @Override
    public Long getSuccessCountByUserId(Long userId) {
        try {
            QueryWrapper<RecognitionRecord> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("user_id", userId)
                       .eq("status", 2) // 2-成功
                       .eq("deleted", 0);

            Long count = recognitionRecordMapper.selectCount(queryWrapper);
            log.info("获取用户{}成功调用次数：{}", userId, count);
            return count;
        } catch (Exception e) {
            log.error("获取用户成功调用次数失败，用户ID：{}", userId, e);
            return 0L;
        }
    }

    @Override
    public Long getFailedCountByUserId(Long userId) {
        try {
            QueryWrapper<RecognitionRecord> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("user_id", userId)
                       .eq("status", 3) // 3-失败
                       .eq("deleted", 0);

            Long count = recognitionRecordMapper.selectCount(queryWrapper);
            log.info("获取用户{}失败调用次数：{}", userId, count);
            return count;
        } catch (Exception e) {
            log.error("获取用户失败调用次数失败，用户ID：{}", userId, e);
            return 0L;
        }
    }

    @Override
    public void recordApiCall(CallHistory callHistory) {
        try {
            log.info("记录API调用历史，用户ID：{}，Agent名称：{}", 
                    callHistory.getUserId(), callHistory.getAgentName());
            
            // 简化实现：仅记录日志
            // 实际实现需要保存到数据库
            log.warn("recordApiCall方法需要完善实现，当前仅记录日志");
        } catch (Exception e) {
            log.error("记录API调用历史失败", e);
        }
    }

    @Override
    public void recordPluginCall(CallHistory callHistory) {
        try {
            log.info("记录插件调用历史，用户ID：{}，Agent名称：{}", 
                    callHistory.getUserId(), callHistory.getAgentName());
            
            // 简化实现：仅记录日志
            // 实际实现需要保存到数据库
            log.warn("recordPluginCall方法需要完善实现，当前仅记录日志");
        } catch (Exception e) {
            log.error("记录插件调用历史失败", e);
        }
    }

    @Override
    public List<CallHistory> getRecentCallHistory(Long userId, int limit) {
        try {
            log.info("获取用户{}最近{}条调用记录", userId, limit);

            QueryWrapper<RecognitionRecord> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("user_id", userId)
                       .eq("deleted", 0)
                       .orderByDesc("created_time")
                       .last("LIMIT " + limit);

            List<RecognitionRecord> records = recognitionRecordMapper.selectList(queryWrapper);

            List<CallHistory> callHistories = new ArrayList<>();
            for (RecognitionRecord record : records) {
                CallHistory callHistory = convertToCallHistory(record);
                callHistories.add(callHistory);
            }

            log.info("获取用户{}最近{}条调用记录成功，实际获取{}条", userId, limit, callHistories.size());
            return callHistories;
        } catch (Exception e) {
            log.error("获取用户最近调用记录失败，用户ID：{}", userId, e);
            return new ArrayList<>();
        }
    }

    @Override
    public Long getMonthlyCallCount(Long userId) {
        try {
            LocalDateTime startOfMonth = LocalDateTime.now().withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0).withNano(0);
            LocalDateTime endOfMonth = startOfMonth.plusMonths(1);

            QueryWrapper<RecognitionRecord> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("user_id", userId)
                       .eq("deleted", 0)
                       .ge("created_time", startOfMonth)
                       .lt("created_time", endOfMonth);

            Long count = recognitionRecordMapper.selectCount(queryWrapper);
            log.info("获取用户{}本月调用数量：{}", userId, count);
            return count;
        } catch (Exception e) {
            log.error("获取用户本月调用数量失败，用户ID：{}", userId, e);
            return 0L;
        }
    }

    @Override
    public Long getMonthlySuccessCount(Long userId) {
        try {
            LocalDateTime startOfMonth = LocalDateTime.now().withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0).withNano(0);
            LocalDateTime endOfMonth = startOfMonth.plusMonths(1);

            QueryWrapper<RecognitionRecord> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("user_id", userId)
                       .eq("status", 2) // 2-成功
                       .eq("deleted", 0)
                       .ge("created_time", startOfMonth)
                       .lt("created_time", endOfMonth);

            Long count = recognitionRecordMapper.selectCount(queryWrapper);
            log.info("获取用户{}本月成功调用数量：{}", userId, count);
            return count;
        } catch (Exception e) {
            log.error("获取用户本月成功调用数量失败，用户ID：{}", userId, e);
            return 0L;
        }
    }

    @Override
    public Double getSuccessRate(Long userId) {
        try {
            Long totalCount = getSuccessCountByUserId(userId) + getFailedCountByUserId(userId);
            if (totalCount == 0) {
                return 0.0;
            }

            Long successCount = getSuccessCountByUserId(userId);
            double successRate = (double) successCount / totalCount * 100;

            log.info("计算用户{}调用成功率：{}%", userId, successRate);
            return Math.round(successRate * 100.0) / 100.0; // 保留两位小数
        } catch (Exception e) {
            log.error("计算用户调用成功率失败，用户ID：{}", userId, e);
            return 0.0;
        }
    }

    @Override
    public Long getTodayCallCount(Long userId) {
        try {
            LocalDateTime startOfDay = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);
            LocalDateTime endOfDay = startOfDay.plusDays(1);

            QueryWrapper<RecognitionRecord> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("user_id", userId)
                       .eq("deleted", 0)
                       .ge("created_time", startOfDay)
                       .lt("created_time", endOfDay);

            Long count = recognitionRecordMapper.selectCount(queryWrapper);
            log.info("获取用户{}今日调用数量：{}", userId, count);
            return count;
        } catch (Exception e) {
            log.error("获取用户今日调用数量失败，用户ID：{}", userId, e);
            return 0L;
        }
    }

    /**
     * 将RecognitionRecord转换为CallHistory
     */
    private CallHistory convertToCallHistory(RecognitionRecord record) {
        CallHistory callHistory = new CallHistory();
        callHistory.setId(record.getId());
        callHistory.setUserId(record.getUserId());

        // 通过agentId查询Agent名称
        String agentName = "未知Agent";
        if (record.getAgentId() != null) {
            try {
                Agent agent = agentService.getAgentById(record.getAgentId());
                if (agent != null) {
                    agentName = agent.getAgentName();
                }
            } catch (Exception e) {
                log.warn("查询Agent名称失败，Agent ID：{}，Record ID：{}", record.getAgentId(), record.getId(), e);
            }
        }
        callHistory.setAgentName(agentName);

        // 设置调用类型，如果数据库中没有设置则默认为API
        String callType = record.getCallType();
        if (callType == null || callType.trim().isEmpty()) {
            // 根据业务逻辑推断调用类型
            // 如果是通过Web界面上传文件识别，设为API
            // 如果有其他判断逻辑可以在这里添加
            callType = "API";
        }
        callHistory.setCallType(callType);
        callHistory.setCallStatus(getCallStatusFromRecordStatus(record.getStatus()));

        // 设置处理状态
        callHistory.setProcessStatus(getProcessStatusFromRecordStatus(record.getStatus()));

        // 设置任务类型，默认为识别任务
        callHistory.setTaskType("RECOGNITION");

        // 请求和响应数据
        callHistory.setRequestParams(record.getInputParams());
        callHistory.setResponseData(record.getRecognitionResult());

        // 性能指标
        callHistory.setResponseTime(record.getProcessingTime() != null ? record.getProcessingTime().longValue() : 0L);
        callHistory.setConfidenceScore(record.getConfidenceScore());

        // 会话和错误信息
        callHistory.setSessionId(record.getSessionId());
        callHistory.setErrorMessage(record.getErrorMessage());

        // 文件相关信息（如果有的话）
        if (record.getFileId() != null && record.getFileId() > 0) {
            try {
                Result<UploadedFile> fileResult = fileService.getFileInfo(record.getFileId());
                if (fileResult.isSuccess() && fileResult.getData() != null) {
                    UploadedFile uploadedFile = fileResult.getData();

                    // 构建标准的预览URL格式：/api/v1/files/{fileId}/preview
                    String previewUrl = String.format("/api/v1/files/%d/preview", record.getFileId());

                    // 构建文件信息JSON
                    String fileInfoJson = String.format(
                        "{\"name\":\"%s\",\"type\":\"%s\",\"size\":%d,\"url\":\"%s\",\"fileId\":%d}",
                        uploadedFile.getOriginalName(),
                        uploadedFile.getFileType(),
                        uploadedFile.getFileSize(),
                        previewUrl,
                        record.getFileId()
                    );
                    callHistory.setUploadFileInfo(fileInfoJson);

                    // 构建文件URL列表，使用标准预览URL格式
                    String fileUrlsJson = String.format("[\"%s\"]", previewUrl);
                    callHistory.setUploadFileUrls(fileUrlsJson);

                    // 设置内容类型
                    String contentType = determineContentType(uploadedFile.getFileType());
                    callHistory.setUploadContentType(contentType);

                    log.debug("获取文件信息成功，文件ID：{}，文件名：{}", record.getFileId(), uploadedFile.getOriginalName());
                } else {
                    log.warn("获取文件信息失败，文件ID：{}，结果：{}", record.getFileId(), fileResult.getMessage());
                    setDefaultFileInfo(callHistory);
                }
            } catch (Exception e) {
                log.debug("获取文件信息异常，文件ID：{}", record.getFileId(), e);
                setDefaultFileInfo(callHistory);
            }
        } else {
            setDefaultFileInfo(callHistory);
        }

        // 时间信息
        callHistory.setCreateTime(record.getCreatedTime());
        return callHistory;
    }

    /**
     * 将RecognitionRecord的状态转换为CallHistory的状态
     */
    private String getCallStatusFromRecordStatus(Integer status) {
        if (status == null) {
            return "UNKNOWN";
        }
        switch (status) {
            case 1: return "PROCESSING"; // 处理中
            case 2: return "SUCCESS";    // 成功
            case 3: return "FAILED";     // 失败
            default: return "UNKNOWN";
        }
    }

    /**
     * 将RecognitionRecord的状态转换为CallHistory的处理状态
     */
    private String getProcessStatusFromRecordStatus(Integer status) {
        if (status == null) {
            return "PENDING";
        }
        switch (status) {
            case 1: return "PROCESSING"; // 处理中
            case 2: return "COMPLETED";  // 已完成
            case 3: return "FAILED";     // 失败
            default: return "PENDING";
        }
    }

    /**
     * 设置默认文件信息
     */
    private void setDefaultFileInfo(CallHistory callHistory) {
        callHistory.setUploadFileInfo("{}");
        callHistory.setUploadFileUrls("[]");
        callHistory.setUploadContentType("UNKNOWN");
    }

    /**
     * 根据文件类型确定内容类型
     */
    private String determineContentType(String fileType) {
        if (fileType == null) {
            return "UNKNOWN";
        }

        String lowerType = fileType.toLowerCase();
        if (lowerType.contains("image") || lowerType.matches(".*\\.(jpg|jpeg|png|gif|bmp|webp|svg)$")) {
            return "IMAGE";
        } else if (lowerType.contains("pdf") || lowerType.endsWith("pdf")) {
            return "PDF";
        } else if (lowerType.contains("text") || lowerType.matches(".*\\.(txt|doc|docx|rtf)$")) {
            return "TEXT";
        } else {
            return "MIXED";
        }
    }
}
