package com.sinoair.agent.service.impl;

import com.sinoair.agent.common.Result;
import com.sinoair.agent.dto.request.CreateUserRequest;
import com.sinoair.agent.dto.request.UpdateUserRequest;
import com.sinoair.agent.dto.request.ChangePasswordRequest;
import com.sinoair.agent.dto.request.UpdateProfileRequest;
import com.sinoair.agent.dto.response.UserVO;
import com.sinoair.agent.security.SecurityUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sinoair.agent.entity.Role;
import com.sinoair.agent.entity.User;
import com.sinoair.agent.mapper.RoleMapper;
import com.sinoair.agent.mapper.UserMapper;
import com.sinoair.agent.service.PermissionService;
import com.sinoair.agent.service.UserRoleService;
import com.sinoair.agent.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户服务实现
 * 
 * <AUTHOR> Team
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserServiceImpl implements UserService {

    private final UserMapper userMapper;
    private final RoleMapper roleMapper;
    private final PasswordEncoder passwordEncoder;
    private final UserRoleService userRoleService;
    private final PermissionService permissionService;

    @Override
    public List<UserVO> getUserList(Long roleId, Integer status, String keyword) {
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();

        // 过滤条件
        if (roleId != null) {
            // 使用用户角色关联表查询
            List<Long> userIds = userRoleService.getUserIdsByRoleId(roleId);
            if (userIds.isEmpty()) {
                return new ArrayList<>(); // 如果没有用户关联此角色，返回空列表
            }
            queryWrapper.in("id", userIds);
        }

        if (status != null) {
            queryWrapper.eq("status", status);
        }

        if (keyword != null && !keyword.trim().isEmpty()) {
            queryWrapper.and(wrapper -> wrapper
                .like("username", keyword)
                .or().like("real_name", keyword)
                .or().like("email", keyword));
        }

        queryWrapper.orderByDesc("created_time");

        List<User> users = userMapper.selectList(queryWrapper);

        // 转换为VO并填充角色信息
        return users.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    @Override
    public UserVO getUserById(Long id) {
        User user = userMapper.selectById(id);
        return user != null ? convertToVO(user) : null;
    }

    @Override
    @Transactional
    public Result<UserVO> createUser(CreateUserRequest request) {
        // 检查用户名是否已存在
        User existingUser = userMapper.findByUsername(request.getUsername());
        if (existingUser != null) {
            return Result.error("用户名已存在");
        }

        // 检查邮箱是否已存在
        if (request.getEmail() != null && !request.getEmail().isEmpty()) {
            User existingEmailUser = userMapper.findByEmail(request.getEmail());
            if (existingEmailUser != null) {
                return Result.error("邮箱已存在");
            }
        }

        // 角色检查已移除，改为创建后通过"分配角色"功能管理

        // 创建用户
        User user = new User();
        BeanUtils.copyProperties(request, user);
        user.setPassword(passwordEncoder.encode(request.getPassword()));
        // 移除role_id字段，使用关联表
        user.setRoleId(null);

        // 设置用户来源和审核状态（如果字段存在）
        try {
            if ("MANAGEMENT".equals(request.getForward())) {
                // 系统创建的用户直接通过审核
                user.setApprovalStatus(1);
                user.setApprovedTime(LocalDateTime.now());
                Long currentUserId = SecurityUtils.getCurrentUserId();
                if (currentUserId != null) {
                    user.setApprovedBy(currentUserId);
                }
            } else {
                // 官网注册的用户需要审核
                user.setApprovalStatus(0);
            }
        } catch (Exception e) {
            log.warn("设置审核状态失败，可能是字段不存在: {}", e.getMessage());
            // 忽略审核相关字段设置
        }

        try {
            userMapper.insert(user);

            // 角色分配已移除，改为创建后通过"分配角色"功能管理
            // 用户创建成功后，管理员可以通过"分配角色"按钮为用户分配角色

            UserVO userVO = convertToVO(user);
            log.info("用户创建成功: id={}, username={}, forward={}", user.getId(), user.getUsername(), user.getForward());
            return Result.success(userVO);
        } catch (Exception e) {
            log.error("创建用户失败", e);
            return Result.error("创建用户失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<UserVO> updateUser(Long id, UpdateUserRequest request) {
        User user = userMapper.selectById(id);
        if (user == null) {
            return Result.error("用户不存在");
        }

        // 检查邮箱是否已被其他用户使用
        if (request.getEmail() != null && !request.getEmail().isEmpty()) {
            User existingUser = userMapper.findByEmail(request.getEmail());
            if (existingUser != null && !existingUser.getId().equals(id)) {
                return Result.error("邮箱已被其他用户使用");
            }
        }

        // 检查角色是否存在
        if (request.getRoleId() != null) {
            Role role = roleMapper.selectById(request.getRoleId());
            if (role == null) {
                return Result.error("指定的角色不存在");
            }
        }

        // 更新用户信息
        if (request.getEmail() != null) user.setEmail(request.getEmail());
        if (request.getPhone() != null) user.setPhone(request.getPhone());
        if (request.getRealName() != null) user.setRealName(request.getRealName());
        if (request.getDepartment() != null) user.setDepartment(request.getDepartment());
        if (request.getStatus() != null) user.setStatus(request.getStatus());

        try {
            userMapper.updateById(user);

            // 更新用户角色
            if (request.getRoleId() != null) {
                List<Long> roleIds = List.of(request.getRoleId());
                userRoleService.assignRolesToUser(user.getId(), roleIds);
            }

            UserVO userVO = convertToVO(user);
            log.info("用户更新成功: id={}, username={}", user.getId(), user.getUsername());
            return Result.success(userVO);
        } catch (Exception e) {
            log.error("更新用户失败", e);
            return Result.error("更新用户失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public boolean deleteUser(Long id) {
        try {
            User user = userMapper.selectById(id);
            if (user == null) {
                return false;
            }

            // 先删除用户角色关联
            userRoleService.deleteByUserId(id);

            // 物理删除用户
            userMapper.deleteById(id);

            log.info("用户删除成功: id={}, username={}", user.getId(), user.getUsername());
            return true;
        } catch (Exception e) {
            log.error("删除用户失败: id={}", id, e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean toggleUserStatus(Long id) {
        try {
            User user = userMapper.selectById(id);
            if (user == null) {
                return false;
            }

            user.setStatus(user.getStatus() == 1 ? 0 : 1);
            userMapper.updateById(user);

            log.info("用户状态切换成功: id={}, username={}, newStatus={}",
                    user.getId(), user.getUsername(), user.getStatus());
            return true;
        } catch (Exception e) {
            log.error("切换用户状态失败: id={}", id, e);
            return false;
        }
    }

    @Override
    @Transactional
    public Result<Map<String, String>> resetPassword(Long id) {
        try {
            User user = userMapper.selectById(id);
            if (user == null) {
                return Result.error("用户不存在");
            }

            // 生成随机密码
            String newPassword = generateRandomPassword();

            user.setPassword(passwordEncoder.encode(newPassword));
            userMapper.updateById(user);

            Map<String, String> result = new HashMap<>();
            result.put("newPassword", newPassword);
            result.put("username", user.getUsername());

            log.info("用户密码重置成功: id={}, username={}", user.getId(), user.getUsername());
            return Result.success(result);
        } catch (Exception e) {
            log.error("重置用户密码失败: id={}", id, e);
            return Result.error("重置密码失败: " + e.getMessage());
        }
    }

    @Override
    public String exportUsers(Long roleId, Integer status) {
        // TODO: 实现用户导出功能
        return "/api/v1/files/download/users_export_" + System.currentTimeMillis() + ".xlsx";
    }

    @Override
    public Map<String, Object> getUserStatistics() {
        Map<String, Object> statistics = new HashMap<>();

        // 使用Mapper的统计方法
        Long totalUsers = userMapper.countUsers();
        Long activeUsers = userMapper.countActiveUsers();

        statistics.put("totalUsers", totalUsers);
        statistics.put("activeUsers", activeUsers);
        statistics.put("disabledUsers", totalUsers - activeUsers);

        // 按角色统计
        Map<String, Long> roleStatistics = new HashMap<>();
        List<Role> roles = roleMapper.findAllActive();
        for (Role role : roles) {
            List<User> roleUsers = userMapper.findByRoleId(role.getId());
            roleStatistics.put(role.getRoleName(), (long) roleUsers.size());
        }
        statistics.put("roleStatistics", roleStatistics);

        return statistics;
    }

    /**
     * 转换User实体为UserVO
     */
    private UserVO convertToVO(User user) {
        UserVO vo = new UserVO();
        BeanUtils.copyProperties(user, vo);

        // 填充角色信息 - 使用新的RBAC系统
        List<Long> roleIds = userRoleService.getRoleIdsByUserId(user.getId());
        if (!roleIds.isEmpty()) {
            // 获取所有角色信息
            List<UserVO.RoleInfo> roles = new ArrayList<>();
            Role primaryRole = null;

            for (Long roleId : roleIds) {
                Role role = roleMapper.selectById(roleId);
                if (role != null) {
                    roles.add(new UserVO.RoleInfo(role.getId(), role.getRoleName(),
                                                 role.getRoleCode(), role.getDescription()));

                    // 第一个角色作为主角色（兼容旧系统）
                    if (primaryRole == null) {
                        primaryRole = role;
                    }
                }
            }

            vo.setRoles(roles);

            // 设置主角色信息（兼容字段）
            if (primaryRole != null) {
                vo.setRoleId(primaryRole.getId());
                vo.setRoleName(primaryRole.getRoleName());
                vo.setRoleCode(primaryRole.getRoleCode());
            }
        } else {
            // 没有角色时设置空列表
            vo.setRoles(new ArrayList<>());
        }

        // 填充审核人姓名
        if (user.getApprovedBy() != null) {
            User approvedByUser = userMapper.selectById(user.getApprovedBy());
            if (approvedByUser != null) {
                vo.setApprovedByName(approvedByUser.getRealName() != null ?
                    approvedByUser.getRealName() : approvedByUser.getUsername());
            }
        }

        return vo;
    }

    /**
     * 生成随机密码
     */
    private String generateRandomPassword() {
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        StringBuilder password = new StringBuilder();
        Random random = new Random();
        
        for (int i = 0; i < 8; i++) {
            password.append(chars.charAt(random.nextInt(chars.length())));
        }
        
        return password.toString();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean assignRolesToUser(Long userId, List<Long> roleIds) {
        return userRoleService.assignRolesToUser(userId, roleIds);
    }

    @Override
    public List<Long> getUserRoleIds(Long userId) {
        return userRoleService.getRoleIdsByUserId(userId);
    }

    @Override
    public List<String> getUserPermissions(Long userId) {
        return permissionService.getButtonPermissionsByUserId(userId);
    }

    @Override
    public boolean hasPermission(Long userId, String permissionCode) {
        return permissionService.hasPermission(userId, permissionCode);
    }

    @Override
    public List<UserVO> getUserListByForward(String forward, Integer approvalStatus, String keyword) {
        try {
            QueryWrapper<User> queryWrapper = new QueryWrapper<>();

            // 按用户来源过滤（如果字段存在）
            if (forward != null && !forward.trim().isEmpty()) {
                try {
                    queryWrapper.eq("forward", forward);
                } catch (Exception e) {
                    // 如果forward字段不存在，根据forward值返回不同的数据
                    log.warn("forward字段不存在，使用降级方案: {}", e.getMessage());
                    if ("PORTAL".equals(forward)) {
                        // 返回空列表，因为还没有平台用户
                        return new ArrayList<>();
                    }
                    // 对于MANAGEMENT，返回所有用户
                }
            }

            // 按审核状态过滤（如果字段存在）
            if (approvalStatus != null) {
                try {
                    queryWrapper.eq("approval_status", approvalStatus);
                } catch (Exception e) {
                    log.warn("approval_status字段不存在，忽略此过滤条件: {}", e.getMessage());
                }
            }

            // 关键词搜索
            if (keyword != null && !keyword.trim().isEmpty()) {
                queryWrapper.and(wrapper -> wrapper
                    .like("username", keyword)
                    .or().like("real_name", keyword)
                    .or().like("email", keyword));
            }

            queryWrapper.orderByDesc("created_time");

            List<User> users = userMapper.selectList(queryWrapper);

            // 转换为VO并填充角色信息
            return users.stream()
                    .map(this::convertToVO)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取用户列表失败: forward={}, approvalStatus={}", forward, approvalStatus, e);
            // 降级方案：返回基础用户列表
            return getUserList(null, null, keyword);
        }
    }

    @Override
    @Transactional
    public Result<Void> approveUser(Long userId, Integer approvalStatus, String approvalRemark) {
        try {
            User user = userMapper.selectById(userId);
            if (user == null) {
                return Result.error("用户不存在");
            }

            // 检查是否支持审核功能（字段是否存在）
            try {
                if (user.getForward() != null && !"PORTAL".equals(user.getForward())) {
                    return Result.error("只能审核官网注册的用户");
                }

                if (user.getApprovalStatus() != null && user.getApprovalStatus() != 0) {
                    return Result.error("该用户已经审核过了");
                }

                // 更新审核信息
                user.setApprovalStatus(approvalStatus);
                user.setApprovedTime(LocalDateTime.now());
                Long currentUserId = SecurityUtils.getCurrentUserId();
                if (currentUserId != null) {
                    user.setApprovedBy(currentUserId);
                }
                user.setApprovalRemark(approvalRemark);

                // 如果审核拒绝，同时禁用用户
                if (approvalStatus == 2) {
                    user.setStatus(0);
                }

                userMapper.updateById(user);

                log.info("用户审核完成: userId={}, approvalStatus={}, approvedBy={}",
                        userId, approvalStatus, currentUserId);

                return Result.success("审核完成");
            } catch (Exception e) {
                log.warn("审核功能不可用，可能是数据库字段不存在: {}", e.getMessage());
                return Result.error("审核功能暂不可用，请先执行数据库迁移");
            }
        } catch (Exception e) {
            log.error("用户审核失败: userId={}", userId, e);
            return Result.error("审核失败: " + e.getMessage());
        }
    }

    @Override
    public Long getPendingApprovalCount() {
        try {
            QueryWrapper<User> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("forward", "PORTAL")
                       .eq("approval_status", 0);
            return userMapper.selectCount(queryWrapper);
        } catch (Exception e) {
            log.warn("获取待审核用户数量失败，可能是字段不存在: {}", e.getMessage());
            // 降级方案：返回0
            return 0L;
        }
    }

    @Override
    @Transactional
    public Result<Void> changePassword(Long userId, ChangePasswordRequest request) {
        try {
            // 验证新密码和确认密码是否一致
            if (!request.getNewPassword().equals(request.getConfirmPassword())) {
                return Result.error("新密码和确认密码不一致");
            }

            User user = userMapper.selectById(userId);
            if (user == null) {
                return Result.error("用户不存在");
            }

            // 验证原密码是否正确
            if (!passwordEncoder.matches(request.getOldPassword(), user.getPassword())) {
                return Result.error("原密码不正确");
            }

            // 更新密码
            user.setPassword(passwordEncoder.encode(request.getNewPassword()));
            user.setUpdatedTime(LocalDateTime.now());
            userMapper.updateById(user);

            log.info("用户密码修改成功: userId={}, username={}", userId, user.getUsername());
            return Result.success("密码修改成功");
        } catch (Exception e) {
            log.error("修改密码失败: userId={}", userId, e);
            return Result.error("修改密码失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<UserVO> updateProfile(Long userId, UpdateProfileRequest request) {
        try {
            User user = userMapper.selectById(userId);
            if (user == null) {
                return Result.error("用户不存在");
            }

            // 检查邮箱是否已被其他用户使用
            if (request.getEmail() != null && !request.getEmail().isEmpty()) {
                User existingUser = userMapper.findByEmail(request.getEmail());
                if (existingUser != null && !existingUser.getId().equals(userId)) {
                    return Result.error("邮箱已被其他用户使用");
                }
            }

            // 更新用户信息
            if (request.getEmail() != null) {
                user.setEmail(request.getEmail());
            }
            if (request.getPhone() != null) {
                user.setPhone(request.getPhone());
            }
            if (request.getRealName() != null) {
                user.setRealName(request.getRealName());
            }
            if (request.getDepartment() != null) {
                user.setDepartment(request.getDepartment());
            }
            user.setUpdatedTime(LocalDateTime.now());

            userMapper.updateById(user);

            UserVO userVO = convertToVO(user);
            log.info("用户资料更新成功: userId={}, username={}", userId, user.getUsername());
            return Result.success(userVO);
        } catch (Exception e) {
            log.error("更新用户资料失败: userId={}", userId, e);
            return Result.error("更新用户资料失败: " + e.getMessage());
        }
    }
}
