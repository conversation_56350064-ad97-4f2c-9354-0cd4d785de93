package com.sinoair.agent.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinoair.agent.entity.Permission;
import com.sinoair.agent.mapper.PermissionMapper;
import com.sinoair.agent.service.PermissionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 权限服务实现类
 *
 * <AUTHOR> Team
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PermissionServiceImpl extends ServiceImpl<PermissionMapper, Permission> implements PermissionService {

    private final PermissionMapper permissionMapper;

    @Override
    public List<Permission> getPermissionsByUserId(Long userId) {
        return permissionMapper.selectPermissionsByUserId(userId);
    }

    @Override
    public List<Permission> getPermissionsByRoleId(Long roleId) {
        return permissionMapper.selectPermissionsByRoleId(roleId);
    }

    @Override
    public List<Permission> getAllPermissionsTree() {
        List<Permission> allPermissions = permissionMapper.selectAllPermissions();
        return buildPermissionTree(allPermissions);
    }

    @Override
    public List<Permission> getPermissionsByType(Integer permissionType) {
        return permissionMapper.selectPermissionsByType(permissionType);
    }

    @Override
    public List<Permission> getPermissionsByParentId(Long parentId) {
        return permissionMapper.selectPermissionsByParentId(parentId);
    }

    @Override
    public Permission getPermissionByCode(String permissionCode) {
        return permissionMapper.selectByPermissionCode(permissionCode);
    }

    @Override
    public List<Permission> getMenuPermissionsByUserId(Long userId) {
        List<Permission> permissions = permissionMapper.selectMenuPermissionsByUserId(userId);
        return buildPermissionTree(permissions);
    }

    @Override
    public List<String> getButtonPermissionsByUserId(Long userId) {
        return permissionMapper.selectButtonPermissionsByUserId(userId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createPermission(Permission permission) {
        // 检查权限编码是否已存在
        if (existsByPermissionCode(permission.getPermissionCode())) {
            throw new RuntimeException("权限编码已存在");
        }
        
        if (permission.getStatus() == null) {
            permission.setStatus(1);
        }
        if (permission.getSortOrder() == null) {
            permission.setSortOrder(0);
        }
        if (permission.getParentId() == null) {
            permission.setParentId(0L);
        }
        
        permission.setCreatedTime(LocalDateTime.now());
        permission.setUpdatedTime(LocalDateTime.now());
        
        return save(permission);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updatePermission(Permission permission) {
        // 检查权限是否存在
        Permission existingPermission = getById(permission.getId());
        if (existingPermission == null) {
            throw new RuntimeException("权限不存在");
        }
        
        // 检查权限编码是否已被其他权限使用
        Permission permissionByCode = getPermissionByCode(permission.getPermissionCode());
        if (permissionByCode != null && !permissionByCode.getId().equals(permission.getId())) {
            throw new RuntimeException("权限编码已被其他权限使用");
        }
        
        permission.setUpdatedTime(LocalDateTime.now());
        return updateById(permission);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deletePermission(Long permissionId) {
        // 检查权限是否存在
        Permission permission = getById(permissionId);
        if (permission == null) {
            throw new RuntimeException("权限不存在");
        }
        
        // 检查是否有子权限
        List<Permission> children = getPermissionsByParentId(permissionId);
        if (!children.isEmpty()) {
            throw new RuntimeException("该权限下还有子权限，无法删除");
        }
        
        // 删除权限
        return removeById(permissionId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deletePermissions(List<Long> permissionIds) {
        for (Long permissionId : permissionIds) {
            deletePermission(permissionId);
        }
        return true;
    }

    @Override
    public boolean updatePermissionStatus(Long permissionId, Integer status) {
        Permission permission = new Permission();
        permission.setId(permissionId);
        permission.setStatus(status);
        permission.setUpdatedTime(LocalDateTime.now());
        return updateById(permission);
    }

    @Override
    public boolean existsByPermissionCode(String permissionCode) {
        if (!StringUtils.hasText(permissionCode)) {
            return false;
        }
        return getPermissionByCode(permissionCode) != null;
    }

    @Override
    public List<Permission> buildPermissionTree(List<Permission> permissions) {
        if (permissions == null || permissions.isEmpty()) {
            return new ArrayList<>();
        }

        // 过滤掉parentId为null的权限，并设置默认值
        permissions.forEach(permission -> {
            if (permission.getParentId() == null) {
                permission.setParentId(0L);
            }
        });

        // 按父ID分组
        Map<Long, List<Permission>> permissionMap = permissions.stream()
                .collect(Collectors.groupingBy(permission ->
                    permission.getParentId() != null ? permission.getParentId() : 0L));

        // 构建树形结构
        List<Permission> rootPermissions = permissionMap.getOrDefault(0L, new ArrayList<>());
        buildChildren(rootPermissions, permissionMap);

        return rootPermissions;
    }
    
    private void buildChildren(List<Permission> permissions, Map<Long, List<Permission>> permissionMap) {
        for (Permission permission : permissions) {
            List<Permission> children = permissionMap.getOrDefault(permission.getId(), new ArrayList<>());
            permission.setChildren(children);
            if (!children.isEmpty()) {
                buildChildren(children, permissionMap);
            }
        }
    }

    @Override
    public boolean hasPermission(Long userId, String permissionCode) {
        List<String> userPermissions = getButtonPermissionsByUserId(userId);
        return userPermissions.contains(permissionCode);
    }

    @Override
    public boolean hasAnyPermission(Long userId, List<String> permissionCodes) {
        List<String> userPermissions = getButtonPermissionsByUserId(userId);
        return permissionCodes.stream().anyMatch(userPermissions::contains);
    }

    @Override
    public boolean hasAllPermissions(Long userId, List<String> permissionCodes) {
        List<String> userPermissions = getButtonPermissionsByUserId(userId);
        return userPermissions.containsAll(permissionCodes);
    }
}
