package com.sinoair.agent.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sinoair.agent.dto.response.UserSubscriptionDTO;
import com.sinoair.agent.entity.UserAgentSubscription;

import java.util.List;

/**
 * 用户Agent订阅服务接口
 */
public interface UserAgentSubscriptionService extends IService<UserAgentSubscription> {

    /**
     * 订阅Agent
     * @param userId 用户ID
     * @param agentId Agent ID
     * @return 订阅结果
     */
    boolean subscribeAgent(Long userId, Long agentId);

    /**
     * 取消订阅Agent
     * @param userId 用户ID
     * @param agentId Agent ID
     * @return 取消订阅结果
     */
    boolean unsubscribeAgent(Long userId, Long agentId);

    /**
     * 检查用户是否已订阅某个Agent
     * @param userId 用户ID
     * @param agentId Agent ID
     * @return 是否已订阅
     */
    boolean isUserSubscribed(Long userId, Long agentId);

    /**
     * 获取用户订阅的Agent ID列表
     * @param userId 用户ID
     * @return Agent ID列表
     */
    List<Long> getUserSubscribedAgentIds(Long userId);

    /**
     * 获取Agent的订阅用户数量
     * @param agentId Agent ID
     * @return 订阅用户数量
     */
    int getAgentSubscriptionCount(Long agentId);

    /**
     * 切换订阅状态（如果已订阅则取消，如果未订阅则订阅）
     * @param userId 用户ID
     * @param agentId Agent ID
     * @return 操作后的订阅状态（true-已订阅，false-未订阅）
     */
    boolean toggleSubscription(Long userId, Long agentId);

    /**
     * 获取用户的订阅列表（包含Agent详细信息）
     * @param userId 用户ID
     * @return 用户订阅列表
     */
    List<UserSubscriptionDTO> getUserSubscriptions(Long userId);

    /**
     * 获取用户的有效订阅列表（只包含已订阅且Agent可用的）
     * @param userId 用户ID
     * @return 有效订阅列表
     */
    List<UserSubscriptionDTO> getUserActiveSubscriptions(Long userId);

    /**
     * 获取用户订阅数量统计
     * @param userId 用户ID
     * @return 订阅数量
     */
    int getUserSubscriptionCount(Long userId);

    /**
     * 获取用户有效订阅数量统计
     * @param userId 用户ID
     * @return 有效订阅数量
     */
    int getUserActiveSubscriptionCount(Long userId);
}
