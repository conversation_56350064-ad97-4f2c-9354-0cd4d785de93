package com.sinoair.agent.service;

import com.sinoair.agent.common.Result;
import com.sinoair.agent.dto.request.CreateUserRequest;
import com.sinoair.agent.dto.request.UpdateUserRequest;
import com.sinoair.agent.dto.request.ChangePasswordRequest;
import com.sinoair.agent.dto.request.UpdateProfileRequest;
import com.sinoair.agent.dto.response.UserVO;

import java.util.List;
import java.util.Map;

/**
 * 用户服务接口
 * 
 * <AUTHOR> Team
 */
public interface UserService {

    /**
     * 获取用户列表
     */
    List<UserVO> getUserList(Long roleId, Integer status, String keyword);

    /**
     * 根据ID获取用户详情
     */
    UserVO getUserById(Long id);

    /**
     * 创建用户
     */
    Result<UserVO> createUser(CreateUserRequest request);

    /**
     * 更新用户
     */
    Result<UserVO> updateUser(Long id, UpdateUserRequest request);

    /**
     * 删除用户
     */
    boolean deleteUser(Long id);

    /**
     * 切换用户状态
     */
    boolean toggleUserStatus(Long id);

    /**
     * 重置用户密码
     */
    Result<Map<String, String>> resetPassword(Long id);

    /**
     * 导出用户列表
     */
    String exportUsers(Long roleId, Integer status);

    /**
     * 获取用户统计信息
     */
    Map<String, Object> getUserStatistics();

    /**
     * 为用户分配角色
     */
    boolean assignRolesToUser(Long userId, List<Long> roleIds);

    /**
     * 获取用户的角色列表
     */
    List<Long> getUserRoleIds(Long userId);

    /**
     * 获取用户的权限列表
     */
    List<String> getUserPermissions(Long userId);

    /**
     * 检查用户是否有指定权限
     */
    boolean hasPermission(Long userId, String permissionCode);

    /**
     * 根据用户来源获取用户列表
     */
    List<UserVO> getUserListByForward(String forward, Integer approvalStatus, String keyword);

    /**
     * 审核用户
     */
    Result<Void> approveUser(Long userId, Integer approvalStatus, String approvalRemark);

    /**
     * 获取待审核用户数量
     */
    Long getPendingApprovalCount();

    /**
     * 修改密码
     */
    Result<Void> changePassword(Long userId, ChangePasswordRequest request);

    /**
     * 更新个人资料
     */
    Result<UserVO> updateProfile(Long userId, UpdateProfileRequest request);
}
