package com.sinoair.agent.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sinoair.agent.common.Result;
import com.sinoair.agent.common.ResultCode;
import com.sinoair.agent.dto.request.AgentCallRequest;
import com.sinoair.agent.dto.response.AgentCallResponse;
import com.sinoair.agent.entity.Agent;
import com.sinoair.agent.entity.RecognitionRecord;
import com.sinoair.agent.entity.UploadedFile;
import com.sinoair.agent.dto.response.FileUploadResponse;
import com.sinoair.agent.mapper.AgentMapper;
import com.sinoair.agent.mapper.RecognitionRecordMapper;
import com.sinoair.agent.service.llm.LLMService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

/**
 * Agent调用服务 - 供外部系统调用
 * 
 * <AUTHOR> Team
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AgentCallService {

    private final AgentMapper agentMapper;
    private final RecognitionRecordMapper recordMapper;
    private final FileService fileService;
    private final LLMService llmService;
    private final ObjectMapper objectMapper;

    /**
     * 文档识别
     */
    @Transactional
    public Result<AgentCallResponse> recognizeDocument(AgentCallRequest request) {
        try {
            // 验证Agent
            Agent agent = agentMapper.findByAgentCode(request.getAgentCode());
            if (agent == null) {
                return Result.error(ResultCode.AGENT_NOT_FOUND, "Agent不存在: " + request.getAgentCode());
            }
            if (agent.getStatus() != Agent.STATUS_PUBLISHED) {
                return Result.error(ResultCode.AGENT_STATUS_ERROR, "Agent未发布，无法调用");
            }

            // 生成任务ID
            String taskId = UUID.randomUUID().toString();

            // 上传文件
            Result<FileUploadResponse> uploadResult = fileService.uploadFile(request.getFile(), "agent_call");
            if (!uploadResult.isSuccess()) {
                return Result.error(uploadResult.getCode(), uploadResult.getMessage());
            }

            // 异步处理识别任务
            CompletableFuture.runAsync(() -> processRecognitionAsync(taskId, agent, uploadResult.getData(), request));

            // 返回处理中状态
            AgentCallResponse response = AgentCallResponse.processing(taskId, request.getAgentCode());
            response.setAgentName(agent.getAgentName());

            log.info("Agent调用任务已提交: taskId={}, agentCode={}", taskId, request.getAgentCode());
            return Result.success("识别任务已提交", response);

        } catch (Exception e) {
            log.error("Agent调用失败: agentCode={}", request.getAgentCode(), e);
            return Result.error("Agent调用失败: " + e.getMessage());
        }
    }

    /**
     * 文本识别
     */
    public Result<AgentCallResponse> recognizeText(String agentCode, AgentCallRequest.TextRequest request) {
        try {
            // 验证Agent
            Agent agent = agentMapper.findByAgentCode(agentCode);
            if (agent == null) {
                return Result.error(ResultCode.AGENT_NOT_FOUND, "Agent不存在: " + agentCode);
            }
            if (agent.getStatus() != Agent.STATUS_PUBLISHED) {
                return Result.error(ResultCode.AGENT_STATUS_ERROR, "Agent未发布，无法调用");
            }

            String taskId = UUID.randomUUID().toString();

            // 直接处理文本识别（同步）
            long startTime = System.currentTimeMillis();
            
            // 构建识别请求
            com.sinoair.agent.dto.request.RecognitionRequest recognitionRequest = 
                new com.sinoair.agent.dto.request.RecognitionRequest();
            recognitionRequest.setAgentId(agent.getId());
            recognitionRequest.setBusinessTypeId(agent.getBusinessTypeId());
            
            // 模拟文本识别处理
            String mockResult = processTextRecognition(agent, request.getTextContent());
            
            long processingTime = System.currentTimeMillis() - startTime;

            AgentCallResponse response = AgentCallResponse.success(
                taskId, agentCode, mockResult, BigDecimal.valueOf(0.95), (int) processingTime);
            response.setAgentName(agent.getAgentName());

            log.info("文本识别完成: taskId={}, agentCode={}", taskId, agentCode);
            return Result.success("文本识别成功", response);

        } catch (Exception e) {
            log.error("文本识别失败: agentCode={}", agentCode, e);
            return Result.error("文本识别失败: " + e.getMessage());
        }
    }

    /**
     * 批量识别
     */
    public Result<AgentCallResponse> batchRecognize(String agentCode, MultipartFile[] files, String businessParams) {
        try {
            String batchId = UUID.randomUUID().toString();
            
            // 为每个文件创建识别任务
            for (MultipartFile file : files) {
                AgentCallRequest request = AgentCallRequest.builder()
                        .agentCode(agentCode)
                        .file(file)
                        .businessParams(businessParams)
                        .build();
                recognizeDocument(request);
            }

            AgentCallResponse.BatchResult batchResult = AgentCallResponse.BatchResult.builder()
                    .batchId(batchId)
                    .totalFiles(files.length)
                    .processingCount(files.length)
                    .successCount(0)
                    .failureCount(0)
                    .build();

            AgentCallResponse response = AgentCallResponse.builder()
                    .taskId(batchId)
                    .agentCode(agentCode)
                    .status("PROCESSING")
                    .result(batchResult)
                    .createdTime(LocalDateTime.now())
                    .build();

            return Result.success("批量识别任务已提交", response);

        } catch (Exception e) {
            log.error("批量识别失败: agentCode={}", agentCode, e);
            return Result.error("批量识别失败: " + e.getMessage());
        }
    }

    /**
     * 获取识别结果
     */
    public Result<AgentCallResponse> getRecognitionResult(String taskId) {
        try {
            // 这里应该从缓存或数据库中查询结果
            // 简化处理，返回模拟结果
            AgentCallResponse response = AgentCallResponse.builder()
                    .taskId(taskId)
                    .status("SUCCESS")
                    .result("{\"consignee\":{\"name\":\"示例收货人\",\"address\":\"示例地址\"},\"shipper\":{\"name\":\"示例发货人\",\"address\":\"示例发货地址\"}}")
                    .confidence(BigDecimal.valueOf(0.95))
                    .processingTime(2500)
                    .createdTime(LocalDateTime.now().minusMinutes(1))
                    .completedTime(LocalDateTime.now())
                    .build();

            return Result.success(response);

        } catch (Exception e) {
            log.error("获取识别结果失败: taskId={}", taskId, e);
            return Result.error("获取识别结果失败: " + e.getMessage());
        }
    }

    /**
     * 获取可用Agent列表
     */
    public Result<?> getAvailableAgents() {
        try {
            List<Agent> agents = agentMapper.findByStatus(Agent.STATUS_PUBLISHED);
            
            List<AgentCallResponse.AgentInfo> agentInfos = agents.stream()
                    .map(agent -> AgentCallResponse.AgentInfo.builder()
                            .agentCode(agent.getAgentCode())
                            .agentName(agent.getAgentName())
                            .description(agent.getDescription())
                            .businessType(agent.getBusinessTypeId().toString())
                            .supportedFileTypes(new String[]{"jpg", "png", "pdf"})
                            .status("ACTIVE")
                            .version(agent.getVersion())
                            .example("curl -X POST /api/v1/agent-call/recognize/" + agent.getAgentCode() + " -F \"file=@document.pdf\"")
                            .build())
                    .toList();

            return Result.success(agentInfos);

        } catch (Exception e) {
            log.error("获取可用Agent列表失败", e);
            return Result.error("获取可用Agent列表失败: " + e.getMessage());
        }
    }

    /**
     * Agent健康检查
     */
    public Result<?> checkAgentHealth(String agentCode) {
        try {
            Agent agent = agentMapper.findByAgentCode(agentCode);
            if (agent == null) {
                return Result.error(ResultCode.AGENT_NOT_FOUND, "Agent不存在");
            }
            boolean isHealthy = agent.getStatus() == Agent.STATUS_PUBLISHED;

            return Result.success(java.util.Map.of(
                    "agentCode", agentCode,
                    "agentName", agent.getAgentName(),
                    "status", isHealthy ? "HEALTHY" : "UNHEALTHY",
                    "version", agent.getVersion(),
                    "lastCheckTime", LocalDateTime.now()
            ));

        } catch (Exception e) {
            log.error("Agent健康检查失败: agentCode={}", agentCode, e);
            return Result.error("Agent健康检查失败: " + e.getMessage());
        }
    }

    /**
     * 异步处理识别任务
     */
    private void processRecognitionAsync(String taskId, Agent agent, FileUploadResponse fileInfo, AgentCallRequest request) {
        try {
            // 模拟识别处理
            Thread.sleep(2000); // 模拟处理时间

            // 这里应该调用实际的LLM服务
            String mockResult = generateMockResult(agent, fileInfo);

            log.info("异步识别任务完成: taskId={}, agentCode={}", taskId, agent.getAgentCode());

            // 如果有回调URL，发送结果
            if (request.getCallbackUrl() != null) {
                sendCallback(request.getCallbackUrl(), taskId, mockResult);
            }

        } catch (Exception e) {
            log.error("异步识别任务失败: taskId={}", taskId, e);
        }
    }

    /**
     * 处理文本识别
     */
    private String processTextRecognition(Agent agent, String textContent) {
        // 根据Agent类型和文本内容生成识别结果
        if (agent.getAgentCode().contains("ORDER")) {
            return "{\"consignee\":{\"name\":\"从文本提取的收货人\",\"address\":\"从文本提取的地址\"},\"shipper\":{\"name\":\"从文本提取的发货人\",\"address\":\"从文本提取的发货地址\"}}";
        } else if (agent.getAgentCode().contains("INVOICE")) {
            return "{\"invoiceNumber\":\"INV-2024-001\",\"date\":\"2024-01-15\",\"totalAmount\":1000.00}";
        }
        return "{\"result\":\"文本识别完成\",\"content\":\"" + textContent.substring(0, Math.min(50, textContent.length())) + "\"}";
    }

    /**
     * 生成模拟结果
     */
    private String generateMockResult(Agent agent, FileUploadResponse fileInfo) {
        if (agent.getAgentCode().contains("ORDER")) {
            return "{\"consignee\":{\"name\":\"ABC Electronics Co., Ltd.\",\"address\":\"123 Technology Street, Shenzhen, China\",\"phone\":\"+86-755-12345678\"},\"shipper\":{\"name\":\"XYZ Import & Export Corp.\",\"address\":\"456 Business Avenue, New York, USA\",\"phone\":\"******-9876543\"},\"cargo\":{\"weight\":100.5,\"pieces\":10,\"description\":\"Electronic Components\"}}";
        } else if (agent.getAgentCode().contains("INVOICE")) {
            return "{\"invoiceNumber\":\"INV-2024-001\",\"date\":\"2024-01-15\",\"seller\":{\"name\":\"示例公司\",\"taxId\":\"91110000123456789X\"},\"totalAmount\":1000.00}";
        }
        return "{\"fileName\":\"" + fileInfo.getOriginalName() + "\",\"fileSize\":" + fileInfo.getFileSize() + ",\"result\":\"识别完成\"}";
    }

    /**
     * 发送回调
     */
    private void sendCallback(String callbackUrl, String taskId, String result) {
        try {
            // 这里应该实现HTTP回调
            log.info("发送回调: url={}, taskId={}", callbackUrl, taskId);
        } catch (Exception e) {
            log.error("发送回调失败: url={}, taskId={}", callbackUrl, taskId, e);
        }
    }
}
