package com.sinoair.agent.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sinoair.agent.dto.request.AgentApprovalDTO;
import com.sinoair.agent.dto.request.AgentApprovalQueryDTO;
import com.sinoair.agent.dto.response.AgentApprovalDetailVO;
import com.sinoair.agent.dto.response.AgentApprovalVO;
import com.sinoair.agent.entity.Agent;
import com.sinoair.agent.entity.AgentApprovalRecord;
import com.sinoair.agent.security.UserPrincipal;

import java.util.List;

/**
 * Agent审批服务接口
 *
 * <AUTHOR> Team
 */
public interface AgentApprovalService {

    /**
     * 分页查询Agent审批列表
     *
     * @param queryDTO 查询条件
     * @param userPrincipal 当前用户信息
     * @return 审批列表
     */
    IPage<AgentApprovalVO> getApprovalList(AgentApprovalQueryDTO queryDTO, UserPrincipal userPrincipal);

    /**
     * 获取Agent审批详情
     *
     * @param agentId Agent ID
     * @return 审批详情
     */
    AgentApprovalDetailVO getApprovalDetail(Long agentId);

    /**
     * 提交审批结果
     *
     * @param approvalDTO 审批请求
     * @param approverId 审批人ID
     * @param approverName 审批人姓名
     * @return 是否成功
     */
    boolean submitApproval(AgentApprovalDTO approvalDTO, Long approverId, String approverName);

    /**
     * 申请发布Agent（客户端调用）
     *
     * @param agentId Agent ID
     * @param userId 用户ID
     * @param changeLog 版本更新说明
     * @return 是否成功
     */
    boolean submitForApproval(Long agentId, Long userId, String changeLog);

    /**
     * 申请发布Agent（带版本信息）
     *
     * @param agentId Agent ID
     * @param userId 用户ID
     * @param changeLog 版本更新说明
     * @param versionId 版本ID
     * @param versionNumber 版本号
     * @return 是否成功
     */
    boolean submitForApprovalWithVersion(Long agentId, Long userId, String changeLog, Long versionId, String versionNumber);

    /**
     * 获取Agent的审批历史记录
     *
     * @param agentId Agent ID
     * @return 审批历史记录
     */
    List<AgentApprovalRecord> getApprovalHistory(Long agentId);

    /**
     * 获取审批状态统计
     *
     * @return 状态统计
     */
    List<java.util.Map<String, Object>> getApprovalStatusStats();

    /**
     * 通过Agent ID和版本ID获取审批状态
     * @param agentId
     * @param versionId
     * @return
     */
    AgentApprovalRecord getApprovalStatus(Long agentId, Long versionId);

    /**
     * 获取Agent版本历史（仅显示审批通过和审批中的版本）
     * @param agentId Agent ID
     * @return 版本历史列表
     */
    List<java.util.Map<String, Object>> getAgentVersionHistory(Long agentId);
}
