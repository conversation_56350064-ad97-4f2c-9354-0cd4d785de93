package com.sinoair.agent.service;

import com.sinoair.agent.common.Result;
import com.sinoair.agent.common.ResultCode;
import com.sinoair.agent.dto.request.LoginRequest;
import com.sinoair.agent.dto.request.RegisterRequest;
import com.sinoair.agent.dto.response.LoginResponse;
import com.sinoair.agent.entity.CustomerProfile;
import com.sinoair.agent.entity.User;
import com.sinoair.agent.mapper.CustomerProfileMapper;
import com.sinoair.agent.mapper.RoleMapper;
import com.sinoair.agent.mapper.UserMapper;
import com.sinoair.agent.security.JwtTokenProvider;
import com.sinoair.agent.security.UserPrincipal;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * 认证服务
 * 
 * <AUTHOR> Team
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AuthService {

    private final AuthenticationManager authenticationManager;
    private final JwtTokenProvider tokenProvider;
    private final UserMapper userMapper;
    private final CustomerProfileMapper customerProfileMapper;
    private final RoleMapper roleMapper;
    private final PasswordEncoder passwordEncoder;

    /**
     * 用户登录
     */
    public Result<LoginResponse> login(LoginRequest request) {
        log.info("开始用户登录: username={}", request.getUsername());
        try {
            // 认证用户
            log.debug("开始认证用户: {}", request.getUsername());
            Authentication authentication = authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(request.getUsername(), request.getPassword())
            );
            log.debug("用户认证成功: {}", request.getUsername());

            SecurityContextHolder.getContext().setAuthentication(authentication);

            // 生成Token
            String accessToken = tokenProvider.generateToken(authentication);
            String refreshToken = tokenProvider.generateRefreshToken(authentication);

            // 更新最后登录时间
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            updateLastLoginTime(userPrincipal.getId());

            // 构建响应
            LoginResponse response = LoginResponse.builder()
                    .token(accessToken)
                    .refreshToken(refreshToken)
                    .expiresIn(7200) // 2小时
                    .userInfo(LoginResponse.UserInfo.builder()
                            .id(userPrincipal.getId())
                            .username(userPrincipal.getUsername())
                            .realName(userPrincipal.getRealName())
                            .email(userPrincipal.getEmail())
                            .role(userPrincipal.getRoleCode())
                            .permissions(userPrincipal.getPermissions())
                            .build())
                    .build();

            log.info("用户登录成功: {}", request.getUsername());
            return Result.success("登录成功", response);

        } catch (Exception e) {
            log.error("用户登录失败: {}", request.getUsername(), e);
            return Result.error(ResultCode.PASSWORD_ERROR, "用户名或密码错误");
        }
    }

    /**
     * 用户注册
     */
    @Transactional
    public Result<Void> register(RegisterRequest request) {
        log.info("开始用户注册: username={}, email={}", request.getUsername(), request.getEmail());

        try {
            // 检查用户名是否已存在
            User existingUser = userMapper.findByUsername(request.getUsername());
            if (existingUser != null) {
                return Result.error("用户名已存在");
            }

            // 检查邮箱是否已存在
            User existingEmailUser = userMapper.findByEmail(request.getEmail());
            if (existingEmailUser != null) {
                return Result.error("邮箱已存在");
            }

            // 获取客户角色（假设客户角色的code为CUSTOMER）
            com.sinoair.agent.entity.Role customerRole = roleMapper.findByRoleCode("CUSTOMER");
            if (customerRole == null) {
                // 如果没有客户角色，使用默认角色ID（假设为2）
                log.warn("未找到CUSTOMER角色，使用默认角色");
            }

            // 创建用户
            User user = new User();
            user.setUsername(request.getUsername());
            user.setPassword(passwordEncoder.encode(request.getPassword()));
            user.setEmail(request.getEmail());
            user.setRealName(request.getRealName());
            user.setPhone(request.getContactPhone());
            user.setRoleId(customerRole != null ? customerRole.getId() : 2L); // 默认客户角色
            user.setStatus(1); // 启用状态
            user.setCreatedTime(LocalDateTime.now());
            user.setUpdatedTime(LocalDateTime.now());

            userMapper.insert(user);

            // 创建客户档案
            CustomerProfile customerProfile = new CustomerProfile();
            customerProfile.setUserId(user.getId());
            customerProfile.setCompanyName(request.getCompanyName());
            customerProfile.setIndustry(request.getIndustry());
            customerProfile.setContactPerson(request.getRealName());
            customerProfile.setContactPhone(request.getContactPhone());
            customerProfile.setContactEmail(request.getEmail());
            customerProfile.setAddress(request.getAddress());
            customerProfile.setWebsite(request.getWebsite());

            // 设置默认配额（免费版）
            customerProfile.setApiQuotaMonthly(1000);
            customerProfile.setApiQuotaDaily(100);
            customerProfile.setApiQuotaHourly(10);
            customerProfile.setAccountType(1); // 免费版
            customerProfile.setAccountStatus(1); // 正常状态
            customerProfile.setTrialExpiresAt(LocalDateTime.now().plusDays(30)); // 30天试用期
            customerProfile.setCreatedTime(LocalDateTime.now());
            customerProfile.setUpdatedTime(LocalDateTime.now());

            customerProfileMapper.insert(customerProfile);

            log.info("用户注册成功: username={}, userId={}", request.getUsername(), user.getId());
            return Result.success("注册成功，请登录您的账号");

        } catch (Exception e) {
            log.error("用户注册失败: username={}", request.getUsername(), e);
            return Result.error("注册失败: " + e.getMessage());
        }
    }

    /**
     * 用户登出
     */
    public Result<Void> logout() {
        SecurityContextHolder.clearContext();
        log.info("用户登出成功");
        return Result.success("登出成功", null);
    }

    /**
     * 刷新Token
     */
    public Result<LoginResponse> refreshToken(String refreshToken) {
        try {
            if (!tokenProvider.validateToken(refreshToken)) {
                return Result.error(ResultCode.TOKEN_INVALID, "刷新Token无效");
            }

            Long userId = tokenProvider.getUserIdFromToken(refreshToken);
            User user = userMapper.selectById(userId);
            if (user == null) {
                throw new RuntimeException("用户不存在");
            }

            // 重新认证用户
            Authentication authentication = authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(user.getUsername(), user.getPassword())
            );

            // 生成新的Token
            String newAccessToken = tokenProvider.generateToken(authentication);
            String newRefreshToken = tokenProvider.generateRefreshToken(authentication);

            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            LoginResponse response = LoginResponse.builder()
                    .token(newAccessToken)
                    .refreshToken(newRefreshToken)
                    .expiresIn(7200)
                    .userInfo(LoginResponse.UserInfo.builder()
                            .id(userPrincipal.getId())
                            .username(userPrincipal.getUsername())
                            .realName(userPrincipal.getRealName())
                            .email(userPrincipal.getEmail())
                            .role(userPrincipal.getRoleCode())
                            .permissions(userPrincipal.getPermissions())
                            .build())
                    .build();

            return Result.success("Token刷新成功", response);

        } catch (Exception e) {
            log.error("Token刷新失败", e);
            return Result.error(ResultCode.TOKEN_INVALID, "Token刷新失败");
        }
    }

    /**
     * 更新最后登录时间
     */
    private void updateLastLoginTime(Long userId) {
        try {
            User user = userMapper.selectById(userId);
            if (user != null) {
                user.setLastLoginTime(LocalDateTime.now());
                userMapper.updateById(user);
                log.debug("更新最后登录时间成功: userId={}", userId);
            }
        } catch (Exception e) {
            log.error("更新最后登录时间失败: userId={}", userId, e);
        }
    }
}
