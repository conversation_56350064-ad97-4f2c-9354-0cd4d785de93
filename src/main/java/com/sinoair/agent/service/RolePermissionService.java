package com.sinoair.agent.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sinoair.agent.entity.RolePermission;

import java.util.List;

/**
 * 角色权限关联服务接口
 *
 * <AUTHOR> Team
 */
public interface RolePermissionService extends IService<RolePermission> {

    /**
     * 根据角色ID删除角色权限关联
     */
    boolean deleteByRoleId(Long roleId);

    /**
     * 根据权限ID删除角色权限关联
     */
    boolean deleteByPermissionId(Long permissionId);

    /**
     * 根据角色ID查询权限ID列表
     */
    List<Long> getPermissionIdsByRoleId(Long roleId);

    /**
     * 根据权限ID查询角色ID列表
     */
    List<Long> getRoleIdsByPermissionId(Long permissionId);

    /**
     * 批量保存角色权限关联
     */
    boolean saveBatch(List<RolePermission> rolePermissions);
}
