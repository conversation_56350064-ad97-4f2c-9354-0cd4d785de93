package com.sinoair.agent.service;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sinoair.agent.entity.ApiKey;
import com.sinoair.agent.entity.ApiKeyUsage;
import com.sinoair.agent.mapper.ApiKeyMapper;
import com.sinoair.agent.mapper.ApiKeyUsageMapper;
import com.sinoair.agent.dto.response.ApiKeyCreateResponse;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * API Key服务类
 *
 * <AUTHOR> Team
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ApiKeyService extends ServiceImpl<ApiKeyMapper, ApiKey> {

    private final ApiKeyMapper apiKeyMapper;
    private final ApiKeyUsageMapper apiKeyUsageMapper;
    private final RedisTemplate<String, Object> redisTemplate;

    private static final String RATE_LIMIT_KEY_PREFIX = "api_key_rate_limit:";
    private static final String DAILY_LIMIT_KEY_PREFIX = "api_key_daily_limit:";
    private static final String MONTHLY_LIMIT_KEY_PREFIX = "api_key_monthly_limit:";

    /**
     * 分页查询API Key列表
     */
    public IPage<ApiKey> getApiKeyPage(int current, int size, Long userId, String keyName, Integer status) {
        Page<ApiKey> page = new Page<>(current, size);
        return apiKeyMapper.selectApiKeyPageWithUser(page, userId, keyName, status);
    }

    /**
     * 创建API Key
     */
    @Transactional
    public ApiKey createApiKey(ApiKey apiKey) {
        // 检查密钥名称是否重复
        if (isKeyNameExistForUser(apiKey.getUserId(), apiKey.getKeyName())) {
            throw new RuntimeException("密钥名称已存在，请使用其他名称");
        }
        // 生成唯一的keyId
        String keyId = generateKeyId();
        while (apiKeyMapper.selectOne(new LambdaQueryWrapper<ApiKey>().eq(ApiKey::getKeyId, keyId)) != null) {
            keyId = generateKeyId();
        }
        
        // 生成keySecret并加密存储
        String keySecret = generateKeySecret();
        String encryptedSecret = DigestUtil.sha256Hex(keySecret);
        
        apiKey.setKeyId(keyId);
        apiKey.setKeySecret(encryptedSecret);
        apiKey.setStatus(1); // 默认启用
        apiKey.setCreatedTime(LocalDateTime.now());
        apiKey.setUpdatedTime(LocalDateTime.now());
        
        // 设置默认限制
        if (apiKey.getRateLimit() == null) {
            apiKey.setRateLimit(100); // 每小时100次
        }
        if (apiKey.getDailyLimit() == null) {
            apiKey.setDailyLimit(1000); // 每日1000次
        }
        if (apiKey.getMonthlyLimit() == null) {
            apiKey.setMonthlyLimit(10000); // 每月10000次
        }
        
        save(apiKey);
        
        // 返回时包含明文keySecret供用户保存
        ApiKey result = new ApiKey();
        result.setId(apiKey.getId());
        result.setKeyId(keyId);
        result.setKeySecret(keySecret); // 明文返回，仅此一次
        result.setKeyName(apiKey.getKeyName());
        result.setDescription(apiKey.getDescription());
        result.setUserId(apiKey.getUserId());
        result.setStatus(apiKey.getStatus());
        result.setRateLimit(apiKey.getRateLimit());
        result.setDailyLimit(apiKey.getDailyLimit());
        result.setMonthlyLimit(apiKey.getMonthlyLimit());
        result.setExpiresAt(apiKey.getExpiresAt());
        result.setCreatedTime(apiKey.getCreatedTime());
        
        log.info("创建API Key成功: keyId={}, userId={}", keyId, apiKey.getUserId());
        return result;
    }

    /**
     * 检查用户的密钥名称是否已存在
     */
    public boolean isKeyNameExistForUser(Long userId, String keyName) {
        if (userId == null || !StringUtils.hasText(keyName)) {
            return false;
        }

        LambdaQueryWrapper<ApiKey> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ApiKey::getUserId, userId)
                   .eq(ApiKey::getKeyName, keyName.trim())
                   .eq(ApiKey::getDeleted, 0); // 只检查未删除的记录

        return apiKeyMapper.selectCount(queryWrapper) > 0;
    }

    /**
     * 创建API Key并返回包含明文keySecret的响应对象
     * 用于前端显示完整密钥信息
     */
    @Transactional
    public ApiKeyCreateResponse createApiKeyWithSecret(ApiKey apiKey) {
        // 检查密钥名称是否重复
        if (isKeyNameExistForUser(apiKey.getUserId(), apiKey.getKeyName())) {
            throw new RuntimeException("密钥名称已存在，请使用其他名称");
        }
        // 生成唯一的keyId
        String keyId = generateKeyId();
        while (apiKeyMapper.selectOne(new LambdaQueryWrapper<ApiKey>().eq(ApiKey::getKeyId, keyId)) != null) {
            keyId = generateKeyId();
        }

        // 生成keySecret并加密存储
        String keySecret = generateKeySecret();
        String encryptedSecret = DigestUtil.sha256Hex(keySecret);

        apiKey.setKeyId(keyId);
        apiKey.setKeySecret(encryptedSecret);
        apiKey.setStatus(1); // 默认启用
        apiKey.setCreatedTime(LocalDateTime.now());

        // 设置默认限制
        if (apiKey.getRateLimit() == null) {
            apiKey.setRateLimit(1000); // 每小时1000次
        }
        if (apiKey.getDailyLimit() == null) {
            apiKey.setDailyLimit(10000); // 每日10000次
        }
        if (apiKey.getMonthlyLimit() == null) {
            apiKey.setMonthlyLimit(100000); // 每月100000次
        }

        save(apiKey);

        // 返回包含明文keySecret的响应对象
        ApiKeyCreateResponse response = ApiKeyCreateResponse.fromApiKey(apiKey, keySecret);

        log.info("创建API Key成功: keyId={}, userId={}", keyId, apiKey.getUserId());
        return response;
    }

    /**
     * 验证API Key
     */
    public ApiKey validateApiKey(String keyId, String keySecret) {
        if (!StringUtils.hasText(keyId) || !StringUtils.hasText(keySecret)) {
            return null;
        }

        // 加密keySecret进行比较
        String encryptedSecret = DigestUtil.sha256Hex(keySecret);
        ApiKey apiKey = apiKeyMapper.selectByKeyIdAndSecret(keyId, encryptedSecret);
        
        if (apiKey != null) {
            // 更新最后使用时间
            updateLastUsed(apiKey.getId(), LocalDateTime.now(), null);
        }
        
        return apiKey;
    }

    /**
     * 检查IP限制
     */
    public boolean checkIpRestriction(ApiKey apiKey, String clientIp) {
        if (!StringUtils.hasText(apiKey.getAllowedIps())) {
            return true; // 没有IP限制
        }
        
        try {
            List<String> allowedIps = JSONUtil.toList(apiKey.getAllowedIps(), String.class);
            return allowedIps.contains(clientIp) || allowedIps.contains("*");
        } catch (Exception e) {
            log.error("解析IP限制配置失败: {}", apiKey.getAllowedIps(), e);
            return false;
        }
    }

    /**
     * 检查请求频率限制
     */
    public boolean checkRateLimit(ApiKey apiKey) {
        String keyId = apiKey.getKeyId();
        
        // 检查小时限制
        if (apiKey.getRateLimit() != null && apiKey.getRateLimit() > 0) {
            String hourlyKey = RATE_LIMIT_KEY_PREFIX + keyId + ":hourly";
            Long hourlyCount = redisTemplate.opsForValue().increment(hourlyKey);
            if (hourlyCount == 1) {
                redisTemplate.expire(hourlyKey, 1, TimeUnit.HOURS);
            }
            if (hourlyCount > apiKey.getRateLimit()) {
                log.warn("API Key小时限制超出: keyId={}, limit={}, current={}", keyId, apiKey.getRateLimit(), hourlyCount);
                return false;
            }
        }
        
        // 检查日限制
        if (apiKey.getDailyLimit() != null && apiKey.getDailyLimit() > 0) {
            String dailyKey = DAILY_LIMIT_KEY_PREFIX + keyId + ":daily";
            Long dailyCount = redisTemplate.opsForValue().increment(dailyKey);
            if (dailyCount == 1) {
                redisTemplate.expire(dailyKey, 1, TimeUnit.DAYS);
            }
            if (dailyCount > apiKey.getDailyLimit()) {
                log.warn("API Key日限制超出: keyId={}, limit={}, current={}", keyId, apiKey.getDailyLimit(), dailyCount);
                return false;
            }
        }
        
        // 检查月限制
        if (apiKey.getMonthlyLimit() != null && apiKey.getMonthlyLimit() > 0) {
            String monthlyKey = MONTHLY_LIMIT_KEY_PREFIX + keyId + ":monthly";
            Long monthlyCount = redisTemplate.opsForValue().increment(monthlyKey);
            if (monthlyCount == 1) {
                redisTemplate.expire(monthlyKey, 30, TimeUnit.DAYS);
            }
            if (monthlyCount > apiKey.getMonthlyLimit()) {
                log.warn("API Key月限制超出: keyId={}, limit={}, current={}", keyId, apiKey.getMonthlyLimit(), monthlyCount);
                return false;
            }
        }
        
        return true;
    }

    /**
     * 记录API使用情况
     */
    public void recordUsage(ApiKey apiKey, HttpServletRequest request) {
        recordUsage(apiKey, request, 200, null, null, null);
    }

    /**
     * 记录API使用情况（完整版本）
     */
    public void recordUsage(ApiKey apiKey, HttpServletRequest request, Integer statusCode,
                           Long requestSize, Long responseSize, Integer responseTime) {
        try {
            ApiKeyUsage usage = new ApiKeyUsage();
            usage.setApiKeyId(apiKey.getId());
            usage.setEndpoint(request.getRequestURI());
            usage.setMethod(request.getMethod());
            usage.setRequestIp(getClientIpAddress(request));
            usage.setUserAgent(request.getHeader("User-Agent"));
            usage.setStatusCode(statusCode != null ? statusCode : 200);
            usage.setRequestSize(requestSize != null ? requestSize : 0L);
            usage.setResponseSize(responseSize != null ? responseSize : 0L);
            usage.setResponseTime(responseTime != null ? responseTime : 0);
            usage.setRequestTime(LocalDateTime.now());

            apiKeyUsageMapper.insert(usage);
        } catch (Exception e) {
            log.error("记录API使用情况失败: keyId={}", apiKey.getKeyId(), e);
        }
    }

    /**
     * 更新最后使用时间
     */
    public void updateLastUsed(Long apiKeyId, LocalDateTime lastUsedAt, String lastUsedIp) {
        apiKeyMapper.updateLastUsed(apiKeyId, lastUsedAt, lastUsedIp);
    }

    /**
     * 生成keyId
     */
    private String generateKeyId() {
        return "ak_" + IdUtil.fastSimpleUUID().substring(0, 16);
    }

    /**
     * 生成keySecret
     */
    private String generateKeySecret() {
        return IdUtil.fastSimpleUUID();
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (StringUtils.hasText(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (StringUtils.hasText(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
}
