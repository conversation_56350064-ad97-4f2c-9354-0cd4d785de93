package com.sinoair.agent.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sinoair.agent.entity.Permission;

import java.util.List;

/**
 * 权限服务接口
 *
 * <AUTHOR> Team
 */
public interface PermissionService extends IService<Permission> {

    /**
     * 根据用户ID查询权限列表
     */
    List<Permission> getPermissionsByUserId(Long userId);

    /**
     * 根据角色ID查询权限列表
     */
    List<Permission> getPermissionsByRoleId(Long roleId);

    /**
     * 查询所有权限（树形结构）
     */
    List<Permission> getAllPermissionsTree();

    /**
     * 根据权限类型查询权限
     */
    List<Permission> getPermissionsByType(Integer permissionType);

    /**
     * 根据父权限ID查询子权限
     */
    List<Permission> getPermissionsByParentId(Long parentId);

    /**
     * 根据权限编码查询权限
     */
    Permission getPermissionByCode(String permissionCode);

    /**
     * 查询用户的菜单权限（树形结构）
     */
    List<Permission> getMenuPermissionsByUserId(Long userId);

    /**
     * 查询用户的按钮权限编码列表
     */
    List<String> getButtonPermissionsByUserId(Long userId);

    /**
     * 创建权限
     */
    boolean createPermission(Permission permission);

    /**
     * 更新权限
     */
    boolean updatePermission(Permission permission);

    /**
     * 删除权限
     */
    boolean deletePermission(Long permissionId);

    /**
     * 批量删除权限
     */
    boolean deletePermissions(List<Long> permissionIds);

    /**
     * 启用/禁用权限
     */
    boolean updatePermissionStatus(Long permissionId, Integer status);

    /**
     * 检查权限编码是否存在
     */
    boolean existsByPermissionCode(String permissionCode);

    /**
     * 构建权限树
     */
    List<Permission> buildPermissionTree(List<Permission> permissions);

    /**
     * 检查用户是否有指定权限
     */
    boolean hasPermission(Long userId, String permissionCode);

    /**
     * 检查用户是否有指定权限列表中的任一权限
     */
    boolean hasAnyPermission(Long userId, List<String> permissionCodes);

    /**
     * 检查用户是否有指定权限列表中的所有权限
     */
    boolean hasAllPermissions(Long userId, List<String> permissionCodes);
}
