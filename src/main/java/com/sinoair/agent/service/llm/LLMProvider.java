package com.sinoair.agent.service.llm;

import com.sinoair.agent.dto.request.RecognitionRequest;
import com.sinoair.agent.dto.response.RecognitionResult;
import com.sinoair.agent.entity.Agent;
import com.sinoair.agent.entity.UploadedFile;

/**
 * LLM提供商接口
 *
 * <AUTHOR> Team
 */
public interface LLMProvider {

    /**
     * 识别文档
     *
     * @param agent Agent
     * @param request 页面请求封装的参数和文件
     * @param fromWeb 是否来自页面
     * @return
     */
    RecognitionResult recognize(Agent agent, RecognitionRequest request, boolean fromWeb);


    /**
     * 获取提供商名称
     */
    String getProviderName();

    /**
     * 检查提供商是否可用
     */
    boolean isAvailable();
}
