package com.sinoair.agent.service.llm;

import com.sinoair.agent.dto.request.RecognitionRequest;
import com.sinoair.agent.dto.response.RecognitionResult;
import com.sinoair.agent.entity.Agent;
import com.sinoair.agent.entity.RecognitionRecord;
import com.sinoair.agent.entity.UploadedFile;
import com.sinoair.agent.mapper.RecognitionRecordMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * LLM服务
 *
 * <AUTHOR> Team
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LLMService {

    private final QianwenLLMProvider qianwenProvider;
    private final ZhongWaiYunLLMProvider zhongWaiYunLLMProvider;
    private final ZhongWaiYunVLMProvider zhongWaiYunVLMProvider;
    private final RecognitionRecordMapper recordMapper;

    /**
     * 执行文档识别
     */
    public RecognitionResult recognize(Agent agent, UploadedFile file, Long userId) {
        RecognitionRequest request = new RecognitionRequest();
        request.setFileId(file.getId());
        RecognitionResult result;

        try {
            log.info("开始执行文档识别: agentId={}, fileId={}", agent.getId(), file.getId());

            // 根据Agent类型选择不同的LLM提供商
            LLMProvider provider = selectProvider(agent);


            // 执行识别
            result = provider.recognize(agent, request, false);


            log.info("文档识别完成: agentId={}, success={}", agent.getId(), result.isSuccess());

        } catch (Exception e) {
            log.error("文档识别失败: agentId={}", agent.getId(), e);
            result = RecognitionResult.failure("识别失败: " + e.getMessage());
        }
        // 保存日志
        saveLog(agent, file.getId(), userId, result);

        return result;


    }

    private void saveLog(Agent agent, Long fileId, Long userId, RecognitionResult recognize) {
        try {
            // 创建识别记录
            RecognitionRecord savedRecord = new RecognitionRecord();
            savedRecord.setAgentId(agent.getId());
            savedRecord.setFileId(fileId);

            savedRecord.setBusinessTypeId(agent.getBusinessTypeId());
            savedRecord.setInputParams(agent.getPromptTemplate());

            savedRecord.setUserId(userId);
            savedRecord.setSessionId(UUID.randomUUID().toString());
            savedRecord.setCreatedTime(LocalDateTime.now());

            savedRecord.setRecognitionResult(recognize.getResult());
            savedRecord.setConfidenceScore(recognize.getConfidence());
            savedRecord.setProcessingTime(recognize.getProcessingTime());
            savedRecord.setStatus(recognize.isSuccess() ? RecognitionRecord.STATUS_SUCCESS : RecognitionRecord.STATUS_FAILED);
            savedRecord.setErrorMessage(recognize.getErrorMessage());
            savedRecord.setCompletedTime(LocalDateTime.now());
            recordMapper.insert(savedRecord);
        } catch (Exception e) {
            log.error("保存识别记录失败: agentId={}", agent.getId(), e);
        }
    }

    /**
     * 执行文档识别
     */
    public RecognitionResult recognize(Agent agent, RecognitionRequest request) {
        try {
            log.info("开始执行文档识别: agentId={}, fileId={}", agent.getId(), request.getFileId());

            // 根据Agent类型选择不同的LLM提供商
            LLMProvider provider = selectProvider(agent);

            // 执行识别
            RecognitionResult result = provider.recognize(agent, request, true);

            log.info("文档识别完成: agentId={}, success={}", agent.getId(), result.isSuccess());
            return result;

        } catch (Exception e) {
            log.error("文档识别失败: agentId={}", agent.getId(), e);
            return RecognitionResult.failure("识别失败: " + e.getMessage());
        }
    }

    /**
     * todo 选择LLM提供商
     */
    private LLMProvider selectProvider(Agent agent) {
        // 这里可以根据Agent配置选择不同的提供商
        Integer agentType = agent.getAgentType();
        log.info("选择LLM提供商平台: agentType={}", agentType);
        switch (agentType) {
            case Agent.TYPE_INTERNAL_LLM:
                return zhongWaiYunLLMProvider;
            case Agent.TYPE_INTERNAL_VLM:
                return zhongWaiYunVLMProvider;
            case Agent.TYPE_EXTERNAL_AGENT:
                return qianwenProvider;
            default:
                // 目前默认使用千问
                return qianwenProvider;
        }
    }


}
