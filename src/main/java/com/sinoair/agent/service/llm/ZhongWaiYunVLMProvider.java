package com.sinoair.agent.service.llm;

import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sinoair.agent.common.Result;
import com.sinoair.agent.dto.request.RecognitionRequest;
import com.sinoair.agent.dto.response.RecognitionResult;
import com.sinoair.agent.entity.Agent;
import com.sinoair.agent.entity.UploadedFile;
import com.sinoair.agent.service.DocumentParserService;
import com.sinoair.agent.service.FileService;
import com.sinoair.agent.util.LLMUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * 中外云VLM提供商实现
 *
 * <AUTHOR> Team
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ZhongWaiYunVLMProvider implements LLMProvider {

    @Value("${app.llm.providers.zhongwaiyun_vlm.appKey}")
    private String appKey;
    @Value("${app.llm.providers.zhongwaiyun_vlm.appId}")
    private String appId;
    @Value("${app.llm.providers.zhongwaiyun_vlm.appSecret}")
    private String appSecret;
    @Value("${app.llm.providers.zhongwaiyun_vlm.docType}")
    private String docType;
    @Value("${app.llm.providers.zhongwaiyun_vlm.outputType}")
    private String outputType;
    @Value("${app.llm.providers.zhongwaiyun_vlm.base-url}")
    private String baseUrl;

    private final RestTemplate restTemplate = new RestTemplate();
    private final ObjectMapper objectMapper;
    private final DocumentParserService documentParserService;
    private final FileService fileService;

    @Override
    public RecognitionResult recognize(Agent agent, RecognitionRequest request, boolean fromWeb) {
        long startTime = System.currentTimeMillis();
        try {
            log.info("开始执行中外云VLM识别: agentId={}, fileId={}", agent.getId(), request.getFileId());

            // 获取文件内容
            if (request.getFileId() == null || request.getFileId() <= 0) {
                throw new IllegalArgumentException("文件ID不能为空");
            }

            // 获取文件信息
            Result<UploadedFile> fileInfoResult = fileService.getFileInfo(request.getFileId());
            if (!fileInfoResult.isSuccess()) {
                throw new RuntimeException("获取文件信息失败: " + fileInfoResult.getMessage());
            }

            UploadedFile fileInfo = fileInfoResult.getData();

            // 获取文件内容
            Result<byte[]> fileContentResult = fileService.downloadFile(request.getFileId());
            if (!fileContentResult.isSuccess()) {
                throw new RuntimeException("获取文件内容失败: " + fileContentResult.getMessage());
            }

            byte[] fileContent = fileContentResult.getData();

            // 构建multipart请求
            MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
            body.add("docType", docType);
            body.add("outputType", outputType);
            body.add("appId", appId);
            body.add("appKey", appKey);
            body.add("appSecret", appSecret);
            body.add("sysCode", "03012");

            // 添加文件
            ByteArrayResource fileResource = new ByteArrayResource(fileContent) {
                @Override
                public String getFilename() {
                    return fileInfo.getOriginalName();
                }
            };
            body.add("files", fileResource);

            // 构建config参数
            Map<String, Object> config = new HashMap<>();
            config.put("page_num", 1);
            if (fromWeb) {
                String prompt = request.getPromptTemplate() != null ? request.getPromptTemplate() : agent.getPromptTemplate();
                // 测试不追加json
                // config.put("prompt", prompt);
                // log.debug("使用自定义提示词: {}", prompt);
                String prompt1 = LLMUtils.getSystemPrompt(prompt, agent.getJsonTemplate());
                config.put("prompt", prompt1);
                log.debug("使用自定义提示词1: {}", prompt1);
                config.put("model", request.getModel());
            } else {
                String prompt1 = LLMUtils.getSystemPrompt(agent.getPromptTemplate(), agent.getJsonTemplate());
                config.put("prompt", prompt1);
                log.debug("使用自定义提示词: {}", prompt1);
                String agentConfig = agent.getConfig();

                config.put("model", JSONUtil.parse(agentConfig).getByPath("llm_config.model").toString());
            }
            body.add("config", JSONUtil.toJsonStr(config));

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);

            HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);

            log.info("发送VLM识别请求到: {}", baseUrl);
            log.info("请求参数: docType={}, outputType={}, appId={}, fileName={}",
                    docType, outputType, appId, fileInfo.getOriginalName());
            log.info("请求参数: {}", body);

            // 发送请求
            ResponseEntity<String> response = restTemplate.exchange(
                    baseUrl,
                    HttpMethod.POST,
                    requestEntity,
                    String.class
            );

            log.info("VLM识别响应状态: {}", response.getStatusCode());
            log.info("VLM识别响应内容: {}", response.getBody());

            // 解析响应
            JsonNode responseJson = objectMapper.readTree(response.getBody());
            String content = extractContent(responseJson);
            content = LLMUtils.trimJsonTag(content);

            // 验证并格式化JSON内容
            content = validateAndFormatJson(content);

            long processingTime = System.currentTimeMillis() - startTime;

            // 构建结果
            return RecognitionResult.builder()
                    .success(true)
                    .result(content)
                    .confidence(BigDecimal.valueOf(0.95))
                    .processingTime((int) processingTime)
                    .build();

        } catch (Exception e) {
            log.error("中外云VLM识别失败", e);
            long processingTime = System.currentTimeMillis() - startTime;
            return RecognitionResult.builder()
                    .success(false)
                    .result("中外云VLM识别失败: " + e.getMessage())
                    .confidence(BigDecimal.valueOf(0))
                    .processingTime((int) processingTime)
                    .usage(null)
                    .build();
        }
    }


    @Override
    public String getProviderName() {
        return "zhongwaiyun_vlm";
    }

    @Override
    public boolean isAvailable() {
        return true;
    }

    /**
     * 从响应中提取内容
     */
    private String extractContent(JsonNode responseJson) {
        try {
            // 检查响应是否成功
            if (responseJson.has("success") && !responseJson.get("success").asBoolean()) {
                String errorMsg = "识别失败";
                if (responseJson.has("resultMessage")) {
                    errorMsg = responseJson.get("resultMessage").asText();
                }
                throw new RuntimeException(errorMsg);
            }

            // 根据中外云VLM的实际响应格式提取内容
            // 响应格式: values.data[0].data.response
            if (responseJson.has("values")) {
                JsonNode values = responseJson.get("values");
                if (values.has("data") && values.get("data").isArray()) {
                    JsonNode dataArray = values.get("data");
                    if (dataArray.size() > 0) {
                        JsonNode firstItem = dataArray.get(0);
                        if (firstItem.has("data")) {
                            JsonNode itemData = firstItem.get("data");
                            if (itemData.has("response")) {
                                String response = itemData.get("response").asText();
                                log.info("成功提取VLM识别结果，内容长度: {}", response.length());
                                return response;
                            }
                        }

                        // 检查是否有statusCode表示错误
                        if (firstItem.has("statusCode")) {
                            int statusCode = firstItem.get("statusCode").asInt();
                            if (statusCode != 200) {
                                String message = firstItem.has("message") ?
                                        firstItem.get("message").asText() : "识别失败";
                                throw new RuntimeException("VLM识别失败，状态码: " + statusCode + ", 消息: " + message);
                            }
                        }
                    }
                }
            }

            // 兼容其他可能的响应格式
            if (responseJson.has("data")) {
                JsonNode data = responseJson.get("data");
                if (data.has("result")) {
                    return data.get("result").asText();
                }
                if (data.has("response")) {
                    return data.get("response").asText();
                }
                if (data.has("content")) {
                    return data.get("content").asText();
                }
                // 如果data是字符串类型
                if (data.isTextual()) {
                    return data.asText();
                }
            }

            // 如果有result字段
            if (responseJson.has("result")) {
                return responseJson.get("result").asText();
            }

            // 如果有content字段
            if (responseJson.has("content")) {
                return responseJson.get("content").asText();
            }

            // 如果都没有找到内容，记录警告并返回整个响应
            log.warn("未找到预期的响应内容字段，响应结构: {}", responseJson.toString());
            return "未找到识别结果，原始响应: " + responseJson.toString();

        } catch (Exception e) {
            log.error("解析VLM响应内容失败: {}", e.getMessage(), e);
            return "解析响应失败: " + e.getMessage();
        }
    }

    /**
     * 验证并格式化JSON内容
     */
    private String validateAndFormatJson(String content) {
        if (content == null || content.trim().isEmpty()) {
            return "{}";
        }

        try {
            // 尝试解析JSON以验证格式
            Object parsed = objectMapper.readValue(content, Object.class);
            // 重新序列化以确保格式正确
            return objectMapper.writeValueAsString(parsed);
        } catch (Exception e) {
            log.warn("JSON格式验证失败，返回原始内容: {}", e.getMessage());
            // 如果不是有效的JSON，返回原始内容
            return content;
        }
    }
}
