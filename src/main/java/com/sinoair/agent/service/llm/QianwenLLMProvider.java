package com.sinoair.agent.service.llm;

import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sinoair.agent.common.Result;
import com.sinoair.agent.dto.request.RecognitionRequest;
import com.sinoair.agent.dto.response.RecognitionResult;
import com.sinoair.agent.entity.Agent;
import com.sinoair.agent.service.DocumentParserService;
import com.sinoair.agent.util.LLMUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * 千问LLM提供商实现
 *
 * <AUTHOR> Team
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class QianwenLLMProvider implements LLMProvider {

    @Value("${app.llm.providers.qianwen.api-key}")
    private String apiKey;

    @Value("${app.llm.providers.qianwen.base-url}")
    private String baseUrl;

    @Value("${app.llm.providers.qianwen.model}")
    private String model;

    private final RestTemplate restTemplate = new RestTemplate();
    private final ObjectMapper objectMapper;
    private final DocumentParserService documentParserService;

    @Override
    public RecognitionResult recognize(Agent agent, RecognitionRequest request, boolean fromWeb) {
        long startTime = System.currentTimeMillis();
        try {

            // 构建请求
            Map<String, Object> requestBody = buildRequest(agent, request);
            log.info("开始执行千wenLLM识别: agentId={}, fileId={}", agent.getId(), request.getFileId());
            log.info("请求体: {}", JSONUtil.toJsonPrettyStr(requestBody));

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.set("Authorization", "Bearer " + apiKey);
            headers.set("Content-Type", "application/json");

            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);

            // 发送请求
            log.info("Request URL: {}/chat/completions", baseUrl);
            // ----------------说明 ：发布到 生产环境 才放开，已经调试通过 测试使用假的数据即可 Start----------------
            ResponseEntity<String> response = restTemplate.exchange(
                    baseUrl + "/chat/completions",
                    HttpMethod.POST,
                    entity,
                    String.class
            );

            // 解析响应
            JsonNode responseJson = objectMapper.readTree(response.getBody());
            String content = responseJson.path("choices").get(0).path("message").path("content").asText();
            // ----------------发布到 生产环境 才放开，已经调试通过 测试使用假的数据即可 END----------------
            // 这是测试的假数据，平时开发使用假数据即可
            // String myJson = """
            //                 {
            //                     "receiver": {
            //                         "name": "陳貞伃",
            //                         "phone": "886-0955545463",
            //                         "address": "台湾 新北市 芦洲区 海棠 101号 中正路"
            //                     },
            //                     "sender": {
            //                         "name": "台湾超峰国际供应链(中外運)",
            //                         "phone": "02-82732467",
            //                         "address": "新北市土城區中華路一段56巷19號"
            //                     }
            //                 }
            //         """;
            // JsonNode responseJson = new ObjectMapper().readTree("""
            //         {
            //             "choices": [
            //                 {
            //                     "message": {
            //                         "role": "assistant",
            //                         "content": "我是阿里云开发的一款超大规模语言模型，我叫通义千问。"
            //                     },
            //                     "finish_reason": "stop",
            //                     "index": 0,
            //                     "logprobs": null
            //                 }
            //             ],
            //             "object": "chat.completion",
            //             "usage": {
            //                 "prompt_tokens": 3019,
            //                 "completion_tokens": 104,
            //                 "total_tokens": 3123,
            //                 "prompt_tokens_details": {
            //                     "cached_tokens": 2048
            //                 }
            //             },
            //             "created": 1735120033,
            //             "system_fingerprint": null,
            //             "model": "qwen-plus",
            //             "id": "chatcmpl-6ada9ed2-7f33-9de2-8bb0-78bd4035025a"
            //         }
            //         """);
            // String content = responseJson.path("choices").get(0).path("message")
            //         .path("content")
            //         .asText();
            // content = myJson;

            // 特殊处理json
            content = LLMUtils.trimJsonTag(content);

            // 验证并格式化JSON内容
            content = validateAndFormatJson(content);

            long processingTime = System.currentTimeMillis() - startTime;

            // 构建结果
            return RecognitionResult.builder()
                    .success(true)
                    .result(content)
                    .confidence(BigDecimal.valueOf(0.95)) // 模拟置信度
                    .processingTime((int) processingTime)
                    .usage(extractUsage(responseJson))
                    .build();

        } catch (Exception e) {
            log.error("千问LLM识别失败", e);
            long processingTime = System.currentTimeMillis() - startTime;
            return RecognitionResult.builder()
                    .success(false)
                    .result("千问LLM识别失败: " + e.getMessage())
                    .confidence(BigDecimal.valueOf(0)) // 模拟置信度
                    .processingTime((int) processingTime)
                    .usage(null)
                    .build();
        }
    }

    @Override
    public String getProviderName() {
        return "qianwen";
    }

    @Override
    public boolean isAvailable() {
        return apiKey != null && !apiKey.trim().isEmpty();
    }

    /**
     * 构建请求体
     */
    private Map<String, Object> buildRequest(Agent agent, RecognitionRequest request) {
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("model", model);

        // 构建消息
        Map<String, Object> message = new HashMap<>();
        message.put("role", "user");

        // 构建提示词
        String prompt = buildPrompt(agent, request);
        message.put("content", prompt);

        requestBody.put("messages", new Object[]{message});
        requestBody.put("temperature", 0.7);
        requestBody.put("max_tokens", 2000);

        return requestBody;
    }

    /**
     * 构建提示词
     */
    private String buildPrompt(Agent agent, RecognitionRequest request) {
        StringBuilder prompt = new StringBuilder();

        // 优先使用请求中的自定义提示词模板
        String templateToUse = null;
        if (request.getPromptTemplate() != null && !request.getPromptTemplate().trim().isEmpty()) {
            templateToUse = request.getPromptTemplate();
        } else if (agent.getPromptTemplate() != null && !agent.getPromptTemplate().trim().isEmpty()) {
            templateToUse = agent.getPromptTemplate();
        }

        if (templateToUse != null) {
            prompt.append(templateToUse).append("\n\n");
        } else {
            // 默认提示词
            prompt.append("请分析以下内容，提取关键信息并按照指定的JSON格式返回结果。\n\n");
        }

        // 添加JSON模板要求
        prompt.append(LLMUtils.getSystemPrompt(prompt.toString(), agent.getJsonTemplate()));


        // 获取内容（文件或文本）
        String content = getContent(request);
        prompt.append("内容：\n");
        prompt.append(content);

        return prompt.toString();
    }

    /**
     * 提取使用量信息
     */
    private RecognitionResult.Usage extractUsage(JsonNode responseJson) {
        JsonNode usage = responseJson.path("usage");
        return RecognitionResult.Usage.builder()
                .promptTokens(usage.path("prompt_tokens").asInt())
                .completionTokens(usage.path("completion_tokens").asInt())
                .totalTokens(usage.path("total_tokens").asInt())
                .build();
    }

    /**
     * 获取内容（文件或文本）
     */
    private String getContent(RecognitionRequest request) {
        // 优先使用文本输入
        if (request.getInputText() != null && !request.getInputText().trim().isEmpty()) {
            log.info("使用文本输入模式，文本长度: {}", request.getInputText().length());
            String content = request.getInputText().trim();

            // // 如果内容过长，截取前面部分并添加提示
            // if (content.length() > 10000) {
            //     content = content.substring(0, 10000) + "\n\n[注意：文本内容过长，已截取前10000字符]";
            // }

            return content;
        }

        // 使用文件模式
        Long fileId = request.getFileId();
        if (fileId == null) {
            log.warn("既没有文本输入也没有文件ID");
            return "错误：未提供文本内容或文件ID。";
        }

        try {
            log.info("使用文件模式，开始解析文档内容: fileId={}", fileId);

            // 使用文档解析服务解析文件内容
            Result<String> parseResult = documentParserService.parseDocument(fileId);

            if (parseResult.isSuccess()) {
                String content = parseResult.getData();
                log.info("文档解析成功: fileId={}, 内容长度={}", fileId, content.length());

                // // 如果内容过长，截取前面部分并添加提示
                // if (content.length() > 10000) {
                //     content = content.substring(0, 10000) + "\n\n[注意：文档内容过长，已截取前10000字符]";
                // }

                return content;
            } else {
                log.error("文档解析失败: fileId={}, 错误={}", fileId, parseResult.getMessage());
                return "文档解析失败: " + parseResult.getMessage() + "\n请检查文件格式是否支持或文件是否损坏。";
            }

        } catch (Exception e) {
            log.error("文档内容获取异常: fileId={}", fileId, e);
            return "文档内容获取异常: " + e.getMessage() + "\n请稍后重试或联系管理员。";
        }
    }

    /**
     * 验证并格式化JSON内容
     */
    private String validateAndFormatJson(String content) {
        if (content == null || content.trim().isEmpty()) {
            return "{}";
        }

        try {
            // 尝试解析JSON以验证格式
            Object parsed = objectMapper.readValue(content, Object.class);
            // 重新序列化以确保格式正确
            return objectMapper.writeValueAsString(parsed);
        } catch (Exception e) {
            log.warn("JSON格式验证失败，返回原始内容: {}", e.getMessage());
            // 如果不是有效的JSON，返回原始内容
            return content;
        }
    }
}
