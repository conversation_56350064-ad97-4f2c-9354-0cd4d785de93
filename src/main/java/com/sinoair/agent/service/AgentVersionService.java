package com.sinoair.agent.service;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.sinoair.agent.common.Result;
import com.sinoair.agent.dto.request.CreateVersionRequest;
import com.sinoair.agent.entity.Agent;
import com.sinoair.agent.entity.AgentApprovalRecord;
import com.sinoair.agent.entity.AgentVersion;
import com.sinoair.agent.entity.User;
import com.sinoair.agent.mapper.AgentMapper;
import com.sinoair.agent.mapper.AgentVersionMapper;
import com.sinoair.agent.mapper.UserMapper;
import com.sinoair.agent.security.UserPrincipal;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Agent版本管理服务
 *
 * <AUTHOR> Team
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AgentVersionService {

    private final AgentVersionMapper agentVersionMapper;
    private final AgentMapper agentMapper;
    private final AgentApprovalService agentApprovalService;
    private final UserMapper userMapper;

    /**
     * 创建Agent版本快照
     */
    @Transactional
    public Result<AgentVersion> createVersionSnapshot(Long agentId, String changeLog) {
        try {
            Agent agent = agentMapper.selectById(agentId);
            if (agent == null) {
                return Result.error("Agent不存在");
            }
            UserPrincipal currentUser = getCurrentUser();

            // 生成新版本号
            String newVersionNumber = generateNextVersion(agentId);

            // 创建版本快照
            AgentVersion version = new AgentVersion();
            BeanUtils.copyProperties(agent, version);
            version.setId(null); // 清空ID
            version.setAgentId(agentId);
            version.setVersionNumber(newVersionNumber);
            version.setIsCurrent(1); // 设为当前版本
            version.setChangeLog(changeLog);
            version.setCreatorId(currentUser.getId());
            version.setCreatedTime(LocalDateTime.now());

            // 清除其他版本的当前标记
            agentVersionMapper.clearCurrentVersionFlag(agentId);

            // 保存新版本
            agentVersionMapper.insert(version);
            AgentVersion savedVersion = version;

            // 更新Agent的版本号
            agent.setVersion(newVersionNumber);
            agent.setUpdatedTime(LocalDateTime.now());
            agentMapper.updateById(agent);

            log.info("创建Agent版本快照成功: agentId={}, version={}", agentId, newVersionNumber);
            return Result.success("版本快照创建成功", savedVersion);

        } catch (Exception e) {
            log.error("创建Agent版本快照失败: agentId={}", agentId, e);
            return Result.error("创建版本快照失败: " + e.getMessage());
        }
    }

    /**
     * 创建新版本（基于调试台参数）
     */
    @Transactional
    public Result<AgentVersion> createNewVersion(Long agentId, CreateVersionRequest request) {
        try {
            Agent agent = agentMapper.selectById(agentId);
            if (agent == null) {
                return Result.error("Agent不存在");
            }
            UserPrincipal currentUser = getCurrentUser();

            // 生成新版本号
            String newVersionNumber = generateNextVersion(agentId);

            // 创建新版本
            AgentVersion version = new AgentVersion();
            BeanUtils.copyProperties(agent, version);
            version.setId(null); // 清空ID
            version.setAgentId(agentId);
            version.setVersionNumber(newVersionNumber);
            version.setApprovalStatus(0);// 未提交审批

            version.setChangeLog(request.getChangeLog());
            version.setPromptTemplate(request.getPromptTemplate());
            version.setConfig(request.getConfig());

            // 设置Agent类型/平台信息
            if (request.getAgentType() != null) {
                version.setAgentType(request.getAgentType());
            }

            version.setCreatorId(currentUser.getId());
            version.setCreatedTime(LocalDateTime.now());
            version.setUpdatedTime(LocalDateTime.now());
            version.setStatus(AgentVersion.STATUS_TESTING); // 测试中状态


            // 根据Agent状态决定版本处理逻辑
            if (agent.getStatus() == Agent.STATUS_PUBLISHED) { // 已发布的Agent
                // 新版本保存为历史记录，不影响当前版本
                version.setIsCurrent(0);

                log.info("已发布Agent创建新版本: agentId={}, version={}, 保存为历史记录", agentId, newVersionNumber);

            } else {
                // 清除所有版本的当前标记
                agentVersionMapper.clearCurrentVersionFlag(agentId);

                // 新版本设为当前版本
                version.setIsCurrent(1);

                log.info("未发布Agent创建新版本: agentId={}, version={}, 设为当前版本", agentId, newVersionNumber);
            }
            agentVersionMapper.insert(version);


            String message = agent.getStatus() == 3 ?
                    "新版本已保存为历史记录，当前版本保持不变" :
                    "新版本已保存并设为当前版本";

            return Result.success(message, version);

        } catch (Exception e) {
            log.error("创建新版本失败: agentId={}", agentId, e);
            return Result.error("创建新版本失败: " + e.getMessage());
        }
    }

    /**
     * 获取Agent版本列表
     */
    public Result<List<AgentVersion>> getVersionList(Long agentId) {
        try {
            List<AgentVersion> versions = agentVersionMapper.findByAgentIdOrderByCreatedTimeDesc(agentId);
            versions.forEach(version -> {
                // 获取审批状态
                AgentApprovalRecord approvalRecord = agentApprovalService.getApprovalStatus(agentId, version.getId());
                if (approvalRecord != null) {
                    version.setApprovalStatus(approvalRecord.getApprovalStatus());
                    // 设置审批人和审批时间
                    if (approvalRecord.getApprovalStatus() != null && approvalRecord.getApprovalStatus() > 1) {
                        version.setApproverName(approvalRecord.getApproverName());
                        version.setApprovalTime(approvalRecord.getApprovalTime());
                    }
                }

                // 设置审批状态名称
                version.setApprovalStatusName(version.getApprovalStatusName());

                // 设置创建者名称
                if (version.getCreatorId() != null) {
                    try {
                        User creator = userMapper.selectById(version.getCreatorId());
                        if (creator != null) {
                            version.setCreatorName(creator.getRealName() != null ? creator.getRealName() : creator.getUsername());
                        } else {
                            version.setCreatorName("未知用户");
                        }
                    } catch (Exception e) {
                        log.warn("获取版本创建者信息失败: creatorId={}", version.getCreatorId(), e);
                        version.setCreatorName("未知用户");
                    }
                } else {
                    version.setCreatorName("系统");
                }
            });

            return Result.success(versions);
        } catch (Exception e) {
            log.error("获取版本列表失败: agentId={}", agentId, e);
            return Result.error("获取版本列表失败: " + e.getMessage());
        }
    }

    /**
     * 回滚到指定版本
     */
    @Transactional
    public Result<String> rollbackToVersion(Long agentId, String versionNumber) {
        try {
            // 查找目标版本
            AgentVersion targetVersion = agentVersionMapper.findByAgentIdAndVersionNumber(agentId, versionNumber);
            if (targetVersion == null) {
                return Result.error("目标版本不存在");
            }

            // 查找当前Agent
            Agent agent = agentMapper.selectById(agentId);
            if (agent == null) {
                return Result.error("Agent不存在");
            }

            // 先创建当前状态的快照
            createVersionSnapshot(agentId, "回滚前自动备份");

            // 恢复到目标版本的配置
            BeanUtils.copyProperties(targetVersion, agent);
            agent.setId(agentId); // 保持原ID
            agent.setVersion(generateNextVersion(agentId)); // 生成新版本号
            agent.setUpdatedTime(LocalDateTime.now());

            agentMapper.updateById(agent);

            // 更新版本标记
            agentVersionMapper.clearCurrentVersionFlag(agentId);
            targetVersion.setIsCurrent(1);
            agentVersionMapper.updateById(targetVersion);

            log.info("Agent回滚成功: agentId={}, targetVersion={}", agentId, versionNumber);
            return Result.success("回滚成功");

        } catch (Exception e) {
            log.error("Agent回滚失败: agentId={}, targetVersion={}", agentId, versionNumber, e);
            return Result.error("回滚失败: " + e.getMessage());
        }
    }

    /**
     * 生成下一个版本号
     */
    private String generateNextVersion(Long agentId) {
        try {
            // 获取最新版本号
            List<AgentVersion> versions = agentVersionMapper.findByAgentIdOrderByCreatedTimeDesc(agentId);

            if (versions.isEmpty()) {
                // 如果没有历史版本，从Agent表获取当前版本
                Agent agent = agentMapper.selectById(agentId);
                if (agent != null && agent.getVersion() != null) {
                    return incrementVersion(agent.getVersion());
                }
                return "1.0.1"; // 默认第一个版本
            }

            // 获取最新版本号并递增
            String latestVersion = versions.get(0).getVersionNumber();
            return incrementVersion(latestVersion);

        } catch (Exception e) {
            log.warn("生成版本号失败，使用默认版本: {}", e.getMessage());
            return "1.0." + System.currentTimeMillis() % 1000; // 使用时间戳避免冲突
        }
    }

    /**
     * 版本号递增逻辑
     */
    private String incrementVersion(String version) {
        try {
            String[] parts = version.split("\\.");
            if (parts.length >= 3) {
                // 递增修订号 (x.y.z -> x.y.z+1)
                int patch = Integer.parseInt(parts[2]) + 1;
                return parts[0] + "." + parts[1] + "." + patch;
            } else if (parts.length == 2) {
                // 添加修订号 (x.y -> x.y.1)
                return version + ".1";
            } else {
                // 添加次版本号和修订号 (x -> x.0.1)
                return version + ".0.1";
            }
        } catch (Exception e) {
            log.warn("版本号递增失败: {}, 使用默认递增", version);
            return version + ".1";
        }
    }

    /**
     * 创建Agent的初始版本
     */
    @Transactional
    public Result<AgentVersion> createInitialVersion(Long agentId) {
        try {
            Agent agent = agentMapper.selectById(agentId);
            if (agent == null) {
                return Result.error("Agent不存在");
            }

            UserPrincipal currentUser = getCurrentUser();

            // 创建初始版本
            AgentVersion version = new AgentVersion();
            BeanUtils.copyProperties(agent, version);
            version.setId(null); // 清空ID
            version.setAgentId(agentId);
            version.setVersionNumber("1.0.0"); // 初始版本号
            version.setIsCurrent(1); // 设为当前版本
            version.setChangeLog("初始版本"); // 变更日志
            version.setStatus(AgentVersion.STATUS_DRAFT); // 草稿状态
            version.setCreatorId(currentUser.getId());
            version.setCreatedTime(LocalDateTime.now());
            version.setUpdatedTime(LocalDateTime.now());

            // 保存初始版本
            agentVersionMapper.insert(version);

            // 更新Agent的版本号
            agent.setVersion("1.0.0");
            agent.setUpdatedTime(LocalDateTime.now());
            agentMapper.updateById(agent);

            log.info("创建Agent初始版本成功: agentId={}, version=1.0.0", agentId);
            return Result.success("初始版本创建成功", version);

        } catch (Exception e) {
            log.error("创建Agent初始版本失败: agentId={}", agentId, e);
            return Result.error("创建初始版本失败: " + e.getMessage());
        }
    }

    /**
     * 更新版本状态为测试中
     */
    @Transactional
    public Result<String> updateVersionStatusToTesting(Long versionId) {
        try {
            // 获取版本信息
            AgentVersion version = agentVersionMapper.selectById(versionId);
            if (version == null) {
                return Result.error("版本不存在");
            }

            // 检查版本状态，只有草稿状态的版本可以设置为测试中
            if (version.getStatus() != AgentVersion.STATUS_DRAFT) {
                return Result.error("只有草稿状态的版本可以设置为测试中");
            }

            // 获取对应的Agent信息
            Agent agent = agentMapper.selectById(version.getAgentId());
            if (agent == null) {
                return Result.error("对应的Agent不存在");
            }

            // 更新版本状态为测试中
            LambdaUpdateWrapper<AgentVersion> versionUpdateWrapper = new LambdaUpdateWrapper<>();
            versionUpdateWrapper.eq(AgentVersion::getId, versionId)
                    .set(AgentVersion::getStatus, AgentVersion.STATUS_TESTING)
                    .set(AgentVersion::getUpdatedTime, LocalDateTime.now());

            int versionResult = agentVersionMapper.update(null, versionUpdateWrapper);
            if (versionResult <= 0) {
                return Result.error("版本状态更新失败");
            }

            // 检查Agent状态，如果是草稿状态，则同时更新为测试中
            if (agent.getStatus() == Agent.STATUS_DRAFT) {
                LambdaUpdateWrapper<Agent> agentUpdateWrapper = new LambdaUpdateWrapper<>();
                agentUpdateWrapper.eq(Agent::getId, agent.getId())
                        .set(Agent::getStatus, Agent.STATUS_TESTING)
                        .set(Agent::getUpdatedTime, LocalDateTime.now());

                int agentResult = agentMapper.update(null, agentUpdateWrapper);
                if (agentResult > 0) {
                    log.info("Agent和版本状态都已更新为测试中: agentId={}, versionId={}, versionNumber={}",
                            agent.getId(), versionId, version.getVersionNumber());
                    return Result.success("Agent和版本状态已更新为测试中");
                } else {
                    log.warn("版本状态更新成功，但Agent状态更新失败: agentId={}, versionId={}",
                            agent.getId(), versionId);
                    return Result.success("版本状态已更新为测试中，但Agent状态更新失败");
                }
            } else {
                log.info("版本状态已更新为测试中，Agent状态保持不变: agentId={}, agentStatus={}, versionId={}, versionNumber={}",
                        agent.getId(), agent.getStatus(), versionId, version.getVersionNumber());
                return Result.success("版本状态已更新为测试中");
            }

        } catch (Exception e) {
            log.error("更新版本状态失败: versionId={}", versionId, e);
            return Result.error("更新版本状态失败: " + e.getMessage());
        }
    }

    /**
     * 获取当前用户
     */
    private UserPrincipal getCurrentUser() {
        return (UserPrincipal) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
    }
}
