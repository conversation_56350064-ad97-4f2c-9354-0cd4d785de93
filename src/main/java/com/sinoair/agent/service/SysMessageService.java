package com.sinoair.agent.service;

import com.sinoair.agent.entity.SysMessage;

import java.util.List;

/**
 * 站内消息服务接口
 *
 * <AUTHOR> Team
 */
public interface SysMessageService {

    /**
     * 发送消息
     *
     * @param userId 接收用户ID
     * @param title 消息标题
     * @param content 消息内容
     * @param messageType 消息类型
     * @param relatedId 关联业务ID
     * @param relatedType 关联业务类型
     * @return 是否成功
     */
    boolean sendMessage(Long userId, String title, String content, Integer messageType, Long relatedId, String relatedType);

    /**
     * 发送审批通知消息
     *
     * @param userId 接收用户ID
     * @param agentName Agent名称
     * @param approved 是否审批通过
     * @param opinion 审批意见
     * @param agentId Agent ID
     * @return 是否成功
     */
    boolean sendApprovalNotification(Long userId, String agentName, boolean approved, String opinion, Long agentId);

    /**
     * 获取用户未读消息数量
     *
     * @param userId 用户ID
     * @return 未读消息数量
     */
    int getUnreadCount(Long userId);

    /**
     * 获取用户消息列表
     *
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 消息列表
     */
    List<SysMessage> getUserMessages(Long userId, Integer limit);

    /**
     * 标记消息为已读
     *
     * @param messageIds 消息ID列表
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean markAsRead(List<Long> messageIds, Long userId);

    /**
     * 标记所有消息为已读
     *
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean markAllAsRead(Long userId);
}
