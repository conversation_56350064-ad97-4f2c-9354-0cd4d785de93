package com.sinoair.agent.service;

import com.sinoair.agent.dto.request.AgentSupplementDTO;
import com.sinoair.agent.dto.response.AgentSupplementVO;

/**
 * Agent补充资料服务接口
 *
 * <AUTHOR> Team
 */
public interface AgentSupplementService {

    /**
     * 获取Agent补充资料
     *
     * @param agentId Agent ID
     * @return 补充资料信息
     */
    AgentSupplementVO getSupplementInfo(Long agentId);

    /**
     * 保存或更新Agent补充资料
     *
     * @param supplementDTO 补充资料请求
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean saveOrUpdateSupplement(AgentSupplementDTO supplementDTO, Long userId);

    /**
     * 提交Agent补充资料
     *
     * @param agentId Agent ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean submitSupplement(Long agentId, Long userId);

    /**
     * 提交Agent补充资料（指定版本）
     *
     * @param agentId Agent ID
     * @param userId 用户ID
     * @param versionId 版本ID
     * @param versionNumber 版本号
     * @return 是否成功
     */
    boolean submitSupplementWithVersion(Long agentId, Long userId, Long versionId, String versionNumber);

    /**
     * 获取Agent统计信息（用于补充资料展示）
     *
     * @param agentId Agent ID
     * @return 统计信息
     */
    AgentSupplementVO getAgentStatistics(Long agentId);
}
