package com.sinoair.agent.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sinoair.agent.entity.Role;

import java.util.List;

/**
 * 角色服务接口
 *
 * <AUTHOR> Team
 */
public interface RoleService extends IService<Role> {

    /**
     * 分页查询角色列表
     */
    IPage<Role> getPageList(int page, int size, String keyword);

    /**
     * 根据用户ID查询角色列表
     */
    List<Role> getRolesByUserId(Long userId);

    /**
     * 查询所有可用角色
     */
    List<Role> getAllActiveRoles();

    /**
     * 根据角色代码查询角色
     */
    Role getRoleByCode(String roleCode);

    /**
     * 根据角色名称查询角色
     */
    Role getRoleByName(String roleName);

    /**
     * 创建角色
     */
    boolean createRole(Role role);

    /**
     * 更新角色
     */
    boolean updateRole(Role role);

    /**
     * 删除角色
     */
    boolean deleteRole(Long roleId);

    /**
     * 批量删除角色
     */
    boolean deleteRoles(List<Long> roleIds);

    /**
     * 为角色分配权限
     */
    boolean assignPermissions(Long roleId, List<Long> permissionIds);

    /**
     * 查询角色及其权限信息
     */
    List<Role> getRolesWithPermissions();

    /**
     * 根据角色ID查询角色及其权限信息
     */
    Role getRoleWithPermissions(Long roleId);

    /**
     * 启用/禁用角色
     */
    boolean updateRoleStatus(Long roleId, Integer status);

    /**
     * 检查角色编码是否存在
     */
    boolean existsByRoleCode(String roleCode);

    /**
     * 检查角色名称是否存在
     */
    boolean existsByRoleName(String roleName);

    /**
     * 获取角色统计信息
     */
    Long getRoleCount();

    /**
     * 获取活跃角色统计信息
     */
    Long getActiveRoleCount();
}
