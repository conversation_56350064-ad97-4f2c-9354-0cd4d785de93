package com.sinoair.agent.service;

import com.sinoair.agent.config.MinioProperties;
import com.sinoair.agent.dto.response.MinioFileInfo;
import com.sinoair.agent.dto.response.MinioUploadResponse;
import com.sinoair.agent.util.NetworkTestUtil;
import io.minio.*;
import io.minio.errors.*;
import io.minio.http.Method;
import io.minio.messages.Bucket;
import io.minio.messages.Item;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;
import org.springframework.web.multipart.MultipartFile;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * MinIO文件存储服务
 * 
 * <AUTHOR> Team
 */
@Slf4j
@Service
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "app.minio", name = "enabled", havingValue = "true", matchIfMissing = true)
public class MinioService {

    private final MinioClient minioClient;
    private final MinioProperties minioProperties;

    /**
     * 初始化存储桶
     */
    @PostConstruct
    public void initBucket() {
        try {
            // 先测试网络连接
            // log.info("检查MinIO网络连接: {}", minioProperties.getEndpoint());
            // boolean networkOk = NetworkTestUtil.testMinioConnection(minioProperties.getEndpoint());
            //
            // if (!networkOk) {
            //     log.warn("MinIO网络连接失败，存储桶初始化跳过");
            //     return;
            // }

            createBucketIfNotExists(minioProperties.getBucketName());
            log.info("MinIO存储桶初始化完成: {}", minioProperties.getBucketName());
        } catch (Exception e) {
            log.error("MinIO存储桶初始化失败", e);
        }
    }

    /**
     * 上传文件
     */
    public MinioUploadResponse uploadFile(MultipartFile file, String objectKey) throws Exception {
        return uploadFile(file.getInputStream(), objectKey, file.getContentType(), file.getSize(), 
                createMetadata(file));
    }

    /**
     * 上传文件流
     */
    public MinioUploadResponse uploadFile(InputStream inputStream, String objectKey,
                                        String contentType, long size, Map<String, String> metadata) throws Exception {
        int maxRetries = 3;
        Exception lastException = null;

        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                log.info("尝试上传文件 (第{}/{}次): objectKey={}", attempt, maxRetries, objectKey);

                // 构建上传参数
                PutObjectArgs.Builder builder = PutObjectArgs.builder()
                        .bucket(minioProperties.getBucketName())
                        .object(objectKey)
                        .stream(inputStream, size, -1)
                        .contentType(contentType);

                // 添加元数据
                if (metadata != null && !metadata.isEmpty()) {
                    builder.userMetadata(metadata);
                }

                // 执行上传
                ObjectWriteResponse response = minioClient.putObject(builder.build());

                log.info("文件上传成功: bucket={}, objectKey={}, etag={}",
                        minioProperties.getBucketName(), objectKey, response.etag());

                // 生成预签名URL
                String presignedUrl = generatePresignedUrl(objectKey, Method.GET, 7, TimeUnit.DAYS);

                return MinioUploadResponse.builder()
                        .bucketName(minioProperties.getBucketName())
                        .objectKey(objectKey)
                        .etag(response.etag())
                        .uploadTime(LocalDateTime.now())
                        .presignedUrl(presignedUrl)
                        .presignedUrlExpiry(LocalDateTime.now().plusDays(7))
                        .build();

            } catch (Exception e) {
                lastException = e;
                log.warn("文件上传失败 (第{}/{}次): objectKey={}, 错误: {}",
                        attempt, maxRetries, objectKey, e.getMessage());

                if (attempt < maxRetries) {
                    try {
                        Thread.sleep(1000 * attempt); // 递增延迟
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
        }

        log.error("文件上传最终失败: objectKey={}", objectKey, lastException);
        throw new RuntimeException("文件上传失败: " + lastException.getMessage(), lastException);
    }

    /**
     * 下载文件
     */
    public InputStream downloadFile(String objectKey) throws Exception {
        try {
            return minioClient.getObject(
                    GetObjectArgs.builder()
                            .bucket(minioProperties.getBucketName())
                            .object(objectKey)
                            .build()
            );
        } catch (Exception e) {
            log.error("文件下载失败: objectKey={}", objectKey, e);
            throw new RuntimeException("文件下载失败: " + e.getMessage(), e);
        }
    }

    /**
     * 删除文件
     */
    public void deleteFile(String objectKey) throws Exception {
        try {
            minioClient.removeObject(
                    RemoveObjectArgs.builder()
                            .bucket(minioProperties.getBucketName())
                            .object(objectKey)
                            .build()
            );
            log.info("文件删除成功: objectKey={}", objectKey);
        } catch (Exception e) {
            log.error("文件删除失败: objectKey={}", objectKey, e);
            throw new RuntimeException("文件删除失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取文件信息
     */
    public MinioFileInfo getFileInfo(String objectKey) throws Exception {
        try {
            StatObjectResponse stat = minioClient.statObject(
                    StatObjectArgs.builder()
                            .bucket(minioProperties.getBucketName())
                            .object(objectKey)
                            .build()
            );

            return MinioFileInfo.builder()
                    .objectKey(objectKey)
                    .fileName(extractFileName(objectKey))
                    .fileSize(stat.size())
                    .contentType(stat.contentType())
                    .etag(stat.etag())
                    .lastModified(LocalDateTime.ofInstant(stat.lastModified().toInstant(), ZoneId.systemDefault()))
                    .userMetadata(stat.userMetadata())
                    .metadata(stat.headers().toMultimap().entrySet().stream()
                            .collect(HashMap::new, (map, entry) -> 
                                    map.put(entry.getKey(), String.join(",", entry.getValue())), HashMap::putAll))
                    .versionId(stat.versionId())
                    .build();

        } catch (Exception e) {
            log.error("获取文件信息失败: objectKey={}", objectKey, e);
            throw new RuntimeException("获取文件信息失败: " + e.getMessage(), e);
        }
    }

    /**
     * 生成预签名URL
     */
    public String generatePresignedUrl(String objectKey, Method method, int duration, TimeUnit timeUnit) throws Exception {
        try {
            return minioClient.getPresignedObjectUrl(
                    GetPresignedObjectUrlArgs.builder()
                            .method(method)
                            .bucket(minioProperties.getBucketName())
                            .object(objectKey)
                            .expiry(duration, timeUnit)
                            .build()
            );
        } catch (Exception e) {
            log.error("生成预签名URL失败: objectKey={}", objectKey, e);
            throw new RuntimeException("生成预签名URL失败: " + e.getMessage(), e);
        }
    }

    /**
     * 列出文件
     */
    public List<MinioFileInfo> listFiles(String prefix) throws Exception {
        try {
            List<MinioFileInfo> files = new ArrayList<>();
            Iterable<Result<Item>> results = minioClient.listObjects(
                    ListObjectsArgs.builder()
                            .bucket(minioProperties.getBucketName())
                            .prefix(prefix)
                            .recursive(true)
                            .build()
            );

            for (Result<Item> result : results) {
                Item item = result.get();
                files.add(MinioFileInfo.builder()
                        .objectKey(item.objectName())
                        .fileName(extractFileName(item.objectName()))
                        .fileSize(item.size())
                        .etag(item.etag())
                        .lastModified(LocalDateTime.ofInstant(item.lastModified().toInstant(), ZoneId.systemDefault()))
                        .isDirectory(item.isDir())
                        .storageClass(item.storageClass())
                        .build());
            }

            return files;
        } catch (Exception e) {
            log.error("列出文件失败: prefix={}", prefix, e);
            throw new RuntimeException("列出文件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 检查文件是否存在
     */
    public boolean fileExists(String objectKey) {
        try {
            minioClient.statObject(
                    StatObjectArgs.builder()
                            .bucket(minioProperties.getBucketName())
                            .object(objectKey)
                            .build()
            );
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 创建存储桶（如果不存在）
     */
    public void createBucketIfNotExists(String bucketName) throws Exception {
        boolean exists = minioClient.bucketExists(
                BucketExistsArgs.builder().bucket(bucketName).build()
        );
        
        if (!exists) {
            minioClient.makeBucket(
                    MakeBucketArgs.builder().bucket(bucketName).build()
            );
            log.info("创建存储桶成功: {}", bucketName);
        }
    }

    /**
     * 列出所有存储桶
     */
    public List<String> listBuckets() throws Exception {
        try {
            List<Bucket> buckets = minioClient.listBuckets();
            return buckets.stream().map(Bucket::name).toList();
        } catch (Exception e) {
            log.error("列出存储桶失败", e);
            throw new RuntimeException("列出存储桶失败: " + e.getMessage(), e);
        }
    }

    /**
     * 创建文件元数据
     */
    private Map<String, String> createMetadata(MultipartFile file) {
        Map<String, String> metadata = new HashMap<>();
        metadata.put("original-name", file.getOriginalFilename());
        metadata.put("upload-time", LocalDateTime.now().toString());
        
        try {
            String md5 = DigestUtils.md5DigestAsHex(file.getBytes());
            metadata.put("md5", md5);
        } catch (IOException e) {
            log.warn("计算文件MD5失败", e);
        }
        
        return metadata;
    }

    /**
     * 从对象键中提取文件名
     */
    private String extractFileName(String objectKey) {
        if (objectKey == null) {
            return null;
        }
        int lastSlash = objectKey.lastIndexOf('/');
        return lastSlash >= 0 ? objectKey.substring(lastSlash + 1) : objectKey;
    }
}
