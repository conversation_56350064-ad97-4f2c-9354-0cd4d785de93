package com.sinoair.agent.security;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.sinoair.agent.entity.User;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Spring Security用户主体
 * 
 * <AUTHOR> Team
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserPrincipal implements UserDetails {

    private Long id;
    private String username;
    private String realName;
    private String email;
    private Long roleId;
    private String roleCode;
    private List<String> permissions;

    @JsonIgnore
    private String password;

    public static UserPrincipal create(User user, Long primaryRoleId, String roleCode, List<String> permissions) {
        return new UserPrincipal(
                user.getId(),
                user.getUsername(),
                user.getRealName(),
                user.getEmail(),
                primaryRoleId, // 使用传入的主角色ID，而不是user.getRoleId()
                roleCode,
                permissions,
                user.getPassword()
        );
    }

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return permissions.stream()
                .map(SimpleGrantedAuthority::new)
                .collect(Collectors.toList());
    }

    @Override
    public String getPassword() {
        return password;
    }

    @Override
    public String getUsername() {
        return username;
    }

    @Override
    public boolean isAccountNonExpired() {
        return true;
    }

    @Override
    public boolean isAccountNonLocked() {
        return true;
    }

    @Override
    public boolean isCredentialsNonExpired() {
        return true;
    }

    @Override
    public boolean isEnabled() {
        return true;
    }
}
