package com.sinoair.agent.security;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sinoair.agent.common.Result;
import com.sinoair.agent.common.ResultCode;
import com.sinoair.agent.entity.ApiKey;
import com.sinoair.agent.service.ApiKeyService;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

/**
 * API Key认证过滤器
 * 
 * <AUTHOR> Team
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ApiKeyAuthenticationFilter extends OncePerRequestFilter {

    private final ApiKeyService apiKeyService;
    private final ObjectMapper objectMapper;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, 
                                  FilterChain filterChain) throws ServletException, IOException {
        
        String requestPath = request.getRequestURI();
        
        // 只对公开API接口进行API Key认证
        if (!requestPath.startsWith("/api/v1/public/")) {
            filterChain.doFilter(request, response);
            return;
        }

        try {
            String apiKey = getApiKeyFromRequest(request);
            
            if (!StringUtils.hasText(apiKey)) {
                handleAuthenticationError(response, "Missing API Key", "API Key is required for public API access");
                return;
            }

            // 验证API Key格式 (keyId.keySecret)
            String[] parts = apiKey.split("\\.", 2);
            if (parts.length != 2) {
                handleAuthenticationError(response, "Invalid API Key format", "API Key format should be: keyId.keySecret");
                return;
            }

            String keyId = parts[0];
            String keySecret = parts[1];

            // 验证API Key
            ApiKey validApiKey = apiKeyService.validateApiKey(keyId, keySecret);
            if (validApiKey == null) {
                handleAuthenticationError(response, "Invalid API Key", "API Key is invalid or expired");
                return;
            }

            // 检查API Key状态
            if (validApiKey.getStatus() != 1) {
                handleAuthenticationError(response, "API Key disabled", "API Key has been disabled");
                return;
            }

            // 检查过期时间
            if (validApiKey.getExpiresAt() != null && validApiKey.getExpiresAt().isBefore(LocalDateTime.now())) {
                handleAuthenticationError(response, "API Key expired", "API Key has expired");
                return;
            }

            // 检查IP限制
            if (!apiKeyService.checkIpRestriction(validApiKey, getClientIpAddress(request))) {
                handleAuthenticationError(response, "IP not allowed", "Your IP address is not allowed to use this API Key");
                return;
            }

            // 检查请求频率限制
            if (!apiKeyService.checkRateLimit(validApiKey)) {
                handleAuthenticationError(response, "Rate limit exceeded", "API Key rate limit exceeded");
                return;
            }

            // 创建认证对象
            ApiKeyAuthenticationToken authentication = new ApiKeyAuthenticationToken(
                validApiKey, 
                Collections.singletonList(new SimpleGrantedAuthority("ROLE_API_USER"))
            );
            authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
            
            SecurityContextHolder.getContext().setAuthentication(authentication);

            // 记录API Key使用
            apiKeyService.recordUsage(validApiKey, request);

        } catch (Exception ex) {
            log.error("Could not set API Key authentication in security context", ex);
            handleAuthenticationError(response, "Authentication failed", "Internal authentication error");
            return;
        }

        filterChain.doFilter(request, response);
    }

    /**
     * 从请求中获取API Key
     */
    private String getApiKeyFromRequest(HttpServletRequest request) {
        // 优先从Header中获取
        String apiKey = request.getHeader("X-API-Key");
        if (StringUtils.hasText(apiKey)) {
            return apiKey;
        }
        
        // 从Authorization Header中获取 (Bearer格式)
        String authHeader = request.getHeader("Authorization");
        if (StringUtils.hasText(authHeader) && authHeader.startsWith("Bearer ")) {
            return authHeader.substring(7);
        }
        
        // 从查询参数中获取
        return request.getParameter("api_key");
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (StringUtils.hasText(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (StringUtils.hasText(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }

    /**
     * 处理认证错误
     */
    private void handleAuthenticationError(HttpServletResponse response, String error, String message) throws IOException {
        response.setContentType("application/json;charset=UTF-8");
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        
        Result<Object> result = Result.error(ResultCode.UNAUTHORIZED.getCode(), message);
        objectMapper.writeValue(response.getOutputStream(), result);
    }
}
