package com.sinoair.agent.security;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sinoair.agent.common.Result;
import com.sinoair.agent.common.ResultCode;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * JWT认证入口点
 * 
 * <AUTHOR> Team
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class JwtAuthenticationEntryPoint implements AuthenticationEntryPoint {

    private final ObjectMapper objectMapper;

    @Override
    public void commence(HttpServletRequest request, HttpServletResponse response,
                        AuthenticationException authException) throws IOException, ServletException {
        log.error("Responding with unauthorized error. Message - {}", authException.getMessage());

        // 检查请求是否为API请求
        String requestPath = request.getRequestURI();
        boolean isApiRequest = requestPath.startsWith("/api/");
        
        if (isApiRequest) {
            // API请求返回JSON
            response.setContentType("application/json;charset=UTF-8");
            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            Result<Object> result = Result.error(ResultCode.UNAUTHORIZED);
            objectMapper.writeValue(response.getOutputStream(), result);
        } else {
            // 页面请求重定向到登录页
            response.sendRedirect("/login.html");
        }
    }
}
