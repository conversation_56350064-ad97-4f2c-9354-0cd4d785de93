package com.sinoair.agent.security;

import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.PermissionEvaluator;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.stereotype.Component;

import java.io.Serializable;

/**
 * 自定义权限评估器
 * 
 * <AUTHOR> Team
 */
@Slf4j
@Component
public class CustomPermissionEvaluator implements PermissionEvaluator {

    @Override
    public boolean hasPermission(Authentication authentication, Object targetDomainObject, Object permission) {
        if (authentication == null || !authentication.isAuthenticated()) {
            return false;
        }

        // 检查是否是超级管理员角色
        if (hasRole(authentication, "SUPER_ADMIN")) {
            log.debug("超级管理员角色，允许所有操作: {}", authentication.getName());
            return true;
        }

        // 检查具体权限
        String permissionStr = permission.toString();
        boolean hasPermission = hasAuthority(authentication, permissionStr);
        
        log.debug("权限检查: user={}, permission={}, result={}", 
                authentication.getName(), permissionStr, hasPermission);
        
        return hasPermission;
    }

    @Override
    public boolean hasPermission(Authentication authentication, Serializable targetId, String targetType, Object permission) {
        return hasPermission(authentication, null, permission);
    }

    /**
     * 检查用户是否有指定角色
     */
    private boolean hasRole(Authentication authentication, String role) {
        return authentication.getAuthorities().stream()
                .map(GrantedAuthority::getAuthority)
                .anyMatch(authority -> authority.equals("ROLE_" + role));
    }

    /**
     * 检查用户是否有指定权限
     */
    private boolean hasAuthority(Authentication authentication, String permission) {
        return authentication.getAuthorities().stream()
                .map(GrantedAuthority::getAuthority)
                .anyMatch(authority -> authority.equals(permission));
    }
}
