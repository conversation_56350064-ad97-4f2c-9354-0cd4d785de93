package com.sinoair.agent.security;

import com.sinoair.agent.entity.ApiKey;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;

import java.util.Collection;

/**
 * API Key认证Token
 * 
 * <AUTHOR> Team
 */
public class ApiKeyAuthenticationToken extends AbstractAuthenticationToken {

    private final ApiKey apiKey;
    private final String credentials;

    public ApiKeyAuthenticationToken(ApiKey apiKey, Collection<? extends GrantedAuthority> authorities) {
        super(authorities);
        this.apiKey = apiKey;
        this.credentials = null;
        setAuthenticated(true);
    }

    @Override
    public Object getCredentials() {
        return credentials;
    }

    @Override
    public Object getPrincipal() {
        return apiKey;
    }

    public ApiKey getApiKey() {
        return apiKey;
    }

    public Long getUserId() {
        return apiKey != null ? apiKey.getUserId() : null;
    }

    public String getKeyId() {
        return apiKey != null ? apiKey.getKeyId() : null;
    }
}
