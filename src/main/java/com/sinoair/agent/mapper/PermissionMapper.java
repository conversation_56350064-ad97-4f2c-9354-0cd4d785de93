package com.sinoair.agent.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sinoair.agent.entity.Permission;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 权限Mapper接口
 *
 * <AUTHOR> Team
 */
@Mapper
public interface PermissionMapper extends BaseMapper<Permission> {

    /**
     * 根据用户ID查询权限列表
     */
    List<Permission> selectPermissionsByUserId(@Param("userId") Long userId);

    /**
     * 根据角色ID查询权限列表
     */
    List<Permission> selectPermissionsByRoleId(@Param("roleId") Long roleId);

    /**
     * 查询所有权限（树形结构）
     */
    List<Permission> selectAllPermissions();

    /**
     * 根据权限类型查询权限
     */
    List<Permission> selectPermissionsByType(@Param("permissionType") Integer permissionType);

    /**
     * 根据父权限ID查询子权限
     */
    List<Permission> selectPermissionsByParentId(@Param("parentId") Long parentId);

    /**
     * 根据权限编码查询权限
     */
    Permission selectByPermissionCode(@Param("permissionCode") String permissionCode);

    /**
     * 查询用户的菜单权限（树形结构）
     */
    List<Permission> selectMenuPermissionsByUserId(@Param("userId") Long userId);

    /**
     * 查询用户的按钮权限
     */
    List<String> selectButtonPermissionsByUserId(@Param("userId") Long userId);
}
