package com.sinoair.agent.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sinoair.agent.entity.UploadedFile;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 上传文件 Mapper接口
 * 
 * <AUTHOR> Team
 */
@Mapper
public interface UploadedFileMapper extends BaseMapper<UploadedFile> {

    /**
     * 根据用户ID查询文件列表
     */
    List<UploadedFile> findByUserId(@Param("userId") Long userId);

    /**
     * 根据文件类型查询文件列表
     */
    List<UploadedFile> findByFileType(@Param("fileType") String fileType);

    /**
     * 根据业务类型查询文件列表
     */
    List<UploadedFile> findByBusinessType(@Param("businessType") String businessType);

    /**
     * 分页查询文件列表
     */
    IPage<UploadedFile> selectPageWithDetails(Page<UploadedFile> page, @Param("keyword") String keyword);

    /**
     * 检查文件是否存在且未删除
     */
    boolean existsByIdAndNotDeleted(@Param("id") Long id);

    /**
     * 根据文件路径查询文件
     */
    UploadedFile findByFilePath(@Param("filePath") String filePath);

    /**
     * 统计文件数量
     */
    Long countFiles();

    /**
     * 统计文件总大小
     */
    Long sumFileSize();

    /**
     * 根据MD5哈希查询文件
     */
    UploadedFile findByMd5Hash(@Param("md5Hash") String md5Hash);

    /**
     * 统计各文件类型数量
     */
    List<java.util.Map<String, Object>> countByFileType();

    /**
     * 统计各用户上传文件数量
     */
    List<java.util.Map<String, Object>> countByUser();

    /**
     * 查询最近上传的文件
     */
    List<UploadedFile> findRecentFiles(@Param("limit") Integer limit);

    /**
     * 查询大文件列表
     */
    List<UploadedFile> findLargeFiles(@Param("minSize") Long minSize);
}
