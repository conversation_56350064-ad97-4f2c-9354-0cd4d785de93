package com.sinoair.agent.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sinoair.agent.entity.AgentOptimization;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Agent优化配置 Mapper接口
 * 
 * <AUTHOR> Team
 */
@Mapper
public interface AgentOptimizationMapper extends BaseMapper<AgentOptimization> {

    /**
     * 根据Agent ID查询优化配置列表（按创建时间倒序）
     */
    List<AgentOptimization> findByAgentIdOrderByCreatedTimeDesc(@Param("agentId") Long agentId);

    /**
     * 根据Agent ID查询激活的优化配置
     */
    AgentOptimization findActiveByAgentId(@Param("agentId") Long agentId);

    /**
     * 根据优化类型查询配置
     */
    List<AgentOptimization> findByAgentIdAndType(@Param("agentId") Long agentId, @Param("optimizationType") String optimizationType);

    /**
     * 统计Agent优化配置数量
     */
    Integer countByAgentId(@Param("agentId") Long agentId);

    /**
     * 激活指定的优化配置
     */
    int activateOptimization(@Param("agentId") Long agentId, @Param("optimizationId") Long optimizationId);

    /**
     * 停用所有优化配置
     */
    int deactivateAllByAgentId(@Param("agentId") Long agentId);

    /**
     * 查询优化效果统计
     */
    List<java.util.Map<String, Object>> getOptimizationStats(@Param("agentId") Long agentId);



    /**
     * 根据优化名称查询配置
     */
    AgentOptimization findByOptimizationName(@Param("optimizationName") String optimizationName);

    /**
     * 统计优化配置总数
     */
    Long countOptimizations();

    /**
     * 统计激活的优化配置数量
     */
    Long countActiveOptimizations();
}
