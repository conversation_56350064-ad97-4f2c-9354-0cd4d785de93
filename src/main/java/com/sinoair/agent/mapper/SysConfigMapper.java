package com.sinoair.agent.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sinoair.agent.entity.SysConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 系统配置Mapper接口
 *
 * <AUTHOR> Team
 */
@Mapper
public interface SysConfigMapper extends BaseMapper<SysConfig> {

    /**
     * 根据配置组查询配置列表
     *
     * @param configGroup 配置组名
     * @return 配置列表
     */
    List<SysConfig> selectByGroup(@Param("configGroup") String configGroup);

    /**
     * 根据配置组和配置键查询配置
     *
     * @param configGroup 配置组名
     * @param configKey   配置键
     * @return 配置信息
     */
    SysConfig selectByGroupAndKey(@Param("configGroup") String configGroup, @Param("configKey") String configKey);

    /**
     * 查询所有有效的配置
     *
     * @return 配置列表
     */
    List<SysConfig> selectAllEnabled();

    /**
     * 根据配置组查询有效的配置列表
     *
     * @param configGroup 配置组名
     * @return 配置列表
     */
    List<SysConfig> selectEnabledByGroup(@Param("configGroup") String configGroup);
}
