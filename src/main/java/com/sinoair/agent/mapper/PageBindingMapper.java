package com.sinoair.agent.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sinoair.agent.entity.PageBinding;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 页面绑定Mapper接口
 * 
 * <AUTHOR> Team
 */
@Mapper
public interface PageBindingMapper extends BaseMapper<PageBinding> {

    /**
     * 根据URL模式查找绑定配置
     */
    PageBinding findByUrlPattern(@Param("urlPattern") String urlPattern);

    /**
     * 根据URL查找匹配的绑定配置
     */
    List<PageBinding> findByUrl(@Param("url") String url);

    /**
     * 分页查询绑定配置列表（带模板信息）
     */
    IPage<PageBinding> selectPageWithTemplate(Page<PageBinding> page, @Param("keyword") String keyword);

    /**
     * 查询所有绑定配置列表（带模板信息）
     */
    List<PageBinding> selectAllWithTemplate();

    /**
     * 根据模板ID查询绑定配置
     */
    List<PageBinding> findByTemplateId(@Param("templateId") Long templateId);

    /**
     * 根据创建者查询绑定配置
     */
    List<PageBinding> findByCreatedBy(@Param("createdBy") Long createdBy);

    /**
     * 统计绑定配置数量
     */
    Long countBindings();

    /**
     * 统计活跃绑定配置数量
     */
    Long countActiveBindings();

    /**
     * 根据绑定名称查找绑定配置
     */
    PageBinding findByBindingName(@Param("bindingName") String bindingName);

    /**
     * 查询所有活跃的绑定配置
     */
    List<PageBinding> findAllActive();

    /**
     * 统计各模板的绑定配置数量
     */
    List<java.util.Map<String, Object>> countByTemplate();

    /**
     * 查找多步骤绑定配置（主步骤）
     */
    List<PageBinding> findMultiStepBindings(@Param("url") String url);

    /**
     * 根据父绑定ID查找子步骤
     */
    List<PageBinding> findSubStepsByParentId(@Param("parentBindingId") Long parentBindingId);

    /**
     * 查找多步骤绑定的完整配置（包含所有子步骤）
     */
    PageBinding findMultiStepBindingWithSteps(@Param("bindingId") Long bindingId);
}
