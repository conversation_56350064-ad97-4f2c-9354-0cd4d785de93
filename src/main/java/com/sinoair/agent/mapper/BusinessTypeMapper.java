package com.sinoair.agent.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sinoair.agent.entity.BusinessType;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 业务类型Mapper接口
 * 
 * <AUTHOR> Team
 */
@Mapper
public interface BusinessTypeMapper extends BaseMapper<BusinessType> {

    /**
     * 根据类型代码查找业务类型
     */
    BusinessType findByTypeCode(@Param("typeCode") String typeCode);

    /**
     * 根据类型名称查找业务类型
     */
    BusinessType findByTypeName(@Param("typeName") String typeName);

    /**
     * 查询所有可用业务类型
     */
    List<BusinessType> findAllActive();

    /**
     * 统计业务类型数量
     */
    Long countBusinessTypes();

    /**
     * 根据分类查询业务类型
     */
    List<BusinessType> findByCategory(@Param("category") String category);

    /**
     * 统计各分类下的业务类型数量
     */
    List<java.util.Map<String, Object>> countByCategory();

    /**
     * 检查业务类型是否有关联的Agent
     */
    Boolean hasAgents(@Param("businessTypeId") Long businessTypeId);

    /**
     * 查询业务类型及其关联的Agent数量
     */
    List<java.util.Map<String, Object>> findWithAgentCount();
}
