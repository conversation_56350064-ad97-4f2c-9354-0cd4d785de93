package com.sinoair.agent.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sinoair.agent.entity.Agent;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Agent Mapper接口
 * 
 * <AUTHOR> Team
 */
@Mapper
public interface AgentMapper extends BaseMapper<Agent> {

    /**
     * 根据Agent代码查找Agent
     */
    Agent findByAgentCode(@Param("agentCode") String agentCode);

    /**
     * 根据Agent名称查找Agent
     */
    Agent findByAgentName(@Param("agentName") String agentName);

    /**
     * 分页查询Agent列表（带关联信息）
     */
    IPage<Agent> selectPageWithDetails(Page<Agent> page,
                                     @Param("keyword") String keyword,
                                     @Param("categoryId") Long categoryId,
                                     @Param("businessTypeId") Long businessTypeId,
                                     @Param("status") Integer status,
                                     @Param("creatorId") Long creatorId);

    /**
     * 根据创建者ID查询Agent列表
     */
    List<Agent> findByCreatorId(@Param("creatorId") Long creatorId);

    /**
     * 根据分类ID查询Agent列表
     */
    List<Agent> findByCategoryId(@Param("categoryId") Long categoryId);

    /**
     * 根据业务类型ID查询Agent列表
     */
    List<Agent> findByBusinessTypeId(@Param("businessTypeId") Long businessTypeId);

    /**
     * 根据状态查询Agent列表
     */
    List<Agent> findByStatus(@Param("status") Integer status);

    /**
     * 查询所有已发布的Agent
     */
    List<Agent> findAllPublished();

    /**
     * 统计Agent数量
     */
    Long countAgents();

    /**
     * 统计已发布Agent数量
     */
    Long countPublishedAgents();

    /**
     * 统计各状态Agent数量
     */
    List<java.util.Map<String, Object>> countByStatus();

    /**
     * 统计各分类Agent数量
     */
    List<java.util.Map<String, Object>> countByCategory();
}
