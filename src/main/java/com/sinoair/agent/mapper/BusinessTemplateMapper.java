package com.sinoair.agent.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sinoair.agent.entity.BusinessTemplate;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 业务模板Mapper接口
 * 
 * <AUTHOR> Team
 */
@Mapper
public interface BusinessTemplateMapper extends BaseMapper<BusinessTemplate> {

    /**
     * 根据模板代码查找模板
     */
    BusinessTemplate findByTemplateCode(@Param("templateCode") String templateCode);

    /**
     * 根据模板名称查找模板
     */
    BusinessTemplate findByTemplateName(@Param("templateName") String templateName);

    /**
     * 分页查询模板列表
     */
    IPage<BusinessTemplate> selectPageWithKeyword(Page<BusinessTemplate> page,
                                                @Param("keyword") String keyword,
                                                @Param("category") String category);

    /**
     * 根据分类查询模板列表
     */
    List<BusinessTemplate> findByCategory(@Param("category") String category);

    /**
     * 查询所有可用模板
     */
    List<BusinessTemplate> findAllActive();

    /**
     * 统计模板数量
     */
    Long countTemplates();

    /**
     * 统计各分类模板数量
     */
    List<java.util.Map<String, Object>> countByCategory();



    /**
     * 根据创建者查询模板
     */
    List<BusinessTemplate> findByCreatedBy(@Param("createdBy") Long createdBy);

    /**
     * 检查模板是否有关联的Agent
     */
    Boolean hasAgents(@Param("templateId") Long templateId);

    /**
     * 检查模板是否有关联的页面绑定
     */
    Boolean hasPageBindings(@Param("templateId") Long templateId);

    /**
     * 查询模板及其关联的Agent数量
     */
    List<java.util.Map<String, Object>> findWithAgentCount();

    /**
     * 查询模板及其关联的页面绑定数量
     */
    List<java.util.Map<String, Object>> findWithBindingCount();
}
