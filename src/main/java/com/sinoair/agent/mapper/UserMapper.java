package com.sinoair.agent.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sinoair.agent.entity.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户Mapper接口
 * 
 * <AUTHOR> Team
 */
@Mapper
public interface UserMapper extends BaseMapper<User> {

    /**
     * 根据用户名查找用户
     */
    User findByUsername(@Param("username") String username);

    /**
     * 根据用户名和状态查找用户
     */
    User findByUsernameAndStatus(@Param("username") String username, @Param("status") Integer status);

    /**
     * 根据邮箱查找用户
     */
    User findByEmail(@Param("email") String email);

    /**
     * 分页查询用户列表（带角色信息）
     */
    IPage<User> selectPageWithRole(Page<User> page, @Param("keyword") String keyword);

    /**
     * 根据角色ID查询用户列表
     */
    List<User> findByRoleId(@Param("roleId") Long roleId);

    /**
     * 统计用户数量
     */
    Long countUsers();

    /**
     * 统计活跃用户数量（最近30天登录）
     */
    Long countActiveUsers();
}
