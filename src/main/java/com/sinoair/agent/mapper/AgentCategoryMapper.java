package com.sinoair.agent.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sinoair.agent.entity.AgentCategory;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * Agent分类Mapper接口
 * 
 * <AUTHOR> Team
 */
@Mapper
public interface AgentCategoryMapper extends BaseMapper<AgentCategory> {

    /**
     * 根据分类代码查找分类
     */
    AgentCategory findByCategoryCode(@Param("categoryCode") String categoryCode);

    /**
     * 根据父级ID查询子分类
     */
    List<AgentCategory> findByParentId(@Param("parentId") Long parentId);

    /**
     * 查询所有根分类
     */
    List<AgentCategory> findRootCategories();

    /**
     * 查询所有可用分类
     */
    List<AgentCategory> findAllActive();

    /**
     * 查询分类树形结构
     */
    List<AgentCategory> findCategoryTree();

    /**
     * 统计分类数量
     */
    Long countCategories();

    /**
     * 统计各分类下的Agent数量
     */
    List<java.util.Map<String, Object>> countAgentsByCategory();

    /**
     * 检查分类是否有子分类
     */
    Boolean hasChildren(@Param("categoryId") Long categoryId);

    /**
     * 检查分类是否有关联的Agent
     */
    Boolean hasAgents(@Param("categoryId") Long categoryId);


}
