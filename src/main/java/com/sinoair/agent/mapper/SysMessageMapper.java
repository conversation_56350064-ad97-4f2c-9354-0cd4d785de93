package com.sinoair.agent.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sinoair.agent.entity.SysMessage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 站内消息Mapper接口
 *
 * <AUTHOR> Team
 */
@Mapper
public interface SysMessageMapper extends BaseMapper<SysMessage> {

    /**
     * 根据用户ID获取未读消息数量
     *
     * @param userId 用户ID
     * @return 未读消息数量
     */
    int selectUnreadCountByUserId(@Param("userId") Long userId);

    /**
     * 根据用户ID获取消息列表
     *
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 消息列表
     */
    List<SysMessage> selectByUserId(@Param("userId") Long userId, @Param("limit") Integer limit);

    /**
     * 批量标记消息为已读
     *
     * @param messageIds 消息ID列表
     * @param userId 用户ID
     * @return 更新数量
     */
    int batchMarkAsRead(@Param("messageIds") List<Long> messageIds, @Param("userId") Long userId);
}
