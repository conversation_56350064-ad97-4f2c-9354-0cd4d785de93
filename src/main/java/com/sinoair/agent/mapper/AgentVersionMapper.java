package com.sinoair.agent.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sinoair.agent.entity.AgentVersion;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Agent版本Mapper接口
 * 
 * <AUTHOR> Team
 */
@Mapper
public interface AgentVersionMapper extends BaseMapper<AgentVersion> {

    /**
     * 根据AgentID查询版本历史
     */
    List<AgentVersion> findByAgentId(@Param("agentId") Long agentId);

    /**
     * 根据AgentID查询当前版本
     */
    AgentVersion findCurrentByAgentId(@Param("agentId") Long agentId);

    /**
     * 根据版本号查询版本
     */
    AgentVersion findByAgentIdAndVersion(@Param("agentId") Long agentId, @Param("versionNumber") String versionNumber);

    /**
     * 根据AgentID和版本号查询版本（别名方法）
     */
    AgentVersion findByAgentIdAndVersionNumber(@Param("agentId") Long agentId, @Param("versionNumber") String versionNumber);

    /**
     * 统计Agent版本数量
     */
    Integer countByAgentId(@Param("agentId") Long agentId);

    /**
     * 查询最新版本号
     */
    String getLatestVersionNumber(@Param("agentId") Long agentId);

    /**
     * 设置当前版本
     */
    int setCurrentVersion(@Param("agentId") Long agentId, @Param("versionId") Long versionId);

    /**
     * 清除所有当前版本标记
     */
    int clearCurrentVersions(@Param("agentId") Long agentId);

    /**
     * 分页查询版本历史
     */
    IPage<AgentVersion> selectPageByAgentId(Page<AgentVersion> page, @Param("agentId") Long agentId);

    /**
     * 查询已发布的版本
     */
    List<AgentVersion> findPublishedByAgentId(@Param("agentId") Long agentId);

    /**
     * 根据AgentID查询版本历史（按创建时间倒序）
     */
    List<AgentVersion> findByAgentIdOrderByCreatedTimeDesc(@Param("agentId") Long agentId);

    /**
     * 清除指定Agent的所有版本的当前标记
     */
    void clearCurrentVersionFlag(@Param("agentId") Long agentId);

    /**
     * 根据AgentID查询最新已发布版本
     */
    AgentVersion findLatestPublishedByAgentId(@Param("agentId") Long agentId);

    /**
     * 根据AgentID查询最新版本（不限状态）
     */
    AgentVersion findLatestByAgentId(@Param("agentId") Long agentId);


}
