package com.sinoair.agent.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sinoair.agent.entity.UserAgentSubscription;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 用户Agent订阅Mapper接口
 */
@Mapper
public interface UserAgentSubscriptionMapper extends BaseMapper<UserAgentSubscription> {

    /**
     * 查询用户是否已订阅某个Agent
     */
    @Select("SELECT COUNT(*) FROM user_agent_subscriptions " +
            "WHERE user_id = #{userId} AND agent_id = #{agentId} " +
            "AND subscription_status = 1 AND deleted = 0")
    int countUserAgentSubscription(@Param("userId") Long userId, @Param("agentId") Long agentId);

    /**
     * 获取用户订阅的Agent ID列表
     */
    @Select("SELECT agent_id FROM user_agent_subscriptions " +
            "WHERE user_id = #{userId} AND subscription_status = 1 AND deleted = 0")
    List<Long> getUserSubscribedAgentIds(@Param("userId") Long userId);

    /**
     * 获取Agent的订阅用户数量
     */
    @Select("SELECT COUNT(*) FROM user_agent_subscriptions " +
            "WHERE agent_id = #{agentId} AND subscription_status = 1 AND deleted = 0")
    int getAgentSubscriptionCount(@Param("agentId") Long agentId);

    /**
     * 获取用户的订阅记录
     */
    @Select("SELECT * FROM user_agent_subscriptions " +
            "WHERE user_id = #{userId} AND agent_id = #{agentId} AND deleted = 0 " +
            "ORDER BY created_time DESC LIMIT 1")
    UserAgentSubscription getUserAgentSubscription(@Param("userId") Long userId, @Param("agentId") Long agentId);
}
