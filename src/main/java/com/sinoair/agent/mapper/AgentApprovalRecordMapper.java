package com.sinoair.agent.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sinoair.agent.dto.request.AgentApprovalQueryDTO;
import com.sinoair.agent.dto.response.AgentApprovalVO;
import com.sinoair.agent.entity.AgentApprovalRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Agent审批记录Mapper接口
 *
 * <AUTHOR> Team
 */
@Mapper
public interface AgentApprovalRecordMapper extends BaseMapper<AgentApprovalRecord> {

    /**
     * 分页查询Agent审批列表
     *
     * @param page 分页参数
     * @param queryDTO 查询条件
     * @return 审批列表
     */
    IPage<AgentApprovalVO> selectApprovalList(Page<AgentApprovalVO> page, @Param("query") AgentApprovalQueryDTO queryDTO);

    /**
     * 根据Agent ID获取最新的审批记录
     *
     * @param agentId Agent ID
     * @return 最新审批记录
     */
    AgentApprovalRecord selectLatestByAgentId(@Param("agentId") Long agentId);

    /**
     * 根据Agent ID获取所有审批历史记录
     *
     * @param agentId   Agent ID
     * @param versionId
     * @return 审批历史记录列表
     */
    List<AgentApprovalRecord> selectHistoryByAgentId(@Param("agentId") Long agentId,
                                                     @Param("versionId") Long versionId);

    /**
     * 统计各状态的审批数量
     *
     * @return 状态统计
     */
    List<java.util.Map<String, Object>> selectApprovalStatusStats();
}
