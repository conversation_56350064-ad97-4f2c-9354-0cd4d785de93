package com.sinoair.agent.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sinoair.agent.entity.ApiKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * API Key Mapper接口
 *
 * <AUTHOR> Team
 */
@Mapper
public interface ApiKeyMapper extends BaseMapper<ApiKey> {

    /**
     * 分页查询API Key列表(包含用户信息)
     */
    IPage<ApiKey> selectApiKeyPageWithUser(Page<ApiKey> page, @Param("userId") Long userId, 
                                          @Param("keyName") String keyName, 
                                          @Param("status") Integer status);

    /**
     * 根据keyId查询API Key(包含用户信息)
     */
    ApiKey selectByKeyIdWithUser(@Param("keyId") String keyId);

    /**
     * 根据keyId和keySecret查询API Key
     */
    ApiKey selectByKeyIdAndSecret(@Param("keyId") String keyId, @Param("keySecret") String keySecret);

    /**
     * 更新最后使用时间和IP
     */
    int updateLastUsed(@Param("id") Long id, @Param("lastUsedAt") LocalDateTime lastUsedAt, 
                      @Param("lastUsedIp") String lastUsedIp);

    /**
     * 获取用户的API Key数量
     */
    int countByUserId(@Param("userId") Long userId);

    /**
     * 获取即将过期的API Key列表
     */
    List<ApiKey> selectExpiringKeys(@Param("days") int days);

    /**
     * 批量更新API Key状态
     */
    int batchUpdateStatus(@Param("ids") List<Long> ids, @Param("status") Integer status);
}
