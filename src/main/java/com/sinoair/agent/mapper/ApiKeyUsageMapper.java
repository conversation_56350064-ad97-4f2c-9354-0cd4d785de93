package com.sinoair.agent.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sinoair.agent.entity.ApiKeyUsage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * API Key使用统计 Mapper接口
 *
 * <AUTHOR> Team
 */
@Mapper
public interface ApiKeyUsageMapper extends BaseMapper<ApiKeyUsage> {

    /**
     * 分页查询API Key使用记录
     */
    IPage<ApiKeyUsage> selectUsagePageWithApiKey(Page<ApiKeyUsage> page, 
                                                @Param("apiKeyId") Long apiKeyId,
                                                @Param("startTime") LocalDateTime startTime,
                                                @Param("endTime") LocalDateTime endTime,
                                                @Param("endpoint") String endpoint,
                                                @Param("statusCode") Integer statusCode);

    /**
     * 统计API Key在指定时间段内的使用次数
     */
    Long countUsageByTimeRange(@Param("apiKeyId") Long apiKeyId,
                              @Param("startTime") LocalDateTime startTime,
                              @Param("endTime") LocalDateTime endTime);

    /**
     * 获取API Key的每日使用统计
     */
    List<Map<String, Object>> getDailyUsageStats(@Param("apiKeyId") Long apiKeyId,
                                                 @Param("startDate") String startDate,
                                                 @Param("endDate") String endDate);

    /**
     * 获取API Key的端点使用统计
     */
    List<Map<String, Object>> getEndpointUsageStats(@Param("apiKeyId") Long apiKeyId,
                                                    @Param("startTime") LocalDateTime startTime,
                                                    @Param("endTime") LocalDateTime endTime);

    /**
     * 获取API Key的错误统计
     */
    List<Map<String, Object>> getErrorStats(@Param("apiKeyId") Long apiKeyId,
                                           @Param("startTime") LocalDateTime startTime,
                                           @Param("endTime") LocalDateTime endTime);

    /**
     * 清理过期的使用记录
     */
    int deleteExpiredRecords(@Param("beforeTime") LocalDateTime beforeTime);

    /**
     * 批量插入使用记录
     */
    int batchInsert(@Param("usageList") List<ApiKeyUsage> usageList);
}
