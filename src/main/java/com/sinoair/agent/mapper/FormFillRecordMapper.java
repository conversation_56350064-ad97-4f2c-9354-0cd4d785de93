package com.sinoair.agent.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sinoair.agent.entity.FormFillRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 表单回填记录 Mapper接口
 * 
 * <AUTHOR> Team
 */
@Mapper
public interface FormFillRecordMapper extends BaseMapper<FormFillRecord> {

    /**
     * 根据用户ID查询回填记录
     */
    List<FormFillRecord> findByUserId(@Param("userId") Long userId);

    /**
     * 根据识别记录ID查询回填记录
     */
    List<FormFillRecord> findByRecognitionRecordId(@Param("recognitionRecordId") Long recognitionRecordId);

    /**
     * 根据页面绑定ID查询回填记录
     */
    List<FormFillRecord> findByPageBindingId(@Param("pageBindingId") Long pageBindingId);

    /**
     * 根据回填结果查询记录
     */
    List<FormFillRecord> findByFillResult(@Param("fillResult") Integer fillResult);

    /**
     * 根据用户反馈查询记录
     */
    List<FormFillRecord> findByUserFeedback(@Param("userFeedback") Integer userFeedback);

    /**
     * 分页查询回填记录（带详细信息）
     */
    IPage<FormFillRecord> selectPageWithDetails(Page<FormFillRecord> page,
                                               @Param("keyword") String keyword,
                                               @Param("fillResult") Integer fillResult,
                                               @Param("userFeedback") Integer userFeedback,
                                               @Param("userId") Long userId,
                                               @Param("startTime") LocalDateTime startTime,
                                               @Param("endTime") LocalDateTime endTime);

    /**
     * 根据ID查询回填记录（带详细信息）
     */
    FormFillRecord selectByIdWithDetails(@Param("id") Long id);

    /**
     * 统计回填记录总数
     */
    Long countRecords();

    /**
     * 统计成功回填记录数
     */
    Long countSuccessRecords();

    /**
     * 统计各回填结果的记录数量
     */
    List<Map<String, Object>> countByFillResult();

    /**
     * 统计各用户反馈的记录数量
     */
    List<Map<String, Object>> countByUserFeedback();

    /**
     * 统计各用户的回填记录数量
     */
    List<Map<String, Object>> countByUser();

    /**
     * 统计各页面绑定的回填记录数量
     */
    List<Map<String, Object>> countByPageBinding();

    /**
     * 查询最近的回填记录
     */
    List<FormFillRecord> findRecentRecords(@Param("limit") Integer limit);

    /**
     * 根据时间范围查询回填记录
     */
    List<FormFillRecord> findByTimeRange(@Param("startTime") LocalDateTime startTime,
                                        @Param("endTime") LocalDateTime endTime);

    /**
     * 查询用户的回填成功率
     */
    Map<String, Object> getUserFillSuccessRate(@Param("userId") Long userId);

    /**
     * 查询页面绑定的回填成功率
     */
    Map<String, Object> getPageBindingFillSuccessRate(@Param("pageBindingId") Long pageBindingId);

    /**
     * 更新用户反馈
     */
    int updateUserFeedback(@Param("id") Long id,
                          @Param("userFeedback") Integer userFeedback,
                          @Param("feedbackTime") LocalDateTime feedbackTime,
                          @Param("feedbackComment") String feedbackComment);

    /**
     * 根据Agent ID统计好评差评数量
     * 通过Agent ID -> RecognitionRecord -> FormFillRecord 的关联查询
     */
    Map<String, Object> countFeedbackByAgentId(@Param("agentId") Long agentId);

    /**
     * 批量查询回填记录
     */
    List<FormFillRecord> findByIds(@Param("ids") List<Long> ids);

    /**
     * 根据目标URL查询回填记录
     */
    List<FormFillRecord> findByTargetUrl(@Param("targetUrl") String targetUrl);

    /**
     * 查询热门回填页面（按回填次数排序）
     */
    List<Map<String, Object>> findPopularFillPages(@Param("limit") Integer limit);

    /**
     * 查询用户回填活跃度统计
     */
    List<Map<String, Object>> getUserFillActivity(@Param("days") Integer days);
}
