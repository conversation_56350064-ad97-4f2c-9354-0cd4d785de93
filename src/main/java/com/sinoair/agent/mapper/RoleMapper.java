package com.sinoair.agent.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sinoair.agent.entity.Role;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 角色Mapper接口
 *
 * <AUTHOR> Team
 */
@Mapper
public interface RoleMapper extends BaseMapper<Role> {

    /**
     * 根据用户ID查询角色列表
     */
    List<Role> selectRolesByUserId(@Param("userId") Long userId);

    /**
     * 根据角色代码查找角色
     */
    Role findByRoleCode(@Param("roleCode") String roleCode);

    /**
     * 根据角色名称查找角色
     */
    Role findByRoleName(@Param("roleName") String roleName);

    /**
     * 分页查询角色列表
     */
    IPage<Role> selectPageWithKeyword(Page<Role> page, @Param("keyword") String keyword);

    /**
     * 查询所有可用角色
     */
    List<Role> findAllActive();

    /**
     * 查询角色及其权限信息
     */
    List<Role> selectRolesWithPermissions();

    /**
     * 根据角色ID查询角色及其权限信息
     */
    Role selectRoleWithPermissions(@Param("roleId") Long roleId);

    /**
     * 查询角色关联的用户数量
     */
    Integer countUsersByRoleId(@Param("roleId") Long roleId);

    /**
     * 统计角色数量
     */
    Long countRoles();

    /**
     * 统计活跃角色数量
     */
    Long countActiveRoles();
}
