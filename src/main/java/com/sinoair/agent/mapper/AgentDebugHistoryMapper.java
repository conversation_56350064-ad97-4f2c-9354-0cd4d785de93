package com.sinoair.agent.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sinoair.agent.entity.AgentDebugHistory;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Agent调试历史Mapper接口
 */
@Mapper
public interface AgentDebugHistoryMapper extends BaseMapper<AgentDebugHistory> {

    /**
     * 根据Agent ID查询调试历史
     */
    List<AgentDebugHistory> findByAgentId(@Param("agentId") Long agentId);

    /**
     * 根据用户ID查询调试历史
     */
    List<AgentDebugHistory> findByUserId(@Param("userId") Long userId);

    /**
     * 根据成功状态查询调试历史
     */
    List<AgentDebugHistory> findByAgentIdAndSuccess(@Param("agentId") Long agentId, @Param("success") Boolean success);

    /**
     * 分页查询调试历史
     */
    IPage<AgentDebugHistory> selectPageWithDetails(Page<AgentDebugHistory> page, 
                                                  @Param("agentId") Long agentId,
                                                  @Param("userId") Long userId,
                                                  @Param("success") Boolean success);

    /**
     * 统计调试历史数量
     */
    Integer countByAgentId(@Param("agentId") Long agentId);

    /**
     * 统计成功调试数量
     */
    Integer countSuccessByAgentId(@Param("agentId") Long agentId);

    /**
     * 统计失败调试数量
     */
    Integer countFailureByAgentId(@Param("agentId") Long agentId);

    /**
     * 查询平均执行时间
     */
    Double getAverageExecutionTime(@Param("agentId") Long agentId);

    /**
     * 查询最近的调试记录
     */
    List<AgentDebugHistory> findRecentByAgentId(@Param("agentId") Long agentId, @Param("limit") Integer limit);

    /**
     * 清理过期的调试记录
     */
    int cleanupOldRecords(@Param("days") Integer days);
}
