package com.sinoair.agent.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sinoair.agent.entity.AgentSupplementInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * Agent补充资料Mapper接口
 *
 * <AUTHOR> Team
 */
@Mapper
public interface AgentSupplementInfoMapper extends BaseMapper<AgentSupplementInfo> {

    /**
     * 根据Agent ID获取补充资料详情
     *
     * @param agentId Agent ID
     * @return 补充资料详情
     */
    AgentSupplementInfo selectByAgentId(@Param("agentId") Long agentId);
}
