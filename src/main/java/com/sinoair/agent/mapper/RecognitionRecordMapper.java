package com.sinoair.agent.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sinoair.agent.entity.RecognitionRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 识别记录 Mapper接口
 * 
 * <AUTHOR> Team
 */
@Mapper
public interface RecognitionRecordMapper extends BaseMapper<RecognitionRecord> {

    /**
     * 根据用户ID查询识别记录
     */
    List<RecognitionRecord> findByUserId(@Param("userId") Long userId);

    /**
     * 根据Agent ID查询识别记录
     */
    List<RecognitionRecord> findByAgentId(@Param("agentId") Long agentId);

    /**
     * 根据状态查询识别记录
     */
    List<RecognitionRecord> findByStatus(@Param("status") Integer status);

    /**
     * 分页查询识别记录（带详细信息）
     */
    IPage<RecognitionRecord> selectPageWithDetails(Page<RecognitionRecord> page,
                                                  @Param("keyword") String keyword,
                                                  @Param("status") Integer status,
                                                  @Param("userId") Long userId,
                                                  @Param("agentId") Long agentId);

    /**
     * 根据文件ID查询识别记录
     */
    List<RecognitionRecord> findByFileId(@Param("fileId") Long fileId);

    /**
     * 根据会话ID查询识别记录
     */
    List<RecognitionRecord> findBySessionId(@Param("sessionId") String sessionId);

    /**
     * 统计识别记录数量
     */
    Long countRecords();

    /**
     * 统计成功识别记录数量
     */
    Long countSuccessRecords();

    /**
     * 统计各状态识别记录数量
     */
    List<java.util.Map<String, Object>> countByStatus();

    /**
     * 统计各Agent的识别记录数量
     */
    List<java.util.Map<String, Object>> countByAgent();

    /**
     * 统计各用户的识别记录数量
     */
    List<java.util.Map<String, Object>> countByUser();

    /**
     * 查询平均处理时间
     */
    Double getAverageProcessingTime();

    /**
     * 查询平均置信度
     */
    Double getAverageConfidenceScore();

    /**
     * 查询最近的识别记录
     */
    List<RecognitionRecord> findRecentRecords(@Param("limit") Integer limit);


}
