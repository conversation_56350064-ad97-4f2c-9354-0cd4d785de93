package com.sinoair.agent.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sinoair.agent.entity.CustomerProfile;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 客户档案 Mapper接口
 *
 * <AUTHOR> Team
 */
@Mapper
public interface CustomerProfileMapper extends BaseMapper<CustomerProfile> {

    /**
     * 分页查询客户档案列表(包含用户信息)
     */
    IPage<CustomerProfile> selectCustomerPageWithUser(Page<CustomerProfile> page,
                                                     @Param("companyName") String companyName,
                                                     @Param("industry") String industry,
                                                     @Param("accountType") Integer accountType,
                                                     @Param("accountStatus") Integer accountStatus);

    /**
     * 根据用户ID查询客户档案(包含用户信息)
     */
    CustomerProfile selectByUserIdWithUser(@Param("userId") Long userId);

    /**
     * 获取即将到期的试用客户
     */
    List<CustomerProfile> selectExpiringTrialCustomers(@Param("days") int days);

    /**
     * 统计各账户类型的客户数量
     */
    List<java.util.Map<String, Object>> countByAccountType();

    /**
     * 统计各账户状态的客户数量
     */
    List<java.util.Map<String, Object>> countByAccountStatus();

    /**
     * 批量更新账户状态
     */
    int batchUpdateAccountStatus(@Param("userIds") List<Long> userIds, @Param("accountStatus") Integer accountStatus);
}
