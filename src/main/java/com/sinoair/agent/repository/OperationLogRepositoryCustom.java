package com.sinoair.agent.repository;

import com.sinoair.agent.entity.OperationLog;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 操作日志自定义Repository接口
 * 用于处理复杂查询和null参数安全处理
 *
 * <AUTHOR> Team
 */
public interface OperationLogRepositoryCustom {

    /**
     * 复合条件查询日志 - 安全处理null参数
     */
    Page<OperationLog> findByConditions(Long userId, String username, String module, 
                                       String operationType, String status, 
                                       LocalDateTime startTime, LocalDateTime endTime, 
                                       Pageable pageable);

    /**
     * 获取最近的操作日志（指定数量）
     */
    List<OperationLog> getRecentLogs(int limit);

    /**
     * 获取指定用户最近的操作日志
     */
    List<OperationLog> getRecentLogsByUserId(Long userId, int limit);

    /**
     * 获取慢操作日志
     */
    Page<OperationLog> getSlowOperations(Long thresholdMs, Pageable pageable);

    /**
     * 获取操作统计信息
     */
    Map<String, Object> getOperationStatistics();

    /**
     * 获取指定时间范围内的操作统计
     */
    Map<String, Object> getOperationStatistics(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 获取用户操作统计
     */
    Map<String, Object> getUserOperationStatistics(Long userId);

    /**
     * 获取模块操作统计
     */
    Map<String, Object> getModuleOperationStatistics();

    /**
     * 获取操作类型统计
     */
    Map<String, Object> getOperationTypeStatistics();

    /**
     * 获取每日操作统计
     */
    List<Map<String, Object>> getDailyOperationStatistics(int days);

    /**
     * 清理过期日志
     */
    long cleanExpiredLogs(int retentionDays);

    /**
     * 批量删除日志
     */
    long deleteLogs(List<String> logIds);
}
