package com.sinoair.agent.repository;

import com.sinoair.agent.entity.OperationLog;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 操作日志自定义Repository实现
 * 安全处理MongoDB查询中的null参数问题
 *
 * <AUTHOR> Team
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class OperationLogRepositoryImpl implements OperationLogRepositoryCustom {

    private final MongoTemplate mongoTemplate;

    @Override
    public Page<OperationLog> findByConditions(Long userId, String username, String module,
                                             String operationType, String status,
                                             LocalDateTime startTime, LocalDateTime endTime,
                                             Pageable pageable) {
        try {
            // 构建查询条件
            Criteria criteria = new Criteria();
            List<Criteria> andCriteria = new ArrayList<>();

            // 用户ID条件
            if (userId != null) {
                andCriteria.add(Criteria.where("userId").is(userId));
            }

            // 用户名条件（模糊查询）
            if (StringUtils.hasText(username)) {
                andCriteria.add(Criteria.where("username").regex(username, "i"));
            }

            // 模块条件
            if (StringUtils.hasText(module)) {
                andCriteria.add(Criteria.where("module").is(module));
            }

            // 操作类型条件
            if (StringUtils.hasText(operationType)) {
                andCriteria.add(Criteria.where("operationType").is(operationType));
            }

            // 状态条件
            if (StringUtils.hasText(status)) {
                andCriteria.add(Criteria.where("status").is(status));
            }

            // 时间范围条件
            if (startTime != null || endTime != null) {
                Criteria timeCriteria = Criteria.where("operationTime");
                if (startTime != null) {
                    timeCriteria = timeCriteria.gte(startTime);
                }
                if (endTime != null) {
                    timeCriteria = timeCriteria.lte(endTime);
                }
                andCriteria.add(timeCriteria);
            }

            // 组合所有条件
            if (!andCriteria.isEmpty()) {
                criteria = criteria.andOperator(andCriteria.toArray(new Criteria[0]));
            }

            // 构建查询
            Query query = new Query(criteria);
            
            // 添加排序（按操作时间倒序）
            query.with(Sort.by(Sort.Direction.DESC, "operationTime"));
            
            // 获取总数
            long total = mongoTemplate.count(query, OperationLog.class);
            
            // 添加分页
            query.with(pageable);
            
            // 执行查询
            List<OperationLog> logs = mongoTemplate.find(query, OperationLog.class);
            
            return new PageImpl<>(logs, pageable, total);
            
        } catch (Exception e) {
            log.error("查询操作日志失败", e);
            // 返回空结果而不是抛出异常
            return new PageImpl<>(Collections.emptyList(), pageable, 0);
        }
    }

    @Override
    public List<OperationLog> getRecentLogs(int limit) {
        try {
            Query query = new Query();
            query.with(Sort.by(Sort.Direction.DESC, "operationTime"));
            query.limit(limit);
            
            return mongoTemplate.find(query, OperationLog.class);
        } catch (Exception e) {
            log.error("获取最近日志失败", e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<OperationLog> getRecentLogsByUserId(Long userId, int limit) {
        try {
            if (userId == null) {
                return Collections.emptyList();
            }
            
            Query query = new Query(Criteria.where("userId").is(userId));
            query.with(Sort.by(Sort.Direction.DESC, "operationTime"));
            query.limit(limit);
            
            return mongoTemplate.find(query, OperationLog.class);
        } catch (Exception e) {
            log.error("获取用户最近日志失败, userId: {}", userId, e);
            return Collections.emptyList();
        }
    }

    @Override
    public Page<OperationLog> getSlowOperations(Long thresholdMs, Pageable pageable) {
        try {
            if (thresholdMs == null || thresholdMs <= 0) {
                thresholdMs = 1000L; // 默认1秒
            }
            
            Criteria criteria = Criteria.where("executionTime").gte(thresholdMs);
            Query query = new Query(criteria);
            query.with(Sort.by(Sort.Direction.DESC, "executionTime"));
            
            long total = mongoTemplate.count(query, OperationLog.class);
            query.with(pageable);
            
            List<OperationLog> logs = mongoTemplate.find(query, OperationLog.class);
            
            return new PageImpl<>(logs, pageable, total);
        } catch (Exception e) {
            log.error("获取慢操作日志失败", e);
            return new PageImpl<>(Collections.emptyList(), pageable, 0);
        }
    }

    @Override
    public Map<String, Object> getOperationStatistics() {
        try {
            Map<String, Object> stats = new HashMap<>();
            
            // 总操作次数
            long totalOperations = mongoTemplate.count(new Query(), OperationLog.class);
            stats.put("totalOperations", totalOperations);
            
            // 成功操作次数
            long successOperations = mongoTemplate.count(
                new Query(Criteria.where("status").is("SUCCESS")), OperationLog.class);
            stats.put("successOperations", successOperations);
            
            // 失败操作次数
            long failedOperations = mongoTemplate.count(
                new Query(Criteria.where("status").in("FAILED", "ERROR")), OperationLog.class);
            stats.put("failedOperations", failedOperations);
            
            // 成功率
            double successRate = totalOperations > 0 ? (double) successOperations / totalOperations * 100 : 0;
            stats.put("successRate", Math.round(successRate * 100.0) / 100.0);
            
            // 今日操作次数
            LocalDateTime todayStart = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);
            long todayOperations = mongoTemplate.count(
                new Query(Criteria.where("operationTime").gte(todayStart)), OperationLog.class);
            stats.put("todayOperations", todayOperations);
            
            return stats;
        } catch (Exception e) {
            log.error("获取操作统计失败", e);
            return new HashMap<>();
        }
    }

    @Override
    public Map<String, Object> getOperationStatistics(LocalDateTime startTime, LocalDateTime endTime) {
        try {
            Map<String, Object> stats = new HashMap<>();
            
            Criteria timeCriteria = new Criteria();
            if (startTime != null && endTime != null) {
                timeCriteria = Criteria.where("operationTime").gte(startTime).lte(endTime);
            } else if (startTime != null) {
                timeCriteria = Criteria.where("operationTime").gte(startTime);
            } else if (endTime != null) {
                timeCriteria = Criteria.where("operationTime").lte(endTime);
            }
            
            Query query = new Query(timeCriteria);
            
            // 总操作次数
            long totalOperations = mongoTemplate.count(query, OperationLog.class);
            stats.put("totalOperations", totalOperations);
            
            // 成功操作次数
            Query successQuery = new Query(timeCriteria.and("status").is("SUCCESS"));
            long successOperations = mongoTemplate.count(successQuery, OperationLog.class);
            stats.put("successOperations", successOperations);
            
            // 失败操作次数
            Query failedQuery = new Query(timeCriteria.and("status").in("FAILED", "ERROR"));
            long failedOperations = mongoTemplate.count(failedQuery, OperationLog.class);
            stats.put("failedOperations", failedOperations);
            
            // 成功率
            double successRate = totalOperations > 0 ? (double) successOperations / totalOperations * 100 : 0;
            stats.put("successRate", Math.round(successRate * 100.0) / 100.0);
            
            return stats;
        } catch (Exception e) {
            log.error("获取时间范围操作统计失败", e);
            return new HashMap<>();
        }
    }

    @Override
    public Map<String, Object> getUserOperationStatistics(Long userId) {
        try {
            if (userId == null) {
                return new HashMap<>();
            }
            
            Map<String, Object> stats = new HashMap<>();
            Criteria userCriteria = Criteria.where("userId").is(userId);
            
            // 用户总操作次数
            long totalOperations = mongoTemplate.count(new Query(userCriteria), OperationLog.class);
            stats.put("totalOperations", totalOperations);
            
            // 用户成功操作次数
            long successOperations = mongoTemplate.count(
                new Query(userCriteria.and("status").is("SUCCESS")), OperationLog.class);
            stats.put("successOperations", successOperations);
            
            // 用户失败操作次数
            long failedOperations = mongoTemplate.count(
                new Query(userCriteria.and("status").in("FAILED", "ERROR")), OperationLog.class);
            stats.put("failedOperations", failedOperations);
            
            // 成功率
            double successRate = totalOperations > 0 ? (double) successOperations / totalOperations * 100 : 0;
            stats.put("successRate", Math.round(successRate * 100.0) / 100.0);
            
            return stats;
        } catch (Exception e) {
            log.error("获取用户操作统计失败, userId: {}", userId, e);
            return new HashMap<>();
        }
    }

    @Override
    public Map<String, Object> getModuleOperationStatistics() {
        try {
            // 使用聚合查询统计各模块的操作次数
            Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.group("module").count().as("count"),
                Aggregation.sort(Sort.Direction.DESC, "count")
            );

            AggregationResults<Map> results = mongoTemplate.aggregate(
                aggregation, "operation_logs", Map.class);

            Map<String, Object> stats = new HashMap<>();
            List<Map<String, Object>> moduleStats = new ArrayList<>();

            for (Map result : results.getMappedResults()) {
                Map<String, Object> moduleStat = new HashMap<>();
                moduleStat.put("module", result.get("_id"));
                moduleStat.put("count", result.get("count"));
                moduleStats.add(moduleStat);
            }

            stats.put("moduleStats", moduleStats);
            return stats;
        } catch (Exception e) {
            log.error("获取模块操作统计失败", e);
            return new HashMap<>();
        }
    }

    @Override
    public Map<String, Object> getOperationTypeStatistics() {
        try {
            // 使用聚合查询统计各操作类型的次数
            Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.group("operationType").count().as("count"),
                Aggregation.sort(Sort.Direction.DESC, "count")
            );

            AggregationResults<Map> results = mongoTemplate.aggregate(
                aggregation, "operation_logs", Map.class);

            Map<String, Object> stats = new HashMap<>();
            List<Map<String, Object>> typeStats = new ArrayList<>();

            for (Map result : results.getMappedResults()) {
                Map<String, Object> typeStat = new HashMap<>();
                typeStat.put("operationType", result.get("_id"));
                typeStat.put("count", result.get("count"));
                typeStats.add(typeStat);
            }

            stats.put("typeStats", typeStats);
            return stats;
        } catch (Exception e) {
            log.error("获取操作类型统计失败", e);
            return new HashMap<>();
        }
    }

    @Override
    public List<Map<String, Object>> getDailyOperationStatistics(int days) {
        try {
            if (days <= 0) {
                days = 7; // 默认7天
            }

            LocalDateTime endDate = LocalDateTime.now().withHour(23).withMinute(59).withSecond(59);
            LocalDateTime startDate = endDate.minusDays(days - 1).withHour(0).withMinute(0).withSecond(0);

            List<Map<String, Object>> dailyStats = new ArrayList<>();

            // 简化实现：按日期循环查询
            for (int i = 0; i < days; i++) {
                LocalDateTime dayStart = startDate.plusDays(i).withHour(0).withMinute(0).withSecond(0);
                LocalDateTime dayEnd = dayStart.withHour(23).withMinute(59).withSecond(59);

                // 查询当天总数
                Query totalQuery = new Query(Criteria.where("operationTime").gte(dayStart).lte(dayEnd));
                long totalCount = mongoTemplate.count(totalQuery, OperationLog.class);

                // 查询当天成功数
                Query successQuery = new Query(Criteria.where("operationTime").gte(dayStart).lte(dayEnd)
                    .and("status").is("SUCCESS"));
                long successCount = mongoTemplate.count(successQuery, OperationLog.class);

                // 查询当天失败数
                Query failedQuery = new Query(Criteria.where("operationTime").gte(dayStart).lte(dayEnd)
                    .and("status").in("FAILED", "ERROR"));
                long failedCount = mongoTemplate.count(failedQuery, OperationLog.class);

                Map<String, Object> dayStat = new HashMap<>();
                dayStat.put("date", dayStart.toLocalDate().toString());
                dayStat.put("totalCount", totalCount);
                dayStat.put("successCount", successCount);
                dayStat.put("failedCount", failedCount);

                dailyStats.add(dayStat);
            }

            return dailyStats;
        } catch (Exception e) {
            log.error("获取每日操作统计失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public long cleanExpiredLogs(int retentionDays) {
        try {
            if (retentionDays <= 0) {
                log.warn("保留天数必须大于0，跳过清理操作");
                return 0;
            }

            LocalDateTime cutoffTime = LocalDateTime.now().minusDays(retentionDays);
            Query query = new Query(Criteria.where("operationTime").lt(cutoffTime));

            long deletedCount = mongoTemplate.remove(query, OperationLog.class).getDeletedCount();
            log.info("清理过期日志完成，删除了 {} 条记录", deletedCount);

            return deletedCount;
        } catch (Exception e) {
            log.error("清理过期日志失败", e);
            return 0;
        }
    }

    @Override
    public long deleteLogs(List<String> logIds) {
        try {
            if (logIds == null || logIds.isEmpty()) {
                return 0;
            }

            Query query = new Query(Criteria.where("id").in(logIds));
            long deletedCount = mongoTemplate.remove(query, OperationLog.class).getDeletedCount();

            log.info("批量删除日志完成，删除了 {} 条记录", deletedCount);
            return deletedCount;
        } catch (Exception e) {
            log.error("批量删除日志失败", e);
            return 0;
        }
    }
}
