package com.sinoair.agent.repository;

import com.sinoair.agent.entity.OperationLog;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 操作日志MongoDB Repository接口
 *
 * <AUTHOR> Team
 */
@Repository
public interface OperationLogRepository extends MongoRepository<OperationLog, String>, OperationLogRepositoryCustom {

    /**
     * 根据用户ID查询操作日志
     */
    Page<OperationLog> findByUserIdOrderByOperationTimeDesc(Long userId, Pageable pageable);

    /**
     * 根据用户名查询操作日志
     */
    Page<OperationLog> findByUsernameOrderByOperationTimeDesc(String username, Pageable pageable);

    /**
     * 根据操作类型查询日志
     */
    Page<OperationLog> findByOperationTypeOrderByOperationTimeDesc(String operationType, Pageable pageable);

    /**
     * 根据模块查询日志
     */
    Page<OperationLog> findByModuleOrderByOperationTimeDesc(String module, Pageable pageable);

    /**
     * 根据状态查询日志
     */
    Page<OperationLog> findByStatusOrderByOperationTimeDesc(String status, Pageable pageable);

    /**
     * 根据时间范围查询日志
     */
    Page<OperationLog> findByOperationTimeBetweenOrderByOperationTimeDesc(
            LocalDateTime startTime, LocalDateTime endTime, Pageable pageable);

    /**
     * 根据用户ID和时间范围查询日志
     */
    Page<OperationLog> findByUserIdAndOperationTimeBetweenOrderByOperationTimeDesc(
            Long userId, LocalDateTime startTime, LocalDateTime endTime, Pageable pageable);

    /**
     * 复合条件查询日志 - 使用自定义实现处理null参数
     */
    Page<OperationLog> findByConditions(Long userId, String username, String module,
                                       String operationType, String status,
                                       LocalDateTime startTime, LocalDateTime endTime,
                                       Pageable pageable);

    /**
     * 统计指定时间范围内的操作次数
     */
    long countByOperationTimeBetween(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 统计指定用户的操作次数
     */
    long countByUserId(Long userId);

    /**
     * 统计指定模块的操作次数
     */
    long countByModule(String module);

    /**
     * 统计指定操作类型的次数
     */
    long countByOperationType(String operationType);

    /**
     * 统计成功操作次数
     */
    long countByStatus(String status);

    /**
     * 查询最近的操作日志
     */
    List<OperationLog> findTop10ByOrderByOperationTimeDesc();

    /**
     * 查询指定用户最近的操作日志
     */
    List<OperationLog> findTop10ByUserIdOrderByOperationTimeDesc(Long userId);

    /**
     * 根据IP地址查询日志
     */
    Page<OperationLog> findByClientIpOrderByOperationTimeDesc(String clientIp, Pageable pageable);

    /**
     * 查询执行时间超过指定阈值的慢操作
     */
    Page<OperationLog> findByExecutionTimeGreaterThanOrderByExecutionTimeDesc(Long threshold, Pageable pageable);

    /**
     * 删除指定时间之前的日志（用于日志清理）
     */
    void deleteByOperationTimeBefore(LocalDateTime cutoffTime);

    /**
     * 根据请求URL模糊查询
     */
    @Query("{ 'requestUrl': { $regex: ?0, $options: 'i' } }")
    Page<OperationLog> findByRequestUrlContaining(String url, Pageable pageable);
}
