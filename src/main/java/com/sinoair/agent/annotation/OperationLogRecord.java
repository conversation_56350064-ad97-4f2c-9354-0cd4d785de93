package com.sinoair.agent.annotation;

import java.lang.annotation.*;

/**
 * 操作日志记录注解
 * 用于标记需要记录操作日志的方法
 *
 * <AUTHOR> Team
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface OperationLogRecord {

    /**
     * 操作模块
     */
    String module() default "";

    /**
     * 操作类型
     */
    String operationType() default "";

    /**
     * 操作描述
     */
    String operationDesc() default "";

    /**
     * 是否记录请求参数
     */
    boolean recordParams() default true;

    /**
     * 是否记录响应结果
     */
    boolean recordResult() default true;

    /**
     * 是否记录执行时间
     */
    boolean recordExecutionTime() default true;

    /**
     * 是否异步记录日志
     */
    boolean async() default true;
}
