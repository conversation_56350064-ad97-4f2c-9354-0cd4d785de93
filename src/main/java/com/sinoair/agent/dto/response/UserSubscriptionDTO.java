package com.sinoair.agent.dto.response;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户订阅展示DTO
 * 用于在订阅管理页面展示用户的Agent订阅信息
 */
@Data
public class UserSubscriptionDTO {

    // 订阅基本信息
    private Long subscriptionId;
    private Long userId;
    private Long agentId;
    private Integer subscriptionStatus;
    private LocalDateTime subscriptionTime;
    private LocalDateTime createdTime;

    // Agent信息
    private String agentName;
    private String agentCode;
    private String agentDescription;
    private Integer agentType;
    private String agentVersion;
    private Integer agentStatus;

    // 分类信息
    private Long categoryId;
    private String categoryName;
    private String categoryCode;

    // 业务类型信息
    private Long businessTypeId;
    private String businessTypeName;
    private String businessTypeCode;

    // 补充信息
    private String agentIntroduction;
    private String usageScenarios;
    private String painPointsSolved;
    private String screenshotUrls;

    // 统计信息
    private Integer totalSubscriptions;
    private Long usageCount;
    private LocalDateTime lastUsedTime;

    /**
     * 获取订阅状态文本
     */
    public String getSubscriptionStatusText() {
        if (subscriptionStatus == null) {
            return "未知";
        }
        switch (subscriptionStatus) {
            case 1:
                return "已订阅";
            case 0:
                return "已取消";
            default:
                return "未知";
        }
    }

    /**
     * 获取Agent类型文本
     */
    public String getAgentTypeText() {
        if (agentType == null) {
            return "未知类型";
        }
        switch (agentType) {
            case 1:
                return "内部LLM";
            case 2:
                return "内部VLM";
            case 3:
                return "阿里百炼";
            default:
                return "未知类型";
        }
    }

    /**
     * 获取Agent状态文本
     */
    public String getAgentStatusText() {
        if (agentStatus == null) {
            return "未知";
        }
        switch (agentStatus) {
            case 1:
                return "草稿";
            case 2:
                return "测试中";
            case 3:
                return "已发布";
            case 4:
                return "已下线";
            default:
                return "未知";
        }
    }

    /**
     * 判断Agent是否可用
     */
    public boolean isAgentAvailable() {
        return agentStatus != null && agentStatus == 3; // 3-已发布
    }

    /**
     * 判断订阅是否有效
     */
    public boolean isSubscriptionActive() {
        return subscriptionStatus != null && subscriptionStatus == 1; // 1-已订阅
    }

    /**
     * 获取显示名称
     */
    public String getDisplayName() {
        return agentName != null ? agentName : "未命名Agent";
    }

    /**
     * 获取分类显示名称
     */
    public String getCategoryDisplayName() {
        return categoryName != null ? categoryName : "未分类";
    }

    /**
     * 获取业务类型显示名称
     */
    public String getBusinessTypeDisplayName() {
        return businessTypeName != null ? businessTypeName : "未知业务类型";
    }

    /**
     * 获取订阅天数
     */
    public long getSubscriptionDays() {
        if (subscriptionTime == null) {
            return 0;
        }
        return java.time.Duration.between(subscriptionTime, LocalDateTime.now()).toDays();
    }

    /**
     * 获取Agent详情页面URL
     */
    public String getAgentDetailUrl() {
        return "/agent-detail/" + agentId;
    }

    /**
     * 获取简短描述（限制长度）
     */
    public String getShortDescription() {
        if (agentDescription == null || agentDescription.trim().isEmpty()) {
            return "暂无描述";
        }
        if (agentDescription.length() > 100) {
            return agentDescription.substring(0, 100) + "...";
        }
        return agentDescription;
    }

    /**
     * 获取使用场景简述
     */
    public String getShortUsageScenarios() {
        if (usageScenarios == null || usageScenarios.trim().isEmpty()) {
            return "暂无使用场景说明";
        }
        if (usageScenarios.length() > 80) {
            return usageScenarios.substring(0, 80) + "...";
        }
        return usageScenarios;
    }

    /**
     * 获取订阅状态CSS类
     */
    public String getSubscriptionStatusClass() {
        if (subscriptionStatus == null) {
            return "text-muted";
        }
        switch (subscriptionStatus) {
            case 1:
                return "text-success";
            case 0:
                return "text-danger";
            default:
                return "text-muted";
        }
    }

    /**
     * 获取Agent状态CSS类
     */
    public String getAgentStatusClass() {
        if (agentStatus == null) {
            return "text-muted";
        }
        switch (agentStatus) {
            case 1:
                return "text-secondary";
            case 2:
                return "text-warning";
            case 3:
                return "text-success";
            case 4:
                return "text-danger";
            default:
                return "text-muted";
        }
    }
}
