package com.sinoair.agent.dto.response;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * Agent广场展示DTO
 * 包含Agent基本信息、版本信息、分类信息、业务类型信息和补充信息
 */
@Data
public class AgentSquareDTO {

    // Agent基本信息
    private Long id;
    private String agentName;
    private String agentCode;
    private String description;
    private Long categoryId;
    private Long businessTypeId;
    private Integer agentType;
    private String version;
    private Integer status;
    private LocalDateTime publishedTime;
    private LocalDateTime createdTime;

    // 当前版本信息
    private String currentVersionNumber;
    private Integer currentVersionStatus;
    private Integer isCurrent;
    private LocalDateTime versionPublishedTime;

    // 分类信息
    private String categoryName;
    private String categoryCode;

    // 业务类型信息
    private String businessTypeName;
    private String businessTypeCode;

    // 补充信息
    private String agentIntroduction;
    private String usageScenarios;
    private String painPointsSolved;
    private String screenshotUrls;
    private String versionInfo;
    private String usageStatistics;
    private String callHistorySummary;

    // 运行时字段
    private Boolean isSubscribed;
    private Integer subscriptionCount;

    // Demo模式下的模拟字段
    private Long demoUsageCount;

    /**
     * 获取显示名称
     */
    public String getDisplayName() {
        return this.agentName != null ? this.agentName : "未命名Agent";
    }

    /**
     * 获取Agent类型文本描述
     */
    public String getTypeText() {
        if (this.agentType == null) {
            return "未知类型";
        }
        switch (this.agentType) {
            case 1:
                return "内部LLM";
            case 2:
                return "内部VLM";
            case 3:
                return "阿里百炼";
            default:
                return "未知类型";
        }
    }

    /**
     * 获取分类文本描述
     */
    public String getCategoryText() {
        return this.categoryName != null ? this.categoryName : "未分类";
    }

    /**
     * 获取业务类型文本描述
     */
    public String getBusinessTypeText() {
        return this.businessTypeName != null ? this.businessTypeName : "未知业务类型";
    }

    /**
     * 获取使用次数（优先使用demo字段，否则返回0）
     */
    public Long getUsageCount() {
        return demoUsageCount != null ? demoUsageCount : 0L;
    }

    /**
     * 获取平均评分（暂时返回0.0，因为数据库中没有此字段）
     */
    public Double getAverageRating() {
        return 0.0;
    }

    /**
     * 获取评价数量（暂时返回0，因为数据库中没有此字段）
     */
    public Integer getReviewCount() {
        return 0;
    }

    /**
     * 获取是否推荐（暂时返回0，因为数据库中没有此字段）
     */
    public Integer getIsRecommended() {
        return 0;
    }

    /**
     * 判断Agent是否可用（已发布状态）
     */
    public boolean isAvailable() {
        return this.status != null && this.status == 3; // 3-已发布
    }

    /**
     * 获取API端点
     */
    public String getApiEndpoint() {
        return "/api/v1/agent/" + (this.agentCode != null ? this.agentCode.toLowerCase() : "unknown");
    }

    /**
     * 获取详细描述
     */
    public String getDetailedDescription() {
        return this.description != null ? this.description : "暂无详细描述";
    }

    /**
     * 好评数量
     */
    private Integer goodRating;

    /**
     * 差评数量
     */
    private Integer badRating;



    /**
     * 获取好评数量
     */
    public Integer getGoodRating() {
        return goodRating != null ? goodRating : 0;
    }

    /**
     * 设置好评数量
     */
    public void setGoodRating(Integer goodRating) {
        this.goodRating = goodRating;
    }

    /**
     * 获取差评数量
     */
    public Integer getBadRating() {
        return badRating != null ? badRating : 0;
    }

    /**
     * 设置差评数量
     */
    public void setBadRating(Integer badRating) {
        this.badRating = badRating;
    }

    /**
     * 获取订阅状态
     */
    public Boolean getIsSubscribed() {
        return isSubscribed != null ? isSubscribed : false;
    }

    /**
     * 设置订阅状态
     */
    public void setIsSubscribed(Boolean isSubscribed) {
        this.isSubscribed = isSubscribed;
    }
}
