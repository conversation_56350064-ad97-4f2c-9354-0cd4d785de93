package com.sinoair.agent.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * Agent调用响应DTO
 * 
 * <AUTHOR> Team
 */
@Data
@Builder
@Schema(description = "Agent调用响应")
public class AgentCallResponse {

    @Schema(description = "任务ID")
    private String taskId;

    @Schema(description = "Agent编码")
    private String agentCode;

    @Schema(description = "Agent名称")
    private String agentName;

    @Schema(description = "处理状态: PROCESSING-处理中, SUCCESS-成功, FAILED-失败")
    private String status;

    @Schema(description = "识别结果(JSON格式)")
    private Object result;

    @Schema(description = "置信度")
    private BigDecimal confidence;

    @Schema(description = "处理耗时(毫秒)")
    private Integer processingTime;

    @Schema(description = "错误信息")
    private String errorMessage;

    @Schema(description = "创建时间")
    private LocalDateTime createdTime;

    @Schema(description = "完成时间")
    private LocalDateTime completedTime;

    @Schema(description = "扩展信息")
    private Map<String, Object> extraInfo;

    /**
     * 批量处理结果
     */
    @Data
    @Builder
    @Schema(description = "批量处理结果")
    public static class BatchResult {
        
        @Schema(description = "批次ID")
        private String batchId;

        @Schema(description = "总文件数")
        private Integer totalFiles;

        @Schema(description = "成功处理数")
        private Integer successCount;

        @Schema(description = "失败处理数")
        private Integer failureCount;

        @Schema(description = "处理中数量")
        private Integer processingCount;

        @Schema(description = "详细结果列表")
        private java.util.List<AgentCallResponse> details;
    }

    /**
     * Agent信息
     */
    @Data
    @Builder
    @Schema(description = "Agent信息")
    public static class AgentInfo {
        
        @Schema(description = "Agent编码")
        private String agentCode;

        @Schema(description = "Agent名称")
        private String agentName;

        @Schema(description = "Agent描述")
        private String description;

        @Schema(description = "业务类型")
        private String businessType;

        @Schema(description = "支持的文件类型")
        private String[] supportedFileTypes;

        @Schema(description = "状态")
        private String status;

        @Schema(description = "版本")
        private String version;

        @Schema(description = "调用示例")
        private String example;
    }

    // 静态工厂方法
    public static AgentCallResponse processing(String taskId, String agentCode) {
        return AgentCallResponse.builder()
                .taskId(taskId)
                .agentCode(agentCode)
                .status("PROCESSING")
                .createdTime(LocalDateTime.now())
                .build();
    }

    public static AgentCallResponse success(String taskId, String agentCode, Object result, 
                                          BigDecimal confidence, Integer processingTime) {
        return AgentCallResponse.builder()
                .taskId(taskId)
                .agentCode(agentCode)
                .status("SUCCESS")
                .result(result)
                .confidence(confidence)
                .processingTime(processingTime)
                .createdTime(LocalDateTime.now())
                .completedTime(LocalDateTime.now())
                .build();
    }

    public static AgentCallResponse failure(String taskId, String agentCode, String errorMessage) {
        return AgentCallResponse.builder()
                .taskId(taskId)
                .agentCode(agentCode)
                .status("FAILED")
                .errorMessage(errorMessage)
                .createdTime(LocalDateTime.now())
                .completedTime(LocalDateTime.now())
                .build();
    }
}
