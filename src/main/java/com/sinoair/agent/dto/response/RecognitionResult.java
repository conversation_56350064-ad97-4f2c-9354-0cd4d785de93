package com.sinoair.agent.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 识别结果DTO
 * 
 * <AUTHOR> Team
 */
@Data
@Builder
@Schema(description = "识别结果")
public class RecognitionResult {

    @Schema(description = "是否成功")
    private boolean success;

    @Schema(description = "识别结果")
    private String result;

    @Schema(description = "置信度")
    private BigDecimal confidence;

    @Schema(description = "处理时间(毫秒)")
    private Integer processingTime;

    @Schema(description = "错误信息")
    private String errorMessage;

    @Schema(description = "使用量信息")
    private Usage usage;

    @Data
    @Builder
    @Schema(description = "使用量信息")
    public static class Usage {
        @Schema(description = "提示Token数")
        private Integer promptTokens;

        @Schema(description = "完成Token数")
        private Integer completionTokens;

        @Schema(description = "总Token数")
        private Integer totalTokens;
    }

    /**
     * 创建成功结果
     */
    public static RecognitionResult success(String result, BigDecimal confidence, Integer processingTime) {
        return RecognitionResult.builder()
                .success(true)
                .result(result)
                .confidence(confidence)
                .processingTime(processingTime)
                .build();
    }

    /**
     * 创建失败结果
     */
    public static RecognitionResult failure(String errorMessage) {
        return RecognitionResult.builder()
                .success(false)
                .errorMessage(errorMessage)
                .build();
    }
}
