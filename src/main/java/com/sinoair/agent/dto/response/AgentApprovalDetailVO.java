package com.sinoair.agent.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * Agent审批详情响应VO
 *
 * <AUTHOR> Team
 */
@Data
@Schema(description = "Agent审批详情")
public class AgentApprovalDetailVO {

    @Schema(description = "基本信息")
    private BasicInfo basicInfo;

    @Schema(description = "配置信息")
    private ConfigInfo configInfo;

    @Schema(description = "使用统计")
    private UsageStats usageStats;

    @Schema(description = "使用场景")
    private UsageScenarios usageScenarios;

    @Schema(description = "API接口信息")
    private ApiInterface apiInterface;

    @Schema(description = "Chrome插件信息")
    private ChromePlugin chromePlugin;

    @Schema(description = "调用历史")
    private List<CallHistory> callHistory;

    @Data
    @Schema(description = "基本信息")
    public static class BasicInfo {
        @Schema(description = "Agent ID")
        private Long id;

        @Schema(description = "Agent名称")
        private String agentName;

        @Schema(description = "Agent编码")
        private String agentCode;

        @Schema(description = "分类名称")
        private String categoryName;

        @Schema(description = "描述")
        private String description;

        @Schema(description = "创建者名称")
        private String creatorName;

        @Schema(description = "创建时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime createTime;

        @Schema(description = "当前版本号")
        private String version;

        @Schema(description = "升级内容")
        private String upgradeContent;

        @Schema(description = "申请发布的版本ID")
        private Long pendingVersionId;

        @Schema(description = "申请发布的版本号")
        private String pendingVersionNumber;

        @Schema(description = "申请发布版本的变更日志")
        private String pendingVersionChangeLog;

        @Schema(description = "申请发布版本的创建时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime pendingVersionCreateTime;
    }

    @Data
    @Schema(description = "配置信息")
    public static class ConfigInfo {
        @Schema(description = "模型名称")
        private String modelName;

        @Schema(description = "模型配置")
        private String modelConfig;

        @Schema(description = "提示词模板")
        private String promptTemplate;

        @Schema(description = "JSON模板")
        private String jsonTemplate;
    }

    @Data
    @Schema(description = "使用统计")
    public static class UsageStats {
        @Schema(description = "总调用次数")
        private Long totalCalls;

        @Schema(description = "成功率")
        private Double successRate;

        @Schema(description = "平均响应时间")
        private Double avgResponseTime;

        @Schema(description = "订阅用户数量")
        private Integer subscriptionCount;

        @Schema(description = "最近一周调用趋势")
        private List<Integer> weeklyTrend;
    }

    @Data
    @Schema(description = "使用场景")
    public static class UsageScenarios {
        @Schema(description = "使用场景描述")
        private String description;

        @Schema(description = "适用业务类型")
        private List<String> businessTypes;

        @Schema(description = "目标用户群体")
        private List<String> targetUsers;

        @Schema(description = "Agent简介（富文本）")
        private String agentIntroduction;

        @Schema(description = "使用场景描述（富文本）")
        private String usageScenariosRichText;

        @Schema(description = "解决的痛点问题（富文本）")
        private String painPointsSolvedRichText;

        @Schema(description = "Chrome插件效果截图")
        private List<String> screenshots;
    }

    @Data
    @Schema(description = "API接口信息")
    public static class ApiInterface {
        @Schema(description = "接口URL")
        private String url;

        @Schema(description = "请求参数说明")
        private String requestParams;

        @Schema(description = "响应格式说明")
        private String responseFormat;

        @Schema(description = "请求示例")
        private String requestExample;

        @Schema(description = "响应示例")
        private String responseExample;
    }

    @Data
    @Schema(description = "Chrome插件信息")
    public static class ChromePlugin {
        @Schema(description = "支持的网站列表")
        private List<String> supportedSites;

        @Schema(description = "插件功能说明")
        private String features;

        @Schema(description = "效果截图")
        private List<String> screenshots;
    }

    @Data
    @Schema(description = "调用历史")
    public static class CallHistory {
        @Schema(description = "调用ID")
        private Long id;

        @Schema(description = "调用时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime callTime;

        @Schema(description = "调用参数")
        private String params;

        @Schema(description = "响应结果")
        private String response;

        @Schema(description = "响应时间(ms)")
        private Integer responseTime;

        @Schema(description = "调用状态")
        private String status;
    }
}
