package com.sinoair.agent.dto.response;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

/**
 * API密钥创建响应DTO
 * 用于在创建API密钥时一次性返回完整信息（包含明文keySecret）
 *
 * <AUTHOR> Team
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ApiKeyCreateResponse {

    /**
     * 密钥ID
     */
    private Long id;

    /**
     * API Key ID
     */
    private String keyId;

    /**
     * API Key密钥(明文，仅创建时返回)
     */
    private String keySecret;

    /**
     * API Key名称
     */
    private String keyName;

    /**
     * 描述
     */
    private String description;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 状态(0-禁用 1-启用)
     */
    private Integer status;

    /**
     * 每小时请求限制
     */
    private Integer rateLimit;

    /**
     * 每日请求限制
     */
    private Integer dailyLimit;

    /**
     * 每月请求限制
     */
    private Integer monthlyLimit;

    /**
     * 过期时间
     */
    private LocalDateTime expiresAt;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 从ApiKey实体转换为创建响应DTO
     */
    public static ApiKeyCreateResponse fromApiKey(com.sinoair.agent.entity.ApiKey apiKey, String plainKeySecret) {
        return ApiKeyCreateResponse.builder()
                .id(apiKey.getId())
                .keyId(apiKey.getKeyId())
                .keySecret(plainKeySecret) // 明文密钥
                .keyName(apiKey.getKeyName())
                .description(apiKey.getDescription())
                .userId(apiKey.getUserId())
                .status(apiKey.getStatus())
                .rateLimit(apiKey.getRateLimit())
                .dailyLimit(apiKey.getDailyLimit())
                .monthlyLimit(apiKey.getMonthlyLimit())
                .expiresAt(apiKey.getExpiresAt())
                .createdTime(apiKey.getCreatedTime())
                .build();
    }
}
