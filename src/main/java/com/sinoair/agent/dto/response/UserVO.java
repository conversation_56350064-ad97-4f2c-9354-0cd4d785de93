package com.sinoair.agent.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户视图对象
 * 
 * <AUTHOR> Team
 */
@Data
public class UserVO {

    private Long id;
    
    private String username;
    
    private String email;
    
    private String phone;
    
    private String realName;
    
    private Long roleId; // 兼容字段，保留主角色ID

    private String roleName; // 兼容字段，保留主角色名称

    private String roleCode; // 兼容字段，保留主角色编码

    /**
     * 用户的所有角色列表
     */
    private List<RoleInfo> roles;
    
    private String department;
    
    private Integer status;

    /**
     * 用户来源：MANAGEMENT-系统创建，PORTAL-官网注册
     */
    private String forward;

    /**
     * 审核状态：0-待审核，1-已通过，2-已拒绝
     */
    private Integer approvalStatus;

    /**
     * 审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime approvedTime;

    /**
     * 审核人ID
     */
    private Long approvedBy;

    /**
     * 审核人姓名
     */
    private String approvedByName;

    /**
     * 审核备注
     */
    private String approvalRemark;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastLoginTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedTime;

    /**
     * 角色信息内部类
     */
    @Data
    public static class RoleInfo {
        private Long roleId;
        private String roleName;
        private String roleCode;
        private String description;

        public RoleInfo(Long roleId, String roleName, String roleCode, String description) {
            this.roleId = roleId;
            this.roleName = roleName;
            this.roleCode = roleCode;
            this.description = description;
        }
    }
}
