package com.sinoair.agent.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Agent审批信息响应VO
 *
 * <AUTHOR> Team
 */
@Data
@Schema(description = "Agent审批信息")
public class AgentApprovalVO {

    @Schema(description = "Agent ID")
    private Long id;

    @Schema(description = "Agent名称")
    private String agentName;

    @Schema(description = "Agent编码")
    private String agentCode;

    @Schema(description = "描述")
    private String description;

    @Schema(description = "审批状态：1-审批中，2-审批通过，3-审批不通过")
    private Integer approvalStatus;

    @Schema(description = "审批状态名称")
    private String approvalStatusName;

    @Schema(description = "分类名称")
    private String categoryName;

    @Schema(description = "模型信息")
    private String modelInfo;

    @Schema(description = "用途")
    private String purpose;

    @Schema(description = "是否有API接口")
    private Boolean hasApiInterface;

    @Schema(description = "是否有Chrome插件支持")
    private Boolean hasChromePlugin;

    @Schema(description = "订阅数量")
    private Integer subscriptionCount;

    @Schema(description = "提交时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime submitTime;

    @Schema(description = "创建者名称")
    private String creatorName;

    @Schema(description = "版本号")
    private String version;

    @Schema(description = "升级内容")
    private String upgradeContent;

    @Schema(description = "最近审批意见")
    private String lastApprovalOpinion;

    @Schema(description = "最近审批人")
    private String lastApproverName;

    @Schema(description = "最近审批时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastApprovalTime;

    /**
     * 获取审批状态名称
     */
    public String getApprovalStatusName() {
        if (approvalStatus == null) {
            return "未知";
        }
        switch (approvalStatus) {
            case 1:
                return "审批中";
            case 2:
                return "审批通过";
            case 3:
                return "审批不通过";
            default:
                return "未知";
        }
    }
}
