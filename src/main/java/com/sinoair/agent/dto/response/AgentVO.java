package com.sinoair.agent.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * Agent视图对象
 * 
 * <AUTHOR> Team
 */
@Data
@Schema(description = "Agent信息")
public class AgentVO {

    @Schema(description = "Agent ID")
    private Long id;

    @Schema(description = "Agent名称")
    private String agentName;

    @Schema(description = "Agent编码")
    private String agentCode;

    @Schema(description = "Agent描述")
    private String description;

    @Schema(description = "分类ID")
    private Long categoryId;

    @Schema(description = "分类名称")
    private String categoryName;

    @Schema(description = "业务类型ID")
    private Long businessTypeId;

    @Schema(description = "业务类型名称")
    private String businessTypeName;

    @Schema(description = "业务模板ID")
    private Long templateId;

    @Schema(description = "Agent类型")
    private Integer agentType;

    @Schema(description = "Agent类型名称")
    private String agentTypeName;

    @Schema(description = "配置信息")
    private String config;

    @Schema(description = "提示词模板")
    private String promptTemplate;

    @Schema(description = "JSON输出模板")
    private String jsonTemplate;

    @Schema(description = "版本号")
    private String version;

    @Schema(description = "状态")
    private Integer status;

    @Schema(description = "状态名称")
    private String statusName;

    @Schema(description = "创建者ID")
    private Long creatorId;

    @Schema(description = "创建者名称")
    private String creatorName;

    @Schema(description = "创建时间")
    private LocalDateTime createdTime;

    @Schema(description = "更新时间")
    private LocalDateTime updatedTime;

    @Schema(description = "发布时间")
    private LocalDateTime publishedTime;

    @Schema(description = "审批状态")
    private Integer approvalStatus;

    @Schema(description = "审批状态名称")
    private String approvalStatusName;

    @Schema(description = "发布版本号")
    private String publishedVersion;

    @Schema(description = "最新版本号")
    private String latestVersion;

    @Schema(description = "提交审批时间")
    private LocalDateTime submitTime;

    /**
     * 获取Agent类型名称
     */
    public String getAgentTypeName() {
        if (agentType == null) return "";
        return switch (agentType) {
            case 1 -> "内部LLM";
            case 2 -> "外部LLM";
            case 3 -> "外部Agent";
            default -> "未知";
        };
    }

    /**
     * 获取状态名称
     */
    public String getStatusName() {
        if (status == null) return "";
        return switch (status) {
            case 1 -> "草稿";
            case 2 -> "测试中";
            case 3 -> "已发布";
            case 4 -> "已下线";
            case 0 -> "已删除";
            default -> "未知";
        };
    }
}
