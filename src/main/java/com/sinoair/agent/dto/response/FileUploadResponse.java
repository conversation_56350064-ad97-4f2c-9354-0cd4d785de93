package com.sinoair.agent.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 文件上传响应DTO
 * 
 * <AUTHOR> Team
 */
@Data
@Builder
@Schema(description = "文件上传响应")
public class FileUploadResponse {

    @Schema(description = "文件ID")
    private Long fileId;

    @Schema(description = "原始文件名")
    private String originalName;

    @Schema(description = "存储文件名")
    private String storedName;

    @Schema(description = "文件大小(字节)")
    private Long fileSize;

    @Schema(description = "文件类型")
    private String fileType;

    @Schema(description = "上传时间")
    private LocalDateTime uploadTime;

    @Schema(description = "预览URL")
    private String previewUrl;

    @Schema(description = "下载URL")
    private String downloadUrl;
}
