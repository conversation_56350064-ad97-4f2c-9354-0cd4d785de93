package com.sinoair.agent.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * MinIO文件信息
 * 
 * <AUTHOR> Team
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MinioFileInfo {

    /**
     * 对象键（路径）
     */
    private String objectKey;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 文件大小（字节）
     */
    private Long fileSize;

    /**
     * 文件类型
     */
    private String contentType;

    /**
     * 文件ETag
     */
    private String etag;

    /**
     * 最后修改时间
     */
    private LocalDateTime lastModified;

    /**
     * 存储类型
     */
    private String storageClass;

    /**
     * 用户元数据
     */
    private Map<String, String> userMetadata;

    /**
     * 系统元数据
     */
    private Map<String, String> metadata;

    /**
     * 是否为目录
     */
    private Boolean isDirectory;

    /**
     * 版本ID
     */
    private String versionId;

    /**
     * 是否为最新版本
     */
    private Boolean isLatest;

    /**
     * 删除标记
     */
    private Boolean isDeleteMarker;
}
