package com.sinoair.agent.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * Agent补充资料响应VO
 *
 * <AUTHOR> Team
 */
@Data
@Schema(description = "Agent补充资料响应")
public class AgentSupplementVO {

    @Schema(description = "补充资料ID")
    private Long id;

    @Schema(description = "Agent ID")
    private Long agentId;

    @Schema(description = "Agent名称")
    private String agentName;

    @Schema(description = "Agent编码")
    private String agentCode;

    @Schema(description = "Agent描述")
    private String agentDescription;

    @Schema(description = "Agent简介（富文本）")
    private String agentIntroduction;

    @Schema(description = "使用场景描述（富文本）")
    private String usageScenarios;

    @Schema(description = "解决的痛点问题（富文本）")
    private String painPointsSolved;

    @Schema(description = "Chrome插件使用效果截图URL列表")
    private List<String> screenshotUrls;

    @Schema(description = "Agent版本信息")
    private Map<String, Object> versionInfo;

    @Schema(description = "使用情况统计")
    private Map<String, Object> usageStatistics;

    @Schema(description = "调用历史汇总")
    private Map<String, Object> callHistorySummary;

    @Schema(description = "状态：1-草稿，2-已提交")
    private Integer status;

    @Schema(description = "状态名称")
    private String statusName;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdTime;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedTime;
}
