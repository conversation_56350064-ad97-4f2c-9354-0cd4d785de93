package com.sinoair.agent.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * MinIO文件上传响应
 * 
 * <AUTHOR> Team
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MinioUploadResponse {

    /**
     * 文件ID
     */
    private Long fileId;

    /**
     * 原始文件名
     */
    private String originalName;

    /**
     * 存储文件名
     */
    private String storedName;

    /**
     * 文件大小（字节）
     */
    private Long fileSize;

    /**
     * 文件类型
     */
    private String fileType;

    /**
     * MIME类型
     */
    private String mimeType;

    /**
     * 存储桶名称
     */
    private String bucketName;

    /**
     * 对象键（路径）
     */
    private String objectKey;

    /**
     * 文件MD5哈希值
     */
    private String md5Hash;

    /**
     * 文件ETag
     */
    private String etag;

    /**
     * 上传时间
     */
    private LocalDateTime uploadTime;

    /**
     * 预览URL
     */
    private String previewUrl;

    /**
     * 下载URL
     */
    private String downloadUrl;

    /**
     * 临时访问URL（带签名）
     */
    private String presignedUrl;

    /**
     * 临时URL过期时间
     */
    private LocalDateTime presignedUrlExpiry;
}
