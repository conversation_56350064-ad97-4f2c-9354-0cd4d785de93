package com.sinoair.agent.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import jakarta.validation.constraints.NotNull;

/**
 * Agent审批请求DTO
 *
 * <AUTHOR> Team
 */
@Data
@Schema(description = "Agent审批请求")
public class AgentApprovalDTO {

    @Schema(description = "Agent ID", required = true)
    @NotNull(message = "Agent ID不能为空")
    private Long agentId;

    @Schema(description = "Agent版本ID")
    private Long agentVersionId;

    @Schema(description = "审批结果：1-通过，2-不通过", required = true)
    @NotNull(message = "审批结果不能为空")
    private Integer approvalResult;

    @Schema(description = "审批意见")
    private String approvalComment;
}
