package com.sinoair.agent.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 创建Agent请求DTO
 * 
 * <AUTHOR> Team
 */
@Data
@Schema(description = "创建Agent请求")
public class CreateAgentRequest {

    @NotBlank(message = "Agent名称不能为空")
    @Schema(description = "Agent名称", example = "订单识别Agent")
    private String agentName;

    @Schema(description = "Agent编码（可选，系统自动生成）", example = "ORDER_RECOGNITION")
    private String agentCode;

    @Schema(description = "Agent描述", example = "识别订单文档并提取关键信息")
    private String description;

    @NotNull(message = "分类ID不能为空")
    @Schema(description = "分类ID", example = "1")
    private Long categoryId;

    @NotNull(message = "业务类型ID不能为空")
    @Schema(description = "业务类型ID", example = "1")
    private Long businessTypeId;

    @Schema(description = "业务模板ID", example = "1")
    private Long templateId;

    @NotNull(message = "Agent类型不能为空")
    @Schema(description = "Agent类型:1-内部LLM,2-外部LLM,3-外部Agent", example = "1")
    private Integer agentType;

    @NotBlank(message = "Agent配置不能为空")
    @Schema(description = "Agent配置信息(JSON格式)")
    private String config;

    @Schema(description = "提示词模板")
    private String promptTemplate;

    @Schema(description = "JSON输出模板")
    private String jsonTemplate;

    @Schema(description = "版本号", example = "1.0.0")
    private String version;
}
