package com.sinoair.agent.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;


import java.util.List;

/**
 * 用户角色分配请求DTO
 *
 * <AUTHOR> Team
 */
@Data
@Schema(description = "用户角色分配请求")
public class UserRoleAssignDTO {

    @Schema(description = "角色ID列表", required = true)
    @NotNull(message = "角色ID列表不能为空")
    @NotEmpty(message = "角色ID列表不能为空")
    private List<Long> roleIds;
}
