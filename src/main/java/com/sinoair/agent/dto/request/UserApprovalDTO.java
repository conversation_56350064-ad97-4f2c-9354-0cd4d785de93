package com.sinoair.agent.dto.request;

import lombok.Data;

import jakarta.validation.constraints.NotNull;

/**
 * 用户审核DTO
 * 
 * <AUTHOR> Team
 */
@Data
public class UserApprovalDTO {

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /**
     * 审核状态：1-通过，2-拒绝
     */
    @NotNull(message = "审核状态不能为空")
    private Integer approvalStatus;

    /**
     * 审核备注
     */
    private String approvalRemark;
}
