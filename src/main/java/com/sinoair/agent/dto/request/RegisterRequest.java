package com.sinoair.agent.dto.request;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 用户注册请求DTO
 *
 * <AUTHOR> Team
 */
@Data
public class RegisterRequest {

    @NotBlank(message = "用户名不能为空")
    @Size(min = 3, max = 50, message = "用户名长度必须在3-50个字符之间")
    private String username;

    @NotBlank(message = "密码不能为空")
    @Size(min = 6, max = 20, message = "密码长度必须在6-20个字符之间")
    private String password;

    @NotBlank(message = "邮箱不能为空")
    @Email(message = "邮箱格式不正确")
    private String email;

    @Size(max = 50, message = "真实姓名长度不能超过50个字符")
    private String realName;

    @Size(max = 20, message = "联系电话长度不能超过20个字符")
    private String contactPhone;

    @Size(max = 200, message = "公司名称长度不能超过200个字符")
    private String companyName;

    @Size(max = 100, message = "所属行业长度不能超过100个字符")
    private String industry;

    @Size(max = 500, message = "公司地址长度不能超过500个字符")
    private String address;

    @Size(max = 200, message = "公司网站长度不能超过200个字符")
    private String website;
}
