package com.sinoair.agent.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * Agent补充资料请求DTO
 *
 * <AUTHOR> Team
 */
@Data
@Schema(description = "Agent补充资料请求")
public class AgentSupplementDTO {

    @Schema(description = "Agent ID", required = true)
    @NotNull(message = "Agent ID不能为空")
    private Long agentId;

    @Schema(description = "Agent简介（富文本）")
    private String agentIntroduction;

    @Schema(description = "使用场景描述（富文本）")
    private String usageScenarios;

    @Schema(description = "解决的痛点问题（富文本）")
    private String painPointsSolved;

    @Schema(description = "Chrome插件使用效果截图URL列表")
    private List<String> screenshotUrls;

    @Schema(description = "状态：1-草稿，2-已提交")
    private Integer status;

    @Schema(description = "版本ID（申请发布特定版本时使用）")
    private Long versionId;

    @Schema(description = "版本号（申请发布特定版本时使用）")
    private String versionNumber;
}
