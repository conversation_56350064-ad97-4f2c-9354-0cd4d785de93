package com.sinoair.agent.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;


import java.util.List;

/**
 * 角色权限分配请求DTO
 *
 * <AUTHOR> Team
 */
@Data
@Schema(description = "角色权限分配请求")
public class RolePermissionAssignDTO {

    @Schema(description = "权限ID列表", required = true)
    @NotNull(message = "权限ID列表不能为空")
    @NotEmpty(message = "权限ID列表不能为空")
    private List<Long> permissionIds;
}
