package com.sinoair.agent.dto.request;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 更新用户请求DTO
 * 
 * <AUTHOR> Team
 */
@Data
public class UpdateUserRequest {

    @Email(message = "邮箱格式不正确")
    private String email;

    @Size(max = 20, message = "手机号长度不能超过20个字符")
    private String phone;

    @Size(max = 50, message = "真实姓名长度不能超过50个字符")
    private String realName;

    private Long roleId;

    @Size(max = 100, message = "部门名称长度不能超过100个字符")
    private String department;

    private Integer status;
}
