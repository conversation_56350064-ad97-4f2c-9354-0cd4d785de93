package com.sinoair.agent.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Builder;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

/**
 * Agent调用请求DTO
 * 
 * <AUTHOR> Team
 */
@Data
@Builder
@Schema(description = "Agent调用请求")
public class AgentCallRequest {

    @Schema(description = "Agent编码")
    private String agentCode;

    @Schema(description = "上传的文件")
    private MultipartFile file;

    @Schema(description = "业务参数(JSON字符串)")
    private String businessParams;

    @Schema(description = "回调URL")
    private String callbackUrl;

    @Schema(description = "客户端标识")
    private String clientId;

    @Schema(description = "请求ID")
    private String requestId;

    @Schema(description = "扩展参数")
    private Map<String, Object> extraParams;

    /**
     * 文本识别请求
     */
    @Data
    @Schema(description = "文本识别请求")
    public static class TextRequest {
        
        @NotBlank(message = "文本内容不能为空")
        @Schema(description = "要识别的文本内容", example = "发货人：ABC公司\\n收货人：XYZ公司\\n货物：电子产品")
        private String textContent;

        @Schema(description = "业务参数")
        private Map<String, Object> businessParams;

        @Schema(description = "回调URL")
        private String callbackUrl;

        @Schema(description = "客户端标识")
        private String clientId;
    }

    /**
     * 批量识别请求
     */
    @Data
    @Schema(description = "批量识别请求")
    public static class BatchRequest {
        
        @Schema(description = "文件URL列表")
        private String[] fileUrls;

        @Schema(description = "业务参数")
        private Map<String, Object> businessParams;

        @Schema(description = "回调URL")
        private String callbackUrl;

        @Schema(description = "客户端标识")
        private String clientId;
    }
}
