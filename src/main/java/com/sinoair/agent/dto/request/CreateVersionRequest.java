package com.sinoair.agent.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 创建Agent版本请求DTO
 * 
 * <AUTHOR> Team
 */
@Data
@Schema(description = "创建Agent版本请求")
public class CreateVersionRequest {

    @Schema(description = "版本变更说明", example = "优化了提示词模板，提高了识别准确率")
    @NotBlank(message = "版本变更说明不能为空")
    private String changeLog;

    @Schema(description = "提示词模板", example = "你是一个专业的文档识别助手...")
    @NotBlank(message = "提示词模板不能为空")
    private String promptTemplate;

    @Schema(description = "Agent配置信息", example = "{\"llm_config\":{\"model\":\"qwen-plus\",\"temperature\":0.7}}")
    @NotBlank(message = "配置信息不能为空")
    private String config;

    @Schema(description = "是否设为当前版本", example = "true")
    @NotNull(message = "是否设为当前版本不能为空")
    private Boolean isCurrent;

    @Schema(description = "是否已发布", example = "false")
    @NotNull(message = "是否已发布不能为空")
    private Boolean isPublished;

    @Schema(description = "Agent类型/平台", example = "1")
    private Integer agentType;
}
