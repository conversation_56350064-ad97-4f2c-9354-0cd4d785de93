package com.sinoair.agent.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.Map;

/**
 * 识别请求DTO
 *
 * <AUTHOR> Team
 */
@Data
@Schema(description = "识别请求")
public class RecognitionRequest {

    @Schema(description = "Agent ID", example = "1")
    private Long agentId;

    @Schema(description = "Agent编码", example = "TEST001")
    private String agentCode;

    @Schema(description = "文件ID（文件模式时必填）", example = "123")
    private Long fileId;

    @Schema(description = "输入文本（文本模式时必填）", example = "这是要识别的文本内容")
    private String inputText;

    @Schema(description = "自定义提示词模板")
    private String promptTemplate;

    @Schema(description = "平台", example = "1=内部大模型LLM,2=内部大模型VLM,3=外部大模型")
    private Integer agentType;

    @Schema(description = "模型名称", example = "qwen-plus")
    private String model;

    @Schema(description = "业务类型ID", example = "1")
    private Long businessTypeId;

    @Schema(description = "识别参数")
    private RecognitionParams params;

    @Data
    @Schema(description = "识别参数")
    public static class RecognitionParams {

        @Schema(description = "温度参数", example = "0.7")
        private Double temperature;
        @Schema(description = "核采样的概率阈值，控制模型生成文本的多样性。\n" +
                "\n" +
                "top_p越高，生成的文本更多样。反之，生成的文本更确定。\n" +
                "\n" +
                "取值范围：（0,1.0]", example = "0.9")
        private Double top_p;
        @Schema(description = "生成过程中采样候选集的大小。例如，取值为50时，仅将单次生成中得分最高的50个Token组成随机采样的候选集。取值越大，生成的随机性越高；取值越小，生成的确定性越高。取值为None或当top_k大于100时，表示不启用top_k策略，此时仅有top_p策略生效。\n" +
                "\n" +
                "取值需要大于或等于0", example = "101")
        private Double top_K;

        @Schema(description = "最大Token数", example = "2000")
        private Integer maxTokens;

        @Schema(description = "是否启用缓存", example = "true")
        private Boolean enableCache;

        @Schema(description = "优先级", example = "normal")
        private String priority;

        @Schema(description = "其他参数")
        private Map<String, Object> extra;
    }
}
