package com.sinoair.agent.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * Agent审批查询请求DTO
 *
 * <AUTHOR> Team
 */
@Data
@Schema(description = "Agent审批查询请求")
public class AgentApprovalQueryDTO {

    @Schema(description = "Agent名称")
    private String agentName;

    @Schema(description = "Agent编码")
    private String agentCode;

    @Schema(description = "审批状态：1-审批中，2-审批通过，3-审批不通过")
    private Integer approvalStatus;

    @Schema(description = "分类ID")
    private Long categoryId;

    @Schema(description = "开始时间")
    private String startTime;

    @Schema(description = "结束时间")
    private String endTime;

    @Schema(description = "创建者ID（用于权限过滤）")
    private Long creatorId;

    @Schema(description = "当前页码", example = "1")
    private Integer current = 1;

    @Schema(description = "每页大小", example = "10")
    private Integer size = 10;
}
