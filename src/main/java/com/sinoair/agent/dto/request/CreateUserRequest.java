package com.sinoair.agent.dto.request;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 创建用户请求DTO
 * 
 * <AUTHOR> Team
 */
@Data
public class CreateUserRequest {

    @NotBlank(message = "用户名不能为空")
    @Size(min = 3, max = 50, message = "用户名长度必须在3-50个字符之间")
    private String username;

    @NotBlank(message = "密码不能为空")
    @Size(min = 6, max = 20, message = "密码长度必须在6-20个字符之间")
    private String password;

    @Email(message = "邮箱格式不正确")
    private String email;

    @Size(max = 20, message = "手机号长度不能超过20个字符")
    private String phone;

    @Size(max = 50, message = "真实姓名长度不能超过50个字符")
    private String realName;

    private Long roleId; // 兼容字段，实际使用多角色

    @Size(max = 100, message = "部门名称长度不能超过100个字符")
    private String department;

    private Integer status = 1; // 默认启用

    /**
     * 用户来源：MANAGEMENT-系统创建，PORTAL-官网注册
     */
    private String forward = "MANAGEMENT"; // 默认为系统创建
}
