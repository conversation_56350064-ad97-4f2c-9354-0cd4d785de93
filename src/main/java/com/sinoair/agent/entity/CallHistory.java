package com.sinoair.agent.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 调用历史记录实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("call_history")
public class CallHistory {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;
    
    /**
     * 订阅ID
     */
    @TableField("subscription_id")
    private Long subscriptionId;
    
    /**
     * Agent名称
     */
    @TableField("agent_name")
    private String agentName;
    
    /**
     * 调用类型：API、PLUGIN
     */
    @TableField("call_type")
    private String callType;
    
    /**
     * 调用状态：SUCCESS、FAILED
     */
    @TableField("call_status")
    private String callStatus;
    
    /**
     * 请求参数
     */
    @TableField("request_params")
    private String requestParams;
    
    /**
     * 响应结果
     */
    @TableField("response_data")
    private String responseData;
    
    /**
     * 上传文件信息（JSON格式）
     */
    @TableField("upload_file_info")
    private String uploadFileInfo;

    /**
     * 原始上传内容类型：TEXT、PDF、IMAGE、MIXED
     */
    @TableField("upload_content_type")
    private String uploadContentType;

    /**
     * 原始文本内容
     */
    @TableField("original_text")
    private String originalText;

    /**
     * 上传文件URL列表（JSON数组）
     */
    @TableField("upload_file_urls")
    private String uploadFileUrls;

    /**
     * 解析结果详情（JSON格式）
     */
    @TableField("parse_result")
    private String parseResult;

    /**
     * 回填URL
     */
    @TableField("callback_url")
    private String callbackUrl;

    /**
     * 处理状态：PENDING、PROCESSING、COMPLETED、FAILED
     */
    @TableField("process_status")
    private String processStatus;

    /**
     * 任务类型：EXTRACT、ANALYZE、TRANSLATE、SUMMARIZE等
     */
    @TableField("task_type")
    private String taskType;
    
    /**
     * 响应时间（毫秒）
     */
    @TableField("response_time")
    private Long responseTime;

    /**
     * 置信度分数
     */
    @TableField("confidence_score")
    private java.math.BigDecimal confidenceScore;

    /**
     * 会话ID
     */
    @TableField("session_id")
    private String sessionId;

    /**
     * 错误信息
     */
    @TableField("error_message")
    private String errorMessage;
    
    /**
     * 使用的API密钥ID
     */
    @TableField("api_key_id")
    private Long apiKeyId;
    
    /**
     * 调用IP地址
     */
    @TableField("call_ip")
    private String callIp;
    
    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 逻辑删除标志
     */
    @TableLogic
    @TableField("deleted")
    private Integer deleted;

    /**
     * 为了前端兼容性，提供createdTime的getter方法
     * 返回createTime的值
     */
    public LocalDateTime getCreatedTime() {
        return this.createTime;
    }
}
