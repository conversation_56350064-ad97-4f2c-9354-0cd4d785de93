package com.sinoair.agent.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 用户实体类
 *
 * <AUTHOR> Team
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("sys_user")
public class User {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("username")
    private String username;

    @JsonIgnore
    @TableField("password")
    private String password;

    @TableField("email")
    private String email;

    @TableField("phone")
    private String phone;

    @TableField("real_name")
    private String realName;

    @TableField("role_id")
    private Long roleId;

    @TableField("department")
    private String department;

    @TableField("status")
    private Integer status;

    /**
     * 用户来源：MANAGEMENT-系统创建，PORTAL-官网注册
     */
    @TableField("forward")
    private String forward;

    /**
     * 审核状态：0-待审核，1-已通过，2-已拒绝
     */
    @TableField("approval_status")
    private Integer approvalStatus;

    /**
     * 审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("approved_time")
    private LocalDateTime approvedTime;

    /**
     * 审核人ID
     */
    @TableField("approved_by")
    private Long approvedBy;

    /**
     * 审核备注
     */
    @TableField("approval_remark")
    private String approvalRemark;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("last_login_time")
    private LocalDateTime lastLoginTime;

    /**
     * 创建人
     */
    @TableField(value = "created_by", fill = FieldFill.INSERT)
    private Long createdBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    /**
     * 更新人
     */
    @TableField(value = "updated_by", fill = FieldFill.INSERT_UPDATE)
    private Long updatedBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "updated_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedTime;

    // 非数据库字段
    @TableField(exist = false)
    private String roleName;

    @TableField(exist = false)
    private String roleCode;

    /**
     * 审核人姓名（非数据库字段）
     */
    @TableField(exist = false)
    private String approvedByName;
}
