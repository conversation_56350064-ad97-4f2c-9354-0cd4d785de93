package com.sinoair.agent.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * Agent审批记录实体类
 *
 * <AUTHOR> Team
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("agent_approval_records")
@Schema(description = "Agent审批记录")
public class AgentApprovalRecord extends BaseEntity {

    @Schema(description = "Agent ID")
    @TableField("agent_id")
    private Long agentId;

    @Schema(description = "Agent版本ID")
    @TableField("agent_version_id")
    private Long agentVersionId;

    @Schema(description = "审批状态：1-审批中，2-审批通过，3-审批不通过")
    @TableField("approval_status")
    private Integer approvalStatus;

    @Schema(description = "审批意见")
    @TableField("approval_opinion")
    private String approvalOpinion;

    @Schema(description = "审批人ID")
    @TableField("approver_id")
    private Long approverId;

    @Schema(description = "审批人姓名")
    @TableField("approver_name")
    private String approverName;

    @Schema(description = "审批时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("approval_time")
    private LocalDateTime approvalTime;

    // 非数据库字段
    @Schema(description = "Agent名称")
    @TableField(exist = false)
    private String agentName;

    @Schema(description = "Agent编码")
    @TableField(exist = false)
    private String agentCode;

    @Schema(description = "Agent描述")
    @TableField(exist = false)
    private String agentDescription;

    @Schema(description = "分类名称")
    @TableField(exist = false)
    private String categoryName;

    @Schema(description = "创建者名称")
    @TableField(exist = false)
    private String creatorName;

    @Schema(description = "版本号")
    @TableField(exist = false)
    private String versionNumber;

    @Schema(description = "审批状态名称")
    @TableField(exist = false)
    private String approvalStatusName;

    /**
     * 获取审批状态名称
     */
    public String getApprovalStatusName() {
        if (approvalStatus == null) {
            return "未知";
        }
        switch (approvalStatus) {
            case 1:
                return "审批中";
            case 2:
                return "审批通过";
            case 3:
                return "审批不通过";
            default:
                return "未知";
        }
    }
}
