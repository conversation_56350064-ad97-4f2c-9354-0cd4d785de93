package com.sinoair.agent.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 客户档案实体类
 *
 * <AUTHOR> Team
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("customer_profiles")
public class CustomerProfile extends BaseEntity {

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 公司名称
     */
    @TableField("company_name")
    private String companyName;

    /**
     * 公司类型
     */
    @TableField("company_type")
    private String companyType;

    /**
     * 所属行业
     */
    @TableField("industry")
    private String industry;

    /**
     * 营业执照号
     */
    @TableField("business_license")
    private String businessLicense;

    /**
     * 联系人
     */
    @TableField("contact_person")
    private String contactPerson;

    /**
     * 联系电话
     */
    @TableField("contact_phone")
    private String contactPhone;

    /**
     * 联系邮箱
     */
    @TableField("contact_email")
    private String contactEmail;

    /**
     * 公司地址
     */
    @TableField("address")
    private String address;

    /**
     * 公司网站
     */
    @TableField("website")
    private String website;

    /**
     * 每月API调用配额
     */
    @TableField("api_quota_monthly")
    private Integer apiQuotaMonthly;

    /**
     * 每日API调用配额
     */
    @TableField("api_quota_daily")
    private Integer apiQuotaDaily;

    /**
     * 每小时API调用配额
     */
    @TableField("api_quota_hourly")
    private Integer apiQuotaHourly;

    /**
     * 账户类型:1-免费版,2-基础版,3-专业版,4-企业版
     */
    @TableField("account_type")
    private Integer accountType;

    /**
     * 账户状态:1-正常,2-试用,3-暂停,4-欠费
     */
    @TableField("account_status")
    private Integer accountStatus;

    /**
     * 试用期结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("trial_expires_at")
    private LocalDateTime trialExpiresAt;

    // 非数据库字段
    @TableField(exist = false)
    private String username;

    @TableField(exist = false)
    private String userRealName;

    @TableField(exist = false)
    private String userEmail;
}
