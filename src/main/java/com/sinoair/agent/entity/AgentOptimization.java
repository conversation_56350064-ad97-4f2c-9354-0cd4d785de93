package com.sinoair.agent.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * Agent优化参数实体
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("agent_optimizations")
public class AgentOptimization extends BaseEntity {

    @TableField("agent_id")
    private Long agentId;

    @TableField("optimization_name")
    private String optimizationName;

    @TableField("llm_config")
    private String llmConfig;

    @TableField("prompt_template")
    private String promptTemplate;

    @TableField("test_results")
    private String testResults;

    @TableField("performance_metrics")
    private String performanceMetrics;

    @TableField("is_active")
    private Integer isActive;

    @TableField("created_by")
    private Long createdBy;
}
