package com.sinoair.agent.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonRawValue;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * Agent版本历史实体类
 *
 * <AUTHOR> Team
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("agent_versions")
public class AgentVersion extends BaseEntity {

    @TableField("agent_id")
    private Long agentId;

    @TableField("version_number")
    private String versionNumber;

    @TableField("agent_name")
    private String agentName;

    @TableField("agent_code")
    private String agentCode;

    @TableField("description")
    private String description;

    @TableField("category_id")
    private Long categoryId;

    @TableField("business_type_id")
    private Long businessTypeId;

    @TableField("agent_type")
    private Integer agentType;

    @JsonRawValue
    @TableField("config")
    private String config;

    @TableField("prompt_template")
    private String promptTemplate;

    @JsonRawValue
    @TableField("json_template")
    private String jsonTemplate;

    @TableField("template_id")
    private Long templateId;

    @TableField("status")
    private Integer status;

    @TableField("is_current")
    private Integer isCurrent;

    @TableField("change_log")
    private String changeLog;

    @TableField("creator_id")
    private Long creatorId;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("published_time")
    private LocalDateTime publishedTime;

    @TableField("approval_status")
    private Integer approvalStatus;

    // 非数据库字段
    @TableField(exist = false)
    private String creatorName;

    @TableField(exist = false)
    private String statusName;

    @TableField(exist = false)
    private String approvalStatusName;

    @TableField(exist = false)
    private String approverName;

    @TableField(exist = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime approvalTime;

    @TableField(exist = false)
    private Long callCount;

    @TableField(exist = false)
    private String successRate;

    /**
     * 获取状态名称
     */
    public String getStatusName() {
        if (status == null) return "";
        return switch (status) {
            case 1 -> "草稿";
            case 2 -> "测试中";
            case 3 -> "已发布";
            case 4 -> "已下线";
            default -> "未知";
        };
    }

    /**
     * 获取审批状态名称
     */
    public String getApprovalStatusName() {
        if (approvalStatus == null) return "未提交";
        return switch (approvalStatus) {
            case 0 -> "未提交";
            case 1 -> "审批中";
            case 2 -> "审批通过";
            case 3 -> "审批不通过";
            default -> "未知";
        };
    }

    // 状态常量
    public static final int STATUS_DRAFT = 1;      // 草稿
    public static final int STATUS_TESTING = 2;    // 测试中
    public static final int STATUS_PUBLISHED = 3;  // 已发布
    public static final int STATUS_OFFLINE = 4;    // 已下线

    // 审批状态常量
    public static final int APPROVAL_STATUS_NOT_SUBMITTED = 0;  // 未提交
    public static final int APPROVAL_STATUS_PENDING = 1;       // 审批中
    public static final int APPROVAL_STATUS_APPROVED = 2;      // 审批通过
    public static final int APPROVAL_STATUS_REJECTED = 3;      // 审批不通过
}
