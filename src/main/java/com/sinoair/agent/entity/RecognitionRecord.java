package com.sinoair.agent.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonRawValue;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 识别记录实体类
 * 
 * <AUTHOR> Team
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("recognition_records")
public class RecognitionRecord {

    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField("agent_id")
    private Long agentId;

    @TableField("file_id")
    private Long fileId;

    @TableField("business_type_id")
    private Long businessTypeId;

    @JsonRawValue
    @TableField("input_params")
    private String inputParams;

    @JsonRawValue
    @TableField("recognition_result")
    private String recognitionResult;

    @TableField("confidence_score")
    private BigDecimal confidenceScore;

    @TableField("processing_time")
    private Integer processingTime;

    private Integer status;

    @TableField("error_message")
    private String errorMessage;

    @TableField("user_id")
    private Long userId;

    @TableField("session_id")
    private String sessionId;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("created_time")
    private LocalDateTime createdTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("completed_time")
    private LocalDateTime completedTime;

    @TableLogic
    private Integer deleted;

    // 非数据库字段
    @TableField(exist = false)
    private String agentName;

    @TableField(exist = false)
    private String fileName;

    @TableField(exist = false)
    private String businessTypeName;

    @TableField(exist = false)
    private String userName;

    @TableField(exist = false)
    private String statusName;

    // 状态常量
    public static final int STATUS_PROCESSING = 1; // 处理中
    public static final int STATUS_SUCCESS = 2;    // 成功
    public static final int STATUS_FAILED = 3;     // 失败
}
