package com.sinoair.agent.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 上传文件实体类
 * 
 * <AUTHOR> Team
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("uploaded_files")
public class UploadedFile {

    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField("original_name")
    private String originalName;

    @TableField("stored_name")
    private String storedName;

    @TableField("file_path")
    private String filePath;

    @TableField("file_size")
    private Long fileSize;

    @TableField("file_type")
    private String fileType;

    @TableField("mime_type")
    private String mimeType;

    @TableField("md5_hash")
    private String md5Hash;

    @TableField("upload_ip")
    private String uploadIp;

    @TableField("uploader_id")
    private Long uploaderId;

    // MinIO相关字段
    @TableField("bucket_name")
    private String bucketName;

    @TableField("object_key")
    private String objectKey;

    @TableField("etag")
    private String etag;

    @TableField("storage_type")
    private String storageType; // LOCAL, MINIO

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("upload_time")
    private LocalDateTime uploadTime;

    private Integer status;

    @TableLogic
    private Integer deleted;

    // 非数据库字段
    @TableField(exist = false)
    private String uploaderName;

    @TableField(exist = false)
    private String previewUrl;

    @TableField(exist = false)
    private String downloadUrl;
}
