package com.sinoair.agent.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 页面绑定配置实体
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("page_bindings")
public class PageBinding extends BaseEntity {

    @TableField("binding_name")
    private String bindingName;

    @TableField("template_id")
    private Long templateId;

    @TableField("target_url")
    private String targetUrl;

    @TableField("url_pattern")
    private String urlPattern;

    @TableField("page_html")
    private String pageHtml;

    @TableField("binding_config")
    private String bindingConfig;

    @TableField("status")
    private Integer status;

    @TableField("created_by")
    private Long createdBy;

    @TableField("description")
    private String description;

    // 关联查询字段
    @TableField(exist = false)
    private BusinessTemplate businessTemplate;

    // 模板名称（关联查询字段）
    @TableField(exist = false)
    private String templateName;

    // JSON模板（关联查询字段）
    @TableField(exist = false)
    private String jsonTemplate;

    // 多步骤支持字段
    @TableField("is_multi_step")
    private Integer isMultiStep;

    @TableField("parent_binding_id")
    private Long parentBindingId;

    @TableField("step_order")
    private Integer stepOrder;

    @TableField("step_name")
    private String stepName;

    @TableField("next_action")
    private String nextAction;

    @TableField("wait_time")
    private Integer waitTime;

    @TableField("is_final_step")
    private Integer isFinalStep;

    // 子步骤列表（关联查询字段）
    @TableField(exist = false)
    private List<PageBinding> subSteps;
}
