package com.sinoair.agent.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * API Key实体类
 *
 * <AUTHOR> Team
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("api_keys")
public class ApiKey extends BaseEntity {

    /**
     * API Key标识符
     */
    @TableField("key_id")
    private String keyId;

    /**
     * API Key密钥(加密存储)
     */
    @JsonIgnore
    @TableField("key_secret")
    private String keySecret;

    /**
     * API Key名称
     */
    @TableField("key_name")
    private String keyName;

    /**
     * API Key描述
     */
    @TableField("description")
    private String description;

    /**
     * 所属用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 状态:1-启用,0-禁用
     */
    @TableField("status")
    private Integer status;

    /**
     * 每小时请求限制
     */
    @TableField("rate_limit")
    private Integer rateLimit;

    /**
     * 每日请求限制
     */
    @TableField("daily_limit")
    private Integer dailyLimit;

    /**
     * 每月请求限制
     */
    @TableField("monthly_limit")
    private Integer monthlyLimit;

    /**
     * 允许的IP地址列表(JSON格式)
     */
    @TableField("allowed_ips")
    private String allowedIps;

    /**
     * 允许的域名列表(JSON格式)
     */
    @TableField("allowed_domains")
    private String allowedDomains;

    /**
     * API Key过期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("expires_at")
    private LocalDateTime expiresAt;

    /**
     * 最后使用时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("last_used_at")
    private LocalDateTime lastUsedAt;

    /**
     * 最后使用IP
     */
    @TableField("last_used_ip")
    private String lastUsedIp;

    // 非数据库字段
    @TableField(exist = false)
    private String username;

    @TableField(exist = false)
    private String userRealName;

    @TableField(exist = false)
    private Long todayUsage;

    @TableField(exist = false)
    private Long monthlyUsage;
}
