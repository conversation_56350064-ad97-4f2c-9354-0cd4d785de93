package com.sinoair.agent.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 站内消息实体类
 *
 * <AUTHOR> Team
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_messages")
@Schema(description = "站内消息")
public class SysMessage extends BaseEntity {

    @Schema(description = "接收用户ID")
    @TableField("user_id")
    private Long userId;

    @Schema(description = "消息标题")
    @TableField("title")
    private String title;

    @Schema(description = "消息内容")
    @TableField("content")
    private String content;

    @Schema(description = "消息类型：1-系统通知，2-审批通知，3-其他")
    @TableField("message_type")
    private Integer messageType;

    @Schema(description = "关联业务ID")
    @TableField("related_id")
    private Long relatedId;

    @Schema(description = "关联业务类型")
    @TableField("related_type")
    private String relatedType;

    @Schema(description = "是否已读：0-未读，1-已读")
    @TableField("is_read")
    private Integer isRead;

    // 非数据库字段
    @Schema(description = "接收用户名称")
    @TableField(exist = false)
    private String userName;

    @Schema(description = "消息类型名称")
    @TableField(exist = false)
    private String messageTypeName;

    /**
     * 获取消息类型名称
     */
    public String getMessageTypeName() {
        if (messageType == null) {
            return "未知";
        }
        switch (messageType) {
            case 1:
                return "系统通知";
            case 2:
                return "审批通知";
            case 3:
                return "其他";
            default:
                return "未知";
        }
    }

    /**
     * 是否已读
     */
    public boolean isRead() {
        return isRead != null && isRead == 1;
    }
}
