package com.sinoair.agent.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 系统配置实体类
 *
 * <AUTHOR> Team
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_config")
public class SysConfig extends BaseEntity {

    /**
     * 配置组名
     */
    @TableField("config_group")
    private String configGroup;

    /**
     * 配置键
     */
    @TableField("config_key")
    private String configKey;

    /**
     * 配置值
     */
    @TableField("config_value")
    private String configValue;

    /**
     * 配置描述
     */
    @TableField("description")
    private String description;

    /**
     * 是否有效：1-有效，0-无效
     */
    @TableField("is_enabled")
    private Integer isEnabled;

    // 配置组常量
    public static final String GROUP_SYSTEM = "system";
    public static final String GROUP_LLM = "llm";
    public static final String GROUP_FILE = "file";
    public static final String GROUP_SECURITY = "security";
    public static final String GROUP_NOTIFICATION = "notification";
    public static final String GROUP_CACHE = "cache";

    // 系统配置键常量
    public static final String KEY_APP_NAME = "app_name";
    public static final String KEY_APP_VERSION = "app_version";
    public static final String KEY_MAINTENANCE_MODE = "maintenance_mode";
    public static final String KEY_SERVER_URL = "server_url";

    // LLM配置键常量
    public static final String KEY_DEFAULT_PROVIDER = "default_provider";
    public static final String KEY_TIMEOUT_SECONDS = "timeout_seconds";
    public static final String KEY_MAX_RETRY_COUNT = "max_retry_count";

    // 文件配置键常量
    public static final String KEY_MAX_UPLOAD_SIZE = "max_upload_size";
    public static final String KEY_ALLOWED_EXTENSIONS = "allowed_extensions";
    public static final String KEY_STORAGE_PATH = "storage_path";

    // 安全配置键常量
    public static final String KEY_JWT_EXPIRE_HOURS = "jwt_expire_hours";
    public static final String KEY_PASSWORD_MIN_LENGTH = "password_min_length";
    public static final String KEY_LOGIN_MAX_ATTEMPTS = "login_max_attempts";

    // 通知配置键常量
    public static final String KEY_EMAIL_ENABLED = "email_enabled";
    public static final String KEY_SMS_ENABLED = "sms_enabled";

    // 缓存配置键常量
    public static final String KEY_REDIS_EXPIRE_SECONDS = "redis_expire_seconds";
    public static final String KEY_ENABLE_CACHE = "enable_cache";

    // 状态常量
    public static final int STATUS_ENABLED = 1;   // 有效
    public static final int STATUS_DISABLED = 0;  // 无效

    /**
     * 获取配置值的布尔类型
     */
    public Boolean getBooleanValue() {
        if (configValue == null || configValue.trim().isEmpty()) {
            return null;
        }
        String value = configValue.trim();
        return "true".equalsIgnoreCase(value) || "1".equals(value) || "yes".equalsIgnoreCase(value);
    }

    /**
     * 获取配置值的整数类型
     */
    public Integer getIntegerValue() {
        if (configValue == null || configValue.trim().isEmpty()) {
            return null;
        }
        try {
            return Integer.parseInt(configValue.trim());
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * 获取配置值的长整数类型
     */
    public Long getLongValue() {
        if (configValue == null || configValue.trim().isEmpty()) {
            return null;
        }
        try {
            return Long.parseLong(configValue.trim());
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * 检查配置是否有效
     */
    public boolean isValid() {
        return isEnabled != null && isEnabled.equals(STATUS_ENABLED);
    }
}
