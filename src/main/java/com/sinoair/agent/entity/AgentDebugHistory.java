package com.sinoair.agent.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * Agent调试历史实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("agent_debug_history")
public class AgentDebugHistory extends BaseEntity {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * Agent ID
     */
    @TableField("agent_id")
    private Long agentId;

    /**
     * 版本ID
     */
    @TableField("version_id")
    private Long versionId;

    /**
     * 使用的模型
     */
    @TableField("model_name")
    private String modelName;

    /**
     * 模型配置参数
     */
    @TableField("model_config")
    private String modelConfig;

    /**
     * 输入数据
     */
    @TableField("input_data")
    private String inputData;

    /**
     * 输出结果
     */
    @TableField("output_data")
    private String outputData;

    /**
     * 执行状态:1-成功,0-失败
     */
    @TableField("status")
    private Integer status;

    /**
     * 错误信息
     */
    @TableField("error_message")
    private String errorMessage;

    /**
     * 响应时间(毫秒)
     */
    @TableField("response_time")
    private Integer responseTime;

    /**
     * 使用的Token数量
     */
    @TableField("tokens_used")
    private Integer tokensUsed;

    /**
     * 调用成本
     */
    @TableField("cost")
    private java.math.BigDecimal cost;

    /**
     * 调试者ID
     */
    @TableField("creator_id")
    private Long creatorId;

    /**
     * 创建时间
     */
    @TableField("created_time")
    private LocalDateTime createdTime;

    // 关联字段（非数据库字段）
    
    /**
     * Agent名称
     */
    @TableField(exist = false)
    private String agentName;

    /**
     * 用户名称
     */
    @TableField(exist = false)
    private String userName;


    /**
     * 更新时间
     */
    @TableField(exist = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedTime;

    // 便捷方法
    public Boolean getSuccess() {
        return status != null && status == 1;
    }

    public void setSuccess(Boolean success) {
        this.status = success != null && success ? 1 : 0;
    }
}
