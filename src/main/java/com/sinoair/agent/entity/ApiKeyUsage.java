package com.sinoair.agent.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * API Key使用统计实体类
 *
 * <AUTHOR> Team
 */
@Data
@TableName("api_key_usage")
public class ApiKeyUsage {

    /**
     * 使用记录ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * API Key ID
     */
    @TableField("api_key_id")
    private Long apiKeyId;

    /**
     * 调用的API端点
     */
    @TableField("endpoint")
    private String endpoint;

    /**
     * HTTP方法
     */
    @TableField("method")
    private String method;

    /**
     * 请求IP
     */
    @TableField("request_ip")
    private String requestIp;

    /**
     * 用户代理
     */
    @TableField("user_agent")
    private String userAgent;

    /**
     * 请求大小(字节)
     */
    @TableField("request_size")
    private Long requestSize;

    /**
     * 响应大小(字节)
     */
    @TableField("response_size")
    private Long responseSize;

    /**
     * 响应时间(毫秒)
     */
    @TableField("response_time")
    private Integer responseTime;

    /**
     * HTTP状态码
     */
    @TableField("status_code")
    private Integer statusCode;

    /**
     * 错误信息
     */
    @TableField("error_message")
    private String errorMessage;

    /**
     * 请求时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("request_time")
    private LocalDateTime requestTime;
}
