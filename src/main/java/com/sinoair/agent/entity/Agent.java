package com.sinoair.agent.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonRawValue;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * Agent实体类
 *
 * <AUTHOR> Team
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("agents")
public class Agent extends BaseEntity {

    @TableField("agent_name")
    private String agentName;

    @TableField("agent_code")
    private String agentCode;

    @TableField("description")
    private String description;

    @TableField("category_id")
    private Long categoryId;

    @TableField("business_type_id")
    private Long businessTypeId;

    @TableField("agent_type")
    private Integer agentType;

    @JsonRawValue
    @TableField("config")
    private String config;

    @TableField("prompt_template")
    private String promptTemplate;

    @JsonRawValue
    @TableField("json_template")
    private String jsonTemplate;

    @TableField("template_id")
    private Long templateId;

    @TableField("version")
    private String version;

    @TableField("status")
    private Integer status;

    @TableField("creator_id")
    private Long creatorId;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("published_time")
    private LocalDateTime publishedTime;

    // 非数据库字段
    @TableField(exist = false)
    private String categoryName;

    @TableField(exist = false)
    private String businessTypeName;

    @TableField(exist = false)
    private String creatorName;

    @TableField(exist = false)
    private String statusName;

    @TableField("approval_status")
    private Integer approvalStatus;

    @TableField("last_approval_id")
    private Long lastApprovalId;

    @TableField("submit_time")
    private LocalDateTime submitTime;

    // 非数据库字段 - 发布版本信息
    @TableField(exist = false)
    private String publishedVersion;

    @TableField(exist = false)
    private String approvalStatusName;

    // Agent状态常量
    public static final int STATUS_DRAFT = 1;      // 草稿
    public static final int STATUS_TESTING = 2;    // 测试中
    public static final int STATUS_PUBLISHED = 3;  // 已发布
    public static final int STATUS_OFFLINE = 4;    // 已下线
    public static final int STATUS_DELETED = 0;    // 已删除

    // Agent类型常量
    /**内部招商大模型-语言类*/
    public static final int TYPE_INTERNAL_LLM = 1;
    /**内部招商大模型-多模态*/
    public static final int TYPE_INTERNAL_VLM = 2;
    /**外部大模型-阿里百炼*/
    public static final int TYPE_EXTERNAL_AGENT = 3;
}
