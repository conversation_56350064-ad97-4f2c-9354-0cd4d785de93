package com.sinoair.agent.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonRawValue;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 业务类型实体类
 *
 * <AUTHOR> Team
 */
@Data
@TableName("business_types")
public class BusinessType {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("type_name")
    private String typeName;

    @TableField("type_code")
    private String typeCode;

    @TableField("description")
    private String description;

    @JsonRawValue
    @TableField("json_schema")
    private String jsonSchema;

    @TableField("file_types")
    private String fileTypes;

    @TableField("status")
    private Integer status;

    @TableField("created_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdTime;

    @TableLogic
    @TableField("deleted")
    private Integer deleted;
}
