package com.sinoair.agent.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonRawValue;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 业务类型实体类
 *
 * <AUTHOR> Team
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("business_types")
public class BusinessType extends BaseEntity {

    @TableField("type_name")
    private String typeName;

    @TableField("type_code")
    private String typeCode;

    @TableField("description")
    private String description;

    @JsonRawValue
    @TableField("json_schema")
    private String jsonSchema;

    @TableField("file_types")
    private String fileTypes;

    @TableField("status")
    private Integer status;
}
