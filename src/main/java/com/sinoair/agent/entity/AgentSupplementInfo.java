package com.sinoair.agent.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonRawValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Agent补充资料实体类
 *
 * <AUTHOR> Team
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("agent_supplement_info")
@Schema(description = "Agent补充资料")
public class AgentSupplementInfo extends BaseEntity {

    @Schema(description = "Agent ID")
    @TableField("agent_id")
    private Long agentId;

    @Schema(description = "Agent简介（富文本）")
    @TableField("agent_introduction")
    private String agentIntroduction;

    @Schema(description = "使用场景描述（富文本）")
    @TableField("usage_scenarios")
    private String usageScenarios;

    @Schema(description = "解决的痛点问题（富文本）")
    @TableField("pain_points_solved")
    private String painPointsSolved;

    @Schema(description = "Chrome插件使用效果截图URL列表")
    @JsonRawValue
    @TableField("screenshot_urls")
    private String screenshotUrls;

    @Schema(description = "Agent版本信息")
    @JsonRawValue
    @TableField("version_info")
    private String versionInfo;

    @Schema(description = "使用情况统计")
    @JsonRawValue
    @TableField("usage_statistics")
    private String usageStatistics;

    @Schema(description = "调用历史汇总")
    @JsonRawValue
    @TableField("call_history_summary")
    private String callHistorySummary;

    @Schema(description = "状态：1-草稿，2-已提交")
    @TableField("status")
    private Integer status;

    // 非数据库字段
    @Schema(description = "Agent名称")
    @TableField(exist = false)
    private String agentName;

    @Schema(description = "Agent编码")
    @TableField(exist = false)
    private String agentCode;

    @Schema(description = "Agent描述")
    @TableField(exist = false)
    private String agentDescription;

    // 状态常量
    public static final int STATUS_DRAFT = 1;      // 草稿
    public static final int STATUS_SUBMITTED = 2;  // 已提交
}
