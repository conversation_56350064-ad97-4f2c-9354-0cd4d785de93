package com.sinoair.agent.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 操作日志实体类
 * 用于记录用户的操作日志、表单数据和返回结果
 *
 * <AUTHOR> Team
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = "operation_logs")
public class OperationLog {

    /**
     * 日志ID
     */
    @Id
    private String id;

    /**
     * 用户ID
     */
    @Field("user_id")
    private Long userId;

    /**
     * 用户名
     */
    @Field("username")
    private String username;

    /**
     * 用户真实姓名
     */
    @Field("real_name")
    private String realName;

    /**
     * 操作模块
     */
    @Field("module")
    private String module;

    /**
     * 操作类型（CREATE, UPDATE, DELETE, QUERY, LOGIN, LOGOUT等）
     */
    @Field("operation_type")
    private String operationType;

    /**
     * 操作描述
     */
    @Field("operation_desc")
    private String operationDesc;

    /**
     * 请求方法（GET, POST, PUT, DELETE等）
     */
    @Field("request_method")
    private String requestMethod;

    /**
     * 请求URL
     */
    @Field("request_url")
    private String requestUrl;

    /**
     * 请求参数
     */
    @Field("request_params")
    private Map<String, Object> requestParams;

    /**
     * 请求体数据（表单数据）
     */
    @Field("request_body")
    private Map<String, Object> requestBody;

    /**
     * 响应结果
     */
    @Field("response_result")
    private Map<String, Object> responseResult;

    /**
     * 响应状态码
     */
    @Field("response_code")
    private Integer responseCode;

    /**
     * 响应消息
     */
    @Field("response_message")
    private String responseMessage;

    /**
     * 执行时间（毫秒）
     */
    @Field("execution_time")
    private Long executionTime;

    /**
     * 客户端IP地址
     */
    @Field("client_ip")
    private String clientIp;

    /**
     * 用户代理
     */
    @Field("user_agent")
    private String userAgent;

    /**
     * 操作状态（SUCCESS, FAILED, ERROR）
     */
    @Field("status")
    private String status;

    /**
     * 错误信息
     */
    @Field("error_message")
    private String errorMessage;

    /**
     * 业务数据（Agent相关的业务数据）
     */
    @Field("business_data")
    private Map<String, Object> businessData;

    /**
     * 操作时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Field("operation_time")
    private LocalDateTime operationTime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Field("created_time")
    private LocalDateTime createdTime;

    /**
     * 操作结果枚举
     */
    public enum Status {
        SUCCESS("SUCCESS", "成功"),
        FAILED("FAILED", "失败"),
        ERROR("ERROR", "错误");

        private final String code;
        private final String desc;

        Status(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }

    /**
     * 操作类型枚举
     */
    public enum OperationType {
        CREATE("CREATE", "创建"),
        UPDATE("UPDATE", "更新"),
        DELETE("DELETE", "删除"),
        QUERY("QUERY", "查询"),
        LOGIN("LOGIN", "登录"),
        LOGOUT("LOGOUT", "登出"),
        UPLOAD("UPLOAD", "上传"),
        DOWNLOAD("DOWNLOAD", "下载"),
        EXPORT("EXPORT", "导出"),
        IMPORT("IMPORT", "导入"),
        RECOGNITION("RECOGNITION", "识别"),
        AGENT_TEST("AGENT_TEST", "Agent测试"),
        AGENT_PUBLISH("AGENT_PUBLISH", "Agent发布");

        private final String code;
        private final String desc;

        OperationType(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }
}
