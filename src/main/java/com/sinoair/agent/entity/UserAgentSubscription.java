package com.sinoair.agent.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 用户Agent订阅实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_agent_subscriptions")
public class UserAgentSubscription implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID，关联sys_user表
     */
    @TableField("user_id")
    private Long userId;

    /**
     * Agent ID，关联agents表
     */
    @TableField("agent_id")
    private Long agentId;

    /**
     * 订阅状态：1-已订阅，0-已取消
     */
    @TableField("subscription_status")
    private Integer subscriptionStatus;

    /**
     * 订阅时间
     */
    @TableField("subscription_time")
    private LocalDateTime subscriptionTime;

    /**
     * 取消订阅时间
     */
    @TableField("unsubscription_time")
    private LocalDateTime unsubscriptionTime;

    /**
     * 创建时间
     */
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @TableField(value = "updated_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedTime;

    /**
     * 删除标记：0-未删除，1-已删除
     */
    @TableField("deleted")
    @TableLogic
    private Integer deleted;

    /**
     * 订阅状态常量
     */
    public static final int STATUS_SUBSCRIBED = 1;
    public static final int STATUS_UNSUBSCRIBED = 0;

    /**
     * 判断是否已订阅
     */
    public boolean isSubscribed() {
        return this.subscriptionStatus != null && this.subscriptionStatus == STATUS_SUBSCRIBED;
    }

    /**
     * 设置为已订阅状态
     */
    public UserAgentSubscription setSubscribed() {
        this.subscriptionStatus = STATUS_SUBSCRIBED;
        this.subscriptionTime = LocalDateTime.now();
        this.unsubscriptionTime = null;
        return this;
    }

    /**
     * 设置为取消订阅状态
     */
    public UserAgentSubscription setUnsubscribed() {
        this.subscriptionStatus = STATUS_UNSUBSCRIBED;
        this.unsubscriptionTime = LocalDateTime.now();
        return this;
    }
}
