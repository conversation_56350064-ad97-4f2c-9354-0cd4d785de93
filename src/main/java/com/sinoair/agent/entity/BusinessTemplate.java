package com.sinoair.agent.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 业务JSON模板实体
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("business_templates")
public class BusinessTemplate extends BaseEntity {

    @TableField("template_name")
    private String templateName;

    @TableField("template_code")
    private String templateCode;

    @TableField("description")
    private String description;

    @TableField("json_template")
    private String jsonTemplate;

    @TableField("category")
    private String category;

    @TableField("status")
    private Integer status;

    @TableField("created_by")
    private Long createdBy;
}
