package com.sinoair.agent;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.boot.autoconfigure.data.redis.RedisRepositoriesAutoConfiguration;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * SinoairAgent平台主启动类
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@SpringBootApplication(exclude = {
    HibernateJpaAutoConfiguration.class
})
@EnableAsync
@EnableScheduling
public class SinoairAgentApplication {

    public static void main(String[] args) {
        SpringApplication.run(SinoairAgentApplication.class, args);
        System.out.println("=================================");
        System.out.println("智能体矩阵平台启动成功！");
        System.out.println("管理平台: http://localhost:8080");
        System.out.println("API文档: http://localhost:8080/doc.html");
        System.out.println("=================================");
    }
}
