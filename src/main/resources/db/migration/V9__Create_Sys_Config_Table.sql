-- 系统配置表
CREATE TABLE sys_config (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '配置ID',
    config_group VARCHAR(50) NOT NULL COMMENT '配置组名',
    config_key VARCHAR(100) NOT NULL COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    description VARCHAR(500) COMMENT '配置描述',
    is_enabled TINYINT DEFAULT 1 COMMENT '是否有效：1-有效，0-无效',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT DEFAULT 0 COMMENT '删除标记：0-未删除，1-已删除',
    
    -- 索引
    INDEX idx_config_group (config_group),
    INDEX idx_config_key (config_key),
    INDEX idx_is_enabled (is_enabled),
    INDEX idx_deleted (deleted),
    
    -- 唯一约束：同一组内的配置键不能重复
    UNIQUE KEY uk_group_key (config_group, config_key, deleted)
) COMMENT='系统配置表';

-- 插入一些默认配置示例
INSERT INTO sys_config (config_group, config_key, config_value, description) VALUES
('system', 'app_name', '智能体矩阵 Platform', '应用名称'),
('system', 'app_version', '1.0.0', '应用版本'),
('system', 'maintenance_mode', 'false', '维护模式开关'),
('system', 'server_url', 'http://localhost:8080', '服务器地址（用于Chrome插件）'),

('llm', 'default_provider', 'qianwen', '默认LLM提供商'),
('llm', 'timeout_seconds', '30', 'LLM请求超时时间（秒）'),
('llm', 'max_retry_count', '3', 'LLM请求最大重试次数'),

('file', 'max_upload_size', '10485760', '文件上传最大大小（字节）'),
('file', 'allowed_extensions', 'jpg,jpeg,png,pdf,doc,docx,txt', '允许上传的文件扩展名'),
('file', 'storage_path', '/uploads', '文件存储路径'),

('security', 'jwt_expire_hours', '24', 'JWT令牌过期时间（小时）'),
('security', 'password_min_length', '6', '密码最小长度'),
('security', 'login_max_attempts', '5', '登录最大尝试次数'),

('notification', 'email_enabled', 'false', '邮件通知开关'),
('notification', 'sms_enabled', 'false', '短信通知开关'),

('cache', 'redis_expire_seconds', '3600', 'Redis缓存过期时间（秒）'),
('cache', 'enable_cache', 'true', '是否启用缓存');
