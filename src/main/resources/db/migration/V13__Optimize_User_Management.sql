-- 用户管理优化：删除role_id字段，添加forward字段
-- V13__Optimize_User_Management.sql

-- 1. 添加forward字段来区分用户来源
ALTER TABLE sys_user ADD COLUMN forward VARCHAR(20) DEFAULT 'MANAGEMENT' COMMENT '用户来源:MANAGEMENT-系统创建,PORTAL-官网注册';

-- 2. 添加审核状态字段（针对平台用户）
ALTER TABLE sys_user ADD COLUMN approval_status TINYINT DEFAULT 1 COMMENT '审核状态:0-待审核,1-已通过,2-已拒绝';

-- 3. 添加审核时间字段
ALTER TABLE sys_user ADD COLUMN approved_time DATETIME COMMENT '审核时间';

-- 4. 添加审核人字段
ALTER TABLE sys_user ADD COLUMN approved_by BIGINT COMMENT '审核人ID';

-- 5. 添加审核备注字段
ALTER TABLE sys_user ADD COLUMN approval_remark VARCHAR(500) COMMENT '审核备注';

-- 6. 更新现有用户的forward字段为MANAGEMENT（系统创建）
UPDATE sys_user SET forward = 'MANAGEMENT' WHERE forward IS NULL;

-- 7. 为forward字段添加索引
CREATE INDEX idx_sys_user_forward ON sys_user(forward);

-- 8. 为审核状态字段添加索引
CREATE INDEX idx_sys_user_approval_status ON sys_user(approval_status);

-- 9. 添加创建人和更新人字段（如果不存在）
ALTER TABLE sys_user ADD COLUMN IF NOT EXISTS created_by BIGINT COMMENT '创建人ID';
ALTER TABLE sys_user ADD COLUMN IF NOT EXISTS updated_by BIGINT COMMENT '更新人ID';

-- 10. 注释：role_id字段保留但不再必填，因为现在支持多角色通过sys_user_role表管理
ALTER TABLE sys_user MODIFY COLUMN role_id BIGINT NULL COMMENT '角色ID(兼容字段，实际使用sys_user_role表)';
