-- 智能体矩阵平台数据库初始化脚本

-- 创建数据库
CREATE DATABASE IF NOT EXISTS sinoair_agent DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE sinoair_agent;

-- 角色表（旧版本，将在V3中被新的sys_role表替代）
CREATE TABLE IF NOT EXISTS roles (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '角色ID',
    role_name VARCHAR(50) NOT NULL UNIQUE COMMENT '角色名称',
    role_code VARCHAR(50) NOT NULL UNIQUE COMMENT '角色编码',
    description TEXT COMMENT '角色描述',
    permissions JSON COMMENT '权限列表',
    status TINYINT DEFAULT 1 COMMENT '状态:1-正常,0-禁用',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT DEFAULT 0 COMMENT '删除标记'
) COMMENT='角色表';

-- 用户表
CREATE TABLE IF NOT EXISTS sys_user (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码(加密)',
    email VARCHAR(100) COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '手机号',
    real_name VARCHAR(50) COMMENT '真实姓名',
    role_id BIGINT NOT NULL COMMENT '角色ID',
    department VARCHAR(100) COMMENT '部门',
    status TINYINT DEFAULT 1 COMMENT '状态:1-正常,0-禁用',
    last_login_time DATETIME COMMENT '最后登录时间',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT DEFAULT 0 COMMENT '删除标记',
    INDEX idx_sys_user_username (username),
    INDEX idx_sys_user_role_id (role_id),
    INDEX idx_sys_user_status (status)
) COMMENT='用户表';

-- Agent分类表
CREATE TABLE IF NOT EXISTS agent_categories (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '分类ID',
    category_name VARCHAR(50) NOT NULL COMMENT '分类名称',
    category_code VARCHAR(50) NOT NULL UNIQUE COMMENT '分类编码',
    parent_id BIGINT DEFAULT 0 COMMENT '父分类ID',
    level TINYINT DEFAULT 1 COMMENT '层级',
    sort_order INT DEFAULT 0 COMMENT '排序',
    description TEXT COMMENT '分类描述',
    status TINYINT DEFAULT 1 COMMENT '状态:1-正常,0-禁用',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    deleted TINYINT DEFAULT 0 COMMENT '删除标记',
    INDEX idx_agent_categories_parent_id (parent_id),
    INDEX idx_agent_categories_status (status)
) COMMENT='Agent分类表';

-- 业务类型表
CREATE TABLE IF NOT EXISTS business_types (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '业务类型ID',
    type_name VARCHAR(50) NOT NULL COMMENT '业务类型名称',
    type_code VARCHAR(50) NOT NULL UNIQUE COMMENT '业务类型编码',
    description TEXT COMMENT '业务描述',
    json_schema JSON COMMENT 'JSON输出规范',
    file_types VARCHAR(200) COMMENT '支持的文件类型',
    status TINYINT DEFAULT 1 COMMENT '状态:1-正常,0-禁用',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    deleted TINYINT DEFAULT 0 COMMENT '删除标记',
    INDEX idx_business_types_status (status)
) COMMENT='业务类型表';

-- Agent主表
CREATE TABLE IF NOT EXISTS agents (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT 'AgentID',
    agent_name VARCHAR(100) NOT NULL COMMENT 'Agent名称',
    agent_code VARCHAR(50) NOT NULL UNIQUE COMMENT 'Agent编码',
    description TEXT COMMENT 'Agent描述',
    category_id BIGINT NOT NULL COMMENT '分类ID',
    business_type_id BIGINT NOT NULL COMMENT '业务类型ID',
    agent_type TINYINT NOT NULL COMMENT 'Agent类型:1-内部LLM,2-外部LLM,3-外部Agent',
    config JSON NOT NULL COMMENT 'Agent配置信息',
    prompt_template TEXT COMMENT '提示词模板',
    json_template JSON COMMENT 'JSON输出模板',
    version VARCHAR(20) DEFAULT '1.0.0' COMMENT '版本号',
    status TINYINT DEFAULT 1 COMMENT '状态:1-草稿,2-测试中,3-已发布,4-已下线,0-已删除',
    creator_id BIGINT NOT NULL COMMENT '创建者ID',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    published_time DATETIME COMMENT '发布时间',
    deleted TINYINT DEFAULT 0 COMMENT '删除标记',
    INDEX idx_agents_category_id (category_id),
    INDEX idx_agents_business_type_id (business_type_id),
    INDEX idx_agents_status (status),
    INDEX idx_agents_creator_id (creator_id)
) COMMENT='Agent主表';

-- 上传文件表
CREATE TABLE IF NOT EXISTS uploaded_files (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '文件ID',
    original_name VARCHAR(255) NOT NULL COMMENT '原始文件名',
    stored_name VARCHAR(255) NOT NULL COMMENT '存储文件名',
    file_path VARCHAR(500) NOT NULL COMMENT '文件路径',
    file_size BIGINT NOT NULL COMMENT '文件大小(字节)',
    file_type VARCHAR(50) NOT NULL COMMENT '文件类型',
    mime_type VARCHAR(100) COMMENT 'MIME类型',
    md5_hash VARCHAR(32) COMMENT 'MD5哈希值',
    upload_ip VARCHAR(45) COMMENT '上传IP',
    uploader_id BIGINT NOT NULL COMMENT '上传者ID',
    upload_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间',
    status TINYINT DEFAULT 1 COMMENT '状态:1-正常,0-已删除',
    deleted TINYINT DEFAULT 0 COMMENT '删除标记',
    INDEX idx_uploaded_files_uploader_id (uploader_id),
    INDEX idx_uploaded_files_upload_time (upload_time),
    INDEX idx_uploaded_files_md5_hash (md5_hash)
) COMMENT='上传文件表';

-- 识别记录表
CREATE TABLE IF NOT EXISTS recognition_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '记录ID',
    agent_id BIGINT NOT NULL COMMENT 'AgentID',
    file_id BIGINT NOT NULL COMMENT '文件ID',
    business_type_id BIGINT NOT NULL COMMENT '业务类型ID',
    input_params JSON COMMENT '输入参数',
    recognition_result JSON COMMENT '识别结果',
    confidence_score DECIMAL(5,4) COMMENT '置信度分数',
    processing_time INT COMMENT '处理耗时(毫秒)',
    status TINYINT NOT NULL COMMENT '状态:1-处理中,2-成功,3-失败',
    error_message TEXT COMMENT '错误信息',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    session_id VARCHAR(100) COMMENT '会话ID',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    completed_time DATETIME COMMENT '完成时间',
    deleted TINYINT DEFAULT 0 COMMENT '删除标记',
    INDEX idx_recognition_records_agent_id (agent_id),
    INDEX idx_recognition_records_user_id (user_id),
    INDEX idx_recognition_records_status (status),
    INDEX idx_recognition_records_created_time (created_time)
) COMMENT='识别记录表';
