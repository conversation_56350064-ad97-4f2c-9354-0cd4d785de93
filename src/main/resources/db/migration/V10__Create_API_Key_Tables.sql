-- 创建API Key相关表

-- API Key表
CREATE TABLE IF NOT EXISTS api_keys (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT 'API Key ID',
    key_id VARCHAR(32) NOT NULL UNIQUE COMMENT 'API Key标识符',
    key_secret VARCHAR(64) NOT NULL COMMENT 'API Key密钥(加密存储)',
    key_name VARCHAR(100) NOT NULL COMMENT 'API Key名称',
    description TEXT COMMENT 'API Key描述',
    user_id BIGINT NOT NULL COMMENT '所属用户ID',
    status TINYINT DEFAULT 1 COMMENT '状态:1-启用,0-禁用',
    rate_limit INT DEFAULT 1000 COMMENT '每小时请求限制',
    daily_limit INT DEFAULT 10000 COMMENT '每日请求限制',
    monthly_limit INT DEFAULT 100000 COMMENT '每月请求限制',
    allowed_ips TEXT COMMENT '允许的IP地址列表(JSON格式)',
    allowed_domains TEXT COMMENT '允许的域名列表(JSON格式)',
    expires_at DATETIME COMMENT 'API Key过期时间',
    last_used_at DATETIME COMMENT '最后使用时间',
    last_used_ip VARCHAR(45) COMMENT '最后使用IP',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT DEFAULT 0 COMMENT '删除标记',
    INDEX idx_api_keys_key_id (key_id),
    INDEX idx_api_keys_user_id (user_id),
    INDEX idx_api_keys_status (status),
    INDEX idx_api_keys_expires_at (expires_at),
    FOREIGN KEY (user_id) REFERENCES sys_user(id) ON DELETE CASCADE
) COMMENT='API Key表';

-- API Key使用统计表
CREATE TABLE IF NOT EXISTS api_key_usage (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '使用记录ID',
    api_key_id BIGINT NOT NULL COMMENT 'API Key ID',
    endpoint VARCHAR(200) NOT NULL COMMENT '调用的API端点',
    method VARCHAR(10) NOT NULL COMMENT 'HTTP方法',
    request_ip VARCHAR(45) COMMENT '请求IP',
    user_agent TEXT COMMENT '用户代理',
    request_size BIGINT DEFAULT 0 COMMENT '请求大小(字节)',
    response_size BIGINT DEFAULT 0 COMMENT '响应大小(字节)',
    response_time INT DEFAULT 0 COMMENT '响应时间(毫秒)',
    status_code INT NOT NULL COMMENT 'HTTP状态码',
    error_message TEXT COMMENT '错误信息',
    request_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '请求时间',
    INDEX idx_api_key_usage_api_key_id (api_key_id),
    INDEX idx_api_key_usage_request_time (request_time),
    INDEX idx_api_key_usage_endpoint (endpoint),
    INDEX idx_api_key_usage_status_code (status_code),
    FOREIGN KEY (api_key_id) REFERENCES api_keys(id) ON DELETE CASCADE
) COMMENT='API Key使用统计表';

-- API Key每日统计表
CREATE TABLE IF NOT EXISTS api_key_daily_stats (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '统计ID',
    api_key_id BIGINT NOT NULL COMMENT 'API Key ID',
    stat_date DATE NOT NULL COMMENT '统计日期',
    total_requests INT DEFAULT 0 COMMENT '总请求数',
    success_requests INT DEFAULT 0 COMMENT '成功请求数',
    failed_requests INT DEFAULT 0 COMMENT '失败请求数',
    total_request_size BIGINT DEFAULT 0 COMMENT '总请求大小(字节)',
    total_response_size BIGINT DEFAULT 0 COMMENT '总响应大小(字节)',
    avg_response_time DECIMAL(10,2) DEFAULT 0 COMMENT '平均响应时间(毫秒)',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_api_key_daily_stats (api_key_id, stat_date),
    INDEX idx_api_key_daily_stats_stat_date (stat_date),
    FOREIGN KEY (api_key_id) REFERENCES api_keys(id) ON DELETE CASCADE
) COMMENT='API Key每日统计表';

-- 客户信息表(扩展用户表的客户相关信息)
CREATE TABLE IF NOT EXISTS customer_profiles (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '客户档案ID',
    user_id BIGINT NOT NULL UNIQUE COMMENT '用户ID',
    company_name VARCHAR(200) COMMENT '公司名称',
    company_type VARCHAR(50) COMMENT '公司类型',
    industry VARCHAR(100) COMMENT '所属行业',
    business_license VARCHAR(100) COMMENT '营业执照号',
    contact_person VARCHAR(50) COMMENT '联系人',
    contact_phone VARCHAR(20) COMMENT '联系电话',
    contact_email VARCHAR(100) COMMENT '联系邮箱',
    address TEXT COMMENT '公司地址',
    website VARCHAR(200) COMMENT '公司网站',
    api_quota_monthly INT DEFAULT 10000 COMMENT '每月API调用配额',
    api_quota_daily INT DEFAULT 1000 COMMENT '每日API调用配额',
    api_quota_hourly INT DEFAULT 100 COMMENT '每小时API调用配额',
    account_type TINYINT DEFAULT 1 COMMENT '账户类型:1-免费版,2-基础版,3-专业版,4-企业版',
    account_status TINYINT DEFAULT 1 COMMENT '账户状态:1-正常,2-试用,3-暂停,4-欠费',
    trial_expires_at DATETIME COMMENT '试用期结束时间',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT DEFAULT 0 COMMENT '删除标记',
    INDEX idx_customer_profiles_user_id (user_id),
    INDEX idx_customer_profiles_account_type (account_type),
    INDEX idx_customer_profiles_account_status (account_status),
    FOREIGN KEY (user_id) REFERENCES sys_user(id) ON DELETE CASCADE
) COMMENT='客户档案表';
