-- 添加MinIO支持的数据库迁移脚本
-- 版本: V1.1
-- 描述: 为uploaded_files表添加MinIO相关字段

-- 为uploaded_files表添加MinIO相关字段
ALTER TABLE uploaded_files 
ADD COLUMN bucket_name VARCHAR(255) COMMENT '存储桶名称',
ADD COLUMN object_key VARCHAR(500) COMMENT '对象键（MinIO中的文件路径）',
ADD COLUMN etag VARCHAR(255) COMMENT '文件ETag（MinIO返回的文件标识）',
ADD COLUMN storage_type VARCHAR(20) DEFAULT 'LOCAL' COMMENT '存储类型：LOCAL-本地存储，MINIO-MinIO存储';

-- 添加索引以提高查询性能
CREATE INDEX idx_uploaded_files_bucket_name ON uploaded_files(bucket_name);
CREATE INDEX idx_uploaded_files_object_key ON uploaded_files(object_key);
CREATE INDEX idx_uploaded_files_storage_type ON uploaded_files(storage_type);
CREATE INDEX idx_uploaded_files_etag ON uploaded_files(etag);

-- 更新现有记录的存储类型为LOCAL
UPDATE uploaded_files SET storage_type = 'LOCAL' WHERE storage_type IS NULL;

-- 添加注释
ALTER TABLE uploaded_files MODIFY COLUMN bucket_name VARCHAR(255) COMMENT '存储桶名称（MinIO）';
ALTER TABLE uploaded_files MODIFY COLUMN object_key VARCHAR(500) COMMENT '对象键，MinIO中的完整文件路径';
ALTER TABLE uploaded_files MODIFY COLUMN etag VARCHAR(255) COMMENT '文件ETag，MinIO返回的文件唯一标识';
ALTER TABLE uploaded_files MODIFY COLUMN storage_type VARCHAR(20) DEFAULT 'LOCAL' COMMENT '存储类型：LOCAL-本地文件系统，MINIO-MinIO对象存储';

-- 添加约束：当storage_type为MINIO时，bucket_name和object_key不能为空
-- 注意：MySQL不支持复杂的CHECK约束，这里用触发器实现
DELIMITER $$

CREATE TRIGGER tr_uploaded_files_minio_check_insert
BEFORE INSERT ON uploaded_files
FOR EACH ROW
BEGIN
    IF NEW.storage_type = 'MINIO' THEN
        IF NEW.bucket_name IS NULL OR NEW.bucket_name = '' THEN
            SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'MinIO存储类型时bucket_name不能为空';
        END IF;
        IF NEW.object_key IS NULL OR NEW.object_key = '' THEN
            SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'MinIO存储类型时object_key不能为空';
        END IF;
    END IF;
END$$

CREATE TRIGGER tr_uploaded_files_minio_check_update
BEFORE UPDATE ON uploaded_files
FOR EACH ROW
BEGIN
    IF NEW.storage_type = 'MINIO' THEN
        IF NEW.bucket_name IS NULL OR NEW.bucket_name = '' THEN
            SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'MinIO存储类型时bucket_name不能为空';
        END IF;
        IF NEW.object_key IS NULL OR NEW.object_key = '' THEN
            SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'MinIO存储类型时object_key不能为空';
        END IF;
    END IF;
END$$

DELIMITER ;

-- 创建MinIO文件统计视图
CREATE VIEW v_file_storage_stats AS
SELECT 
    storage_type,
    COUNT(*) as file_count,
    SUM(file_size) as total_size,
    AVG(file_size) as avg_size,
    MIN(upload_time) as first_upload,
    MAX(upload_time) as last_upload
FROM uploaded_files 
WHERE deleted = 0
GROUP BY storage_type;

-- 添加视图注释
ALTER VIEW v_file_storage_stats COMMENT = '文件存储统计视图，按存储类型统计文件数量和大小';

-- 插入示例数据（可选，用于测试）
-- INSERT INTO uploaded_files (
--     original_name, stored_name, file_path, file_size, file_type, mime_type, 
--     md5_hash, uploader_id, upload_time, status, storage_type, bucket_name, object_key, etag
-- ) VALUES (
--     'test-minio.txt', '20240101_12345678.txt', 'test/2024/01/01/20240101_12345678.txt', 
--     1024, 'txt', 'text/plain', 'abc123def456', 1, NOW(), 1, 
--     'MINIO', 'sinoair-agent', 'test/2024/01/01/20240101_12345678.txt', 'etag123456'
-- );

-- 创建存储类型枚举检查（MySQL 8.0+支持）
-- ALTER TABLE uploaded_files ADD CONSTRAINT chk_storage_type 
-- CHECK (storage_type IN ('LOCAL', 'MINIO'));

-- 记录迁移完成
INSERT INTO schema_version (version, description, executed_at) 
VALUES ('V1.1', 'Add MinIO support to uploaded_files table', NOW())
ON DUPLICATE KEY UPDATE 
    description = VALUES(description),
    executed_at = VALUES(executed_at);
