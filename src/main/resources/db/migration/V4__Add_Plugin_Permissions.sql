-- 添加插件功能权限和智能表单配置权限
-- V4__Add_Plugin_Permissions.sql

-- 插入智能表单配置权限
INSERT INTO sys_permission (permission_name, permission_code, permission_type, parent_id, path, component, icon, sort_order, description) VALUES
-- 智能表单配置菜单权限
('智能表单配置', 'business:smart-form', 1, (SELECT id FROM (SELECT id FROM sys_permission WHERE permission_code = 'business') AS temp), '/business/smart-form-config', 'business/smart-form-config', '', 215, '智能表单配置管理'),
('智能表单查看', 'business:smart-form:view', 2, (SELECT id FROM (SELECT id FROM sys_permission WHERE permission_code = 'business:smart-form') AS temp), '', '', '', 2151, '查看智能表单配置'),
('智能表单创建', 'business:smart-form:create', 2, (SELECT id FROM (SELECT id FROM sys_permission WHERE permission_code = 'business:smart-form') AS temp), '', '', '', 2152, '创建智能表单配置'),
('智能表单编辑', 'business:smart-form:edit', 2, (SELECT id FROM (SELECT id FROM sys_permission WHERE permission_code = 'business:smart-form') AS temp), '', '', '', 2153, '编辑智能表单配置'),
('智能表单删除', 'business:smart-form:delete', 2, (SELECT id FROM (SELECT id FROM sys_permission WHERE permission_code = 'business:smart-form') AS temp), '', '', '', 2154, '删除智能表单配置');

-- 插入插件功能权限（不创建菜单，只是功能权限）
INSERT INTO sys_permission (permission_name, permission_code, permission_type, parent_id, path, component, icon, sort_order, description) VALUES
-- 插件功能权限（type=3表示功能权限）
('插件HTML提取', 'plugin:html:extract', 3, 0, '', '', '', 9001, '允许使用Chrome插件提取页面HTML'),
('插件表单填充', 'plugin:form:fill', 3, 0, '', '', '', 9002, '允许使用Chrome插件自动填充表单'),
('插件数据识别', 'plugin:data:recognize', 3, 0, '', '', '', 9003, '允许使用Chrome插件识别数据'),
('插件配置管理', 'plugin:config:manage', 3, 0, '', '', '', 9004, '允许Chrome插件访问配置管理'),
('插件调试模式', 'plugin:debug', 3, 0, '', '', '', 9005, '允许使用Chrome插件调试功能（仅管理员）');

-- 为超级管理员分配所有插件权限
INSERT INTO sys_role_permission (role_id, permission_id)
SELECT 1, id FROM sys_permission WHERE permission_code LIKE 'plugin:%';

-- 为超级管理员分配智能表单配置权限
INSERT INTO sys_role_permission (role_id, permission_id)
SELECT 1, id FROM sys_permission WHERE permission_code LIKE 'business:smart-form%';

-- 为业务管理员分配智能表单配置权限
INSERT INTO sys_role_permission (role_id, permission_id)
SELECT 3, id FROM sys_permission WHERE permission_code LIKE 'business:smart-form%';

-- 为业务管理员分配基础插件权限（不包括调试）
INSERT INTO sys_role_permission (role_id, permission_id)
SELECT 3, id FROM sys_permission WHERE permission_code IN (
    'plugin:html:extract', 'plugin:form:fill', 'plugin:data:recognize', 'plugin:config:manage'
);

-- 为普通用户分配基础插件使用权限
INSERT INTO sys_role_permission (role_id, permission_id)
SELECT 2, id FROM sys_permission WHERE permission_code IN (
    'plugin:form:fill', 'plugin:data:recognize'
);

-- 为普通用户分配智能表单查看权限
INSERT INTO sys_role_permission (role_id, permission_id)
SELECT 2, id FROM sys_permission WHERE permission_code IN (
    'business:smart-form', 'business:smart-form:view'
);
