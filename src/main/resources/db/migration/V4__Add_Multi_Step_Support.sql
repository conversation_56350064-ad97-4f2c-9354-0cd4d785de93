-- 添加多步骤支持的数据库迁移脚本

-- 为页面绑定表添加多步骤支持字段
ALTER TABLE page_bindings 
ADD COLUMN is_multi_step TINYINT DEFAULT 0 COMMENT '是否为多步骤绑定：1-是，0-否',
ADD COLUMN parent_binding_id BIGINT NULL COMMENT '父绑定ID（用于多步骤中的子步骤）',
ADD COLUMN step_order INT DEFAULT 1 COMMENT '步骤顺序（多步骤时使用）',
ADD COLUMN step_name VARCHAR(100) NULL COMMENT '步骤名称',
ADD COLUMN next_action JSON NULL COMMENT '下一步动作配置',
ADD COLUMN wait_time INT DEFAULT 3000 COMMENT '等待时间（毫秒）',
ADD COLUMN is_final_step TINYINT DEFAULT 1 COMMENT '是否为最后一步：1-是，0-否';

-- 添加索引
ALTER TABLE page_bindings 
ADD INDEX idx_parent_binding_id (parent_binding_id),
ADD INDEX idx_step_order (step_order),
ADD INDEX idx_is_multi_step (is_multi_step);

-- 添加外键约束
ALTER TABLE page_bindings 
ADD CONSTRAINT fk_parent_binding 
FOREIGN KEY (parent_binding_id) REFERENCES page_bindings(id) ON DELETE CASCADE;

-- 创建多步骤执行状态表
CREATE TABLE multi_step_executions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    session_id VARCHAR(64) NOT NULL COMMENT '会话ID',
    binding_id BIGINT NOT NULL COMMENT '绑定配置ID',
    agent_id BIGINT NOT NULL COMMENT 'Agent ID',
    current_step INT DEFAULT 1 COMMENT '当前步骤',
    total_steps INT NOT NULL COMMENT '总步骤数',
    execution_data JSON NOT NULL COMMENT '执行数据',
    status TINYINT DEFAULT 1 COMMENT '状态：1-进行中，2-已完成，3-已失败，4-已取消',
    error_message TEXT NULL COMMENT '错误信息',
    started_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    completed_at DATETIME NULL COMMENT '完成时间',
    created_by BIGINT COMMENT '创建人ID',
    
    INDEX idx_session_id (session_id),
    INDEX idx_binding_id (binding_id),
    INDEX idx_agent_id (agent_id),
    INDEX idx_status (status),
    INDEX idx_started_at (started_at),
    
    FOREIGN KEY (binding_id) REFERENCES page_bindings(id) ON DELETE CASCADE,
    FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE CASCADE
) COMMENT='多步骤执行状态表';
