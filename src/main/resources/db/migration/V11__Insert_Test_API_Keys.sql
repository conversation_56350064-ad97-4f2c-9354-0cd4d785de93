-- 插入测试用的API Key数据
-- 注意：这些是测试数据，生产环境请删除此文件

-- 测试API Key明文对照表：
-- ak_test001.sk_test123456789abcdef123456789abcdef123456789
-- ak_test002.sk_demo987654321fedcba987654321fedcba987654321
-- ak_demo001.sk_limited_key_for_testing_only_12345678

-- 使用Java DigestUtil.sha256Hex()相同的算法计算的哈希值
INSERT INTO api_keys (
    key_id,
    key_secret,
    key_name,
    description,
    user_id,
    status,
    rate_limit,
    daily_limit,
    monthly_limit,
    expires_at,
    created_time,
    updated_time
) VALUES
-- 开发测试API Key
(
    'ak_test001',
    '72b53c1febcf1755f39a299e1789d0429608b685a8aecca52f9d1527c3955168', -- SHA256('sk_test123456789abcdef123456789abcdef123456789')
    '开发测试API Key',
    '用于开发环境测试的API Key',
    1, -- admin用户
    1, -- 启用
    1000, -- 每小时1000次
    10000, -- 每日10000次
    100000, -- 每月100000次
    DATE_ADD(NOW(), INTERVAL 1 YEAR),
    NOW(),
    NOW()
),

-- 演示API Key
(
    'ak_test002',
    'c5f9b283b1c3839ac30ba32e3a4de38da36e7d37ac9f3c3d1625fe2bebf30b4a', -- SHA256('sk_demo987654321fedcba987654321fedcba987654321')
    '演示API Key',
    '用于产品演示的API Key',
    1, -- admin用户
    1, -- 启用
    500, -- 每小时500次
    5000, -- 每日5000次
    50000, -- 每月50000次
    DATE_ADD(NOW(), INTERVAL 6 MONTH),
    NOW(),
    NOW()
),

-- 受限API Key
(
    'ak_demo001',
    '6b7a5c4ca45dd44818754523fb8836fe964abb8a5cb66cdaedf9afe8a4ca6c61', -- SHA256('sk_limited_key_for_testing_only_12345678')
    '受限演示API Key',
    '用于受限环境演示的API Key',
    1, -- admin用户
    1, -- 启用
    100, -- 每小时100次
    1000, -- 每日1000次
    10000, -- 每月10000次
    DATE_ADD(NOW(), INTERVAL 3 MONTH),
    NOW(),
    NOW()
);
