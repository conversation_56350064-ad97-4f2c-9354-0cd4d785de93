-- 重命名现有表为sys_前缀，并调整表结构以适应新的RBAC系统

-- 1. 重命名用户表（如果存在users表）
-- 检查users表是否存在，如果存在则重命名
SET @table_exists = (SELECT COUNT(*) FROM information_schema.tables
                     WHERE table_schema = DATABASE() AND table_name = 'users');

SET @sql = IF(@table_exists > 0,
              'ALTER TABLE users RENAME TO sys_user',
              'SELECT "users table does not exist, sys_user table may already exist" as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 2. 重命名角色表（如果存在）
-- 检查roles表是否存在，如果存在则重命名
SET @table_exists = (SELECT COUNT(*) FROM information_schema.tables 
                     WHERE table_schema = DATABASE() AND table_name = 'roles');

SET @sql = IF(@table_exists > 0, 
              'ALTER TABLE roles RENAME TO sys_role_old', 
              'SELECT "roles table does not exist" as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 3. 为sys_user表添加缺失的字段（如果不存在）
-- 检查并添加created_by字段
SET @col_exists = (SELECT COUNT(*) FROM information_schema.columns 
                   WHERE table_schema = DATABASE() AND table_name = 'sys_user' AND column_name = 'created_by');

SET @sql = IF(@col_exists = 0, 
              'ALTER TABLE sys_user ADD COLUMN created_by BIGINT COMMENT "创建人"', 
              'SELECT "created_by column already exists" as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加updated_by字段
SET @col_exists = (SELECT COUNT(*) FROM information_schema.columns 
                   WHERE table_schema = DATABASE() AND table_name = 'sys_user' AND column_name = 'updated_by');

SET @sql = IF(@col_exists = 0, 
              'ALTER TABLE sys_user ADD COLUMN updated_by BIGINT COMMENT "更新人"', 
              'SELECT "updated_by column already exists" as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加updated_time字段
SET @col_exists = (SELECT COUNT(*) FROM information_schema.columns 
                   WHERE table_schema = DATABASE() AND table_name = 'sys_user' AND column_name = 'updated_time');

SET @sql = IF(@col_exists = 0, 
              'ALTER TABLE sys_user ADD COLUMN updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT "更新时间"', 
              'SELECT "updated_time column already exists" as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 4. 修改created_time字段的默认值（如果需要）
ALTER TABLE sys_user MODIFY COLUMN created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';

-- 5. 如果role_id字段不存在，添加它
SET @col_exists = (SELECT COUNT(*) FROM information_schema.columns 
                   WHERE table_schema = DATABASE() AND table_name = 'sys_user' AND column_name = 'role_id');

SET @sql = IF(@col_exists = 0, 
              'ALTER TABLE sys_user ADD COLUMN role_id BIGINT COMMENT "角色ID"', 
              'SELECT "role_id column already exists" as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 6. 确保status字段存在
SET @col_exists = (SELECT COUNT(*) FROM information_schema.columns 
                   WHERE table_schema = DATABASE() AND table_name = 'sys_user' AND column_name = 'status');

SET @sql = IF(@col_exists = 0, 
              'ALTER TABLE sys_user ADD COLUMN status TINYINT DEFAULT 1 COMMENT "状态：1-启用，0-禁用"', 
              'SELECT "status column already exists" as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 7. 为现有用户数据设置默认值
UPDATE sys_user SET created_by = 1 WHERE created_by IS NULL;
UPDATE sys_user SET updated_by = 1 WHERE updated_by IS NULL;
UPDATE sys_user SET updated_time = created_time WHERE updated_time IS NULL;
UPDATE sys_user SET status = 1 WHERE status IS NULL;

-- 8. 删除deleted字段（如果存在）
SET @col_exists = (SELECT COUNT(*) FROM information_schema.columns 
                   WHERE table_schema = DATABASE() AND table_name = 'sys_user' AND column_name = 'deleted');

SET @sql = IF(@col_exists > 0, 
              'ALTER TABLE sys_user DROP COLUMN deleted', 
              'SELECT "deleted column does not exist" as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
