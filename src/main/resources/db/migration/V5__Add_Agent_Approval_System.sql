-- Agent审批系统相关表结构
-- 版本：V5
-- 创建日期：2024-06-25
-- 描述：添加Agent审批功能相关的数据库表

-- 1. Agent审批记录表
CREATE TABLE IF NOT EXISTS agent_approval_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '审批记录ID',
    agent_id BIGINT NOT NULL COMMENT 'Agent ID',
    agent_version_id BIGINT COMMENT 'Agent版本ID（可选）',
    approval_status TINYINT NOT NULL COMMENT '审批状态：1-审批中，2-审批通过，3-审批不通过',
    approval_opinion TEXT COMMENT '审批意见',
    approver_id BIGINT NOT NULL COMMENT '审批人ID',
    approver_name VARCHAR(100) NOT NULL COMMENT '审批人姓名',
    approval_time DATETIME COMMENT '审批时间',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT DEFAULT 0 COMMENT '删除标记：0-未删除，1-已删除',
    INDEX idx_agent_id (agent_id),
    INDEX idx_approver_id (approver_id),
    INDEX idx_approval_status (approval_status),
    INDEX idx_created_time (created_time),
    FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE CASCADE,
    FOREIGN KEY (approver_id) REFERENCES sys_user(id) ON DELETE RESTRICT
) COMMENT='Agent审批记录表';

-- 2. 站内消息表
CREATE TABLE IF NOT EXISTS sys_messages (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '消息ID',
    user_id BIGINT NOT NULL COMMENT '接收用户ID',
    title VARCHAR(200) NOT NULL COMMENT '消息标题',
    content TEXT COMMENT '消息内容',
    message_type TINYINT DEFAULT 1 COMMENT '消息类型：1-系统通知，2-审批通知，3-其他',
    related_id BIGINT COMMENT '关联业务ID（如Agent ID）',
    related_type VARCHAR(50) COMMENT '关联业务类型（如AGENT_APPROVAL）',
    is_read TINYINT DEFAULT 0 COMMENT '是否已读：0-未读，1-已读',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT DEFAULT 0 COMMENT '删除标记：0-未删除，1-已删除',
    INDEX idx_user_id (user_id),
    INDEX idx_message_type (message_type),
    INDEX idx_is_read (is_read),
    INDEX idx_related (related_type, related_id),
    INDEX idx_created_time (created_time),
    FOREIGN KEY (user_id) REFERENCES sys_user(id) ON DELETE CASCADE
) COMMENT='站内消息表';

-- 3. 扩展Agent表，添加审批相关字段
ALTER TABLE agents 
ADD COLUMN approval_status TINYINT DEFAULT 0 COMMENT '审批状态：0-未提交，1-审批中，2-审批通过，3-审批不通过',
ADD COLUMN submit_time DATETIME COMMENT '提交审批时间',
ADD COLUMN last_approval_id BIGINT COMMENT '最近一次审批记录ID',
ADD INDEX idx_approval_status (approval_status),
ADD INDEX idx_submit_time (submit_time);

-- 4. 添加外键约束（如果last_approval_id不为空）
-- 注意：这个外键约束可能会在某些情况下造成循环依赖，建议在应用层处理
-- ALTER TABLE agents ADD CONSTRAINT fk_agents_last_approval 
-- FOREIGN KEY (last_approval_id) REFERENCES agent_approval_records(id) ON DELETE SET NULL;

-- 5. 初始化现有Agent的审批状态
-- 将所有已发布的Agent设置为审批通过状态
UPDATE agents 
SET approval_status = 2, 
    submit_time = published_time 
WHERE status = 3 AND approval_status = 0;

-- 将所有草稿状态的Agent设置为未提交状态
UPDATE agents 
SET approval_status = 0 
WHERE status = 1 AND approval_status IS NULL;

-- 6. 创建Agent调用统计视图（用于审批页面展示使用情况）
CREATE OR REPLACE VIEW v_agent_usage_stats AS
SELECT 
    a.id as agent_id,
    a.agent_name,
    a.agent_code,
    COUNT(rr.id) as total_calls,
    COUNT(CASE WHEN rr.status = 1 THEN 1 END) as success_calls,
    ROUND(COUNT(CASE WHEN rr.status = 1 THEN 1 END) * 100.0 / NULLIF(COUNT(rr.id), 0), 2) as success_rate,
    AVG(CASE WHEN rr.processing_time IS NOT NULL THEN rr.processing_time END) as avg_response_time,
    COUNT(DISTINCT rr.user_id) as unique_users,
    MAX(rr.created_time) as last_call_time
FROM agents a
LEFT JOIN recognition_records rr ON a.id = rr.agent_id AND rr.deleted = 0
WHERE a.deleted = 0
GROUP BY a.id, a.agent_name, a.agent_code;

-- 7. 插入测试数据（可选，用于开发测试）
-- 注意：生产环境中应该删除这部分
INSERT INTO agent_approval_records (agent_id, approval_status, approval_opinion, approver_id, approver_name, approval_time) 
SELECT 
    id as agent_id,
    2 as approval_status,
    '系统初始化时自动审批通过' as approval_opinion,
    1 as approver_id,
    'System' as approver_name,
    NOW() as approval_time
FROM agents 
WHERE status = 3 AND approval_status = 2
LIMIT 5;

-- 8. 创建索引优化查询性能
CREATE INDEX idx_agents_approval_submit ON agents(approval_status, submit_time);
CREATE INDEX idx_approval_records_time_status ON agent_approval_records(approval_time, approval_status);
