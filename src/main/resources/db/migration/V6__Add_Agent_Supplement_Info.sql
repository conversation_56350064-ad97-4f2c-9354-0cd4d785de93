-- V6__Add_Agent_Supplement_Info.sql
-- 添加Agent补充资料表

-- 1. Agent补充资料表
CREATE TABLE IF NOT EXISTS agent_supplement_info (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '补充资料ID',
    agent_id BIGINT NOT NULL COMMENT 'Agent ID',
    agent_introduction TEXT COMMENT 'Agent简介（富文本）',
    usage_scenarios TEXT COMMENT '使用场景描述（富文本）',
    pain_points_solved TEXT COMMENT '解决的痛点问题（富文本）',
    screenshot_urls JSON COMMENT 'Chrome插件使用效果截图URL列表',
    version_info JSON COMMENT 'Agent版本信息',
    usage_statistics JSON COMMENT '使用情况统计',
    call_history_summary JSON COMMENT '调用历史汇总',
    status TINYINT DEFAULT 1 COMMENT '状态：1-草稿，2-已提交',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted TINYINT DEFAULT 0 COMMENT '删除标记：0-未删除，1-已删除',
    INDEX idx_agent_id (agent_id),
    INDEX idx_status (status),
    INDEX idx_created_time (created_time),
    FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE CASCADE
) COMMENT='Agent补充资料表';

-- 2. 扩展Agent审批记录表，添加补充资料关联
ALTER TABLE agent_approval_records 
ADD COLUMN supplement_info_id BIGINT COMMENT '补充资料ID',
ADD INDEX idx_supplement_info_id (supplement_info_id);

-- 3. 添加外键约束
ALTER TABLE agent_approval_records 
ADD CONSTRAINT fk_approval_supplement 
FOREIGN KEY (supplement_info_id) REFERENCES agent_supplement_info(id) ON DELETE SET NULL;

-- 4. 创建索引优化查询性能
CREATE INDEX idx_agent_supplement_agent_status ON agent_supplement_info(agent_id, status);
CREATE INDEX idx_agent_supplement_updated ON agent_supplement_info(updated_time, status);
