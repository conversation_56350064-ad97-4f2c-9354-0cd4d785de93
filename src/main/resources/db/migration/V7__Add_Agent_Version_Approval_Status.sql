-- 为agent_versions表添加审批状态字段
-- 用于记录每个版本的审批进度状态

-- 添加approval_status字段到agent_versions表
ALTER TABLE agent_versions 
ADD COLUMN approval_status TINYINT DEFAULT 0 COMMENT '审批状态：0-未提交，1-审批中，2-审批通过，3-审批不通过';

-- 添加索引优化查询性能
CREATE INDEX IF NOT EXISTS idx_agent_versions_approval_status ON agent_versions(approval_status);
CREATE INDEX IF NOT EXISTS idx_agent_versions_agent_approval ON agent_versions(agent_id, approval_status);

-- 为已发布的版本设置审批通过状态
UPDATE agent_versions 
SET approval_status = 2 
WHERE status = 3 AND approval_status = 0;

-- 为当前版本但未发布的版本设置未提交状态
UPDATE agent_versions 
SET approval_status = 0 
WHERE status != 3 AND approval_status = 0;
