-- 清理重复的页面绑定数据
-- 删除重复的主绑定（保留最新的）

-- 1. 标记重复的主绑定为删除状态（保留最新的一个）
UPDATE page_bindings pb1 
SET deleted = 1 
WHERE pb1.parent_binding_id IS NULL 
AND pb1.deleted = 0
AND EXISTS (
    SELECT 1 FROM page_bindings pb2 
    WHERE pb2.binding_name = pb1.binding_name 
    AND pb2.parent_binding_id IS NULL 
    AND pb2.deleted = 0
    AND pb2.id > pb1.id
);

-- 2. 删除孤立的子步骤（父绑定已被删除的）
UPDATE page_bindings 
SET deleted = 1 
WHERE parent_binding_id IS NOT NULL 
AND deleted = 0
AND parent_binding_id NOT IN (
    SELECT id FROM page_bindings 
    WHERE parent_binding_id IS NULL 
    AND deleted = 0
);
