-- Agent版本历史表
CREATE TABLE IF NOT EXISTS agent_versions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '版本ID',
    agent_id BIGINT NOT NULL COMMENT 'Agent ID',
    version_number VARCHAR(20) NOT NULL COMMENT '版本号',
    agent_name VARCHAR(100) NOT NULL COMMENT 'Agent名称',
    agent_code VARCHAR(50) NOT NULL COMMENT 'Agent编码',
    description TEXT COMMENT 'Agent描述',
    category_id BIGINT NOT NULL COMMENT '分类ID',
    business_type_id BIGINT NOT NULL COMMENT '业务类型ID',
    agent_type TINYINT NOT NULL COMMENT 'Agent类型',
    config JSON NOT NULL COMMENT 'Agent配置信息',
    prompt_template TEXT COMMENT '提示词模板',
    json_template JSON COMMENT 'JSON输出模板',
    template_id BIGINT COMMENT '关联的业务模板ID',
    status TINYINT NOT NULL COMMENT '版本状态:1-草稿,2-测试中,3-已发布,4-已下线',
    is_current TINYINT DEFAULT 0 COMMENT '是否当前版本:1-是,0-否',
    change_log TEXT COMMENT '版本变更说明',
    creator_id BIGINT NOT NULL COMMENT '创建者ID',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    published_time DATETIME COMMENT '发布时间',
    INDEX idx_agent_id (agent_id),
    INDEX idx_version_number (version_number),
    INDEX idx_is_current (is_current),
    FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE CASCADE
) COMMENT='Agent版本历史表';

-- Agent调试历史表
CREATE TABLE IF NOT EXISTS agent_debug_history (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '调试记录ID',
    agent_id BIGINT NOT NULL COMMENT 'Agent ID',
    version_id BIGINT COMMENT '版本ID',
    model_name VARCHAR(50) NOT NULL COMMENT '使用的模型',
    model_config JSON NOT NULL COMMENT '模型配置参数',
    input_data TEXT NOT NULL COMMENT '输入数据',
    output_data JSON COMMENT '输出结果',
    status TINYINT NOT NULL COMMENT '执行状态:1-成功,0-失败',
    error_message TEXT COMMENT '错误信息',
    response_time INT NOT NULL COMMENT '响应时间(毫秒)',
    tokens_used INT COMMENT '使用的Token数量',
    cost DECIMAL(10,4) COMMENT '调用成本',
    creator_id BIGINT NOT NULL COMMENT '调试者ID',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '调试时间',
    INDEX idx_agent_id (agent_id),
    INDEX idx_version_id (version_id),
    INDEX idx_status (status),
    INDEX idx_created_time (created_time),
    FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE CASCADE,
    FOREIGN KEY (version_id) REFERENCES agent_versions(id) ON DELETE SET NULL
) COMMENT='Agent调试历史表';

-- Agent性能统计表
CREATE TABLE IF NOT EXISTS agent_performance_stats (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '统计ID',
    agent_id BIGINT NOT NULL COMMENT 'Agent ID',
    version_id BIGINT COMMENT '版本ID',
    date_key DATE NOT NULL COMMENT '统计日期',
    total_calls INT DEFAULT 0 COMMENT '总调用次数',
    success_calls INT DEFAULT 0 COMMENT '成功调用次数',
    failed_calls INT DEFAULT 0 COMMENT '失败调用次数',
    avg_response_time INT DEFAULT 0 COMMENT '平均响应时间(毫秒)',
    min_response_time INT DEFAULT 0 COMMENT '最小响应时间(毫秒)',
    max_response_time INT DEFAULT 0 COMMENT '最大响应时间(毫秒)',
    total_tokens INT DEFAULT 0 COMMENT '总Token使用量',
    total_cost DECIMAL(10,4) DEFAULT 0 COMMENT '总成本',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_agent_date (agent_id, version_id, date_key),
    INDEX idx_agent_id (agent_id),
    INDEX idx_date_key (date_key),
    FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE CASCADE,
    FOREIGN KEY (version_id) REFERENCES agent_versions(id) ON DELETE SET NULL
) COMMENT='Agent性能统计表';
