-- 业务JSON模板表
CREATE TABLE business_templates (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    template_name VARCHAR(100) NOT NULL COMMENT '业务模板名称',
    template_code VARCHAR(50) NOT NULL UNIQUE COMMENT '业务模板编码',
    description TEXT COMMENT '模板描述',
    json_template JSON NOT NULL COMMENT 'JSON输出模板',
    category VARCHAR(50) DEFAULT 'general' COMMENT '业务分类',
    status TINYINT DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
    created_by BIGINT COMMENT '创建人ID',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_template_code (template_code),
    INDEX idx_category (category),
    INDEX idx_status (status)
) COMMENT='业务JSON模板表';

-- 页面绑定配置表
CREATE TABLE page_bindings (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    binding_name VARCHAR(100) NOT NULL COMMENT '绑定配置名称',
    template_id BIGINT NOT NULL COMMENT '关联的业务模板ID',
    target_url VARCHAR(500) NOT NULL COMMENT '目标页面URL',
    url_pattern VARCHAR(500) NOT NULL COMMENT 'URL匹配模式',
    page_html TEXT COMMENT '页面HTML内容',
    binding_config JSON NOT NULL COMMENT '字段绑定配置',
    status TINYINT DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
    created_by BIGINT COMMENT '创建人ID',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (template_id) REFERENCES business_templates(id) ON DELETE CASCADE,
    INDEX idx_template_id (template_id),
    INDEX idx_url_pattern (url_pattern),
    INDEX idx_status (status)
) COMMENT='页面绑定配置表';

-- Agent优化参数表
CREATE TABLE agent_optimizations (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    agent_id BIGINT NOT NULL COMMENT '关联的Agent ID',
    optimization_name VARCHAR(100) COMMENT '优化配置名称',
    llm_config JSON NOT NULL COMMENT 'LLM参数配置',
    prompt_template TEXT NOT NULL COMMENT '优化后的提示词模板',
    test_results JSON COMMENT '测试结果数据',
    performance_metrics JSON COMMENT '性能指标',
    is_active TINYINT DEFAULT 0 COMMENT '是否为当前使用的配置：1-是，0-否',
    created_by BIGINT COMMENT '创建人ID',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE CASCADE,
    INDEX idx_agent_id (agent_id),
    INDEX idx_is_active (is_active)
) COMMENT='Agent优化参数表';

-- 修改agents表，添加template_id字段
ALTER TABLE agents 
ADD COLUMN template_id BIGINT COMMENT '关联的业务模板ID',
ADD INDEX idx_template_id (template_id);

-- 插入一些示例业务模板
INSERT INTO business_templates (template_name, template_code, description, json_template, category) VALUES
('发票识别', 'INVOICE_RECOGNITION', '识别各类发票信息，包括开票方、金额、日期等', 
 '{"invoice_number": "发票号码", "invoice_date": "开票日期", "seller_name": "销售方名称", "seller_tax_number": "销售方税号", "buyer_name": "购买方名称", "buyer_tax_number": "购买方税号", "total_amount": "价税合计", "tax_amount": "税额", "goods_list": [{"name": "货物名称", "quantity": "数量", "unit_price": "单价", "amount": "金额"}]}', 
 'finance'),

('运单识别', 'WAYBILL_RECOGNITION', '识别航空运单信息，包括运单号、发货人、收货人等',
 '{"waybill_number": "运单号", "origin": "起运地", "destination": "目的地", "shipper_name": "发货人姓名", "shipper_address": "发货人地址", "shipper_phone": "发货人电话", "consignee_name": "收货人姓名", "consignee_address": "收货人地址", "consignee_phone": "收货人电话", "goods_description": "货物描述", "weight": "重量", "pieces": "件数", "flight_number": "航班号", "flight_date": "航班日期"}',
 'logistics'),

('合同识别', 'CONTRACT_RECOGNITION', '识别合同关键信息，包括甲乙方、金额、期限等',
 '{"contract_number": "合同编号", "contract_title": "合同标题", "party_a": "甲方名称", "party_b": "乙方名称", "contract_amount": "合同金额", "start_date": "开始日期", "end_date": "结束日期", "payment_terms": "付款条件", "delivery_terms": "交付条件", "key_terms": ["关键条款"]}',
 'legal'),

('身份证识别', 'ID_CARD_RECOGNITION', '识别身份证信息，包括姓名、身份证号、地址等',
 '{"name": "姓名", "id_number": "身份证号", "gender": "性别", "birth_date": "出生日期", "address": "住址", "issuing_authority": "签发机关", "valid_period": "有效期限"}',
 'identity'),

('银行卡识别', 'BANK_CARD_RECOGNITION', '识别银行卡信息，包括卡号、有效期、持卡人等',
 '{"card_number": "卡号", "card_holder": "持卡人", "expiry_date": "有效期", "bank_name": "发卡银行", "card_type": "卡片类型"}',
 'finance');
