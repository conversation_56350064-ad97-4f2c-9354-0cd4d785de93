-- -- V8__Add_OCR_Support.sql
-- -- 添加OCR图片文字识别功能支持
--
-- -- 更新uploaded_files表，添加OCR相关字段
-- ALTER TABLE uploaded_files
-- ADD COLUMN ocr_text LONGTEXT COMMENT 'OCR识别的文字内容' AFTER file_size,
-- ADD COLUMN ocr_status TINYINT DEFAULT 0 COMMENT 'OCR状态：0-未处理，1-处理中，2-成功，3-失败' AFTER ocr_text,
-- ADD COLUMN ocr_error_message VARCHAR(500) COMMENT 'OCR错误信息' AFTER ocr_status,
-- ADD COLUMN ocr_processed_time DATETIME COMMENT 'OCR处理时间' AFTER ocr_error_message;
--
-- -- 添加索引
-- CREATE INDEX idx_uploaded_files_ocr_status ON uploaded_files(ocr_status);
-- CREATE INDEX idx_uploaded_files_mime_type ON uploaded_files(mime_type);
--
-- -- 插入OCR相关权限
-- INSERT INTO sys_permission (permission_name, permission_code, permission_type, parent_id, sort_order, description, created_time, updated_time, deleted)
-- VALUES
-- ('OCR管理', 'ocr', 'MENU', NULL, 80, 'OCR图片文字识别管理', NOW(), NOW(), 0),
-- ('OCR识别', 'ocr:recognize', 'BUTTON', (SELECT id FROM (SELECT id FROM sys_permission WHERE permission_code = 'ocr') AS temp), 1, '执行OCR图片文字识别', NOW(), NOW(), 0),
-- ('OCR状态查看', 'ocr:status', 'BUTTON', (SELECT id FROM (SELECT id FROM sys_permission WHERE permission_code = 'ocr') AS temp), 2, '查看OCR服务状态', NOW(), NOW(), 0);
--
-- -- 为管理员角色添加OCR权限
-- INSERT INTO sys_role_permission (role_id, permission_id, created_time, updated_time, deleted)
-- SELECT
--     r.id as role_id,
--     p.id as permission_id,
--     NOW() as created_time,
--     NOW() as updated_time,
--     0 as deleted
-- FROM sys_role r
-- CROSS JOIN sys_permission p
-- WHERE r.role_code = 'ADMIN'
--   AND p.permission_code IN ('ocr', 'ocr:recognize', 'ocr:status')
--   AND NOT EXISTS (
--       SELECT 1 FROM sys_role_permission rp
--       WHERE rp.role_id = r.id AND rp.permission_id = p.id AND rp.deleted = 0
--   );
--
-- -- 更新recognition_records表，添加文件类型字段
-- ALTER TABLE recognition_records
-- ADD COLUMN file_type VARCHAR(50) COMMENT '文件类型：document-文档，image-图片' AFTER file_id;
--
-- -- 更新现有记录的文件类型为document
-- UPDATE recognition_records SET file_type = 'document' WHERE file_type IS NULL;
--
-- -- 添加索引
-- CREATE INDEX idx_recognition_records_file_type ON recognition_records(file_type);
--
-- -- 创建OCR处理日志表（可选，用于记录OCR处理历史）
-- CREATE TABLE ocr_processing_logs (
--     id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
--     file_id BIGINT NOT NULL COMMENT '文件ID',
--     original_filename VARCHAR(255) NOT NULL COMMENT '原始文件名',
--     file_size BIGINT NOT NULL COMMENT '文件大小（字节）',
--     mime_type VARCHAR(100) COMMENT '文件MIME类型',
--     ocr_engine VARCHAR(50) DEFAULT 'tesseract' COMMENT 'OCR引擎',
--     language_config VARCHAR(100) COMMENT '语言配置',
--     processing_time_ms BIGINT COMMENT '处理耗时（毫秒）',
--     recognized_text_length INT COMMENT '识别文字长度',
--     status TINYINT NOT NULL COMMENT '处理状态：1-成功，2-失败',
--     error_message VARCHAR(500) COMMENT '错误信息',
--     created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
--     updated_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
--     deleted TINYINT NOT NULL DEFAULT 0 COMMENT '删除标记：0-未删除，1-已删除',
--
--     INDEX idx_file_id (file_id),
--     INDEX idx_status (status),
--     INDEX idx_created_time (created_time),
--     FOREIGN KEY (file_id) REFERENCES uploaded_files(id) ON DELETE CASCADE
-- ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='OCR处理日志表';
