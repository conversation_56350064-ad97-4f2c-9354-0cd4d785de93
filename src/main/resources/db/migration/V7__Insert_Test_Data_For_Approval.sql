-- -- 插入测试数据用于Agent审批列表
--
-- -- 1. 确保有Agent分类数据
-- INSERT IGNORE INTO agent_categories (id, category_name, description, created_time, updated_time) VALUES
-- (1, '文档识别', '文档识别相关的Agent', NOW(), NOW()),
-- (2, '表单填写', '表单自动填写相关的Agent', NOW(), NOW()),
-- (3, '数据处理', '数据处理相关的Agent', NOW(), NOW());
--
-- -- 2. 确保有业务类型数据
-- INSERT IGNORE INTO business_types (id, type_name, description, created_time, updated_time) VALUES
-- (1, '物流单据', '物流运输单据处理', NOW(), NOW()),
-- (2, '财务报表', '财务报表处理', NOW(), NOW()),
-- (3, '合同文档', '合同文档处理', NOW(), NOW());
--
-- -- 3. 确保有用户数据
-- INSERT IGNORE INTO sys_user (id, username, password, real_name, email, phone, status, created_time, updated_time) VALUES
-- (1, 'admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKyZOzx8Qx.Ew.Ew.Ew.Ew.Ew.Ew', '系统管理员', '<EMAIL>', '***********', 1, NOW(), NOW()),
-- (2, 'user1', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKyZOzx8Qx.Ew.Ew.Ew.Ew.Ew.Ew', '测试用户1', '<EMAIL>', '***********', 1, NOW(), NOW()),
-- (3, 'user2', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKyZOzx8Qx.Ew.Ew.Ew.Ew.Ew.Ew', '测试用户2', '<EMAIL>', '***********', 1, NOW(), NOW());
--
-- -- 4. 插入测试Agent数据（包含不同的审批状态）
-- INSERT IGNORE INTO agents (id, agent_name, agent_code, description, category_id, business_type_id, agent_type, config, prompt_template, json_template, version, status, creator_id, approval_status, submit_time, created_time, updated_time) VALUES
-- (1, '物流单据识别Agent', 'LOGISTICS_DOC_AGENT', '专门用于识别物流运输单据的智能Agent', 1, 1, 1,
--  '{"provider":"qianwen","model":"qwen-plus","temperature":0.7}',
--  '请识别以下物流单据中的关键信息：{content}',
--  '{"consignee":{"name":"","address":""},"shipper":{"name":"","address":""},"goods":[]}',
--  '1.0.0', 2, 2, 1, NOW(), NOW(), NOW()),
--
-- (2, '财务报表分析Agent', 'FINANCE_REPORT_AGENT', '用于分析财务报表数据的智能Agent', 2, 2, 1,
--  '{"provider":"openai","model":"gpt-4","temperature":0.5}',
--  '请分析以下财务报表数据：{content}',
--  '{"revenue":"","profit":"","expenses":[]}',
--  '1.1.0', 2, 2, 2, NOW(), NOW(), NOW()),
--
-- (3, '合同条款提取Agent', 'CONTRACT_EXTRACT_AGENT', '从合同文档中提取关键条款信息', 3, 3, 1,
--  '{"provider":"deepseek","model":"deepseek-chat","temperature":0.3}',
--  '请从以下合同中提取关键条款：{content}',
--  '{"parties":[],"terms":[],"amount":"","duration":""}',
--  '2.0.0', 2, 3, 3, NOW(), NOW(), NOW()),
--
-- (4, '发票信息识别Agent', 'INVOICE_RECOGNITION_AGENT', '识别发票中的关键信息', 1, 2, 1,
--  '{"provider":"qianwen","model":"qwen-plus","temperature":0.6}',
--  '请识别发票中的关键信息：{content}',
--  '{"invoice_number":"","amount":"","date":"","vendor":""}',
--  '1.2.0', 1, 1, 1, NOW(), NOW(), NOW()),
--
-- (5, '表单自动填写Agent', 'FORM_AUTOFILL_AGENT', '根据文档内容自动填写表单', 2, 1, 2,
--  '{"provider":"openai","model":"gpt-3.5-turbo","temperature":0.4}',
--  '根据以下信息自动填写表单：{content}',
--  '{"form_data":{}}',
--  '1.0.1', 1, 2, 0, NULL, NOW(), NOW());
--
-- -- 5. 插入审批记录数据
-- INSERT IGNORE INTO agent_approval_records (id, agent_id, agent_version_id, approval_status, approval_opinion, approver_id, approver_name, approval_time, created_time, updated_time) VALUES
-- (1, 1, NULL, 1, '申请发布物流单据识别Agent，已完成基础功能开发', NULL, NULL, NULL, NOW(), NOW()),
-- (2, 2, NULL, 2, '财务报表分析功能完善，审批通过', 1, '系统管理员', NOW(), NOW(), NOW()),
-- (3, 3, NULL, 3, '合同条款提取准确率需要提升，暂不通过', 1, '系统管理员', NOW(), NOW(), NOW()),
-- (4, 4, NULL, 1, '发票识别功能申请发布', NULL, NULL, NULL, NOW(), NOW());
--
-- -- 6. 更新Agent表的last_approval_id字段
-- UPDATE agents SET last_approval_id = 1 WHERE id = 1;
-- UPDATE agents SET last_approval_id = 2 WHERE id = 2;
-- UPDATE agents SET last_approval_id = 3 WHERE id = 3;
-- UPDATE agents SET last_approval_id = 4 WHERE id = 4;
