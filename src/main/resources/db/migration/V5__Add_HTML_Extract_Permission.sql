-- 添加HTML提取功能权限
-- V5__Add_HTML_Extract_Permission.sql

-- 插入HTML提取相关权限
INSERT INTO sys_permission (permission_name, permission_code, permission_type, parent_id, path, component, icon, sort_order, description) VALUES
-- HTML提取功能权限
('HTML提取工具', 'plugin:html:extract:tool', 3, 0, '', '', '', 9006, '允许使用Chrome插件HTML提取工具'),
('HTML提取查看', 'plugin:html:extract:view', 3, 0, '', '', '', 9007, '允许查看HTML提取结果'),
('HTML提取配置', 'plugin:html:extract:config', 3, 0, '', '', '', 9008, '允许配置HTML提取参数');

-- 为超级管理员分配HTML提取权限
INSERT INTO sys_role_permission (role_id, permission_id)
SELECT 1, id FROM sys_permission WHERE permission_code LIKE 'plugin:html:extract:%';

-- 为业务管理员分配HTML提取权限
INSERT INTO sys_role_permission (role_id, permission_id)
SELECT 3, id FROM sys_permission WHERE permission_code LIKE 'plugin:html:extract:%';

-- 为普通用户分配基础HTML提取权限
INSERT INTO sys_role_permission (role_id, permission_id)
SELECT 2, id FROM sys_permission WHERE permission_code IN (
    'plugin:html:extract:tool', 'plugin:html:extract:view'
);
