-- 修复Agent表的approval_status字段类型
-- 确保approval_status字段为TINYINT类型，并设置默认值

-- 检查并修改approval_status字段类型
ALTER TABLE agents MODIFY COLUMN approval_status TINYINT DEFAULT 0 COMMENT '审批状态：0-未提交，1-审批中，2-审批通过，3-审批不通过';

-- 检查并修改last_approval_id字段类型
ALTER TABLE agents MODIFY COLUMN last_approval_id BIGINT COMMENT '最近一次审批记录ID';

-- 确保所有现有记录的approval_status不为NULL
UPDATE agents SET approval_status = 0 WHERE approval_status IS NULL;

-- 为已发布的Agent设置审批通过状态（如果还没有设置）
UPDATE agents 
SET approval_status = 2 
WHERE status = 3 AND approval_status = 0;

-- 添加索引优化查询性能（如果不存在）
CREATE INDEX IF NOT EXISTS idx_agents_approval_status ON agents(approval_status);
CREATE INDEX IF NOT EXISTS idx_agents_creator_approval ON agents(creator_id, approval_status);
