-- 角色表
CREATE TABLE sys_role (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    role_name VARCHAR(50) NOT NULL COMMENT '角色名称',
    role_code VARCHAR(50) NOT NULL UNIQUE COMMENT '角色编码',
    description VARCHAR(200) COMMENT '角色描述',
    status TINYINT DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
    sort_order INT DEFAULT 0 COMMENT '排序',
    created_by BIGINT COMMENT '创建人',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by BIGINT COMMENT '更新人',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT '系统角色表';

-- 权限表
CREATE TABLE sys_permission (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    permission_name VARCHAR(50) NOT NULL COMMENT '权限名称',
    permission_code VARCHAR(100) NOT NULL UNIQUE COMMENT '权限编码',
    permission_type TINYINT NOT NULL COMMENT '权限类型：1-菜单，2-按钮，3-接口',
    parent_id BIGINT DEFAULT 0 COMMENT '父权限ID',
    path VARCHAR(200) COMMENT '路由路径',
    component VARCHAR(200) COMMENT '组件路径',
    icon VARCHAR(50) COMMENT '图标',
    sort_order INT DEFAULT 0 COMMENT '排序',
    status TINYINT DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
    description VARCHAR(200) COMMENT '权限描述',
    created_by BIGINT COMMENT '创建人',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by BIGINT COMMENT '更新人',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT '系统权限表';

-- 用户角色关联表
CREATE TABLE sys_user_role (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    role_id BIGINT NOT NULL COMMENT '角色ID',
    created_by BIGINT COMMENT '创建人',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    UNIQUE KEY uk_user_role (user_id, role_id),
    FOREIGN KEY (user_id) REFERENCES sys_user(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES sys_role(id) ON DELETE CASCADE
) COMMENT '用户角色关联表';

-- 角色权限关联表
CREATE TABLE sys_role_permission (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    role_id BIGINT NOT NULL COMMENT '角色ID',
    permission_id BIGINT NOT NULL COMMENT '权限ID',
    created_by BIGINT COMMENT '创建人',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    UNIQUE KEY uk_role_permission (role_id, permission_id),
    FOREIGN KEY (role_id) REFERENCES sys_role(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES sys_permission(id) ON DELETE CASCADE
) COMMENT '角色权限关联表';

-- 插入默认角色
INSERT INTO sys_role (role_name, role_code, description, status, sort_order) VALUES
('超级管理员', 'ADMIN', '系统超级管理员，拥有所有权限', 1, 1),
('普通用户', 'USER', '普通用户，基础权限', 1, 2),
('业务管理员', 'BUSINESS_ADMIN', '业务管理员，管理业务相关功能', 1, 3);

-- 插入系统权限
INSERT INTO sys_permission (permission_name, permission_code, permission_type, parent_id, path, component, icon, sort_order, description) VALUES
-- 一级菜单
('系统管理', 'system', 1, 0, '/system', '', 'bi-gear', 1, '系统管理模块'),
('业务管理', 'business', 1, 0, '/business', '', 'bi-briefcase', 2, '业务管理模块'),
('监控中心', 'monitor', 1, 0, '/monitor', '', 'bi-activity', 3, '系统监控模块'),

-- 系统管理子菜单
('用户管理', 'system:user', 1, 1, '/system/users', 'users.html', 'bi-people', 11, '用户管理页面'),
('角色管理', 'system:role', 1, 1, '/system/roles', 'roles.html', 'bi-person-badge', 12, '角色管理页面'),
('权限管理', 'system:permission', 1, 1, '/system/permissions', 'permissions.html', 'bi-shield-check', 13, '权限管理页面'),

-- 业务管理子菜单
('模板管理', 'business:template', 1, 2, '/business/templates', 'templates.html', 'bi-file-earmark-text', 21, '业务模板管理'),
('页面绑定', 'business:binding', 1, 2, '/business/bindings', 'bindings.html', 'bi-link-45deg', 22, '页面绑定管理'),
('智能体管理', 'business:agent', 1, 2, '/business/agents', 'agents.html', 'bi-robot', 23, '智能体管理'),

-- 用户管理按钮权限
('用户查看', 'system:user:view', 2, 4, '', '', '', 111, '查看用户信息'),
('用户新增', 'system:user:create', 2, 4, '', '', '', 112, '新增用户'),
('用户编辑', 'system:user:update', 2, 4, '', '', '', 113, '编辑用户'),
('用户删除', 'system:user:delete', 2, 4, '', '', '', 114, '删除用户'),

-- 角色管理按钮权限
('角色查看', 'system:role:view', 2, 5, '', '', '', 121, '查看角色信息'),
('角色新增', 'system:role:create', 2, 5, '', '', '', 122, '新增角色'),
('角色编辑', 'system:role:update', 2, 5, '', '', '', 123, '编辑角色'),
('角色删除', 'system:role:delete', 2, 5, '', '', '', 124, '删除角色'),
('角色授权', 'system:role:assign', 2, 5, '', '', '', 125, '角色权限分配'),

-- 权限管理按钮权限
('权限查看', 'system:permission:view', 2, 6, '', '', '', 131, '查看权限信息'),
('权限新增', 'system:permission:create', 2, 6, '', '', '', 132, '新增权限'),
('权限编辑', 'system:permission:update', 2, 6, '', '', '', 133, '编辑权限'),
('权限删除', 'system:permission:delete', 2, 6, '', '', '', 134, '删除权限'),

-- 模板管理按钮权限
('模板查看', 'business:template:view', 2, 7, '', '', '', 211, '查看模板信息'),
('模板新增', 'business:template:create', 2, 7, '', '', '', 212, '新增模板'),
('模板编辑', 'business:template:update', 2, 7, '', '', '', 213, '编辑模板'),
('模板删除', 'business:template:delete', 2, 7, '', '', '', 214, '删除模板'),

-- 页面绑定按钮权限
('绑定查看', 'business:binding:view', 2, 8, '', '', '', 221, '查看绑定信息'),
('绑定新增', 'business:binding:create', 2, 8, '', '', '', 222, '新增绑定'),
('绑定编辑', 'business:binding:update', 2, 8, '', '', '', 223, '编辑绑定'),
('绑定删除', 'business:binding:delete', 2, 8, '', '', '', 224, '删除绑定'),

-- 智能体管理按钮权限
('智能体查看', 'business:agent:view', 2, 9, '', '', '', 231, '查看智能体信息'),
('智能体新增', 'business:agent:create', 2, 9, '', '', '', 232, '新增智能体'),
('智能体编辑', 'business:agent:update', 2, 9, '', '', '', 233, '编辑智能体'),
('智能体删除', 'business:agent:delete', 2, 9, '', '', '', 234, '删除智能体');

-- 为超级管理员分配所有权限
INSERT INTO sys_role_permission (role_id, permission_id)
SELECT 1, id FROM sys_permission;

-- 为普通用户分配基础权限（只能查看）
INSERT INTO sys_role_permission (role_id, permission_id)
SELECT 2, id FROM sys_permission WHERE permission_code IN (
    'business', 'business:template', 'business:binding', 'business:agent',
    'business:template:view', 'business:binding:view', 'business:agent:view'
);

-- 为业务管理员分配业务相关权限
INSERT INTO sys_role_permission (role_id, permission_id)
SELECT 3, id FROM sys_permission WHERE permission_code LIKE 'business%';

-- 给默认管理员用户分配超级管理员角色
-- 注意：这里假设第一个用户的ID是1，实际部署时可能需要调整
INSERT INTO sys_user_role (user_id, role_id, created_by, created_time)
SELECT 1, 1, 1, NOW()
WHERE EXISTS (SELECT 1 FROM sys_user WHERE id = 1);
