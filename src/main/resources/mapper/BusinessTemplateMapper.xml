<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinoair.agent.mapper.BusinessTemplateMapper">

    <!-- 根据分类查询模板列表 -->
    <select id="findByCategory" resultType="com.sinoair.agent.entity.BusinessTemplate">
        SELECT * FROM business_templates 
        WHERE category = #{category} AND deleted = 0
    </select>

    <!-- 查询所有可用模板 -->
    <select id="findAllActive" resultType="com.sinoair.agent.entity.BusinessTemplate">
        SELECT * FROM business_templates 
        WHERE status = 1 AND deleted = 0 
        ORDER BY created_time DESC
    </select>

    <!-- 统计模板数量 -->
    <select id="countTemplates" resultType="java.lang.Long">
        SELECT COUNT(*) FROM business_templates WHERE deleted = 0
    </select>

    <!-- 统计各分类模板数量 -->
    <select id="countByCategory" resultType="java.util.Map">
        SELECT category, COUNT(*) as count 
        FROM business_templates 
        WHERE deleted = 0 
        GROUP BY category
    </select>

    <!-- 根据模板代码查找模板 -->
    <select id="findByTemplateCode" resultType="com.sinoair.agent.entity.BusinessTemplate">
        SELECT * FROM business_templates 
        WHERE template_code = #{templateCode} AND deleted = 0
    </select>

    <!-- 根据模板名称查找模板 -->
    <select id="findByTemplateName" resultType="com.sinoair.agent.entity.BusinessTemplate">
        SELECT * FROM business_templates 
        WHERE template_name = #{templateName} AND deleted = 0
    </select>

    <!-- 分页查询模板列表 -->
    <select id="selectPageWithKeyword" resultType="com.sinoair.agent.entity.BusinessTemplate">
        SELECT * FROM business_templates 
        WHERE deleted = 0 
        <if test="keyword != null and keyword != ''">
            AND (template_name LIKE CONCAT('%', #{keyword}, '%') 
            OR template_code LIKE CONCAT('%', #{keyword}, '%') 
            OR description LIKE CONCAT('%', #{keyword}, '%'))
        </if>
        <if test="category != null and category != ''">
            AND category = #{category}
        </if>
        ORDER BY created_time DESC
    </select>

    <!-- 根据创建者查询模板 -->
    <select id="findByCreatedBy" resultType="com.sinoair.agent.entity.BusinessTemplate">
        SELECT * FROM business_templates 
        WHERE created_by = #{createdBy} AND deleted = 0 
        ORDER BY created_time DESC
    </select>

    <!-- 检查模板是否有关联的Agent -->
    <select id="hasAgents" resultType="java.lang.Boolean">
        SELECT COUNT(*) > 0 FROM agents 
        WHERE template_id = #{templateId} AND deleted = 0
    </select>

    <!-- 检查模板是否有关联的页面绑定 -->
    <select id="hasPageBindings" resultType="java.lang.Boolean">
        SELECT COUNT(*) > 0 FROM page_bindings 
        WHERE template_id = #{templateId} AND deleted = 0
    </select>

    <!-- 查询模板及其关联的Agent数量 -->
    <select id="findWithAgentCount" resultType="java.util.Map">
        SELECT bt.*, COUNT(a.id) as agent_count 
        FROM business_templates bt 
        LEFT JOIN agents a ON bt.id = a.template_id AND a.deleted = 0 
        WHERE bt.deleted = 0 
        GROUP BY bt.id 
        ORDER BY bt.created_time DESC
    </select>

    <!-- 查询模板及其关联的页面绑定数量 -->
    <select id="findWithBindingCount" resultType="java.util.Map">
        SELECT bt.*, COUNT(pb.id) as binding_count 
        FROM business_templates bt 
        LEFT JOIN page_bindings pb ON bt.id = pb.template_id AND pb.deleted = 0 
        WHERE bt.deleted = 0 
        GROUP BY bt.id 
        ORDER BY bt.created_time DESC
    </select>

</mapper>
