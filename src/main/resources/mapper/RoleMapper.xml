<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinoair.agent.mapper.RoleMapper">

    <!-- 角色结果映射 -->
    <resultMap id="BaseResultMap" type="com.sinoair.agent.entity.Role">
        <id column="id" property="id" />
        <result column="role_name" property="roleName" />
        <result column="role_code" property="roleCode" />
        <result column="description" property="description" />
        <result column="status" property="status" />
        <result column="sort_order" property="sortOrder" />
        <result column="created_by" property="createdBy" />
        <result column="created_time" property="createdTime" />
        <result column="updated_by" property="updatedBy" />
        <result column="updated_time" property="updatedTime" />
    </resultMap>

    <!-- 角色权限结果映射 -->
    <resultMap id="RoleWithPermissionsMap" type="com.sinoair.agent.entity.Role" extends="BaseResultMap">
        <collection property="permissions" ofType="com.sinoair.agent.entity.Permission">
            <id column="p_id" property="id" />
            <result column="p_permission_name" property="permissionName" />
            <result column="p_permission_code" property="permissionCode" />
            <result column="p_permission_type" property="permissionType" />
            <result column="p_parent_id" property="parentId" />
            <result column="p_path" property="path" />
            <result column="p_component" property="component" />
            <result column="p_icon" property="icon" />
            <result column="p_sort_order" property="sortOrder" />
            <result column="p_status" property="status" />
            <result column="p_description" property="description" />
        </collection>
    </resultMap>

    <!-- 查询角色及其权限信息 -->
    <select id="selectRolesWithPermissions" resultMap="RoleWithPermissionsMap">
        SELECT 
            r.id, r.role_name, r.role_code, r.description, r.status, r.sort_order,
            r.created_by, r.created_time, r.updated_by, r.updated_time,
            p.id as p_id, p.permission_name as p_permission_name, 
            p.permission_code as p_permission_code, p.permission_type as p_permission_type,
            p.parent_id as p_parent_id, p.path as p_path, p.component as p_component,
            p.icon as p_icon, p.sort_order as p_sort_order, p.status as p_status,
            p.description as p_description
        FROM sys_role r
        LEFT JOIN sys_role_permission rp ON r.id = rp.role_id
        LEFT JOIN sys_permission p ON rp.permission_id = p.id AND p.status = 1
        WHERE r.status = 1
        ORDER BY r.sort_order ASC, p.sort_order ASC
    </select>

    <!-- 根据角色ID查询角色及其权限信息 -->
    <select id="selectRoleWithPermissions" resultMap="RoleWithPermissionsMap">
        SELECT
            r.id, r.role_name, r.role_code, r.description, r.status, r.sort_order,
            r.created_by, r.created_time, r.updated_by, r.updated_time,
            p.id as p_id, p.permission_name as p_permission_name,
            p.permission_code as p_permission_code, p.permission_type as p_permission_type,
            p.parent_id as p_parent_id, p.path as p_path, p.component as p_component,
            p.icon as p_icon, p.sort_order as p_sort_order, p.status as p_status,
            p.description as p_description
        FROM sys_role r
        LEFT JOIN sys_role_permission rp ON r.id = rp.role_id
        LEFT JOIN sys_permission p ON rp.permission_id = p.id AND p.status = 1
        WHERE r.id = #{roleId}
        ORDER BY p.sort_order ASC
    </select>

    <!-- 根据用户ID查询角色列表 -->
    <select id="selectRolesByUserId" resultType="com.sinoair.agent.entity.Role">
        SELECT r.*
        FROM sys_role r
        INNER JOIN sys_user_role ur ON r.id = ur.role_id
        WHERE ur.user_id = #{userId} AND r.status = 1
    </select>

    <!-- 根据角色代码查找角色 -->
    <select id="findByRoleCode" resultType="com.sinoair.agent.entity.Role">
        SELECT * FROM sys_role WHERE role_code = #{roleCode}
    </select>

    <!-- 根据角色名称查找角色 -->
    <select id="findByRoleName" resultType="com.sinoair.agent.entity.Role">
        SELECT * FROM sys_role WHERE role_name = #{roleName}
    </select>

    <!-- 分页查询角色列表 -->
    <select id="selectPageWithKeyword" resultType="com.sinoair.agent.entity.Role">
        SELECT * FROM sys_role
        WHERE 1=1
        <if test="keyword != null and keyword != ''">
            AND (role_name LIKE CONCAT('%', #{keyword}, '%')
            OR role_code LIKE CONCAT('%', #{keyword}, '%')
            OR description LIKE CONCAT('%', #{keyword}, '%'))
        </if>
        ORDER BY sort_order ASC, created_time DESC
    </select>

    <!-- 查询所有可用角色 -->
    <select id="findAllActive" resultType="com.sinoair.agent.entity.Role">
        SELECT * FROM sys_role
        WHERE status = 1
        ORDER BY sort_order ASC, created_time ASC
    </select>

    <!-- 查询角色关联的用户数量 -->
    <select id="countUsersByRoleId" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM sys_user_role WHERE role_id = #{roleId}
    </select>

    <!-- 统计角色数量 -->
    <select id="countRoles" resultType="java.lang.Long">
        SELECT COUNT(*) FROM sys_role
    </select>

    <!-- 统计活跃角色数量 -->
    <select id="countActiveRoles" resultType="java.lang.Long">
        SELECT COUNT(*) FROM sys_role WHERE status = 1
    </select>

</mapper>
