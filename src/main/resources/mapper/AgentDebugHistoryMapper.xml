<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinoair.agent.mapper.AgentDebugHistoryMapper">

    <!-- 结果映射 -->
    <resultMap id="AgentDebugHistoryWithDetailsMap" type="com.sinoair.agent.entity.AgentDebugHistory">
        <id column="id" property="id"/>
        <result column="agent_id" property="agentId"/>
        <result column="version_id" property="versionId"/>
        <result column="model_name" property="modelName"/>
        <result column="model_config" property="modelConfig"/>
        <result column="input_data" property="inputData"/>
        <result column="output_data" property="outputData"/>
        <result column="status" property="status"/>
        <result column="error_message" property="errorMessage"/>
        <result column="response_time" property="responseTime"/>
        <result column="tokens_used" property="tokensUsed"/>
        <result column="cost" property="cost"/>
        <result column="creator_id" property="creatorId"/>
        <result column="created_time" property="createdTime"/>
        <result column="agent_name" property="agentName"/>
        <result column="user_name" property="userName"/>
    </resultMap>

    <!-- 根据Agent ID查询调试历史 -->
    <select id="findByAgentId" resultMap="AgentDebugHistoryWithDetailsMap">
        SELECT adh.*, a.agent_name, u.real_name as user_name
        FROM agent_debug_history adh
        LEFT JOIN agents a ON adh.agent_id = a.id
        LEFT JOIN sys_user u ON adh.creator_id = u.id
        WHERE adh.agent_id = #{agentId}
        ORDER BY adh.created_time DESC
    </select>

    <!-- 根据用户ID查询调试历史 -->
    <select id="findByUserId" resultMap="AgentDebugHistoryWithDetailsMap">
        SELECT adh.*, a.agent_name, u.real_name as user_name
        FROM agent_debug_history adh
        LEFT JOIN agents a ON adh.agent_id = a.id
        LEFT JOIN sys_user u ON adh.creator_id = u.id
        WHERE adh.creator_id = #{userId}
        ORDER BY adh.created_time DESC
    </select>

    <!-- 根据成功状态查询调试历史 -->
    <select id="findByAgentIdAndSuccess" resultMap="AgentDebugHistoryWithDetailsMap">
        SELECT adh.*, a.agent_name, u.real_name as user_name
        FROM agent_debug_history adh
        LEFT JOIN agents a ON adh.agent_id = a.id
        LEFT JOIN sys_user u ON adh.creator_id = u.id
        WHERE adh.agent_id = #{agentId} AND adh.status = #{success}
        ORDER BY adh.created_time DESC
    </select>

    <!-- 分页查询调试历史 -->
    <select id="selectPageWithDetails" resultMap="AgentDebugHistoryWithDetailsMap">
        SELECT adh.*, a.agent_name, u.real_name as user_name
        FROM agent_debug_history adh
        LEFT JOIN agents a ON adh.agent_id = a.id
        LEFT JOIN sys_user u ON adh.creator_id = u.id
        WHERE 1=1
        <if test="agentId != null">
            AND adh.agent_id = #{agentId}
        </if>
        <if test="userId != null">
            AND adh.creator_id = #{userId}
        </if>
        <if test="success != null">
            AND adh.status = #{success}
        </if>
        ORDER BY adh.created_time DESC
    </select>

    <!-- 统计调试历史数量 -->
    <select id="countByAgentId" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM agent_debug_history
        WHERE agent_id = #{agentId}
    </select>

    <!-- 统计成功调试数量 -->
    <select id="countSuccessByAgentId" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM agent_debug_history
        WHERE agent_id = #{agentId} AND status = 1
    </select>

    <!-- 统计失败调试数量 -->
    <select id="countFailureByAgentId" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM agent_debug_history
        WHERE agent_id = #{agentId} AND status = 0
    </select>

    <!-- 查询平均执行时间 -->
    <select id="getAverageExecutionTime" resultType="java.lang.Double">
        SELECT AVG(response_time) FROM agent_debug_history
        WHERE agent_id = #{agentId} AND status = 1 AND response_time > 0
    </select>

    <!-- 查询最近的调试记录 -->
    <select id="findRecentByAgentId" resultMap="AgentDebugHistoryWithDetailsMap">
        SELECT adh.*, a.agent_name, u.real_name as user_name
        FROM agent_debug_history adh
        LEFT JOIN agents a ON adh.agent_id = a.id
        LEFT JOIN sys_user u ON adh.creator_id = u.id
        WHERE adh.agent_id = #{agentId}
        ORDER BY adh.created_time DESC
        LIMIT #{limit}
    </select>

    <!-- 清理过期的调试记录 -->
    <update id="cleanupOldRecords">
        DELETE FROM agent_debug_history
        WHERE created_time &lt; DATE_SUB(NOW(), INTERVAL #{days} DAY)
    </update>

</mapper>
