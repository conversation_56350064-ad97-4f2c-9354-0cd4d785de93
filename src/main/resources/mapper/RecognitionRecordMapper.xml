<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinoair.agent.mapper.RecognitionRecordMapper">

    <!-- 结果映射 -->
    <resultMap id="RecognitionRecordWithDetailsMap" type="com.sinoair.agent.entity.RecognitionRecord">
        <id column="id" property="id"/>
        <result column="agent_id" property="agentId"/>
        <result column="file_id" property="fileId"/>
        <result column="business_type_id" property="businessTypeId"/>
        <result column="input_params" property="inputParams"/>
        <result column="recognition_result" property="recognitionResult"/>
        <result column="confidence_score" property="confidenceScore"/>
        <result column="processing_time" property="processingTime"/>
        <result column="status" property="status"/>
        <result column="error_message" property="errorMessage"/>
        <result column="user_id" property="userId"/>
        <result column="session_id" property="sessionId"/>
        <result column="created_time" property="createdTime"/>
        <result column="completed_time" property="completedTime"/>
        <result column="deleted" property="deleted"/>
        <result column="agent_name" property="agentName"/>
        <result column="file_name" property="fileName"/>
        <result column="user_name" property="userName"/>
        <result column="business_type_name" property="businessTypeName"/>
    </resultMap>

    <!-- 根据用户ID查询识别记录 -->
    <select id="findByUserId" resultType="com.sinoair.agent.entity.RecognitionRecord">
        SELECT * FROM recognition_records 
        WHERE user_id = #{userId} AND deleted = 0 
        ORDER BY created_time DESC
    </select>

    <!-- 根据Agent ID查询识别记录 -->
    <select id="findByAgentId" resultType="com.sinoair.agent.entity.RecognitionRecord">
        SELECT * FROM recognition_records 
        WHERE agent_id = #{agentId} AND deleted = 0 
        ORDER BY created_time DESC
    </select>

    <!-- 根据状态查询识别记录 -->
    <select id="findByStatus" resultType="com.sinoair.agent.entity.RecognitionRecord">
        SELECT * FROM recognition_records 
        WHERE status = #{status} AND deleted = 0 
        ORDER BY created_time DESC
    </select>

    <!-- 分页查询识别记录（带详细信息） -->
    <select id="selectPageWithDetails" resultMap="RecognitionRecordWithDetailsMap">
        SELECT rr.*, a.agent_name, f.original_name as file_name, 
               u.real_name as user_name, bt.type_name as business_type_name 
        FROM recognition_records rr 
        LEFT JOIN agents a ON rr.agent_id = a.id 
        LEFT JOIN uploaded_files f ON rr.file_id = f.id 
        LEFT JOIN sys_user u ON rr.user_id = u.id 
        LEFT JOIN business_types bt ON rr.business_type_id = bt.id 
        WHERE rr.deleted = 0 
        <if test="keyword != null and keyword != ''">
            AND (a.agent_name LIKE CONCAT('%', #{keyword}, '%') 
            OR f.original_name LIKE CONCAT('%', #{keyword}, '%') 
            OR u.real_name LIKE CONCAT('%', #{keyword}, '%'))
        </if>
        <if test="status != null">
            AND rr.status = #{status}
        </if>
        <if test="userId != null">
            AND rr.user_id = #{userId}
        </if>
        <if test="agentId != null">
            AND rr.agent_id = #{agentId}
        </if>
        ORDER BY rr.created_time DESC
    </select>

    <!-- 根据文件ID查询识别记录 -->
    <select id="findByFileId" resultType="com.sinoair.agent.entity.RecognitionRecord">
        SELECT * FROM recognition_records 
        WHERE file_id = #{fileId} AND deleted = 0 
        ORDER BY created_time DESC
    </select>

    <!-- 根据会话ID查询识别记录 -->
    <select id="findBySessionId" resultType="com.sinoair.agent.entity.RecognitionRecord">
        SELECT * FROM recognition_records 
        WHERE session_id = #{sessionId} AND deleted = 0 
        ORDER BY created_time DESC
    </select>

    <!-- 统计识别记录数量 -->
    <select id="countRecords" resultType="java.lang.Long">
        SELECT COUNT(*) FROM recognition_records WHERE deleted = 0
    </select>

    <!-- 统计成功识别记录数量 -->
    <select id="countSuccessRecords" resultType="java.lang.Long">
        SELECT COUNT(*) FROM recognition_records 
        WHERE status = 2 AND deleted = 0
    </select>

    <!-- 统计各状态识别记录数量 -->
    <select id="countByStatus" resultType="java.util.Map">
        SELECT status, COUNT(*) as count 
        FROM recognition_records 
        WHERE deleted = 0 
        GROUP BY status
    </select>

    <!-- 统计各Agent的识别记录数量 -->
    <select id="countByAgent" resultType="java.util.Map">
        SELECT a.agent_name, COUNT(rr.id) as count 
        FROM agents a 
        LEFT JOIN recognition_records rr ON a.id = rr.agent_id AND rr.deleted = 0 
        WHERE a.deleted = 0 
        GROUP BY a.id, a.agent_name 
        ORDER BY count DESC
    </select>

    <!-- 统计各用户的识别记录数量 -->
    <select id="countByUser" resultType="java.util.Map">
        SELECT u.real_name as user_name, COUNT(rr.id) as count 
        FROM sys_user u 
        LEFT JOIN recognition_records rr ON u.id = rr.user_id AND rr.deleted = 0 
        GROUP BY u.id, u.real_name 
        HAVING count > 0 
        ORDER BY count DESC
    </select>

    <!-- 查询平均处理时间 -->
    <select id="getAverageProcessingTime" resultType="java.lang.Double">
        SELECT AVG(processing_time) FROM recognition_records 
        WHERE status = 2 AND deleted = 0 AND processing_time > 0
    </select>

    <!-- 查询平均置信度 -->
    <select id="getAverageConfidenceScore" resultType="java.lang.Double">
        SELECT AVG(confidence_score) FROM recognition_records 
        WHERE status = 2 AND deleted = 0 AND confidence_score > 0
    </select>

    <!-- 查询最近的识别记录 -->
    <select id="findRecentRecords" resultMap="RecognitionRecordWithDetailsMap">
        SELECT rr.*, a.agent_name, f.original_name as file_name, 
               u.real_name as user_name, bt.type_name as business_type_name 
        FROM recognition_records rr 
        LEFT JOIN agents a ON rr.agent_id = a.id 
        LEFT JOIN uploaded_files f ON rr.file_id = f.id 
        LEFT JOIN sys_user u ON rr.user_id = u.id 
        LEFT JOIN business_types bt ON rr.business_type_id = bt.id 
        WHERE rr.deleted = 0 
        ORDER BY rr.created_time DESC 
        LIMIT #{limit}
    </select>

</mapper>
