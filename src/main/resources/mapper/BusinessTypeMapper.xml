<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinoair.agent.mapper.BusinessTypeMapper">

    <!-- 根据类型代码查找业务类型 -->
    <select id="findByTypeCode" resultType="com.sinoair.agent.entity.BusinessType">
        SELECT * FROM business_types 
        WHERE type_code = #{typeCode} AND deleted = 0
    </select>

    <!-- 根据类型名称查找业务类型 -->
    <select id="findByTypeName" resultType="com.sinoair.agent.entity.BusinessType">
        SELECT * FROM business_types 
        WHERE type_name = #{typeName} AND deleted = 0
    </select>

    <!-- 查询所有可用业务类型 -->
    <select id="findAllActive" resultType="com.sinoair.agent.entity.BusinessType">
        SELECT * FROM business_types 
        WHERE status = 1 AND deleted = 0 
        ORDER BY created_time ASC
    </select>

    <!-- 统计业务类型数量 -->
    <select id="countBusinessTypes" resultType="java.lang.Long">
        SELECT COUNT(*) FROM business_types WHERE deleted = 0
    </select>

    <!-- 根据分类查询业务类型 -->
    <select id="findByCategory" resultType="com.sinoair.agent.entity.BusinessType">
        SELECT * FROM business_types 
        WHERE category = #{category} AND deleted = 0 
        ORDER BY created_time ASC
    </select>

    <!-- 统计各分类下的业务类型数量 -->
    <select id="countByCategory" resultType="java.util.Map">
        SELECT category, COUNT(*) as count 
        FROM business_types 
        WHERE deleted = 0 
        GROUP BY category
    </select>

    <!-- 检查业务类型是否有关联的Agent -->
    <select id="hasAgents" resultType="java.lang.Boolean">
        SELECT COUNT(*) > 0 FROM agents 
        WHERE business_type_id = #{businessTypeId} AND deleted = 0
    </select>

    <!-- 查询业务类型及其关联的Agent数量 -->
    <select id="findWithAgentCount" resultType="java.util.Map">
        SELECT bt.*, COUNT(a.id) as agent_count 
        FROM business_types bt 
        LEFT JOIN agents a ON bt.id = a.business_type_id AND a.deleted = 0 
        WHERE bt.deleted = 0 
        GROUP BY bt.id 
        ORDER BY bt.created_time ASC
    </select>

</mapper>
