<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinoair.agent.mapper.CustomerProfileMapper">

    <!-- 结果映射 -->
    <resultMap id="CustomerProfileWithUserMap" type="com.sinoair.agent.entity.CustomerProfile">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="company_name" property="companyName"/>
        <result column="company_type" property="companyType"/>
        <result column="industry" property="industry"/>
        <result column="business_license" property="businessLicense"/>
        <result column="contact_person" property="contactPerson"/>
        <result column="contact_phone" property="contactPhone"/>
        <result column="contact_email" property="contactEmail"/>
        <result column="address" property="address"/>
        <result column="website" property="website"/>
        <result column="api_quota_monthly" property="apiQuotaMonthly"/>
        <result column="api_quota_daily" property="apiQuotaDaily"/>
        <result column="api_quota_hourly" property="apiQuotaHourly"/>
        <result column="account_type" property="accountType"/>
        <result column="account_status" property="accountStatus"/>
        <result column="trial_expires_at" property="trialExpiresAt"/>
        <result column="created_time" property="createdTime"/>
        <result column="updated_time" property="updatedTime"/>
        <result column="deleted" property="deleted"/>
        <result column="username" property="username"/>
        <result column="user_real_name" property="userRealName"/>
        <result column="user_email" property="userEmail"/>
    </resultMap>

    <!-- 分页查询客户档案列表(包含用户信息) -->
    <select id="selectCustomerPageWithUser" resultMap="CustomerProfileWithUserMap">
        SELECT 
            cp.id, cp.user_id, cp.company_name, cp.company_type, cp.industry,
            cp.business_license, cp.contact_person, cp.contact_phone, cp.contact_email,
            cp.address, cp.website, cp.api_quota_monthly, cp.api_quota_daily, cp.api_quota_hourly,
            cp.account_type, cp.account_status, cp.trial_expires_at, cp.created_time, cp.updated_time,
            u.username, u.real_name as user_real_name, u.email as user_email
        FROM customer_profiles cp
        LEFT JOIN sys_user u ON cp.user_id = u.id
        WHERE cp.deleted = 0
        <if test="companyName != null and companyName != ''">
            AND cp.company_name LIKE CONCAT('%', #{companyName}, '%')
        </if>
        <if test="industry != null and industry != ''">
            AND cp.industry = #{industry}
        </if>
        <if test="accountType != null">
            AND cp.account_type = #{accountType}
        </if>
        <if test="accountStatus != null">
            AND cp.account_status = #{accountStatus}
        </if>
        ORDER BY cp.created_time DESC
    </select>

    <!-- 根据用户ID查询客户档案(包含用户信息) -->
    <select id="selectByUserIdWithUser" resultMap="CustomerProfileWithUserMap">
        SELECT 
            cp.id, cp.user_id, cp.company_name, cp.company_type, cp.industry,
            cp.business_license, cp.contact_person, cp.contact_phone, cp.contact_email,
            cp.address, cp.website, cp.api_quota_monthly, cp.api_quota_daily, cp.api_quota_hourly,
            cp.account_type, cp.account_status, cp.trial_expires_at, cp.created_time, cp.updated_time,
            u.username, u.real_name as user_real_name, u.email as user_email
        FROM customer_profiles cp
        LEFT JOIN sys_user u ON cp.user_id = u.id
        WHERE cp.user_id = #{userId} AND cp.deleted = 0
    </select>

    <!-- 获取即将到期的试用客户 -->
    <select id="selectExpiringTrialCustomers" resultType="com.sinoair.agent.entity.CustomerProfile">
        SELECT * FROM customer_profiles 
        WHERE trial_expires_at IS NOT NULL 
        AND trial_expires_at BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL #{days} DAY)
        AND account_status = 2 AND deleted = 0
        ORDER BY trial_expires_at ASC
    </select>

    <!-- 统计各账户类型的客户数量 -->
    <select id="countByAccountType" resultType="map">
        SELECT 
            account_type,
            COUNT(*) as count,
            CASE account_type
                WHEN 1 THEN '免费版'
                WHEN 2 THEN '基础版'
                WHEN 3 THEN '专业版'
                WHEN 4 THEN '企业版'
                ELSE '未知'
            END as type_name
        FROM customer_profiles 
        WHERE deleted = 0
        GROUP BY account_type
        ORDER BY account_type
    </select>

    <!-- 统计各账户状态的客户数量 -->
    <select id="countByAccountStatus" resultType="map">
        SELECT 
            account_status,
            COUNT(*) as count,
            CASE account_status
                WHEN 1 THEN '正常'
                WHEN 2 THEN '试用'
                WHEN 3 THEN '暂停'
                WHEN 4 THEN '欠费'
                ELSE '未知'
            END as status_name
        FROM customer_profiles 
        WHERE deleted = 0
        GROUP BY account_status
        ORDER BY account_status
    </select>

    <!-- 批量更新账户状态 -->
    <update id="batchUpdateAccountStatus">
        UPDATE customer_profiles SET account_status = #{accountStatus}
        WHERE user_id IN
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </update>

</mapper>
