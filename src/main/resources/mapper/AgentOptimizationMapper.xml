<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinoair.agent.mapper.AgentOptimizationMapper">

    <!-- 根据Agent ID查询优化配置列表（按创建时间倒序） -->
    <select id="findByAgentIdOrderByCreatedTimeDesc" resultType="com.sinoair.agent.entity.AgentOptimization">
        SELECT * FROM agent_optimizations 
        WHERE agent_id = #{agentId} AND deleted = 0 
        ORDER BY created_time DESC
    </select>

    <!-- 根据Agent ID查询激活的优化配置 -->
    <select id="findActiveByAgentId" resultType="com.sinoair.agent.entity.AgentOptimization">
        SELECT * FROM agent_optimizations 
        WHERE agent_id = #{agentId} AND is_active = 1 AND deleted = 0 
        LIMIT 1
    </select>

    <!-- 根据优化类型查询配置 -->
    <select id="findByAgentIdAndType" resultType="com.sinoair.agent.entity.AgentOptimization">
        SELECT * FROM agent_optimizations 
        WHERE agent_id = #{agentId} AND optimization_type = #{optimizationType} AND deleted = 0 
        ORDER BY created_time DESC
    </select>

    <!-- 统计Agent优化配置数量 -->
    <select id="countByAgentId" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM agent_optimizations 
        WHERE agent_id = #{agentId} AND deleted = 0
    </select>

    <!-- 激活指定的优化配置 -->
    <update id="activateOptimization">
        UPDATE agent_optimizations 
        SET is_active = CASE 
            WHEN id = #{optimizationId} THEN 1 
            ELSE 0 
        END 
        WHERE agent_id = #{agentId}
    </update>

    <!-- 停用所有优化配置 -->
    <update id="deactivateAllByAgentId">
        UPDATE agent_optimizations 
        SET is_active = 0 
        WHERE agent_id = #{agentId}
    </update>

    <!-- 查询优化效果统计 -->
    <select id="getOptimizationStats" resultType="java.util.Map">
        SELECT
            optimization_type,
            COUNT(*) as total_count,
            SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active_count,
            AVG(performance_score) as avg_performance_score
        FROM agent_optimizations
        WHERE agent_id = #{agentId} AND deleted = 0
        GROUP BY optimization_type
    </select>

    <!-- 根据优化名称查询配置 -->
    <select id="findByOptimizationName" resultType="com.sinoair.agent.entity.AgentOptimization">
        SELECT * FROM agent_optimizations
        WHERE optimization_name = #{optimizationName} AND deleted = 0
    </select>

    <!-- 统计优化配置总数 -->
    <select id="countOptimizations" resultType="java.lang.Long">
        SELECT COUNT(*) FROM agent_optimizations WHERE deleted = 0
    </select>

    <!-- 统计激活的优化配置数量 -->
    <select id="countActiveOptimizations" resultType="java.lang.Long">
        SELECT COUNT(*) FROM agent_optimizations
        WHERE is_active = 1 AND deleted = 0
    </select>

</mapper>
