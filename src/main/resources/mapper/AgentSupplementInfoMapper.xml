<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinoair.agent.mapper.AgentSupplementInfoMapper">

    <!-- 结果映射 -->
    <resultMap id="AgentSupplementInfoMap" type="com.sinoair.agent.entity.AgentSupplementInfo">
        <id column="id" property="id"/>
        <result column="agent_id" property="agentId"/>
        <result column="agent_introduction" property="agentIntroduction"/>
        <result column="usage_scenarios" property="usageScenarios"/>
        <result column="pain_points_solved" property="painPointsSolved"/>
        <result column="screenshot_urls" property="screenshotUrls"/>
        <result column="version_info" property="versionInfo"/>
        <result column="usage_statistics" property="usageStatistics"/>
        <result column="call_history_summary" property="callHistorySummary"/>
        <result column="status" property="status"/>
        <result column="created_time" property="createdTime"/>
        <result column="updated_time" property="updatedTime"/>
        <result column="deleted" property="deleted"/>
        <!-- 关联字段 -->
        <result column="agent_name" property="agentName"/>
        <result column="agent_code" property="agentCode"/>
        <result column="agent_description" property="agentDescription"/>
    </resultMap>

    <!-- 根据Agent ID获取补充资料详情 -->
    <select id="selectByAgentId" resultMap="AgentSupplementInfoMap">
        SELECT 
            asi.*,
            a.agent_name,
            a.agent_code,
            a.description as agent_description
        FROM agent_supplement_info asi
        LEFT JOIN agents a ON asi.agent_id = a.id
        WHERE asi.agent_id = #{agentId}
          AND asi.deleted = 0
        ORDER BY asi.updated_time DESC
        LIMIT 1
    </select>

</mapper>
