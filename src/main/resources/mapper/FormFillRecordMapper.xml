<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinoair.agent.mapper.FormFillRecordMapper">

    <!-- 结果映射 -->
    <resultMap id="FormFillRecordWithDetailsMap" type="com.sinoair.agent.entity.FormFillRecord">
        <id column="id" property="id"/>
        <result column="recognition_record_id" property="recognitionRecordId"/>
        <result column="page_binding_id" property="pageBindingId"/>
        <result column="target_url" property="targetUrl"/>
        <result column="fill_data" property="fillData"/>
        <result column="fill_result" property="fillResult"/>
        <result column="error_message" property="errorMessage"/>
        <result column="user_id" property="userId"/>
        <result column="user_feedback" property="userFeedback"/>
        <result column="feedback_time" property="feedbackTime"/>
        <result column="feedback_comment" property="feedbackComment"/>
        <result column="fill_time" property="fillTime"/>
        <result column="created_time" property="createdTime"/>
        <result column="updated_time" property="updatedTime"/>
        <result column="deleted" property="deleted"/>
        <result column="user_name" property="userName"/>
        <result column="user_real_name" property="userRealName"/>
        <result column="binding_name" property="bindingName"/>
    </resultMap>

    <!-- 根据用户ID查询回填记录 -->
    <select id="findByUserId" resultType="com.sinoair.agent.entity.FormFillRecord">
        SELECT * FROM form_fill_records 
        WHERE user_id = #{userId} AND deleted = 0 
        ORDER BY fill_time DESC
    </select>

    <!-- 根据识别记录ID查询回填记录 -->
    <select id="findByRecognitionRecordId" resultType="com.sinoair.agent.entity.FormFillRecord">
        SELECT * FROM form_fill_records 
        WHERE recognition_record_id = #{recognitionRecordId} AND deleted = 0 
        ORDER BY fill_time DESC
    </select>

    <!-- 根据页面绑定ID查询回填记录 -->
    <select id="findByPageBindingId" resultType="com.sinoair.agent.entity.FormFillRecord">
        SELECT * FROM form_fill_records 
        WHERE page_binding_id = #{pageBindingId} AND deleted = 0 
        ORDER BY fill_time DESC
    </select>

    <!-- 根据回填结果查询记录 -->
    <select id="findByFillResult" resultType="com.sinoair.agent.entity.FormFillRecord">
        SELECT * FROM form_fill_records 
        WHERE fill_result = #{fillResult} AND deleted = 0 
        ORDER BY fill_time DESC
    </select>

    <!-- 根据用户反馈查询记录 -->
    <select id="findByUserFeedback" resultType="com.sinoair.agent.entity.FormFillRecord">
        SELECT * FROM form_fill_records 
        WHERE user_feedback = #{userFeedback} AND deleted = 0 
        ORDER BY fill_time DESC
    </select>

    <!-- 分页查询回填记录（带详细信息） -->
    <select id="selectPageWithDetails" resultMap="FormFillRecordWithDetailsMap">
        SELECT ffr.*, u.username as user_name, u.real_name as user_real_name, 
               pb.binding_name
        FROM form_fill_records ffr 
        LEFT JOIN sys_user u ON ffr.user_id = u.id 
        LEFT JOIN page_bindings pb ON ffr.page_binding_id = pb.id 
        WHERE ffr.deleted = 0 
        <if test="keyword != null and keyword != ''">
            AND (pb.binding_name LIKE CONCAT('%', #{keyword}, '%') 
            OR ffr.target_url LIKE CONCAT('%', #{keyword}, '%') 
            OR u.real_name LIKE CONCAT('%', #{keyword}, '%')
            OR u.username LIKE CONCAT('%', #{keyword}, '%'))
        </if>
        <if test="fillResult != null">
            AND ffr.fill_result = #{fillResult}
        </if>
        <if test="userFeedback != null">
            AND ffr.user_feedback = #{userFeedback}
        </if>
        <if test="userId != null">
            AND ffr.user_id = #{userId}
        </if>
        <if test="startTime != null">
            AND ffr.fill_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND ffr.fill_time &lt;= #{endTime}
        </if>
        ORDER BY ffr.fill_time DESC
    </select>

    <!-- 根据ID查询回填记录（带详细信息） -->
    <select id="selectByIdWithDetails" resultMap="FormFillRecordWithDetailsMap">
        SELECT ffr.*, u.username as user_name, u.real_name as user_real_name, 
               pb.binding_name
        FROM form_fill_records ffr 
        LEFT JOIN sys_user u ON ffr.user_id = u.id 
        LEFT JOIN page_bindings pb ON ffr.page_binding_id = pb.id 
        WHERE ffr.id = #{id} AND ffr.deleted = 0
    </select>

    <!-- 统计回填记录总数 -->
    <select id="countRecords" resultType="java.lang.Long">
        SELECT COUNT(*) FROM form_fill_records WHERE deleted = 0
    </select>

    <!-- 统计成功回填记录数 -->
    <select id="countSuccessRecords" resultType="java.lang.Long">
        SELECT COUNT(*) FROM form_fill_records 
        WHERE fill_result = 1 AND deleted = 0
    </select>

    <!-- 统计各回填结果的记录数量 -->
    <select id="countByFillResult" resultType="java.util.Map">
        SELECT 
            fill_result as fillResult,
            CASE 
                WHEN fill_result = 1 THEN '成功'
                WHEN fill_result = 2 THEN '部分成功'
                WHEN fill_result = 3 THEN '失败'
                ELSE '未知'
            END as fillResultName,
            COUNT(*) as count
        FROM form_fill_records 
        WHERE deleted = 0 
        GROUP BY fill_result
        ORDER BY fill_result
    </select>

    <!-- 统计各用户反馈的记录数量 -->
    <select id="countByUserFeedback" resultType="java.util.Map">
        SELECT 
            CASE 
                WHEN user_feedback IS NULL THEN 'NO_FEEDBACK'
                WHEN user_feedback = 1 THEN 'LIKE'
                WHEN user_feedback = 0 THEN 'DISLIKE'
                ELSE 'UNKNOWN'
            END as feedbackType,
            CASE 
                WHEN user_feedback IS NULL THEN '无反馈'
                WHEN user_feedback = 1 THEN '点赞'
                WHEN user_feedback = 0 THEN '踩'
                ELSE '未知'
            END as feedbackName,
            COUNT(*) as count
        FROM form_fill_records 
        WHERE deleted = 0 
        GROUP BY user_feedback
        ORDER BY user_feedback DESC
    </select>

    <!-- 统计各用户的回填记录数量 -->
    <select id="countByUser" resultType="java.util.Map">
        SELECT ffr.user_id as userId, u.username, u.real_name as realName, 
               COUNT(*) as totalCount,
               SUM(CASE WHEN ffr.fill_result = 1 THEN 1 ELSE 0 END) as successCount,
               SUM(CASE WHEN ffr.user_feedback = 1 THEN 1 ELSE 0 END) as likeCount,
               SUM(CASE WHEN ffr.user_feedback = 0 THEN 1 ELSE 0 END) as dislikeCount
        FROM form_fill_records ffr 
        LEFT JOIN sys_user u ON ffr.user_id = u.id 
        WHERE ffr.deleted = 0 
        GROUP BY ffr.user_id, u.username, u.real_name
        ORDER BY totalCount DESC
    </select>

    <!-- 统计各页面绑定的回填记录数量 -->
    <select id="countByPageBinding" resultType="java.util.Map">
        SELECT ffr.page_binding_id as pageBindingId, pb.binding_name as bindingName, 
               COUNT(*) as totalCount,
               SUM(CASE WHEN ffr.fill_result = 1 THEN 1 ELSE 0 END) as successCount,
               SUM(CASE WHEN ffr.user_feedback = 1 THEN 1 ELSE 0 END) as likeCount
        FROM form_fill_records ffr 
        LEFT JOIN page_bindings pb ON ffr.page_binding_id = pb.id 
        WHERE ffr.deleted = 0 
        GROUP BY ffr.page_binding_id, pb.binding_name
        ORDER BY totalCount DESC
    </select>

    <!-- 查询最近的回填记录 -->
    <select id="findRecentRecords" resultMap="FormFillRecordWithDetailsMap">
        SELECT ffr.*, u.username as user_name, u.real_name as user_real_name, 
               pb.binding_name
        FROM form_fill_records ffr 
        LEFT JOIN sys_user u ON ffr.user_id = u.id 
        LEFT JOIN page_bindings pb ON ffr.page_binding_id = pb.id 
        WHERE ffr.deleted = 0 
        ORDER BY ffr.fill_time DESC 
        LIMIT #{limit}
    </select>

    <!-- 根据时间范围查询回填记录 -->
    <select id="findByTimeRange" resultType="com.sinoair.agent.entity.FormFillRecord">
        SELECT * FROM form_fill_records 
        WHERE deleted = 0 
        AND fill_time >= #{startTime} 
        AND fill_time &lt;= #{endTime}
        ORDER BY fill_time DESC
    </select>

    <!-- 查询用户的回填成功率 -->
    <select id="getUserFillSuccessRate" resultType="java.util.Map">
        SELECT 
            COUNT(*) as totalCount,
            SUM(CASE WHEN fill_result = 1 THEN 1 ELSE 0 END) as successCount,
            ROUND(SUM(CASE WHEN fill_result = 1 THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as successRate
        FROM form_fill_records 
        WHERE user_id = #{userId} AND deleted = 0
    </select>

    <!-- 查询页面绑定的回填成功率 -->
    <select id="getPageBindingFillSuccessRate" resultType="java.util.Map">
        SELECT 
            COUNT(*) as totalCount,
            SUM(CASE WHEN fill_result = 1 THEN 1 ELSE 0 END) as successCount,
            ROUND(SUM(CASE WHEN fill_result = 1 THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as successRate
        FROM form_fill_records 
        WHERE page_binding_id = #{pageBindingId} AND deleted = 0
    </select>

    <!-- 更新用户反馈 -->
    <update id="updateUserFeedback">
        UPDATE form_fill_records 
        SET user_feedback = #{userFeedback},
            feedback_time = #{feedbackTime},
            feedback_comment = #{feedbackComment},
            updated_time = NOW()
        WHERE id = #{id} AND deleted = 0
    </update>

    <!-- 批量查询回填记录 -->
    <select id="findByIds" resultType="com.sinoair.agent.entity.FormFillRecord">
        SELECT * FROM form_fill_records 
        WHERE id IN 
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND deleted = 0
        ORDER BY fill_time DESC
    </select>

    <!-- 根据目标URL查询回填记录 -->
    <select id="findByTargetUrl" resultType="com.sinoair.agent.entity.FormFillRecord">
        SELECT * FROM form_fill_records 
        WHERE target_url = #{targetUrl} AND deleted = 0 
        ORDER BY fill_time DESC
    </select>

    <!-- 查询热门回填页面（按回填次数排序） -->
    <select id="findPopularFillPages" resultType="java.util.Map">
        SELECT 
            target_url as targetUrl,
            COUNT(*) as fillCount,
            SUM(CASE WHEN fill_result = 1 THEN 1 ELSE 0 END) as successCount,
            SUM(CASE WHEN user_feedback = 1 THEN 1 ELSE 0 END) as likeCount,
            MAX(fill_time) as lastFillTime
        FROM form_fill_records 
        WHERE deleted = 0 
        GROUP BY target_url
        ORDER BY fillCount DESC
        LIMIT #{limit}
    </select>

    <!-- 查询用户回填活跃度统计 -->
    <select id="getUserFillActivity" resultType="java.util.Map">
        SELECT 
            DATE(fill_time) as fillDate,
            COUNT(*) as fillCount,
            COUNT(DISTINCT user_id) as activeUsers
        FROM form_fill_records 
        WHERE deleted = 0 
        AND fill_time >= DATE_SUB(NOW(), INTERVAL #{days} DAY)
        GROUP BY DATE(fill_time)
        ORDER BY fillDate DESC
    </select>

    <!-- 根据Agent ID统计好评差评数量 -->
    <select id="countFeedbackByAgentId" resultType="java.util.Map">
        SELECT
            SUM(CASE WHEN ffr.user_feedback = 1 THEN 1 ELSE 0 END) as goodCount,
            SUM(CASE WHEN ffr.user_feedback = 0 THEN 1 ELSE 0 END) as badCount,
            COUNT(CASE WHEN ffr.user_feedback IS NOT NULL THEN 1 END) as totalFeedbackCount
        FROM form_fill_records ffr
        INNER JOIN recognition_records rr ON ffr.recognition_record_id = rr.id
        WHERE rr.agent_id = #{agentId}
        AND ffr.deleted = 0
        AND rr.deleted = 0
    </select>

</mapper>
