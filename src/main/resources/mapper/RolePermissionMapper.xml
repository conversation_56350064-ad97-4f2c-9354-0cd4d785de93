<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinoair.agent.mapper.RolePermissionMapper">

    <!-- 批量插入角色权限关联 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO sys_role_permission (role_id, permission_id, created_by, created_time)
        VALUES
        <foreach collection="rolePermissions" item="item" separator=",">
            (#{item.roleId}, #{item.permissionId}, #{item.createdBy}, #{item.createdTime})
        </foreach>
    </insert>

    <!-- 根据角色ID删除角色权限关联 -->
    <delete id="deleteByRoleId">
        DELETE FROM sys_role_permission WHERE role_id = #{roleId}
    </delete>

    <!-- 根据权限ID删除角色权限关联 -->
    <delete id="deleteByPermissionId">
        DELETE FROM sys_role_permission WHERE permission_id = #{permissionId}
    </delete>

    <!-- 根据角色ID查询权限ID列表 -->
    <select id="selectPermissionIdsByRoleId" resultType="java.lang.Long">
        SELECT permission_id FROM sys_role_permission WHERE role_id = #{roleId}
    </select>

    <!-- 根据权限ID查询角色ID列表 -->
    <select id="selectRoleIdsByPermissionId" resultType="java.lang.Long">
        SELECT role_id FROM sys_role_permission WHERE permission_id = #{permissionId}
    </select>

</mapper>
