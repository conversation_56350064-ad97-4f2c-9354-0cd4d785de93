<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinoair.agent.mapper.AgentMapper">

    <!-- 结果映射 -->
    <resultMap id="AgentWithDetailsMap" type="com.sinoair.agent.entity.Agent">
        <id column="id" property="id"/>
        <result column="agent_name" property="agentName"/>
        <result column="agent_code" property="agentCode"/>
        <result column="description" property="description"/>
        <result column="category_id" property="categoryId"/>
        <result column="business_type_id" property="businessTypeId"/>
        <result column="agent_type" property="agentType"/>
        <result column="config" property="config"/>
        <result column="prompt_template" property="promptTemplate"/>
        <result column="json_template" property="jsonTemplate"/>
        <result column="template_id" property="templateId"/>
        <result column="version" property="version"/>
        <result column="status" property="status"/>
        <result column="creator_id" property="creatorId"/>
        <result column="published_time" property="publishedTime"/>
        <result column="created_time" property="createdTime"/>
        <result column="updated_time" property="updatedTime"/>
        <result column="deleted" property="deleted"/>
        <result column="category_name" property="categoryName"/>
        <result column="business_type_name" property="businessTypeName"/>
        <result column="creator_name" property="creatorName"/>
    </resultMap>

    <!-- 根据Agent代码查找Agent -->
    <select id="findByAgentCode" resultMap="AgentWithDetailsMap">
        SELECT a.*, c.category_name, bt.type_name as business_type_name, u.real_name as creator_name
        FROM agents a
        LEFT JOIN agent_categories c ON a.category_id = c.id
        LEFT JOIN business_types bt ON a.business_type_id = bt.id
        LEFT JOIN sys_user u ON a.creator_id = u.id
        WHERE a.agent_code = #{agentCode} AND a.deleted = 0
    </select>

    <!-- 根据Agent名称查找Agent -->
    <select id="findByAgentName" resultMap="AgentWithDetailsMap">
        SELECT a.*, c.category_name, bt.type_name as business_type_name, u.real_name as creator_name
        FROM agents a
        LEFT JOIN agent_categories c ON a.category_id = c.id
        LEFT JOIN business_types bt ON a.business_type_id = bt.id
        LEFT JOIN sys_user u ON a.creator_id = u.id
        WHERE a.agent_name = #{agentName} AND a.deleted = 0
    </select>

    <!-- 分页查询Agent列表（带详细信息） -->
    <select id="selectPageWithDetails" resultMap="AgentWithDetailsMap">
        SELECT a.*, c.category_name, bt.type_name as business_type_name, u.real_name as creator_name
        FROM agents a
        LEFT JOIN agent_categories c ON a.category_id = c.id
        LEFT JOIN business_types bt ON a.business_type_id = bt.id
        LEFT JOIN sys_user u ON a.creator_id = u.id
        WHERE a.deleted = 0
        <if test="keyword != null and keyword != ''">
            AND (a.agent_name LIKE CONCAT('%', #{keyword}, '%')
            OR a.agent_code LIKE CONCAT('%', #{keyword}, '%')
            OR a.description LIKE CONCAT('%', #{keyword}, '%'))
        </if>
        <if test="categoryId != null">
            AND a.category_id = #{categoryId}
        </if>
        <if test="businessTypeId != null">
            AND a.business_type_id = #{businessTypeId}
        </if>
        <if test="status != null">
            AND a.status = #{status}
        </if>
        <if test="creatorId != null">
            AND a.creator_id = #{creatorId}
        </if>
        ORDER BY a.created_time DESC
    </select>

    <!-- 根据分类ID查询Agent列表 -->
    <select id="findByCategoryId" resultMap="AgentWithDetailsMap">
        SELECT a.*, c.category_name, bt.type_name as business_type_name, u.real_name as creator_name 
        FROM agents a 
        LEFT JOIN agent_categories c ON a.category_id = c.id 
        LEFT JOIN business_types bt ON a.business_type_id = bt.id 
        LEFT JOIN sys_user u ON a.creator_id = u.id 
        WHERE a.category_id = #{categoryId} AND a.deleted = 0 
        ORDER BY a.created_time DESC
    </select>

    <!-- 根据业务类型ID查询Agent列表 -->
    <select id="findByBusinessTypeId" resultMap="AgentWithDetailsMap">
        SELECT a.*, c.category_name, bt.type_name as business_type_name, u.real_name as creator_name 
        FROM agents a 
        LEFT JOIN agent_categories c ON a.category_id = c.id 
        LEFT JOIN business_types bt ON a.business_type_id = bt.id 
        LEFT JOIN sys_user u ON a.creator_id = u.id 
        WHERE a.business_type_id = #{businessTypeId} AND a.deleted = 0 
        ORDER BY a.created_time DESC
    </select>

    <!-- 根据状态查询Agent列表 -->
    <select id="findByStatus" resultMap="AgentWithDetailsMap">
        SELECT a.*, c.category_name, bt.type_name as business_type_name, u.real_name as creator_name 
        FROM agents a 
        LEFT JOIN agent_categories c ON a.category_id = c.id 
        LEFT JOIN business_types bt ON a.business_type_id = bt.id 
        LEFT JOIN sys_user u ON a.creator_id = u.id 
        WHERE a.status = #{status} AND a.deleted = 0 
        ORDER BY a.created_time DESC
    </select>

    <!-- 根据创建者ID查询Agent列表 -->
    <select id="findByCreatorId" resultMap="AgentWithDetailsMap">
        SELECT a.*, c.category_name, bt.type_name as business_type_name, u.real_name as creator_name 
        FROM agents a 
        LEFT JOIN agent_categories c ON a.category_id = c.id 
        LEFT JOIN business_types bt ON a.business_type_id = bt.id 
        LEFT JOIN sys_user u ON a.creator_id = u.id 
        WHERE a.creator_id = #{creatorId} AND a.deleted = 0 
        ORDER BY a.created_time DESC
    </select>

    <!-- 查询所有已发布的Agent -->
    <select id="findAllPublished" resultMap="AgentWithDetailsMap">
        SELECT a.*, c.category_name, bt.type_name as business_type_name, u.real_name as creator_name 
        FROM agents a 
        LEFT JOIN agent_categories c ON a.category_id = c.id 
        LEFT JOIN business_types bt ON a.business_type_id = bt.id 
        LEFT JOIN sys_user u ON a.creator_id = u.id 
        WHERE a.status = 3 AND a.deleted = 0 
        ORDER BY a.published_time DESC
    </select>

    <!-- 统计Agent数量 -->
    <select id="countAgents" resultType="java.lang.Long">
        SELECT COUNT(*) FROM agents WHERE deleted = 0
    </select>

    <!-- 统计已发布Agent数量 -->
    <select id="countPublishedAgents" resultType="java.lang.Long">
        SELECT COUNT(*) FROM agents WHERE status = 3 AND deleted = 0
    </select>

    <!-- 统计各状态Agent数量 -->
    <select id="countByStatus" resultType="java.util.Map">
        SELECT status, COUNT(*) as count 
        FROM agents 
        WHERE deleted = 0 
        GROUP BY status
    </select>

    <!-- 统计各分类Agent数量 -->
    <select id="countByCategory" resultType="java.util.Map">
        SELECT c.category_name, COUNT(a.id) as count 
        FROM agent_categories c 
        LEFT JOIN agents a ON c.id = a.category_id AND a.deleted = 0 
        WHERE c.deleted = 0 
        GROUP BY c.id, c.category_name
    </select>

</mapper>
