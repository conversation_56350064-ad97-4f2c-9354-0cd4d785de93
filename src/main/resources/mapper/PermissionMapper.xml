<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinoair.agent.mapper.PermissionMapper">

    <!-- 根据用户ID查询权限列表 -->
    <select id="selectPermissionsByUserId" resultType="com.sinoair.agent.entity.Permission">
        SELECT DISTINCT p.* 
        FROM sys_permission p 
        INNER JOIN sys_role_permission rp ON p.id = rp.permission_id 
        INNER JOIN sys_user_role ur ON rp.role_id = ur.role_id 
        WHERE ur.user_id = #{userId} AND p.status = 1 
        ORDER BY p.sort_order ASC
    </select>

    <!-- 根据角色ID查询权限列表 -->
    <select id="selectPermissionsByRoleId" resultType="com.sinoair.agent.entity.Permission">
        SELECT p.* 
        FROM sys_permission p 
        INNER JOIN sys_role_permission rp ON p.id = rp.permission_id 
        WHERE rp.role_id = #{roleId} AND p.status = 1 
        ORDER BY p.sort_order ASC
    </select>

    <!-- 查询所有权限（树形结构） -->
    <select id="selectAllPermissions" resultType="com.sinoair.agent.entity.Permission">
        SELECT * FROM sys_permission 
        WHERE status = 1 
        ORDER BY sort_order ASC
    </select>

    <!-- 根据权限类型查询权限 -->
    <select id="selectPermissionsByType" resultType="com.sinoair.agent.entity.Permission">
        SELECT * FROM sys_permission 
        WHERE permission_type = #{permissionType} AND status = 1 
        ORDER BY sort_order ASC
    </select>

    <!-- 根据父权限ID查询子权限 -->
    <select id="selectPermissionsByParentId" resultType="com.sinoair.agent.entity.Permission">
        SELECT * FROM sys_permission 
        WHERE parent_id = #{parentId} AND status = 1 
        ORDER BY sort_order ASC
    </select>

    <!-- 根据权限编码查询权限 -->
    <select id="selectByPermissionCode" resultType="com.sinoair.agent.entity.Permission">
        SELECT * FROM sys_permission 
        WHERE permission_code = #{permissionCode}
    </select>

    <!-- 查询用户的菜单权限（树形结构） -->
    <select id="selectMenuPermissionsByUserId" resultType="com.sinoair.agent.entity.Permission">
        SELECT DISTINCT p.* 
        FROM sys_permission p 
        INNER JOIN sys_role_permission rp ON p.id = rp.permission_id 
        INNER JOIN sys_user_role ur ON rp.role_id = ur.role_id 
        WHERE ur.user_id = #{userId} AND p.status = 1 AND p.permission_type = 1 
        ORDER BY p.sort_order ASC
    </select>

    <!-- 查询用户的按钮权限（包含菜单权限和功能权限） -->
    <select id="selectButtonPermissionsByUserId" resultType="java.lang.String">
        SELECT DISTINCT p.permission_code
        FROM sys_permission p
        INNER JOIN sys_role_permission rp ON p.id = rp.permission_id
        INNER JOIN sys_user_role ur ON rp.role_id = ur.role_id
        WHERE ur.user_id = #{userId} AND p.status = 1 AND p.permission_code IS NOT NULL AND p.permission_code != ''
    </select>

</mapper>
