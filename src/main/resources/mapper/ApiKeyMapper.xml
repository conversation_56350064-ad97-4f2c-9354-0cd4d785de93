<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinoair.agent.mapper.ApiKeyMapper">

    <!-- 结果映射 -->
    <resultMap id="ApiKeyWithUserMap" type="com.sinoair.agent.entity.ApiKey">
        <id column="id" property="id"/>
        <result column="key_id" property="keyId"/>
        <result column="key_secret" property="keySecret"/>
        <result column="key_name" property="keyName"/>
        <result column="description" property="description"/>
        <result column="user_id" property="userId"/>
        <result column="status" property="status"/>
        <result column="rate_limit" property="rateLimit"/>
        <result column="daily_limit" property="dailyLimit"/>
        <result column="monthly_limit" property="monthlyLimit"/>
        <result column="allowed_ips" property="allowedIps"/>
        <result column="allowed_domains" property="allowedDomains"/>
        <result column="expires_at" property="expiresAt"/>
        <result column="last_used_at" property="lastUsedAt"/>
        <result column="last_used_ip" property="lastUsedIp"/>
        <result column="created_time" property="createdTime"/>
        <result column="updated_time" property="updatedTime"/>
        <result column="deleted" property="deleted"/>
        <result column="username" property="username"/>
        <result column="user_real_name" property="userRealName"/>
    </resultMap>

    <!-- 分页查询API Key列表(包含用户信息) -->
    <select id="selectApiKeyPageWithUser" resultMap="ApiKeyWithUserMap">
        SELECT 
            ak.id, ak.key_id, ak.key_name, ak.description, ak.user_id, ak.status,
            ak.rate_limit, ak.daily_limit, ak.monthly_limit, ak.allowed_ips, ak.allowed_domains,
            ak.expires_at, ak.last_used_at, ak.last_used_ip, ak.created_time, ak.updated_time,
            u.username, u.real_name as user_real_name
        FROM api_keys ak
        LEFT JOIN sys_user u ON ak.user_id = u.id
        WHERE ak.deleted = 0
        <if test="userId != null">
            AND ak.user_id = #{userId}
        </if>
        <if test="keyName != null and keyName != ''">
            AND ak.key_name LIKE CONCAT('%', #{keyName}, '%')
        </if>
        <if test="status != null">
            AND ak.status = #{status}
        </if>
        ORDER BY ak.created_time DESC
    </select>

    <!-- 根据keyId查询API Key(包含用户信息) -->
    <select id="selectByKeyIdWithUser" resultMap="ApiKeyWithUserMap">
        SELECT 
            ak.id, ak.key_id, ak.key_secret, ak.key_name, ak.description, ak.user_id, ak.status,
            ak.rate_limit, ak.daily_limit, ak.monthly_limit, ak.allowed_ips, ak.allowed_domains,
            ak.expires_at, ak.last_used_at, ak.last_used_ip, ak.created_time, ak.updated_time,
            u.username, u.real_name as user_real_name
        FROM api_keys ak
        LEFT JOIN sys_user u ON ak.user_id = u.id
        WHERE ak.key_id = #{keyId} AND ak.deleted = 0
    </select>

    <!-- 根据keyId和keySecret查询API Key -->
    <select id="selectByKeyIdAndSecret" resultType="com.sinoair.agent.entity.ApiKey">
        SELECT * FROM api_keys 
        WHERE key_id = #{keyId} AND key_secret = #{keySecret} AND deleted = 0
    </select>

    <!-- 更新最后使用时间和IP -->
    <update id="updateLastUsed">
        UPDATE api_keys 
        SET last_used_at = #{lastUsedAt}, last_used_ip = #{lastUsedIp}
        WHERE id = #{id}
    </update>

    <!-- 获取用户的API Key数量 -->
    <select id="countByUserId" resultType="int">
        SELECT COUNT(*) FROM api_keys 
        WHERE user_id = #{userId} AND deleted = 0
    </select>

    <!-- 获取即将过期的API Key列表 -->
    <select id="selectExpiringKeys" resultType="com.sinoair.agent.entity.ApiKey">
        SELECT * FROM api_keys 
        WHERE expires_at IS NOT NULL 
        AND expires_at BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL #{days} DAY)
        AND status = 1 AND deleted = 0
        ORDER BY expires_at ASC
    </select>

    <!-- 批量更新API Key状态 -->
    <update id="batchUpdateStatus">
        UPDATE api_keys SET status = #{status}
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

</mapper>
