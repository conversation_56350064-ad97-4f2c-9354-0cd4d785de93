<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinoair.agent.mapper.AgentCategoryMapper">

    <!-- 根据分类代码查找分类 -->
    <select id="findByCategoryCode" resultType="com.sinoair.agent.entity.AgentCategory">
        SELECT * FROM agent_categories 
        WHERE category_code = #{categoryCode} AND deleted = 0
    </select>

    <!-- 根据父级ID查询子分类 -->
    <select id="findByParentId" resultType="com.sinoair.agent.entity.AgentCategory">
        SELECT * FROM agent_categories 
        WHERE parent_id = #{parentId} AND deleted = 0 
        ORDER BY sort_order ASC
    </select>

    <!-- 查询所有根分类 -->
    <select id="findRootCategories" resultType="com.sinoair.agent.entity.AgentCategory">
        SELECT * FROM agent_categories 
        WHERE parent_id = 0 AND deleted = 0 
        ORDER BY sort_order ASC
    </select>

    <!-- 查询所有可用分类 -->
    <select id="findAllActive" resultType="com.sinoair.agent.entity.AgentCategory">
        SELECT * FROM agent_categories 
        WHERE status = 1 AND deleted = 0 
        ORDER BY sort_order ASC
    </select>

    <!-- 查询分类树形结构 -->
    <select id="findCategoryTree" resultType="com.sinoair.agent.entity.AgentCategory">
        SELECT * FROM agent_categories 
        WHERE deleted = 0 
        ORDER BY parent_id ASC, sort_order ASC
    </select>

    <!-- 统计分类数量 -->
    <select id="countCategories" resultType="java.lang.Long">
        SELECT COUNT(*) FROM agent_categories WHERE deleted = 0
    </select>

    <!-- 统计各分类下的Agent数量 -->
    <select id="countAgentsByCategory" resultType="java.util.Map">
        SELECT c.category_name, COUNT(a.id) as agent_count 
        FROM agent_categories c 
        LEFT JOIN agents a ON c.id = a.category_id AND a.deleted = 0 
        WHERE c.deleted = 0 
        GROUP BY c.id, c.category_name 
        ORDER BY c.sort_order ASC
    </select>

    <!-- 检查分类是否有子分类 -->
    <select id="hasChildren" resultType="java.lang.Boolean">
        SELECT COUNT(*) > 0 FROM agent_categories 
        WHERE parent_id = #{categoryId} AND deleted = 0
    </select>

    <!-- 检查分类是否有关联的Agent -->
    <select id="hasAgents" resultType="java.lang.Boolean">
        SELECT COUNT(*) > 0 FROM agents 
        WHERE category_id = #{categoryId} AND deleted = 0
    </select>

</mapper>
