<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinoair.agent.mapper.ApiKeyUsageMapper">

    <!-- 分页查询API Key使用记录 -->
    <select id="selectUsagePageWithApiKey" resultType="com.sinoair.agent.entity.ApiKeyUsage">
        SELECT 
            aku.id, aku.api_key_id, aku.endpoint, aku.method, aku.request_ip,
            aku.user_agent, aku.request_size, aku.response_size, aku.response_time,
            aku.status_code, aku.error_message, aku.request_time
        FROM api_key_usage aku
        LEFT JOIN api_keys ak ON aku.api_key_id = ak.id
        WHERE 1=1
        <if test="apiKeyId != null">
            AND aku.api_key_id = #{apiKeyId}
        </if>
        <if test="startTime != null">
            AND aku.request_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND aku.request_time &lt;= #{endTime}
        </if>
        <if test="endpoint != null and endpoint != ''">
            AND aku.endpoint LIKE CONCAT('%', #{endpoint}, '%')
        </if>
        <if test="statusCode != null">
            AND aku.status_code = #{statusCode}
        </if>
        ORDER BY aku.request_time DESC
    </select>

    <!-- 统计API Key在指定时间段内的使用次数 -->
    <select id="countUsageByTimeRange" resultType="long">
        SELECT COUNT(*) FROM api_key_usage
        WHERE api_key_id = #{apiKeyId}
        <if test="startTime != null">
            AND request_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND request_time &lt;= #{endTime}
        </if>
    </select>

    <!-- 获取API Key的每日使用统计 -->
    <select id="getDailyUsageStats" resultType="map">
        SELECT 
            DATE(request_time) as date,
            COUNT(*) as total_requests,
            COUNT(CASE WHEN status_code = 200 THEN 1 END) as success_requests,
            COUNT(CASE WHEN status_code != 200 THEN 1 END) as failed_requests,
            AVG(response_time) as avg_response_time
        FROM api_key_usage
        WHERE api_key_id = #{apiKeyId}
        AND DATE(request_time) BETWEEN #{startDate} AND #{endDate}
        GROUP BY DATE(request_time)
        ORDER BY DATE(request_time)
    </select>

    <!-- 获取API Key的端点使用统计 -->
    <select id="getEndpointUsageStats" resultType="map">
        SELECT 
            endpoint,
            COUNT(*) as request_count,
            COUNT(CASE WHEN status_code = 200 THEN 1 END) as success_count,
            AVG(response_time) as avg_response_time
        FROM api_key_usage
        WHERE api_key_id = #{apiKeyId}
        <if test="startTime != null">
            AND request_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND request_time &lt;= #{endTime}
        </if>
        GROUP BY endpoint
        ORDER BY request_count DESC
    </select>

    <!-- 获取API Key的错误统计 -->
    <select id="getErrorStats" resultType="map">
        SELECT 
            status_code,
            error_message,
            COUNT(*) as error_count
        FROM api_key_usage
        WHERE api_key_id = #{apiKeyId}
        AND status_code != 200
        <if test="startTime != null">
            AND request_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND request_time &lt;= #{endTime}
        </if>
        GROUP BY status_code, error_message
        ORDER BY error_count DESC
    </select>

    <!-- 清理过期的使用记录 -->
    <delete id="deleteExpiredRecords">
        DELETE FROM api_key_usage
        WHERE request_time &lt; #{beforeTime}
    </delete>

    <!-- 批量插入使用记录 -->
    <insert id="batchInsert">
        INSERT INTO api_key_usage (
            api_key_id, endpoint, method, request_ip, user_agent,
            request_size, response_size, response_time, status_code,
            error_message, request_time
        ) VALUES
        <foreach collection="usageList" item="usage" separator=",">
            (
                #{usage.apiKeyId}, #{usage.endpoint}, #{usage.method},
                #{usage.requestIp}, #{usage.userAgent}, #{usage.requestSize},
                #{usage.responseSize}, #{usage.responseTime}, #{usage.statusCode},
                #{usage.errorMessage}, #{usage.requestTime}
            )
        </foreach>
    </insert>

</mapper>
