<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinoair.agent.mapper.AgentVersionMapper">

    <!-- 结果映射 -->
    <resultMap id="AgentVersionWithCreatorMap" type="com.sinoair.agent.entity.AgentVersion">
        <id column="id" property="id"/>
        <result column="agent_id" property="agentId"/>
        <result column="version_number" property="versionNumber"/>
        <result column="agent_name" property="agentName"/>
        <result column="agent_code" property="agentCode"/>
        <result column="description" property="description"/>
        <result column="category_id" property="categoryId"/>
        <result column="business_type_id" property="businessTypeId"/>
        <result column="agent_type" property="agentType"/>
        <result column="config" property="config"/>
        <result column="prompt_template" property="promptTemplate"/>
        <result column="json_template" property="jsonTemplate"/>
        <result column="template_id" property="templateId"/>
        <result column="status" property="status"/>
        <result column="is_current" property="isCurrent"/>
        <result column="change_log" property="changeLog"/>
        <result column="creator_id" property="creatorId"/>
        <result column="published_time" property="publishedTime"/>
        <result column="created_time" property="createdTime"/>
        <result column="updated_time" property="updatedTime"/>
        <result column="deleted" property="deleted"/>
        <!-- 非数据库字段 -->
        <result column="creator_name" property="creatorName"/>
    </resultMap>

    <!-- 根据AgentID查询版本历史 -->
    <select id="findByAgentId" resultMap="AgentVersionWithCreatorMap">
        SELECT av.*, u.real_name as creator_name
        FROM agent_versions av
        LEFT JOIN sys_user u ON av.creator_id = u.id
        WHERE av.agent_id = #{agentId} AND av.deleted = 0
        ORDER BY av.created_time DESC
    </select>

    <!-- 根据AgentID查询当前版本 -->
    <select id="findCurrentByAgentId" resultMap="AgentVersionWithCreatorMap">
        SELECT av.*, u.real_name as creator_name
        FROM agent_versions av
        LEFT JOIN sys_user u ON av.creator_id = u.id
        WHERE av.agent_id = #{agentId} AND av.is_current = 1 AND av.deleted = 0
    </select>

    <!-- 根据版本号查询版本 -->
    <select id="findByAgentIdAndVersion" resultMap="AgentVersionWithCreatorMap">
        SELECT av.*, u.real_name as creator_name
        FROM agent_versions av
        LEFT JOIN sys_user u ON av.creator_id = u.id
        WHERE av.agent_id = #{agentId} AND av.version_number = #{versionNumber} AND av.deleted = 0
    </select>

    <!-- 根据AgentID和版本号查询版本（别名方法） -->
    <select id="findByAgentIdAndVersionNumber" resultMap="AgentVersionWithCreatorMap">
        SELECT av.*, u.real_name as creator_name
        FROM agent_versions av
        LEFT JOIN sys_user u ON av.creator_id = u.id
        WHERE av.agent_id = #{agentId} AND av.version_number = #{versionNumber} AND av.deleted = 0
    </select>

    <!-- 统计Agent版本数量 -->
    <select id="countByAgentId" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM agent_versions 
        WHERE agent_id = #{agentId} AND deleted = 0
    </select>

    <!-- 查询最新版本号 -->
    <select id="getLatestVersionNumber" resultType="java.lang.String">
        SELECT version_number FROM agent_versions 
        WHERE agent_id = #{agentId} AND deleted = 0 
        ORDER BY created_time DESC 
        LIMIT 1
    </select>

    <!-- 设置当前版本 -->
    <update id="setCurrentVersion">
        UPDATE agent_versions 
        SET is_current = CASE 
            WHEN id = #{versionId} THEN 1 
            ELSE 0 
        END 
        WHERE agent_id = #{agentId}
    </update>

    <!-- 清除所有当前版本标记 -->
    <update id="clearCurrentVersions">
        UPDATE agent_versions
        SET is_current = 0
        WHERE agent_id = #{agentId}
    </update>

    <!-- 分页查询版本历史 -->
    <select id="selectPageByAgentId" resultMap="AgentVersionWithCreatorMap">
        SELECT av.*, u.real_name as creator_name
        FROM agent_versions av
        LEFT JOIN sys_user u ON av.creator_id = u.id
        WHERE av.agent_id = #{agentId} AND av.deleted = 0
        ORDER BY av.created_time DESC
    </select>

    <!-- 查询已发布的版本 -->
    <select id="findPublishedByAgentId" resultMap="AgentVersionWithCreatorMap">
        SELECT av.*, u.real_name as creator_name
        FROM agent_versions av
        LEFT JOIN sys_user u ON av.creator_id = u.id
        WHERE av.agent_id = #{agentId} AND av.status = 3 AND av.deleted = 0
        ORDER BY av.published_time DESC
    </select>

    <!-- 根据AgentID查询版本历史（按创建时间倒序） -->
    <select id="findByAgentIdOrderByCreatedTimeDesc" resultMap="AgentVersionWithCreatorMap">
        SELECT av.*, u.real_name as creator_name
        FROM agent_versions av
        LEFT JOIN sys_user u ON av.creator_id = u.id
        WHERE av.agent_id = #{agentId} AND av.deleted = 0
        ORDER BY av.created_time DESC
    </select>

    <!-- 清除指定Agent的所有版本的当前标记 -->
    <update id="clearCurrentVersionFlag">
        UPDATE agent_versions SET is_current = 0 WHERE agent_id = #{agentId}
    </update>

    <!-- 根据AgentID查询最新已发布版本 -->
    <select id="findLatestPublishedByAgentId" resultMap="AgentVersionWithCreatorMap">
        SELECT av.*, u.real_name as creator_name
        FROM agent_versions av
        LEFT JOIN sys_user u ON av.creator_id = u.id
        WHERE av.agent_id = #{agentId} AND av.status = 3 AND av.deleted = 0
        ORDER BY av.published_time DESC
        LIMIT 1
    </select>

    <!-- 根据AgentID查询最新版本（不限状态） -->
    <select id="findLatestByAgentId" resultMap="AgentVersionWithCreatorMap">
        SELECT av.*, u.real_name as creator_name
        FROM agent_versions av
        LEFT JOIN sys_user u ON av.creator_id = u.id
        WHERE av.agent_id = #{agentId} AND av.deleted = 0
        ORDER BY av.created_time DESC
        LIMIT 1
    </select>

</mapper>
