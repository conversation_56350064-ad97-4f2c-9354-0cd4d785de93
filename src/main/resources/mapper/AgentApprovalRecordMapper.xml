<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinoair.agent.mapper.AgentApprovalRecordMapper">

    <!-- 结果映射 -->
    <resultMap id="AgentApprovalRecordMap" type="com.sinoair.agent.entity.AgentApprovalRecord">
        <id column="id" property="id"/>
        <result column="agent_id" property="agentId"/>
        <result column="agent_version_id" property="agentVersionId"/>
        <result column="approval_status" property="approvalStatus"/>
        <result column="approval_opinion" property="approvalOpinion"/>
        <result column="approver_id" property="approverId"/>
        <result column="approver_name" property="approverName"/>
        <result column="approval_time" property="approvalTime"/>
        <result column="created_time" property="createdTime"/>
        <result column="updated_time" property="updatedTime"/>
        <result column="deleted" property="deleted"/>
        <!-- 关联字段 -->
        <result column="agent_name" property="agentName"/>
        <result column="agent_code" property="agentCode"/>
        <result column="agent_description" property="agentDescription"/>
        <result column="category_name" property="categoryName"/>
        <result column="creator_name" property="creatorName"/>
        <result column="version_number" property="versionNumber"/>
    </resultMap>

    <!-- 审批列表结果映射 -->
    <resultMap id="AgentApprovalVOMap" type="com.sinoair.agent.dto.response.AgentApprovalVO">
        <id column="id" property="id"/>
        <result column="agent_name" property="agentName"/>
        <result column="agent_code" property="agentCode"/>
        <result column="description" property="description"/>
        <result column="approval_status" property="approvalStatus"/>
        <result column="category_name" property="categoryName"/>
        <result column="model_info" property="modelInfo"/>
        <result column="purpose" property="purpose"/>
        <result column="has_api_interface" property="hasApiInterface"/>
        <result column="has_chrome_plugin" property="hasChromePlugin"/>
        <result column="subscription_count" property="subscriptionCount"/>
        <result column="submit_time" property="submitTime"/>
        <result column="creator_name" property="creatorName"/>
        <result column="version" property="version"/>
        <result column="upgrade_content" property="upgradeContent"/>
        <result column="last_approval_opinion" property="lastApprovalOpinion"/>
        <result column="last_approver_name" property="lastApproverName"/>
        <result column="last_approval_time" property="lastApprovalTime"/>
    </resultMap>

    <!-- 分页查询Agent审批列表 -->
    <select id="selectApprovalList" resultMap="AgentApprovalVOMap">
        SELECT
            a.id,
            a.agent_name,
            a.agent_code,
            a.description,
            a.approval_status,
            ac.category_name,
            CONCAT(
                CASE
                    WHEN JSON_EXTRACT(COALESCE(pending_av.config, a.config), '$.provider') = 'qianwen' THEN '千问大模型'
                    WHEN JSON_EXTRACT(COALESCE(pending_av.config, a.config), '$.provider') = 'openai' THEN 'OpenAI'
                    WHEN JSON_EXTRACT(COALESCE(pending_av.config, a.config), '$.provider') = 'deepseek' THEN 'DeepSeek'
                    ELSE '未知模型'
                END
            ) as model_info,
            a.description as purpose,
            CASE WHEN a.agent_type IN (1, 2) THEN 1 ELSE 0 END as has_api_interface,
            1 as has_chrome_plugin,
            0 as subscription_count, -- 暂时使用固定值
            a.submit_time,
            COALESCE(u.real_name, u.username, '未知用户') as creator_name,
            COALESCE(pending_av.version_number, a.version) as version,
            COALESCE(pending_av.change_log, av.change_log) as upgrade_content,
            aar.approval_opinion as last_approval_opinion,
            aar.approver_name as last_approver_name,
            aar.approval_time as last_approval_time
        FROM agents a
        LEFT JOIN agent_categories ac ON a.category_id = ac.id
        LEFT JOIN sys_user u ON a.creator_id = u.id
        LEFT JOIN agent_versions av ON a.id = av.agent_id AND av.is_current = 1
        LEFT JOIN agent_versions pending_av ON a.id = pending_av.agent_id AND pending_av.approval_status = 1
        LEFT JOIN agent_approval_records aar ON a.last_approval_id = aar.id
        WHERE a.deleted = 0
        <if test="query.agentName != null and query.agentName != ''">
            AND a.agent_name LIKE CONCAT('%', #{query.agentName}, '%')
        </if>
        <if test="query.agentCode != null and query.agentCode != ''">
            AND a.agent_code LIKE CONCAT('%', #{query.agentCode}, '%')
        </if>
        <if test="query.approvalStatus != null">
            AND a.approval_status = #{query.approvalStatus}
        </if>
        <if test="query.categoryId != null">
            AND a.category_id = #{query.categoryId}
        </if>
        <if test="query.startTime != null and query.startTime != ''">
            AND a.submit_time >= CONCAT(#{query.startTime}, ' 00:00:00')
        </if>
        <if test="query.endTime != null and query.endTime != ''">
            AND a.submit_time &lt;= CONCAT(#{query.endTime}, ' 23:59:59')
        </if>
        <if test="query.creatorId != null">
            AND a.creator_id = #{query.creatorId}
        </if>
        ORDER BY
            CASE WHEN a.approval_status = 1 THEN 0 ELSE 1 END,
            a.submit_time DESC
    </select>

    <!-- 根据Agent ID获取最新的审批记录 -->
    <select id="selectLatestByAgentId" resultMap="AgentApprovalRecordMap">
        SELECT
            aar.*,
            a.agent_name,
            a.agent_code,
            a.description as agent_description,
            ac.category_name,
            u.real_name as creator_name,
            av.version_number
        FROM agent_approval_records aar
        LEFT JOIN agents a ON aar.agent_id = a.id
        LEFT JOIN agent_categories ac ON a.category_id = ac.id
        LEFT JOIN sys_user u ON a.creator_id = u.id
        LEFT JOIN agent_versions av ON aar.agent_version_id = av.id
        WHERE aar.agent_id = #{agentId}
        AND aar.deleted = 0
        ORDER BY aar.created_time DESC
        LIMIT 1
    </select>

    <!-- 根据Agent ID获取所有审批历史记录 -->
    <select id="selectHistoryByAgentId" resultMap="AgentApprovalRecordMap">
        SELECT
            aar.*,
            a.agent_name,
            a.agent_code,
            a.description as agent_description,
            ac.category_name,
            u.real_name as creator_name,
            av.version_number
        FROM agent_approval_records aar
        LEFT JOIN agents a ON aar.agent_id = a.id
        LEFT JOIN agent_categories ac ON a.category_id = ac.id
        LEFT JOIN sys_user u ON a.creator_id = u.id
        LEFT JOIN agent_versions av ON aar.agent_version_id = av.id
        WHERE aar.agent_id = #{agentId}

        <if test="versionId != null">
            AND aar.agent_version_id = #{versionId}
        </if>
        AND aar.deleted = 0
        ORDER BY aar.created_time DESC
    </select>

    <!-- 统计各状态的审批数量 -->
    <select id="selectApprovalStatusStats" resultType="java.util.Map">
        SELECT 
            approval_status,
            COUNT(*) as count,
            CASE 
                WHEN approval_status = 1 THEN '审批中'
                WHEN approval_status = 2 THEN '审批通过'
                WHEN approval_status = 3 THEN '审批不通过'
                ELSE '未知'
            END as status_name
        FROM agents 
        WHERE deleted = 0 
        AND approval_status IS NOT NULL
        GROUP BY approval_status
    </select>

</mapper>
