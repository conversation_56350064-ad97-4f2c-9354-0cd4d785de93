<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinoair.agent.mapper.UploadedFileMapper">

    <!-- 结果映射 -->
    <resultMap id="UploadedFileWithUserMap" type="com.sinoair.agent.entity.UploadedFile">
        <id column="id" property="id"/>
        <result column="original_name" property="originalName"/>
        <result column="stored_name" property="storedName"/>
        <result column="file_path" property="filePath"/>
        <result column="file_size" property="fileSize"/>
        <result column="file_type" property="fileType"/>
        <result column="mime_type" property="mimeType"/>
        <result column="md5_hash" property="md5Hash"/>
        <result column="upload_user_id" property="uploadUserId"/>
        <result column="status" property="status"/>
        <result column="created_time" property="createdTime"/>
        <result column="deleted" property="deleted"/>
        <result column="uploader_name" property="uploaderName"/>
    </resultMap>

    <!-- 根据用户ID查询文件列表 -->
    <select id="findByUserId" resultType="com.sinoair.agent.entity.UploadedFile">
        SELECT * FROM uploaded_files 
        WHERE upload_user_id = #{userId} AND deleted = 0 
        ORDER BY created_time DESC
    </select>

    <!-- 根据文件类型查询文件列表 -->
    <select id="findByFileType" resultType="com.sinoair.agent.entity.UploadedFile">
        SELECT * FROM uploaded_files 
        WHERE file_type = #{fileType} AND deleted = 0 
        ORDER BY created_time DESC
    </select>

    <!-- 根据业务类型查询文件列表 -->
    <select id="findByBusinessType" resultType="com.sinoair.agent.entity.UploadedFile">
        SELECT * FROM uploaded_files 
        WHERE business_type = #{businessType} AND deleted = 0 
        ORDER BY created_time DESC
    </select>

    <!-- 分页查询文件列表 -->
    <select id="selectPageWithDetails" resultMap="UploadedFileWithUserMap">
        SELECT f.*, u.real_name as uploader_name 
        FROM uploaded_files f 
        LEFT JOIN sys_user u ON f.upload_user_id = u.id 
        WHERE f.deleted = 0 
        <if test="keyword != null and keyword != ''">
            AND (f.original_name LIKE CONCAT('%', #{keyword}, '%') 
            OR f.file_type LIKE CONCAT('%', #{keyword}, '%'))
        </if>
        ORDER BY f.created_time DESC
    </select>

    <!-- 检查文件是否存在且未删除 -->
    <select id="existsByIdAndNotDeleted" resultType="java.lang.Boolean">
        SELECT COUNT(*) > 0 FROM uploaded_files 
        WHERE id = #{id} AND deleted = 0
    </select>

    <!-- 根据文件路径查询文件 -->
    <select id="findByFilePath" resultType="com.sinoair.agent.entity.UploadedFile">
        SELECT * FROM uploaded_files 
        WHERE file_path = #{filePath} AND deleted = 0
    </select>

    <!-- 统计文件数量 -->
    <select id="countFiles" resultType="java.lang.Long">
        SELECT COUNT(*) FROM uploaded_files WHERE deleted = 0
    </select>

    <!-- 统计文件总大小 -->
    <select id="sumFileSize" resultType="java.lang.Long">
        SELECT COALESCE(SUM(file_size), 0) FROM uploaded_files WHERE deleted = 0
    </select>

    <!-- 根据MD5哈希查询文件 -->
    <select id="findByMd5Hash" resultType="com.sinoair.agent.entity.UploadedFile">
        SELECT * FROM uploaded_files 
        WHERE md5_hash = #{md5Hash} AND deleted = 0
    </select>

    <!-- 统计各文件类型数量 -->
    <select id="countByFileType" resultType="java.util.Map">
        SELECT file_type, COUNT(*) as count 
        FROM uploaded_files 
        WHERE deleted = 0 
        GROUP BY file_type 
        ORDER BY count DESC
    </select>

    <!-- 统计各用户上传文件数量 -->
    <select id="countByUser" resultType="java.util.Map">
        SELECT u.real_name as user_name, COUNT(f.id) as count 
        FROM sys_user u 
        LEFT JOIN uploaded_files f ON u.id = f.upload_user_id AND f.deleted = 0 
        GROUP BY u.id, u.real_name 
        HAVING count > 0 
        ORDER BY count DESC
    </select>

    <!-- 查询最近上传的文件 -->
    <select id="findRecentFiles" resultMap="UploadedFileWithUserMap">
        SELECT f.*, u.real_name as uploader_name 
        FROM uploaded_files f 
        LEFT JOIN sys_user u ON f.upload_user_id = u.id 
        WHERE f.deleted = 0 
        ORDER BY f.created_time DESC 
        LIMIT #{limit}
    </select>

    <!-- 查询大文件列表 -->
    <select id="findLargeFiles" resultMap="UploadedFileWithUserMap">
        SELECT f.*, u.real_name as uploader_name 
        FROM uploaded_files f 
        LEFT JOIN sys_user u ON f.upload_user_id = u.id 
        WHERE f.deleted = 0 AND f.file_size > #{minSize} 
        ORDER BY f.file_size DESC
    </select>

</mapper>
