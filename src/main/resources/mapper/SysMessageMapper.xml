<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinoair.agent.mapper.SysMessageMapper">

    <!-- 结果映射 -->
    <resultMap id="SysMessageMap" type="com.sinoair.agent.entity.SysMessage">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="title" property="title"/>
        <result column="content" property="content"/>
        <result column="message_type" property="messageType"/>
        <result column="related_id" property="relatedId"/>
        <result column="related_type" property="relatedType"/>
        <result column="is_read" property="isRead"/>
        <result column="created_time" property="createdTime"/>
        <result column="updated_time" property="updatedTime"/>
        <result column="deleted" property="deleted"/>
        <!-- 关联字段 -->
        <result column="user_name" property="userName"/>
    </resultMap>

    <!-- 根据用户ID获取未读消息数量 -->
    <select id="selectUnreadCountByUserId" resultType="int">
        SELECT COUNT(*)
        FROM sys_messages
        WHERE user_id = #{userId}
        AND is_read = 0
        AND deleted = 0
    </select>

    <!-- 根据用户ID获取消息列表 -->
    <select id="selectByUserId" resultMap="SysMessageMap">
        SELECT 
            sm.*,
            u.real_name as user_name
        FROM sys_messages sm
        LEFT JOIN sys_user u ON sm.user_id = u.id
        WHERE sm.user_id = #{userId}
        AND sm.deleted = 0
        ORDER BY sm.created_time DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 批量标记消息为已读 -->
    <update id="batchMarkAsRead">
        UPDATE sys_messages 
        SET is_read = 1, updated_time = NOW()
        WHERE user_id = #{userId}
        AND deleted = 0
        <if test="messageIds != null and messageIds.size() > 0">
            AND id IN
            <foreach collection="messageIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </update>

</mapper>
