<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinoair.agent.mapper.UserRoleMapper">

    <!-- 批量插入用户角色关联 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO sys_user_role (user_id, role_id, created_by, created_time)
        VALUES
        <foreach collection="userRoles" item="item" separator=",">
            (#{item.userId}, #{item.roleId}, #{item.createdBy}, #{item.createdTime})
        </foreach>
    </insert>

    <!-- 根据用户ID删除用户角色关联 -->
    <delete id="deleteByUserId">
        DELETE FROM sys_user_role WHERE user_id = #{userId}
    </delete>

    <!-- 根据角色ID删除用户角色关联 -->
    <delete id="deleteByRoleId">
        DELETE FROM sys_user_role WHERE role_id = #{roleId}
    </delete>

    <!-- 根据用户ID查询角色ID列表 -->
    <select id="selectRoleIdsByUserId" resultType="java.lang.Long">
        SELECT role_id FROM sys_user_role WHERE user_id = #{userId}
    </select>

    <!-- 根据角色ID查询用户ID列表 -->
    <select id="selectUserIdsByRoleId" resultType="java.lang.Long">
        SELECT user_id FROM sys_user_role WHERE role_id = #{roleId}
    </select>

</mapper>
