<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinoair.agent.mapper.UserMapper">

    <!-- 结果映射 -->
    <resultMap id="UserWithRoleMap" type="com.sinoair.agent.entity.User">
        <id column="id" property="id"/>
        <result column="username" property="username"/>
        <result column="password" property="password"/>
        <result column="email" property="email"/>
        <result column="phone" property="phone"/>
        <result column="real_name" property="realName"/>
        <result column="role_id" property="roleId"/>
        <result column="department" property="department"/>
        <result column="status" property="status"/>
        <result column="last_login_time" property="lastLoginTime"/>
        <result column="created_time" property="createdTime"/>
        <result column="updated_time" property="updatedTime"/>
        <result column="deleted" property="deleted"/>
        <result column="role_name" property="roleName"/>
        <result column="role_code" property="roleCode"/>
    </resultMap>

    <!-- 根据用户名查找用户 -->
    <select id="findByUsername" resultMap="UserWithRoleMap">
        SELECT u.*, r.role_name, r.role_code 
        FROM sys_user u 
        LEFT JOIN sys_role r ON u.role_id = r.id 
        WHERE u.username = #{username}
    </select>

    <!-- 根据用户名和状态查找用户 -->
    <select id="findByUsernameAndStatus" resultMap="UserWithRoleMap">
        SELECT u.*, r.role_name, r.role_code 
        FROM sys_user u 
        LEFT JOIN sys_role r ON u.role_id = r.id 
        WHERE u.username = #{username} AND u.status = #{status}
    </select>

    <!-- 根据邮箱查找用户 -->
    <select id="findByEmail" resultMap="UserWithRoleMap">
        SELECT u.*, r.role_name, r.role_code 
        FROM sys_user u 
        LEFT JOIN sys_role r ON u.role_id = r.id 
        WHERE u.email = #{email}
    </select>

    <!-- 分页查询用户列表（带角色信息） -->
    <select id="selectPageWithRole" resultMap="UserWithRoleMap">
        SELECT u.*, r.role_name, r.role_code 
        FROM sys_user u 
        LEFT JOIN sys_role r ON u.role_id = r.id 
        WHERE 1=1 
        <if test="keyword != null and keyword != ''">
            AND (u.username LIKE CONCAT('%', #{keyword}, '%') 
            OR u.real_name LIKE CONCAT('%', #{keyword}, '%') 
            OR u.email LIKE CONCAT('%', #{keyword}, '%'))
        </if>
        ORDER BY u.created_time DESC
    </select>

    <!-- 根据角色ID查询用户列表 -->
    <select id="findByRoleId" resultMap="UserWithRoleMap">
        SELECT u.*, r.role_name, r.role_code 
        FROM sys_user u 
        LEFT JOIN sys_role r ON u.role_id = r.id 
        WHERE u.role_id = #{roleId}
    </select>

    <!-- 统计用户数量 -->
    <select id="countUsers" resultType="java.lang.Long">
        SELECT COUNT(*) FROM sys_user
    </select>

    <!-- 统计活跃用户数量（最近30天登录） -->
    <select id="countActiveUsers" resultType="java.lang.Long">
        SELECT COUNT(*) FROM sys_user 
        WHERE last_login_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)
    </select>

</mapper>
