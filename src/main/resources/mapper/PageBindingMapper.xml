<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinoair.agent.mapper.PageBindingMapper">

    <!-- 结果映射 -->
    <resultMap id="PageBindingWithTemplateMap" type="com.sinoair.agent.entity.PageBinding">
        <id column="id" property="id"/>
        <result column="binding_name" property="bindingName"/>
        <result column="template_id" property="templateId"/>
        <result column="target_url" property="targetUrl"/>
        <result column="url_pattern" property="urlPattern"/>
        <result column="page_html" property="pageHtml"/>
        <result column="binding_config" property="bindingConfig"/>
        <result column="status" property="status"/>
        <result column="created_by" property="createdBy"/>
        <result column="description" property="description"/>
        <result column="created_time" property="createdTime"/>
        <result column="updated_time" property="updatedTime"/>
        <result column="deleted" property="deleted"/>
        <result column="template_name" property="templateName"/>
        <result column="json_template" property="jsonTemplate"/>
        <result column="is_multi_step" property="isMultiStep"/>
        <result column="parent_binding_id" property="parentBindingId"/>
        <result column="step_order" property="stepOrder"/>
        <result column="step_name" property="stepName"/>
        <result column="next_action" property="nextAction"/>
        <result column="wait_time" property="waitTime"/>
        <result column="is_final_step" property="isFinalStep"/>
    </resultMap>

    <!-- 根据URL模式查找绑定配置 -->
    <select id="findByUrlPattern" resultMap="PageBindingWithTemplateMap">
        SELECT pb.*, bt.template_name, bt.json_template 
        FROM page_bindings pb 
        LEFT JOIN business_templates bt ON pb.template_id = bt.id 
        WHERE pb.url_pattern = #{urlPattern} AND pb.deleted = 0
    </select>

    <!-- 根据URL查找匹配的绑定配置（只返回主绑定） -->
    <select id="findByUrl" resultMap="PageBindingWithTemplateMap">
        SELECT pb.*, bt.template_name, bt.json_template
        FROM page_bindings pb
        LEFT JOIN business_templates bt ON pb.template_id = bt.id
        WHERE #{url} LIKE CONCAT('%', pb.url_pattern, '%')
        AND pb.status = 1 AND pb.deleted = 0
        AND (pb.parent_binding_id IS NULL OR pb.parent_binding_id = 0)
    </select>

    <!-- 分页查询绑定配置列表（带模板信息） -->
    <select id="selectPageWithTemplate" resultMap="PageBindingWithTemplateMap">
        SELECT pb.*, bt.template_name, bt.json_template
        FROM page_bindings pb
        LEFT JOIN business_templates bt ON pb.template_id = bt.id
        WHERE pb.deleted = 0
        AND (pb.parent_binding_id IS NULL OR pb.parent_binding_id = 0)
        <if test="keyword != null and keyword != ''">
            AND (pb.binding_name LIKE CONCAT('%', #{keyword}, '%')
            OR pb.target_url LIKE CONCAT('%', #{keyword}, '%')
            OR pb.url_pattern LIKE CONCAT('%', #{keyword}, '%'))
        </if>
        ORDER BY pb.created_time DESC
    </select>

    <!-- 查询所有绑定配置列表（带模板信息） -->
    <select id="selectAllWithTemplate" resultMap="PageBindingWithTemplateMap">
        SELECT pb.*, bt.template_name, bt.json_template
        FROM page_bindings pb
        LEFT JOIN business_templates bt ON pb.template_id = bt.id
        WHERE pb.deleted = 0
        AND (pb.parent_binding_id IS NULL OR pb.parent_binding_id = 0)
        ORDER BY pb.created_time DESC
    </select>

    <!-- 根据模板ID查询绑定配置 -->
    <select id="findByTemplateId" resultMap="PageBindingWithTemplateMap">
        SELECT pb.*, bt.template_name, bt.json_template 
        FROM page_bindings pb 
        LEFT JOIN business_templates bt ON pb.template_id = bt.id 
        WHERE pb.template_id = #{templateId} AND pb.deleted = 0
    </select>

    <!-- 根据创建者查询绑定配置 -->
    <select id="findByCreatedBy" resultMap="PageBindingWithTemplateMap">
        SELECT pb.*, bt.template_name, bt.json_template 
        FROM page_bindings pb 
        LEFT JOIN business_templates bt ON pb.template_id = bt.id 
        WHERE pb.created_by = #{createdBy} AND pb.deleted = 0
    </select>

    <!-- 统计绑定配置数量 -->
    <select id="countBindings" resultType="java.lang.Long">
        SELECT COUNT(*) FROM page_bindings WHERE deleted = 0
    </select>

    <!-- 统计活跃绑定配置数量 -->
    <select id="countActiveBindings" resultType="java.lang.Long">
        SELECT COUNT(*) FROM page_bindings 
        WHERE status = 1 AND deleted = 0
    </select>

    <!-- 根据绑定名称查找绑定配置 -->
    <select id="findByBindingName" resultMap="PageBindingWithTemplateMap">
        SELECT pb.*, bt.template_name, bt.json_template 
        FROM page_bindings pb 
        LEFT JOIN business_templates bt ON pb.template_id = bt.id 
        WHERE pb.binding_name = #{bindingName} AND pb.deleted = 0
    </select>

    <!-- 查询所有活跃的绑定配置 -->
    <select id="findAllActive" resultMap="PageBindingWithTemplateMap">
        SELECT pb.*, bt.template_name, bt.json_template 
        FROM page_bindings pb 
        LEFT JOIN business_templates bt ON pb.template_id = bt.id 
        WHERE pb.status = 1 AND pb.deleted = 0 
        ORDER BY pb.created_time DESC
    </select>

    <!-- 统计各模板的绑定配置数量 -->
    <select id="countByTemplate" resultType="java.util.Map">
        SELECT bt.template_name, COUNT(pb.id) as count
        FROM business_templates bt
        LEFT JOIN page_bindings pb ON bt.id = pb.template_id AND pb.deleted = 0
        WHERE bt.deleted = 0
        GROUP BY bt.id, bt.template_name
        ORDER BY count DESC
    </select>

    <!-- 查找多步骤绑定配置（主步骤） -->
    <select id="findMultiStepBindings" resultMap="PageBindingWithTemplateMap">
        SELECT pb.*, bt.template_name, bt.json_template
        FROM page_bindings pb
        LEFT JOIN business_templates bt ON pb.template_id = bt.id
        WHERE #{url} LIKE CONCAT('%', pb.url_pattern, '%')
        AND pb.is_multi_step = 1
        AND pb.parent_binding_id IS NULL
        AND pb.status = 1 AND pb.deleted = 0
        ORDER BY pb.created_time DESC
    </select>

    <!-- 根据父绑定ID查找子步骤 -->
    <select id="findSubStepsByParentId" resultMap="PageBindingWithTemplateMap">
        SELECT pb.*, bt.template_name, bt.json_template
        FROM page_bindings pb
        LEFT JOIN business_templates bt ON pb.template_id = bt.id
        WHERE pb.parent_binding_id = #{parentBindingId}
        AND pb.deleted = 0
        ORDER BY pb.step_order ASC
    </select>

    <!-- 查找多步骤绑定的完整配置（包含所有子步骤） -->
    <select id="findMultiStepBindingWithSteps" resultMap="PageBindingWithTemplateMap">
        SELECT pb.*, bt.template_name, bt.json_template
        FROM page_bindings pb
        LEFT JOIN business_templates bt ON pb.template_id = bt.id
        WHERE (pb.id = #{bindingId} OR pb.parent_binding_id = #{bindingId})
        AND pb.deleted = 0
        ORDER BY pb.parent_binding_id ASC, pb.step_order ASC
    </select>

</mapper>
