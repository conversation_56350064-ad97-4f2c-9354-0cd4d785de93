<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sinoair.agent.mapper.SysConfigMapper">

    <!-- 系统配置结果映射 -->
    <resultMap id="BaseResultMap" type="com.sinoair.agent.entity.SysConfig">
        <id column="id" property="id" />
        <result column="config_group" property="configGroup" />
        <result column="config_key" property="configKey" />
        <result column="config_value" property="configValue" />
        <result column="description" property="description" />
        <result column="is_enabled" property="isEnabled" />
        <result column="created_time" property="createdTime" />
        <result column="updated_time" property="updatedTime" />
        <result column="deleted" property="deleted" />
    </resultMap>

    <!-- 根据配置组查询配置列表 -->
    <select id="selectByGroup" resultMap="BaseResultMap">
        SELECT * FROM sys_config 
        WHERE config_group = #{configGroup} AND deleted = 0 
        ORDER BY config_key
    </select>

    <!-- 根据配置组和配置键查询配置 -->
    <select id="selectByGroupAndKey" resultMap="BaseResultMap">
        SELECT * FROM sys_config 
        WHERE config_group = #{configGroup} 
        AND config_key = #{configKey} 
        AND deleted = 0
    </select>

    <!-- 查询所有有效的配置 -->
    <select id="selectAllEnabled" resultMap="BaseResultMap">
        SELECT * FROM sys_config 
        WHERE is_enabled = 1 AND deleted = 0 
        ORDER BY config_group, config_key
    </select>

    <!-- 根据配置组查询有效的配置列表 -->
    <select id="selectEnabledByGroup" resultMap="BaseResultMap">
        SELECT * FROM sys_config 
        WHERE config_group = #{configGroup} 
        AND is_enabled = 1 AND deleted = 0 
        ORDER BY config_key
    </select>

</mapper>
