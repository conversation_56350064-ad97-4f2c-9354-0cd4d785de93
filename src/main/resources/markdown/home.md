# 🚀 SinoairAgent API文档中心

欢迎使用SinoairAgent智能文档识别与自动填写平台的API文档！

## 📋 API分组说明

### 🌐 公开API
- **目标用户**: 第三方开发者、外部集成
- **认证方式**: API Key认证
- **访问路径**: `/api/v1/public/**`
- **主要功能**: 文档解析、Agent调用、状态查询

### 🔧 内部管理API  
- **目标用户**: 内部管理人员、开发团队
- **认证方式**: JWT Bearer Token认证
- **访问路径**: `/api/v1/**`（排除公开API）
- **主要功能**: Agent管理、用户管理、系统配置

## 🔗 快速链接

### 公开API相关
- 🌐 [客户门户](https://portal.sinoair-agent.com) - 申请API Key
- 📖 [开发者文档](https://docs.sinoair-agent.com) - 详细使用指南
- 💬 [技术支持](mailto:<EMAIL>) - 获取帮助

### 内部管理相关
- 🏢 [管理后台](https://admin.sinoair-agent.com) - 系统管理
- 📊 [监控面板](https://monitor.sinoair-agent.com) - 系统监控
- 🔧 [内部文档](https://internal-docs.sinoair-agent.com) - 内部资料

## 🚀 快速开始

### 公开API使用流程
1. 访问客户门户申请API Key
2. 获取Agent列表：`GET /api/v1/public/agents`
3. 上传文件解析：`POST /api/v1/public/files/parse`
4. 查询解析状态：`GET /api/v1/public/files/parse/status/{taskId}`

### 内部管理使用流程
1. 使用管理员账号登录：`POST /api/v1/auth/login`
2. 获取JWT Token进行后续API调用
3. 管理Agent、用户、系统配置等

## 📞 联系我们

- **官网**: https://www.sinoair-agent.com
- **技术支持**: <EMAIL>
- **商务合作**: <EMAIL>

---

*© 2024 SinoairAgent Team. All rights reserved.*
