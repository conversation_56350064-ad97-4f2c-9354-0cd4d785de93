<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agent详情 - 智能体矩阵平台</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- 统一详情页面样式 -->
    <link href="/assets/css/agent-unified-detail.css" rel="stylesheet">
</head>
<body>

<!-- 页面加载状态 -->
<div class="page-loading loading-spinner" style="display: none;">
    <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">加载中...</span>
    </div>
    <div class="mt-2 text-muted">正在加载数据...</div>
</div>

<!-- 浮动操作区域（仅补充资料模式显示） -->
<div class="floating-actions" style="display: none;">
    <div class="floating-actions-title">快速操作</div>
    <div class="floating-actions-desc">保存草稿可随时修改，提交后将发送给管理员审核</div>
    <button type="button" class="btn btn-custom btn-success" id="saveBtn">
        <i class="bi bi-save"></i>
        <span>保存草稿</span>
    </button>
    <button type="button" class="btn btn-custom btn-primary" id="previewBtn">
        <i class="bi bi-eye"></i>
        <span>预览效果</span>
    </button>
    <button type="button" class="btn btn-custom btn-warning" id="submitBtn">
        <i class="bi bi-check-circle"></i>
        <span>提交审核</span>
    </button>
    <button type="button" class="btn btn-custom btn-outline-dark" onclick="goBackToSourcePage()">
        <i class="bi bi-arrow-left"></i>
        <span>返回列表</span>
    </button>
</div>

<!-- 主容器 -->
<div class="agent-unified-container">
    <div class="unified-wrapper">
        <!-- 主要内容卡片 -->
        <div class="main-card">
            
            <!-- 头部操作区域 -->
            <div class="header-actions">
                <div class="header-actions-left">
                    <div class="header-actions-title">
                        <i class="bi bi-info-circle"></i>
                        Agent详情信息
                    </div>
                    <div class="header-actions-desc">
                        查看Agent的详细信息、使用场景和配置参数
                    </div>
                </div>
                <div class="header-actions-right">
                    <!-- 操作按钮将通过JavaScript动态生成 -->
                </div>
            </div>

            <!-- Tab导航 -->
            <ul class="nav nav-tabs" id="unifiedDetailTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="basic-info-tab" data-bs-toggle="tab" data-bs-target="#basic-info" type="button" role="tab">
                        <i class="bi bi-info-circle"></i>
                        基本信息
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="usage-scenarios-tab" data-bs-toggle="tab" data-bs-target="#usage-scenarios" type="button" role="tab">
                        <i class="bi bi-lightbulb"></i>
                        使用场景
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="screenshots-tab" data-bs-toggle="tab" data-bs-target="#screenshots" type="button" role="tab">
                        <i class="bi bi-images"></i>
                        Chrome插件截图
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="preview-tab" data-bs-toggle="tab" data-bs-target="#preview" type="button" role="tab">
                        <i class="bi bi-eye"></i>
                        预览效果
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="version-history-tab" data-bs-toggle="tab" data-bs-target="#version-history" type="button" role="tab">
                        <i class="bi bi-clock-history"></i>
                        版本历史
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="approval-history-tab" data-bs-toggle="tab" data-bs-target="#approval-history" type="button" role="tab">
                        <i class="bi bi-list-check"></i>
                        审批历史
                    </button>
                </li>
            </ul>

            <!-- Tab内容 -->
            <div class="tab-content" id="unifiedDetailTabContent">
                
                <!-- 基本信息Tab -->
                <div class="tab-pane fade show active" id="basic-info" role="tabpanel">
                    
                    <!-- Agent基本信息 -->
                    <div class="info-card">
                        <div class="info-card-title">
                            <i class="bi bi-info-circle"></i>
                            Agent基本信息
                        </div>
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">
                                    <i class="bi bi-tag"></i>
                                    Agent名称
                                </div>
                                <div class="info-value" id="agentName">-</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">
                                    <i class="bi bi-code-slash"></i>
                                    Agent编码
                                </div>
                                <div class="info-value" id="agentCode">-</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">
                                    <i class="bi bi-folder"></i>
                                    分类
                                </div>
                                <div class="info-value" id="categoryName">-</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">
                                    <i class="bi bi-circle-fill"></i>
                                    当前状态
                                </div>
                                <div class="info-value" id="agentStatus">-</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">
                                    <i class="bi bi-person"></i>
                                    创建者
                                </div>
                                <div class="info-value" id="creatorName">-</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">
                                    <i class="bi bi-calendar"></i>
                                    创建时间
                                </div>
                                <div class="info-value" id="createTime">-</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">
                                    <i class="bi bi-tag-fill"></i>
                                    当前版本
                                </div>
                                <div class="info-value" id="currentVersion">-</div>
                            </div>
                            <div class="info-item" id="pendingVersionItem" style="display: none;">
                                <div class="info-label">
                                    <i class="bi bi-clock-history"></i>
                                    待审批版本
                                </div>
                                <div class="info-value" id="pendingVersion">-</div>
                            </div>
                        </div>

                        <!-- Agent描述 -->
                        <div class="mt-3">
                            <div class="info-label">
                                <i class="bi bi-file-text"></i>
                                Agent描述
                            </div>
                            <div class="info-value" id="description" style="margin-top: 0.5rem; padding: 1rem; background-color: var(--background-color); border-radius: var(--small-radius); border: 1px solid var(--border-color);">-</div>
                        </div>
                    </div>

                    <!-- Agent简介 -->
                    <div class="info-card">
                        <div class="info-card-title">
                            <i class="bi bi-file-text"></i>
                            Agent简介
                        </div>
                        
                        <!-- 查看模式：只读显示 -->
                        <div class="view-mode-content">
                            <div class="rich-text-content" id="agentIntroductionRichText">
                                <div class="text-muted">暂无Agent简介</div>
                            </div>
                        </div>
                        
                        <!-- 补充资料模式：可编辑 -->
                        <div class="supplement-mode-content" style="display: none;">
                            <div class="form-section">
                                <div class="mb-3">
                                    <label class="form-label">详细介绍Agent的功能特点、技术优势等</label>
                                    <textarea class="form-control" id="agentIntroduction" rows="8"
                                              placeholder="请详细介绍Agent的功能特点、技术优势、适用范围等..."></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 使用场景Tab -->
                <div class="tab-pane fade" id="usage-scenarios" role="tabpanel">
                    
                    <!-- 查看模式：只读显示 -->
                    <div class="view-mode-content">
                        <div class="info-card">
                            <div class="info-card-title">
                                <i class="bi bi-lightbulb"></i>
                                使用场景描述
                            </div>
                            <div class="rich-text-content" id="usageScenariosRichText">
                                <div class="text-muted">暂无使用场景描述</div>
                            </div>
                        </div>

                        <!-- 痛点问题部分已隐藏 -->
                    </div>
                    
                    <!-- 补充资料模式：可编辑 -->
                    <div class="supplement-mode-content" style="display: none;">
                        <div class="form-section">
                            <div class="section-title">
                                <i class="bi bi-lightbulb"></i>
                                使用场景描述
                            </div>
                            <div class="mb-3">
                                <label class="form-label">描述Agent的具体使用场景和应用领域</label>
                                <textarea class="form-control" id="usageScenarios" rows="8"
                                          placeholder="请描述Agent的具体使用场景、应用领域、目标用户群体等..."></textarea>
                            </div>
                        </div>

                        <!-- 痛点问题编辑器已隐藏 -->
                    </div>
                </div>

                <!-- Chrome插件截图Tab -->
                <div class="tab-pane fade" id="screenshots" role="tabpanel">

                    <!-- 查看模式：轮播图展示 -->
                    <div class="view-mode-content">
                        <div class="info-card" id="screenshotsSection" style="display: none;">
                            <div class="info-card-title">
                                <i class="bi bi-images"></i>
                                Chrome插件效果截图
                            </div>
                            <div id="screenshotsCarousel" class="carousel slide" data-bs-ride="carousel">
                                <div class="carousel-indicators" id="carouselIndicators">
                                    <!-- 动态生成指示器 -->
                                </div>
                                <div class="carousel-inner" id="carouselInner">
                                    <!-- 动态生成轮播项 -->
                                </div>
                                <button class="carousel-control-prev" type="button" data-bs-target="#screenshotsCarousel" data-bs-slide="prev">
                                    <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                                    <span class="visually-hidden">Previous</span>
                                </button>
                                <button class="carousel-control-next" type="button" data-bs-target="#screenshotsCarousel" data-bs-slide="next">
                                    <span class="carousel-control-next-icon" aria-hidden="true"></span>
                                    <span class="visually-hidden">Next</span>
                                </button>
                            </div>
                        </div>
                        <div class="info-card" id="noScreenshotsMessage">
                            <div class="text-center py-4">
                                <i class="bi bi-images" style="font-size: 3rem; color: #dee2e6;"></i>
                                <p class="text-muted mt-2">暂无Chrome插件截图</p>
                            </div>
                        </div>
                    </div>

                    <!-- 补充资料模式：上传功能 -->
                    <div class="supplement-mode-content" style="display: none;">
                        <div class="form-section">
                            <div class="section-title">
                                <i class="bi bi-images"></i>
                                Chrome插件使用效果截图
                            </div>
                            <div class="mb-3">
                                <label class="form-label">上传Chrome插件的使用效果截图（支持拖拽上传）</label>
                                <div class="screenshot-upload" id="screenshotUpload">
                                    <i class="bi bi-cloud-upload" style="font-size: 2rem; color: #6c757d;"></i>
                                    <div class="mt-2">
                                        <strong>点击上传或拖拽文件到此处</strong>
                                    </div>
                                    <div class="text-muted mt-1">支持 JPG、PNG、GIF 格式，单个文件不超过 5MB</div>
                                    <input type="file" id="screenshotInput" multiple accept="image/*" style="display: none;">
                                </div>
                                <div class="screenshot-preview" id="screenshotPreview"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 预览效果Tab -->
                <div class="tab-pane fade" id="preview" role="tabpanel">
                    <div class="info-card">
                        <div class="info-card-title">
                            <i class="bi bi-eye"></i>
                            预览效果
                        </div>
                        <div id="previewContainer">
                            <!-- 预览内容将通过JavaScript动态生成 -->
                            <div class="loading-spinner">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                                <div class="mt-2 text-muted">正在生成预览...</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 版本历史Tab -->
                <div class="tab-pane fade" id="version-history" role="tabpanel">
                    <div class="info-card">
                        <div class="info-card-title">
                            <i class="bi bi-clock-history"></i>
                            版本历史信息
                            <button type="button" class="btn btn-outline-primary btn-sm ms-auto" onclick="loadVersionHistory()">
                                <i class="bi bi-arrow-clockwise"></i> 刷新历史
                            </button>
                        </div>
                        <div class="timeline" id="versionHistoryContent">
                            <!-- 版本历史内容将通过JavaScript动态加载 -->
                            <div class="loading-spinner">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                                <div class="mt-2 text-muted">正在加载版本历史...</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 审批历史Tab -->
                <div class="tab-pane fade" id="approval-history" role="tabpanel">
                    <div class="info-card">
                        <div class="info-card-title">
                            <i class="bi bi-list-check"></i>
                            审批历史记录
                            <button type="button" class="btn btn-outline-primary btn-sm ms-auto" onclick="loadApprovalHistory()">
                                <i class="bi bi-arrow-clockwise"></i> 刷新历史
                            </button>
                        </div>
                        <div class="timeline" id="approvalHistoryContent">
                            <!-- 审批历史内容将通过JavaScript动态加载 -->
                            <div class="loading-spinner">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                                <div class="mt-2 text-muted">正在加载审批历史...</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- jQuery (Summernote依赖) -->
<script src="https://cdn.jsdelivr.net/npm/jquery@3.7.1/dist/jquery.min.js"></script>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<!-- 通用工具函数 -->
<script src="/assets/js/common.js"></script>
<script src="/assets/js/admin-common.js"></script>

<!-- Summernote富文本编辑器 -->
<link href="https://cdn.jsdelivr.net/npm/summernote@0.8.20/dist/summernote-bs5.min.css" rel="stylesheet">
<script src="https://cdn.jsdelivr.net/npm/summernote@0.8.20/dist/summernote-bs5.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/summernote@0.8.20/dist/lang/summernote-zh-CN.min.js"></script>

<!-- 统一详情页面脚本 -->
<script src="/assets/js/agent-unified-detail.js"></script>

</body>
</html>
