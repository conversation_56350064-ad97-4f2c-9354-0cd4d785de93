<!-- 页面绑定管理页面 -->
<style>
/* 页面绑定专用样式 */
.json-field-item {
    cursor: pointer;
    transition: all 0.2s;
    border-radius: 4px;
    margin-bottom: 4px;
}

.json-field-item:hover {
    background-color: #e3f2fd;
    transform: translateX(2px);
}

.json-field-item.bound {
    background-color: #e8f5e8;
    border-left: 3px solid #28a745;
}

.json-field-item.dragging {
    opacity: 0.5;
    transform: rotate(5deg);
}

.page-element {
    position: relative;
    transition: all 0.2s;
}

.page-element:hover {
    outline: 2px dashed #007bff;
    outline-offset: 2px;
}

.page-element.bound {
    outline: 2px solid #28a745;
    outline-offset: 2px;
    background-color: rgba(40, 167, 69, 0.1);
}

.page-element.highlight {
    outline: 2px solid #ffc107;
    outline-offset: 2px;
    background-color: rgba(255, 193, 7, 0.1);
}

.binding-indicator {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #28a745;
    color: white;
    border-radius: 50%;
    width: 16px;
    height: 16px;
    font-size: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

.field-selector-popup {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 2000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.field-selector-content {
    background: white;
    border-radius: 8px;
    padding: 0;
    max-width: 400px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.field-selector-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #e9ecef;
    background: #f8f9fa;
    border-radius: 8px 8px 0 0;
}

.field-selector-body {
    padding: 15px 20px;
    max-height: 400px;
    overflow-y: auto;
}

.field-selector-list {
    max-height: 300px;
    overflow-y: auto;
}

.field-selector-item {
    padding: 8px 12px;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    margin-bottom: 4px;
    cursor: pointer;
    transition: all 0.2s;
}

.field-selector-item:hover {
    background-color: #f8f9fa;
    border-color: #007bff;
}

.field-selector-item.bound {
    background-color: #f8f9fa;
    border-color: #6c757d;
    cursor: not-allowed;
    opacity: 0.6;
}

.field-name {
    font-weight: 500;
    font-size: 14px;
    color: #495057;
}

.field-description {
    font-size: 12px;
    color: #6c757d;
    margin-top: 2px;
}

.field-path {
    font-size: 11px;
    color: #adb5bd;
    font-family: monospace;
    margin-top: 2px;
}

/* 小尺寸alert */
.alert-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
}

/* 滚动条样式 */
#jsonFieldsList::-webkit-scrollbar {
    width: 6px;
}

#jsonFieldsList::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

#jsonFieldsList::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

#jsonFieldsList::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 三列布局优化 */
.modal-xl {
    max-width: 95vw;
}

/* 模态框高度优化 */
#createBindingModal .modal-content {
    height: 90vh;
    max-height: 800px;
    min-height: 600px;
}

#createBindingModal .modal-body {
    height: calc(100% - 120px);
}

/* 响应式优化 */
@media (max-width: 1400px) {
    .modal-xl {
        max-width: 90vw;
    }

    #createBindingModal .modal-content {
        height: 85vh;
        max-height: 700px;
    }
}

@media (max-width: 1200px) {
    .col-3 {
        min-width: 250px;
    }

    #createBindingModal .modal-content {
        height: 80vh;
        max-height: 600px;
    }
}

@media (max-height: 700px) {
    #createBindingModal .modal-content {
        height: 95vh;
        max-height: none;
    }

    #createBindingModal .modal-body {
        height: calc(100% - 100px);
    }
}
</style>

<div class="bindings-content">
    <!-- 操作栏 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <button class="btn btn-primary" onclick="showCreateBindingModal()">
                <i class="bi bi-plus-lg"></i> 创建绑定
            </button>
            <button class="btn btn-outline-success ms-2" onclick="testBinding()">
                <i class="bi bi-play-circle"></i> 测试绑定
            </button>
        </div>
        <div class="d-flex gap-2">
            <input type="text" class="form-control" placeholder="搜索绑定..." id="bindingSearchInput" style="width: 250px;">
            <button class="btn btn-outline-secondary" onclick="searchBindings()">
                <i class="bi bi-search"></i>
            </button>
        </div>
    </div>

    <!-- 统计卡片 -->
    <div class="row g-4 mb-4">
        <div class="col-md-4">
            <div class="card text-center">
                <div class="card-body">
                    <i class="bi bi-link-45deg text-primary" style="font-size: 2rem;"></i>
                    <h5 class="mt-2" id="totalBindings">-</h5>
                    <small class="text-muted">总绑定数</small>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card text-center">
                <div class="card-body">
                    <i class="bi bi-check-circle text-success" style="font-size: 2rem;"></i>
                    <h5 class="mt-2" id="activeBindings">-</h5>
                    <small class="text-muted">启用绑定</small>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card text-center">
                <div class="card-body">
                    <i class="bi bi-globe text-info" style="font-size: 2rem;"></i>
                    <h5 class="mt-2" id="uniqueDomains">-</h5>
                    <small class="text-muted">覆盖域名</small>
                </div>
            </div>
        </div>
    </div>

    <!-- 绑定列表 -->
    <div class="card">
        <div class="card-body p-0">
            <div id="bindingsTable">
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <div class="mt-2 text-muted">正在加载绑定列表...</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 创建页面绑定模态框 -->
<div class="modal fade" id="createBindingModal" tabindex="-1">
    <div class="modal-dialog modal-xl modal-dialog-centered">
        <div class="modal-content" style="max-height: 90vh; display: flex; flex-direction: column;">
            <div class="modal-header">
                <h5 class="modal-title"><i class="bi bi-link-45deg me-2"></i>创建页面绑定</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body p-0 flex-grow-1" style="overflow: hidden;">
                <div class="container-fluid h-100">
                    <div class="row h-100 g-0">
                        <!-- 左列：配置信息 -->
                        <div class="col-3 border-end bg-light p-3" style="overflow-y: auto;">
                            <h6 class="fw-bold mb-3 text-primary">
                                <i class="bi bi-gear me-2"></i>配置信息
                            </h6>

                            <form id="createBindingForm">
                                <div class="mb-3">
                                    <label class="form-label fw-bold small">绑定名称 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control form-control-sm" name="bindingName" required
                                           placeholder="发票录入页面绑定">
                                </div>

                                <div class="mb-3">
                                    <label class="form-label fw-bold small">业务模板 <span class="text-danger">*</span></label>
                                    <select class="form-select form-select-sm" name="templateId" id="bindingTemplateSelect" required onchange="loadTemplateFields()">
                                        <option value="">请选择业务模板</option>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label fw-bold small">目标页面URL <span class="text-danger">*</span></label>
                                    <div class="input-group input-group-sm">
                                        <input type="url" class="form-control" name="targetUrl" id="targetUrlInput" required
                                               placeholder="https://example.com">
                                        <button type="button" class="btn btn-primary" onclick="loadPageFromUrl()" title="从URL加载页面">
                                            <i class="bi bi-download"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary" onclick="showManualHtmlModal()" title="手动输入HTML">
                                            <i class="bi bi-code-square"></i>
                                        </button>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label fw-bold small">URL匹配模式 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control form-control-sm" name="urlPattern" required
                                           placeholder="example.com/invoice">
                                </div>

                                <div class="mb-3" id="pageLoadStatus" style="display: none;">
                                    <div class="alert alert-warning alert-sm py-2">
                                        <i class="bi bi-exclamation-triangle me-1"></i>
                                        <small id="pageLoadMessage">正在加载页面...</small>
                                    </div>
                                </div>

                                <!-- 快捷操作 -->
                                <div class="mb-3">
                                    <div class="d-grid gap-2">
                                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="openTargetPage()">
                                            <i class="bi bi-box-arrow-up-right"></i> 打开目标页面
                                        </button>
                                        <button type="button" class="btn btn-outline-info btn-sm" onclick="refreshPreview()">
                                            <i class="bi bi-arrow-clockwise"></i> 刷新预览
                                        </button>
                                    </div>
                                </div>
                            </form>

                            <hr class="my-3">

                            <!-- 绑定关系统计 -->
                            <div class="binding-stats">
                                <h6 class="fw-bold mb-2 text-success">
                                    <i class="bi bi-check-circle me-2"></i>绑定统计
                                </h6>
                                <div class="small">
                                    <div class="d-flex justify-content-between mb-1">
                                        <span>JSON字段:</span>
                                        <span class="badge bg-info" id="jsonFieldCount">0</span>
                                    </div>
                                    <div class="d-flex justify-content-between mb-1">
                                        <span>已绑定:</span>
                                        <span class="badge bg-success" id="boundFieldCount">0</span>
                                    </div>
                                    <div class="d-flex justify-content-between">
                                        <span>未绑定:</span>
                                        <span class="badge bg-warning" id="unboundFieldCount">0</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 中列：JSON字段列表 -->
                        <div class="col-3 border-end bg-white p-3 d-flex flex-column" style="height: 100%; max-height: 100%;">
                            <div class="d-flex justify-content-between align-items-center mb-3 flex-shrink-0">
                                <h6 class="fw-bold mb-0 text-primary">
                                    <i class="bi bi-file-code me-2"></i>JSON字段
                                </h6>
                                <small class="text-muted" id="fieldsCount">0 个字段</small>
                            </div>

                            <!-- 字段搜索 -->
                            <div class="mb-3 flex-shrink-0">
                                <div class="input-group input-group-sm">
                                    <span class="input-group-text"><i class="bi bi-search"></i></span>
                                    <input type="text" class="form-control" id="fieldSearchInput"
                                           placeholder="搜索字段..." onkeyup="filterFields()">
                                    <button class="btn btn-outline-secondary" type="button" onclick="clearFieldSearch()">
                                        <i class="bi bi-x"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- 已绑定字段提示 -->
                            <div class="mb-3 flex-shrink-0" id="boundFieldsInfo" style="display: none;">
                                <div class="alert alert-success alert-sm py-2 mb-0">
                                    <i class="bi bi-check-circle me-1"></i>
                                    <small>已绑定 <span id="boundCount">0</span> 个字段</small>
                                    <button class="btn btn-sm btn-link p-0 ms-2" onclick="showBindingRelationsModal()">查看详情</button>
                                </div>
                            </div>

                            <!-- 字段列表 - 严格限制高度 -->
                            <div class="flex-grow-1 d-flex flex-column" style="min-height: 0; overflow: hidden;">
                                <div id="jsonFieldsList" class="border rounded p-2" style="height: 100%; overflow-y: auto; min-height: 200px;">
                                    <div class="text-muted text-center py-4">
                                        <i class="bi bi-file-code fs-1"></i>
                                        <p class="mt-2 small">请先选择业务模板</p>
                                    </div>
                                </div>
                            </div>

                            <!-- 操作按钮 -->
                            <div class="mt-3 d-grid gap-2 flex-shrink-0">
                                <button class="btn btn-sm btn-outline-primary" onclick="resetAllFields()">
                                    <i class="bi bi-arrow-clockwise"></i> 重置字段
                                </button>
                                <button class="btn btn-sm btn-outline-success" onclick="autoBindFields()">
                                    <i class="bi bi-magic"></i> 智能绑定
                                </button>
                                <!-- 绑定关系查看按钮 -->
                                <button class="btn btn-sm btn-info" onclick="showBindingRelationsModal()" id="viewBindingsBtn" style="display: none;">
                                    <i class="bi bi-link-45deg"></i> 查看绑定 (<span id="bindingCountInBtn">0</span>)
                                </button>
                            </div>
                        </div>

                        <!-- 右列：页面预览 -->
                        <div class="col-6 p-3 d-flex flex-column" style="height: 100%; max-height: 100%;">
                            <div class="d-flex justify-content-between align-items-center mb-3 flex-shrink-0">
                                <h6 class="fw-bold mb-0 text-primary">
                                    <i class="bi bi-globe me-2"></i>页面预览
                                </h6>
                                <div class="btn-group btn-group-sm">
                                    <button type="button" class="btn btn-outline-primary" id="startBindingBtn" onclick="startElementBinding()" title="开始绑定模式" style="display: none;">
                                        <i class="bi bi-cursor"></i> 开始绑定
                                    </button>
                                    <button type="button" class="btn btn-outline-warning" id="stopBindingBtn" onclick="stopElementBinding()" title="停止绑定模式" style="display: none;">
                                        <i class="bi bi-stop"></i> 停止绑定
                                    </button>
                                    <button type="button" class="btn btn-outline-info" onclick="highlightInputs()">
                                        <i class="bi bi-eye"></i> 高亮输入框
                                    </button>
                                    <button type="button" class="btn btn-outline-success" onclick="refreshPreview()">
                                        <i class="bi bi-arrow-clockwise"></i> 刷新
                                    </button>
                                </div>
                            </div>

                            <!-- 页面预览区域 - 严格限制高度 -->
                            <div class="flex-grow-1" style="min-height: 0; overflow: hidden;">
                                <div id="pagePreview" class="border rounded p-3" style="height: 100%; overflow-y: auto; min-height: 300px;">
                                    <div class="text-muted text-center py-5">
                                        <i class="bi bi-globe fs-1"></i>
                                        <p class="mt-2">请输入目标URL并点击"加载页面"</p>
                                        <small>如果页面需要登录，会自动打开新窗口供您登录</small>

                                        <!-- 临时测试输入框 -->
                                        <div class="mt-4 p-3 border rounded bg-light">
                                            <h6>测试输入框（临时）</h6>
                                            <div class="mb-2">
                                                <input type="text" class="form-control" placeholder="发票号码" id="testInput1">
                                            </div>
                                            <div class="mb-2">
                                                <input type="text" class="form-control" placeholder="开票日期" id="testInput2">
                                            </div>
                                            <div class="mb-2">
                                                <input type="text" class="form-control" placeholder="总金额" id="testInput3">
                                            </div>
                                            <button class="btn btn-sm btn-primary" onclick="setupInputClickBinding()">
                                                设置点击绑定
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer border-top bg-white" style="flex-shrink: 0;">
                <div class="d-flex justify-content-between align-items-center w-100">
                    <div>
                        <!-- 绑定统计信息 -->
                        <span class="text-muted small" id="bindingStatusText">请配置绑定信息</span>
                    </div>
                    <div class="btn-group">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-outline-warning" onclick="saveAsDraft()">
                            <i class="bi bi-save"></i> 保存草稿
                        </button>
                        <button type="button" class="btn btn-primary" onclick="createBinding()">
                            <i class="bi bi-check-lg"></i> 创建绑定
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 编辑绑定模态框 -->
<div class="modal fade" id="editBindingModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-pencil me-2"></i>编辑页面绑定
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editBindingForm">
                    <input type="hidden" id="editBindingId">
                    <div class="row g-3">
                        <div class="col-12">
                            <label class="form-label fw-bold">绑定名称 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="editBindingName" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label fw-bold">业务模板 <span class="text-danger">*</span></label>
                            <select class="form-select" id="editBindingTemplateSelect" required>
                                <option value="">请选择业务模板</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label fw-bold">状态</label>
                            <select class="form-select" id="editBindingStatus">
                                <option value="1">启用</option>
                                <option value="0">禁用</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label fw-bold">目标URL <span class="text-danger">*</span></label>
                            <input type="url" class="form-control" id="editTargetUrl" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label fw-bold">URL匹配模式 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="editUrlPattern" required>
                        </div>
                        <div class="col-12">
                            <label class="form-label fw-bold">描述</label>
                            <textarea class="form-control" id="editBindingDescription" rows="3"></textarea>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="updateBinding()">
                    <i class="bi bi-check-lg"></i> 保存修改
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 绑定详情模态框 -->
<div class="modal fade" id="bindingDetailModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">绑定详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="bindingDetailContent">
                <!-- 详情内容将通过JavaScript动态填充 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-warning" id="editBindingFromDetailBtn">编辑</button>
                <button type="button" class="btn btn-success" id="testBindingFromDetailBtn">测试</button>
            </div>
        </div>
    </div>
</div>

<!-- 手动HTML输入模态框 -->
<div class="modal fade" id="manualHtmlModal" tabindex="-1" aria-labelledby="manualHtmlModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="manualHtmlModalLabel">
                    <i class="bi bi-code-square me-2"></i>手动输入HTML内容
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="bi bi-info-circle me-2"></i>
                    <strong>使用说明：</strong>如果页面无法直接加载（需要登录、跨域限制等），您可以：
                    <ol class="mb-0 mt-2">
                        <li>在浏览器中打开目标页面</li>
                        <li>按F12打开开发者工具</li>
                        <li>在Elements/元素标签页中右键点击&lt;html&gt;标签</li>
                        <li>选择"Copy" → "Copy outerHTML"</li>
                        <li>将复制的HTML代码粘贴到下方文本框中</li>
                    </ol>
                </div>

                <div class="mb-3">
                    <label for="manualUrlInput" class="form-label">页面URL（可选）</label>
                    <input type="url" class="form-control" id="manualUrlInput" placeholder="https://example.com">
                    <div class="form-text">用于标识页面来源，不影响功能</div>
                </div>

                <div class="mb-3">
                    <label for="manualHtmlTextarea" class="form-label">HTML内容 <span class="text-danger">*</span></label>
                    <textarea class="form-control" id="manualHtmlTextarea" rows="15"
                              placeholder="请粘贴完整的HTML代码..."></textarea>
                    <div class="form-text">
                        <span id="htmlCharCount">0</span> 字符
                        <span class="text-muted ms-3">支持完整的HTML文档</span>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="loadManualHtml()">
                    <i class="bi bi-check-lg me-1"></i>加载HTML
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 字段选择器弹窗 -->
<div class="field-selector-popup" id="fieldSelectorPopup" style="display: none;">
    <div class="field-selector-content">
        <div class="field-selector-header">
            <h6 class="mb-0">
                <i class="bi bi-search me-2"></i>选择绑定字段
            </h6>
            <button type="button" class="btn-close btn-sm" onclick="hideFieldSelector()"></button>
        </div>
        <div class="field-selector-body">
            <div class="mb-2">
                <input type="text" class="form-control form-control-sm" id="fieldSelectorSearch"
                       placeholder="搜索字段名称或描述..." onkeyup="filterSelectorFields()">
            </div>
            <div class="field-selector-list" id="fieldSelectorList">
                <div class="text-muted text-center py-3">
                    <i class="bi bi-file-code"></i>
                    <p class="mt-1 mb-0 small">请先选择业务模板</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 绑定关系查看模态框 -->
<div class="modal fade" id="bindingRelationsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-link-45deg me-2"></i>绑定关系详情
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <div>
                        <span class="badge bg-primary" id="totalBindingsCount">0 个绑定</span>
                        <span class="text-muted ms-2">点击页面输入框选择JSON字段建立绑定关系</span>
                    </div>
                    <button class="btn btn-sm btn-outline-danger" onclick="clearAllBindings()">
                        <i class="bi bi-trash"></i> 清空所有绑定
                    </button>
                </div>

                <div id="bindingRelationsDetail" style="max-height: 400px; overflow-y: auto;">
                    <div class="text-muted text-center py-5">
                        <i class="bi bi-link-45deg fs-1"></i>
                        <p class="mt-2">暂无绑定关系</p>
                        <small>点击页面输入框，选择JSON字段建立绑定</small>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" onclick="exportBindingConfig()">
                    <i class="bi bi-download"></i> 导出配置
                </button>
            </div>
        </div>
    </div>
</div>
