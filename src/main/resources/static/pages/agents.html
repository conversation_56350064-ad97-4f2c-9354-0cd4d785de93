<!-- Agent管理页面 -->
<style>
    /* 现代化表格样式 - 参考Agent审批列表页面 */

    /* CSS变量定义 - 支持主题切换 */
    :root {
        /* 主色调 */
        --primary-color: #009EF7;
        --success-color: #50CD89;
        --warning-color: #FFC700;
        --danger-color: #F1416C;
        --info-color: #7239EA;

        /* 中性色调 */
        --dark-text: #181C32;
        --medium-text: #5E6278;
        --light-text: #A1A5B7;
        --border-color: #E4E6EA;
        --background-color: #F9F9F9;
        --card-background: #FFFFFF;

        /* 阴影系统 */
        --light-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        --normal-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        --deep-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
        --hover-shadow: 0 0.5rem 2rem rgba(0, 0, 0, 0.1);

        /* 圆角系统 */
        --small-radius: 0.375rem;
        --medium-radius: 0.5rem;
        --large-radius: 0.75rem;

        /* 间距系统 */
        --spacing-xs: 0.25rem;
        --spacing-sm: 0.5rem;
        --spacing-md: 1rem;
        --spacing-lg: 1.5rem;
        --spacing-xl: 3rem;

        /* 过渡效果 */
        --theme-transition: all 0.3s ease;
    }

    /* 深色主题变量 */
    .theme-dark {
        --dark-text: #FFFFFF;
        --medium-text: #A1A5B7;
        --light-text: #5E6278;
        --border-color: #2B2B40;
        --background-color: #1B1B29;
        --card-background: #1E1E2D;

        --light-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.3);
        --normal-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.4);
        --deep-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.5);
        --hover-shadow: 0 0.5rem 2rem rgba(0, 0, 0, 0.3);
    }

    /* 表格容器样式 */
    .agents-table {
        background-color: var(--card-background);
        border-radius: var(--medium-radius);
        border: 1px solid var(--border-color);
        box-shadow: var(--light-shadow);
        transition: var(--theme-transition);
        overflow: hidden;
    }

    /* 表格样式 */
    .agents-table .table {
        color: var(--dark-text);
        margin-bottom: 0;
        border-collapse: separate;
        border-spacing: 0;
    }

    .agents-table .table thead th {
        background-color: #F9F9F9;
        color: var(--dark-text);
        font-weight: 600;
        font-size: 0.875rem;
        border-bottom: 1px solid var(--border-color);
        border-top: none;
        padding: 0.75rem 1rem;
        text-align: left;
        vertical-align: middle;
        position: sticky;
        top: 0;
        z-index: 10;
        transition: var(--theme-transition);
    }

    .agents-table .table thead th:first-child {
        border-top-left-radius: var(--medium-radius);
    }

    .agents-table .table thead th:last-child {
        border-top-right-radius: var(--medium-radius);
    }

    /* 深色主题下的表格标题 */
    .theme-dark .agents-table .table thead th {
        background-color: #2B2B40;
        color: var(--dark-text);
    }

    /* 可排序的表格标题样式 */
    .agents-table .table thead th.sortable {
        cursor: pointer;
        user-select: none;
        position: relative;
        transition: background-color 0.2s ease;
    }

    .agents-table .table thead th.sortable:hover {
        background-color: #E9ECEF;
    }

    .theme-dark .agents-table .table thead th.sortable:hover {
        background-color: #3A3A52;
    }

    /* 排序图标 */
    .agents-table .table thead th.sortable::after {
        content: '\f0dc';
        font-family: 'bootstrap-icons';
        position: absolute;
        right: 0.5rem;
        top: 50%;
        transform: translateY(-50%);
        opacity: 0.5;
        font-size: 0.75rem;
        transition: opacity 0.2s ease;
    }

    .agents-table .table thead th.sortable:hover::after {
        opacity: 0.8;
    }

    .agents-table .table thead th.sortable.asc::after {
        content: '\f0d8';
        opacity: 1;
        color: var(--primary-color);
    }

    .agents-table .table thead th.sortable.desc::after {
        content: '\f0d7';
        opacity: 1;
        color: var(--primary-color);
    }

    /* 调试历史表格样式 */
    .table thead.table-light th {
        background-color: #F9F9F9 !important;
        color: var(--dark-text);
        font-weight: 600;
        font-size: 0.875rem;
        padding: 0.75rem 1rem;
        border-bottom: 1px solid var(--border-color);
        border-top: none;
        text-align: left;
        vertical-align: middle;
        transition: var(--theme-transition);
    }

    /* 深色主题下的调试历史表格标题 */
    .theme-dark .table thead.table-light th {
        background-color: #2B2B40 !important;
        color: var(--dark-text);
    }

    .agents-table .table tbody td {
        padding: var(--spacing-md);
        border-bottom: 1px solid var(--border-color);
        vertical-align: middle;
        transition: var(--theme-transition);
        height: 48px; /* 符合UI规范的行高 */
    }

    .agents-table .table tbody tr {
        transition: var(--theme-transition);
    }

    .agents-table .table tbody tr:hover {
        background-color: var(--background-color);
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .agents-table .table tbody tr:nth-child(even) {
        background-color: rgba(0, 0, 0, 0.02);
    }

    .theme-dark .agents-table .table tbody tr:nth-child(even) {
        background-color: rgba(255, 255, 255, 0.02);
    }

    /* Agent信息样式优化 */
    .agent-info {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
    }

    .agent-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: linear-gradient(135deg, var(--primary-color), var(--info-color));
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 600;
        font-size: 0.875rem;
        flex-shrink: 0;
    }

    .agent-name {
        color: var(--primary-color);
        text-decoration: none;
        font-weight: 600;
        font-size: 0.875rem;
        transition: var(--theme-transition);
    }

    .agent-name:hover {
        color: var(--dark-text);
        text-decoration: underline;
    }

    .agent-code {
        font-size: 0.75rem;
        color: var(--medium-text);
        font-family: "JetBrains Mono", "Fira Code", Consolas, monospace;
    }

    /* 状态徽章样式 */
    .badge {
        font-size: 0.75rem;
        font-weight: 500;
        padding: 0.375rem 0.75rem;
        border-radius: var(--small-radius);
    }

    /* 操作按钮样式 */
    .btn-sm {
        padding: 0.375rem 0.75rem;
        font-size: 0.75rem;
        border-radius: var(--small-radius);
        transition: var(--theme-transition);
    }

    /* 空状态和加载状态样式 */
    .empty-state, .loading-state {
        background-color: var(--card-background);
        border-radius: var(--medium-radius);
        border: 1px solid var(--border-color);
        box-shadow: var(--light-shadow);
        transition: var(--theme-transition);
    }

    /* 搜索栏和工具栏样式优化 */
    .search-wrapper {
        flex: 1;
        min-width: 200px;
    }

    .search-input-group {
        border-radius: var(--medium-radius);
        overflow: hidden;
    }

    .search-input-group .input-group-text {
        background-color: var(--card-background);
        border-color: var(--border-color);
        color: var(--medium-text);
        border-right: none;
    }

    .search-input-group .form-control {
        border-color: var(--border-color);
        transition: var(--theme-transition);
        border-left: none;
    }

    .search-input-group .form-control:focus {
        border-color: var(--border-color);
        box-shadow: none;
    }

    /* 搜索按钮样式 */
    .search-btn {
        background-color: #1890ff;
        border-color: #1890ff;
        color: white;
        border-radius: 4px;
        padding: 0.375rem 1rem;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .search-btn:hover, .search-btn:focus {
        background-color: #40a9ff;
        border-color: #40a9ff;
        color: white;
        box-shadow: 0 2px 8px rgba(24, 144, 255, 0.35);
    }

    /* 重置按钮样式 */
    .reset-btn {
        border-color: #d9d9d9;
        color: rgba(0, 0, 0, 0.65);
        background-color: white;
        border-radius: 4px;
        padding: 0.375rem 1rem;
        font-weight: 400;
        transition: all 0.3s ease;
    }

    .reset-btn:hover, .reset-btn:focus {
        color: #40a9ff;
        border-color: #40a9ff;
        background-color: white;
    }

    /* 过滤器下拉框样式 */
    .filter-select {
        width: 120px;
        border-radius: 4px;
        border-color: #d9d9d9;
        color: rgba(0, 0, 0, 0.65);
        transition: all 0.3s ease;
    }

    .filter-select:focus {
        border-color: #40a9ff;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }

    /* 工具栏按钮样式 */
    .toolbar-card .btn {
        border-radius: 4px;
        transition: var(--theme-transition);
    }

    .toolbar-card .btn:hover {
        transform: translateY(-1px);
        box-shadow: var(--hover-shadow);
    }

    /* 工具栏两行布局优化 */
    .toolbar-card .row.mb-2 {
        border-bottom: 1px solid var(--border-color);
        padding-bottom: 0.75rem;
    }

    .toolbar-card .row:last-child {
        padding-top: 0.5rem;
    }

    /* 新建按钮样式优化 */
    .toolbar-card .btn-primary {
        background-color: #1890ff;
        border-color: #1890ff;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .toolbar-card .btn-primary:hover {
        background-color: #40a9ff;
        border-color: #40a9ff;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(24, 144, 255, 0.35);
    }

    /* 深色主题适配 */
    .theme-dark .search-input-group .input-group-text {
        background-color: var(--card-background);
        border-color: var(--border-color);
        color: var(--medium-text);
    }

    .theme-dark .search-input-group .form-control {
        background-color: var(--card-background);
        border-color: var(--border-color);
        color: var(--dark-text);
    }

    .theme-dark .reset-btn {
        background-color: var(--card-background);
        border-color: var(--border-color);
        color: var(--medium-text);
    }

    .theme-dark .reset-btn:hover {
        color: #40a9ff;
        border-color: #40a9ff;
        background-color: var(--card-background);
    }

    .theme-dark .filter-select {
        background-color: var(--card-background);
        border-color: var(--border-color);
        color: var(--dark-text);
    }

    /* 深色主题工具栏分割线 */
    .theme-dark .toolbar-card .row.mb-2 {
        border-bottom-color: var(--border-color);
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .agents-table .table thead th {
            padding: 0.5rem 0.75rem;
            font-size: 0.75rem;
        }

        .agents-table .table thead th.sortable::after {
            right: 0.25rem;
            font-size: 0.65rem;
        }

        .agents-table .table tbody td {
            padding: var(--spacing-sm);
        }

        .agent-avatar {
            width: 32px;
            height: 32px;
            font-size: 0.75rem;
        }

        .agent-name {
            font-size: 0.8rem;
        }

        .agent-code {
            font-size: 0.7rem;
        }

        .btn-sm {
            font-size: 0.7rem;
            padding: 0.25rem 0.5rem;
        }

        .badge {
            font-size: 0.7rem;
            padding: 0.25rem 0.5rem;
        }

        /* 移动端工具栏优化 */
        .toolbar-card .row {
            margin-bottom: 0.5rem;
        }

        .toolbar-card .row:last-child {
            margin-bottom: 0;
        }

        .toolbar-card .col,
        .toolbar-card .col-auto {
            width: 100%;
            max-width: 100%;
        }

        .toolbar-card .d-flex {
            flex-wrap: wrap;
            gap: 0.5rem;
            justify-content: center;
        }

        .search-wrapper {
            flex: 1;
            min-width: 200px;
            order: 1;
        }

        .search-btn, .reset-btn {
            order: 2;
            white-space: nowrap;
        }

        .filter-select {
            order: 3;
            width: auto !important;
            min-width: 120px;
        }

        /* 移动端新建按钮居中 */
        .toolbar-card .row:first-child .col-auto {
            display: flex;
            justify-content: center;
            margin-top: 0.5rem;
        }

        /* 移动端视图切换居中 */
        .toolbar-card .row:last-child .col-auto {
            display: flex;
            justify-content: center;
        }
    }

    /* 超小屏幕优化 */
    @media (max-width: 576px) {
        .agents-table .table thead th:last-child,
        .agents-table .table tbody td:last-child {
            min-width: 200px;
        }

        /* 超小屏幕工具栏优化 */
        .toolbar-card {
            padding: var(--spacing-sm);
        }

        .toolbar-card .row {
            margin-bottom: 1rem;
        }

        .search-btn, .reset-btn {
            font-size: 0.875rem;
            padding: 0.375rem 0.75rem;
        }

        .search-input-group .form-control {
            font-size: 0.875rem;
        }

        .filter-select {
            font-size: 0.875rem;
            min-width: 100px;
        }

        .toolbar-card .d-flex {
            flex-direction: column;
            align-items: stretch;
            gap: 0.5rem;
        }

        .search-wrapper {
            width: 100%;
        }

        .search-btn, .reset-btn, .filter-select {
            width: 100%;
        }

        /* 超小屏幕按钮全宽 */
        .toolbar-card .row .col-auto button {
            width: 100%;
        }
    }

    /* ==================== Agent卡片样式 - 符合UI规范 ==================== */
    .agent-card {
        background: var(--card-background);
        border: 1px solid var(--border-color);
        border-radius: var(--medium-radius);
        box-shadow: var(--light-shadow);
        transition: all 0.3s ease;
        height: 100%;
        overflow: hidden;
        position: relative;
    }

    .agent-card:hover {
        box-shadow: var(--hover-shadow);
        transform: translateY(-2px);
        border-color: var(--primary-color);
    }

    /* 卡片头部 */
    .agent-card-header {
        background: linear-gradient(135deg, #F9F9F9 0%, #F1F5F9 100%);
        border-bottom: 1px solid var(--border-color);
        padding: 1.5rem;
        position: relative;
    }

    .theme-dark .agent-card-header {
        background: linear-gradient(135deg, #2B2B40 0%, #1E1E2D 100%);
    }

    .agent-card-title {
        font-size: 1.125rem;
        font-weight: 600;
        color: var(--dark-text);
        margin-bottom: 0.5rem;
        line-height: 1.4;
    }

    .agent-card-title .agent-name-link {
        color: var(--dark-text);
        text-decoration: none;
        transition: color 0.2s ease;
    }

    .agent-card-title .agent-name-link:hover {
        color: var(--primary-color);
        text-decoration: underline;
    }

    .agent-card-code {
        font-size: 0.75rem;
        color: var(--light-text);
        font-family: 'Monaco', 'Menlo', 'Consolas', monospace;
        background: rgba(0, 158, 247, 0.1);
        padding: 0.25rem 0.5rem;
        border-radius: var(--small-radius);
        display: inline-block;
        margin-top: 0.25rem;
    }

    /* 状态徽章 */
    .agent-status-badge {
        position: absolute;
        top: 1rem;
        right: 1rem;
        font-size: 0.75rem;
        font-weight: 500;
        padding: 0.25rem 0.5rem;
        border-radius: 0.75rem;
        border: 1px solid;
        z-index: 2;
    }

    .agent-status-badge.status-published {
        background: rgba(80, 205, 137, 0.1);
        color: var(--success-color);
        border-color: rgba(80, 205, 137, 0.3);
    }

    .agent-status-badge.status-testing {
        background: rgba(255, 199, 0, 0.1);
        color: var(--warning-color);
        border-color: rgba(255, 199, 0, 0.3);
    }

    .agent-status-badge.status-draft {
        background: rgba(161, 165, 183, 0.1);
        color: var(--light-text);
        border-color: rgba(161, 165, 183, 0.3);
    }

    .agent-status-badge.status-offline {
        background: rgba(241, 65, 108, 0.1);
        color: var(--danger-color);
        border-color: rgba(241, 65, 108, 0.3);
    }

    .agent-status-badge.status-warning {
        background: rgba(255, 199, 0, 0.1);
        color: var(--warning-color);
        border-color: rgba(255, 199, 0, 0.3);
    }

    /* 卡片主体 */
    .agent-card-body {
        padding: 1.5rem;
        flex: 1;
        display: flex;
        flex-direction: column;
    }

    .agent-description {
        color: var(--medium-text);
        font-size: 0.875rem;
        line-height: 1.5;
        margin-bottom: 1rem;
        flex: 1;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    /* Agent元信息网格 */
    .agent-meta {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
        margin-bottom: 1rem;
    }

    .agent-meta-item {
        text-align: center;
        padding: 0.75rem;
        background: rgba(0, 158, 247, 0.05);
        border-radius: var(--small-radius);
        border: 1px solid rgba(0, 158, 247, 0.1);
        transition: all 0.2s ease;
    }

    .agent-meta-item:hover {
        background: rgba(0, 158, 247, 0.1);
        border-color: rgba(0, 158, 247, 0.2);
    }

    .agent-meta-item .meta-label {
        font-size: 0.75rem;
        color: var(--light-text);
        margin-bottom: 0.25rem;
        display: block;
    }

    .agent-meta-item .meta-value {
        font-size: 0.875rem;
        font-weight: 500;
        color: var(--dark-text);
    }

    /* 卡片底部 */
    .agent-card-footer {
        padding: 1rem 1.5rem;
        background: rgba(249, 249, 249, 0.5);
        border-top: 1px solid var(--border-color);
    }

    .theme-dark .agent-card-footer {
        background: rgba(43, 43, 64, 0.5);
    }

    .agent-card-footer .btn {
        font-size: 0.75rem;
        padding: 0.375rem 0.75rem;
        border-radius: var(--small-radius);
        transition: all 0.2s ease;
    }

    .agent-card-footer .btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .agent-card-header {
            padding: 1rem;
        }

        .agent-card-body {
            padding: 1rem;
        }

        .agent-card-footer {
            padding: 0.75rem 1rem;
        }

        .agent-meta {
            grid-template-columns: 1fr;
            gap: 0.5rem;
        }

        .agent-card-title {
            font-size: 1rem;
        }

        .agent-status-badge {
            top: 0.75rem;
            right: 0.75rem;
            font-size: 0.65rem;
            padding: 0.2rem 0.4rem;
        }
    }

    @media (max-width: 576px) {
        .agent-card-header {
            padding: 0.75rem;
        }

        .agent-card-body {
            padding: 0.75rem;
        }

        .agent-card-footer {
            padding: 0.5rem 0.75rem;
        }

        .agent-card-footer .d-flex {
            flex-direction: column;
            gap: 0.5rem;
        }

        .agent-card-footer .btn {
            width: 100%;
            justify-content: center;
        }
    }

    /* ==================== 新建Agent模态框深色主题适配 ==================== */

    /* 模态框主体深色主题 */
    .theme-dark #createAgentModal .modal-content {
        background-color: var(--card-background);
        color: var(--dark-text);
    }

    .theme-dark #createAgentModal .modal-header {
        background-color: var(--primary-color);
        color: #FFFFFF;
    }

    .theme-dark #createAgentModal .modal-body {
        background-color: var(--card-background);
        color: var(--dark-text);
    }

    .theme-dark #createAgentModal .modal-footer {
        background-color: var(--background-color);
        border-top-color: var(--border-color);
        color: var(--dark-text);
    }

    /* 卡片组件深色主题 */
    .theme-dark #createAgentModal .card {
        background-color: var(--background-color);
        border-color: var(--border-color);
        color: var(--dark-text);
    }

    .theme-dark #createAgentModal .card.bg-light {
        background-color: var(--background-color) !important;
        color: var(--dark-text) !important;
    }

    .theme-dark #createAgentModal .card-header {
        background-color: transparent !important;
        border-bottom-color: var(--border-color);
        color: var(--dark-text);
    }

    .theme-dark #createAgentModal .card-body {
        background-color: var(--background-color);
        color: var(--dark-text);
    }

    .theme-dark #createAgentModal .card-title {
        color: var(--dark-text) !important;
    }

    .theme-dark #createAgentModal .text-dark {
        color: var(--dark-text) !important;
    }

    .theme-dark #createAgentModal .text-muted {
        color: var(--light-text) !important;
    }

    /* 表单控件深色主题 */
    .theme-dark #createAgentModal .form-control,
    .theme-dark #createAgentModal .form-select {
        background-color: var(--card-background);
        border-color: var(--border-color);
        color: var(--dark-text);
    }

    .theme-dark #createAgentModal .form-control:focus,
    .theme-dark #createAgentModal .form-select:focus {
        background-color: var(--card-background);
        border-color: var(--primary-color);
        color: var(--dark-text);
        box-shadow: 0 0 0 0.2rem rgba(0, 158, 247, 0.25);
    }

    .theme-dark #createAgentModal .form-control::placeholder {
        color: var(--light-text);
    }

    .theme-dark #createAgentModal .form-label {
        color: var(--dark-text);
    }

    .theme-dark #createAgentModal .form-text {
        color: var(--light-text);
    }

    /* JSON模板显示区域深色主题 */
    .theme-dark #createAgentModal .border.rounded.bg-white {
        background-color: var(--card-background) !important;
        border-color: var(--border-color) !important;
    }

    .theme-dark #createAgentModal #selectedTemplateJson {
        color: var(--dark-text) !important;
        background-color: var(--card-background);
    }

    /* 提示词模板区域深色主题 */
    .theme-dark #createAgentModal #createPromptTemplate {
        background-color: var(--card-background);
        border-color: var(--border-color);
        color: var(--dark-text);
    }

    .theme-dark #createAgentModal #createPromptTemplate:focus {
        background-color: var(--card-background);
        border-color: var(--primary-color);
        color: var(--dark-text);
    }

    /* 徽章和进度条深色主题 */
    .theme-dark #createAgentModal .badge {
        background-color: var(--background-color);
        color: var(--dark-text);
    }

    .theme-dark #createAgentModal .progress {
        background-color: var(--background-color);
    }

    .theme-dark #createAgentModal .progress-bar {
        background-color: var(--primary-color);
    }

    /* 按钮深色主题适配 */
    .theme-dark #createAgentModal .btn-outline-primary {
        border-color: var(--primary-color);
        color: var(--primary-color);
    }

    .theme-dark #createAgentModal .btn-outline-primary:hover {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
        color: #FFFFFF;
    }

    .theme-dark #createAgentModal .btn-outline-secondary {
        border-color: var(--border-color);
        color: var(--medium-text);
    }

    .theme-dark #createAgentModal .btn-outline-secondary:hover {
        background-color: var(--border-color);
        border-color: var(--border-color);
        color: var(--dark-text);
    }

    /* ==================== 提示词模板模态框深色主题适配 ==================== */

    .theme-dark #agentPromptTemplateModal .modal-content {
        background-color: var(--card-background);
        color: var(--dark-text);
    }

    .theme-dark #agentPromptTemplateModal .modal-header {
        background-color: var(--background-color);
        border-bottom-color: var(--border-color);
        color: var(--dark-text);
    }

    .theme-dark #agentPromptTemplateModal .modal-body {
        background-color: var(--card-background);
        color: var(--dark-text);
    }

    .theme-dark #agentPromptTemplateModal .modal-footer {
        background-color: var(--background-color);
        border-top-color: var(--border-color);
        color: var(--dark-text);
    }

    .theme-dark #agentPromptTemplateModal .nav-tabs {
        border-bottom-color: var(--border-color);
    }

    .theme-dark #agentPromptTemplateModal .nav-tabs .nav-link {
        color: var(--medium-text);
        border-color: transparent;
    }

    .theme-dark #agentPromptTemplateModal .nav-tabs .nav-link.active {
        color: var(--dark-text);
        background-color: var(--card-background);
        border-color: var(--border-color) var(--border-color) var(--card-background);
    }

    .theme-dark #agentPromptTemplateModal .nav-tabs .nav-link:hover {
        color: var(--dark-text);
        border-color: var(--border-color);
    }

    .theme-dark #agentPromptTemplateModal .tab-content {
        background-color: var(--card-background);
        color: var(--dark-text);
    }

    .theme-dark #agentPromptTemplateModal textarea {
        background-color: var(--card-background);
        border-color: var(--border-color);
        color: var(--dark-text);
    }

    .theme-dark #agentPromptTemplateModal textarea:focus {
        background-color: var(--card-background);
        border-color: var(--primary-color);
        color: var(--dark-text);
    }

    .theme-dark #agentPromptTemplateModal .border.rounded {
        border-color: var(--border-color) !important;
        background-color: var(--background-color) !important;
    }

    .theme-dark #agentPromptTemplateModal pre {
        color: var(--dark-text);
        background-color: var(--background-color);
    }

    .theme-dark #agentPromptTemplateModal .alert-info {
        background-color: rgba(114, 57, 234, 0.1);
        border-color: rgba(114, 57, 234, 0.2);
        color: var(--dark-text);
    }
</style>

<div class="agents-container">
    <!-- 工具栏 -->
    <div class="toolbar-card mb-2">
        <div class="card-body py-2">
            <!-- 第一行：搜索区域 + 新建按钮 -->
            <div class="row align-items-center g-2 mb-2">
                <!-- 搜索和过滤区域 -->
                <div class="col">
                    <div class="d-flex gap-2 align-items-center flex-wrap">
                        <!-- 搜索框 -->
                        <div class="search-wrapper">
                            <div class="input-group search-input-group">
                                <span class="input-group-text">
                                    <i class="bi bi-search text-muted"></i>
                                </span>
                                <input type="text" class="form-control"
                                       placeholder="搜索Agent名称、编码..."
                                       id="agentSearchInput">
                            </div>
                        </div>

                        <!-- 搜索按钮 -->
                        <button class="btn btn-primary search-btn" onclick="searchAgents()" type="button">
                            <i class="bi bi-search me-1"></i>查询
                        </button>

                        <!-- 重置按钮 -->
                        <button class="btn btn-outline-secondary reset-btn" onclick="clearFilters()" type="button">
                            <i class="bi bi-arrow-clockwise me-1"></i>重置
                        </button>

                        <!-- 过滤器 -->
                        <select class="form-select filter-select" id="statusFilter" onchange="filterAgents()">
                            <option value="">全部状态</option>
                            <option value="published">已发布</option>
                            <option value="draft">草稿</option>
                            <option value="testing">测试中</option>
                        </select>

                        <select class="form-select filter-select" id="categoryFilter" onchange="filterByCategory()">
                            <option value="">全部分类</option>
                        </select>
                    </div>
                </div>

                <!-- 新建Agent按钮 -->
                <div class="col-auto">
                    <button class="btn btn-primary" onclick="showCreateAgentModal()">
                        <i class="bi bi-plus-lg me-2"></i>新建Agent
                    </button>
                </div>
            </div>

            <!-- 第二行：视图切换 -->
            <div class="row align-items-center g-2">
                <div class="col">
                    <!-- 左侧可以放其他内容，目前为空 -->
                </div>

                <!-- 视图切换 -->
                <div class="col-auto">
                    <div class="d-flex align-items-center gap-2">
                        <span class="text-muted small">视图:</span>
                        <div class="btn-group btn-group-sm" role="group">
                            <button type="button" class="btn btn-outline-secondary"
                                    id="cardViewBtn" onclick="switchView('card')">
                                <i class="bi bi-grid-3x3-gap"></i>
                            </button>
                            <button type="button" class="btn btn-outline-secondary active"
                                    id="tableViewBtn" onclick="switchView('table')">
                                <i class="bi bi-table"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Agent列表 -->
    <div class="card">
        <div class="card-body p-0">
            <!-- 加载状态 -->
            <div id="loadingState" class="loading-state text-center py-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <div class="mt-2 text-muted">正在加载Agent列表...</div>
            </div>

            <!-- Agent列表容器 -->
            <div id="agentsTable" style="display: none;">
                <!-- 动态内容将在这里渲染 -->
            </div>

            <!-- 空状态 -->
            <div id="emptyState" class="empty-state text-center py-5" style="display: none;">
                <i class="bi bi-robot text-muted" style="font-size: 4rem;"></i>
                <h5 class="text-muted mt-3 mb-2">暂无Agent</h5>
                <p class="text-muted mb-4">您还没有创建任何Agent，点击下方按钮开始创建</p>
                <button class="btn btn-primary" onclick="showCreateAgentModal()">
                    <i class="bi bi-plus-lg me-2"></i>创建第一个Agent
                </button>
            </div>
        </div>

        <!-- 分页 -->
        <div class="card-footer bg-light py-2">
            <div class="d-flex justify-content-between align-items-center">
                <div class="text-muted small" id="paginationInfo">
                    显示第 1-10 条，共 0 条记录
                </div>
                <div id="paginationContainer">
                    <!-- 分页控件将在这里渲染 -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 创建Agent模态框 -->
<div class="modal fade" id="createAgentModal" tabindex="-1" aria-labelledby="createAgentModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-centered modal-dialog-scrollable">
        <div class="modal-content shadow-lg border-0">
            <!-- 模态框头部 -->
            <div class="modal-header bg-primary text-white border-0">
                <div class="d-flex align-items-center">
                    <div class="bg-white bg-opacity-20 rounded-circle p-2 me-3">
                        <i class="bi bi-robot text-white" style="font-size: 1.25rem;"></i>
                    </div>
                    <div>
                        <h5 class="modal-title mb-0 fw-bold" id="createAgentModalLabel">创建智能Agent</h5>
                        <small class="text-white-50">配置您的智能助手</small>
                    </div>
                </div>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="关闭"></button>
            </div>



            <!-- 模态框主体 -->
            <div class="modal-body px-4 py-4">
                <form id="createAgentForm">
                    <!-- 基础信息区域 -->
                    <div class="row g-4">
                        <!-- 左侧基础信息 -->
                        <div class="col-lg-6">
                            <div class="card border-0 bg-light h-100">
                                <div class="card-header bg-transparent border-0 pb-2">
                                    <h6 class="card-title mb-0 fw-bold text-dark">
                                        <i class="bi bi-info-circle text-primary me-2"></i>基础信息
                                    </h6>
                                    <small class="text-muted">配置Agent的基本属性</small>
                                </div>
                                <div class="card-body pt-2">
                                    <!-- Agent名称 -->
                                    <div class="mb-4">
                                        <label class="form-label fw-semibold text-dark">
                                            Agent名称 <span class="text-danger">*</span>
                                        </label>
                                        <input type="text" class="form-control form-control-lg" name="agentName" required
                                               placeholder="例如：发票识别Agent">
                                        <div class="form-text">
                                            <i class="bi bi-lightbulb text-warning me-1"></i>
                                            建议使用简洁明了的名称，便于识别和管理
                                        </div>
                                    </div>

                                    <!-- Agent编码（隐藏） -->
                                    <div class="mb-4" hidden="hidden">
                                        <label class="form-label fw-semibold text-dark">Agent编码</label>
                                        <input type="text" class="form-control" name="agentCode"
                                               placeholder="系统自动生成" readonly>
                                        <div class="form-text">系统将自动生成唯一编码</div>
                                    </div>

                                    <!-- 描述 -->
                                    <div class="mb-4">
                                        <label class="form-label fw-semibold text-dark">功能描述</label>
                                        <textarea class="form-control" name="description" rows="3"
                                                  placeholder="详细描述Agent的功能和用途，例如：专门用于识别和提取发票信息的智能助手..."></textarea>
                                        <div class="form-text">
                                            <i class="bi bi-chat-text text-info me-1"></i>
                                            清晰的描述有助于团队成员理解Agent的用途
                                        </div>
                                    </div>

                                    <!-- 业务模板 -->
                                    <div class="mb-4">
                                        <label class="form-label fw-semibold text-dark">
                                            业务模板 <span class="text-danger">*</span>
                                        </label>
                                        <select class="form-select form-select-lg" name="templateId"
                                                id="agentTemplateSelect" required onchange="loadSelectedTemplate()">
                                            <option value="">请选择业务模板</option>
                                        </select>
                                        <div class="form-text">
                                            <i class="bi bi-file-earmark-code text-success me-1"></i>
                                            选择业务模板将自动设置JSON输出格式
                                        </div>
                                    </div>

                                    <!-- 分类 -->
                                    <div class="mb-3">
                                        <label class="form-label fw-semibold text-dark">Agent分类</label>
                                        <select class="form-select" name="categoryId">
                                            <option value="">请选择分类...</option>
                                        </select>
                                        <div class="form-text">
                                            <i class="bi bi-tags text-secondary me-1"></i>
                                            选择合适的分类便于管理和查找
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- 右侧提示词配置 -->
                        <div class="col-lg-6">
                            <div class="card border-0 bg-light h-100">
                                <div class="card-header bg-transparent border-0 pb-2">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="card-title mb-0 fw-bold text-dark">
                                                <i class="bi bi-chat-quote text-primary me-2"></i>提示词模板
                                                <span class="text-danger">*</span>
                                            </h6>
                                            <small class="text-muted">定义Agent的行为和能力</small>
                                        </div>
                                        <button type="button" class="btn btn-outline-primary btn-sm"
                                                onclick="showCreatePromptTemplateModal()">
                                            <i class="bi bi-pencil-square me-1"></i>高级编辑器
                                        </button>
                                    </div>
                                </div>
                                <div class="card-body pt-2">
                                    <div class="position-relative">
                                        <textarea class="form-control" name="promptTemplate"
                                                  id="createPromptTemplate" rows="18" required
                                                  placeholder="请输入提示词模板，或点击'高级编辑器'使用Tab式编辑器...&#10;&#10;示例：&#10;你是一位专业的文档识别专家，擅长从各类文档中提取关键信息。&#10;&#10;你的主要技能包括：&#10;1. 准确识别文档类型和格式&#10;2. 提取关键字段和数据&#10;3. 按照指定格式输出结果&#10;&#10;请严格按照以下要求执行任务：&#10;- 仔细分析输入的文档内容&#10;- 提取所有相关信息&#10;- 按照JSON格式输出结果"
                                                  style="font-family: 'Consolas', 'Monaco', monospace; font-size: 13px; line-height: 1.5;"></textarea>
                                        <div class="position-absolute top-0 end-0 mt-2 me-2">
                                            <span class="badge bg-secondary" id="promptCharCount">0/2000</span>
                                        </div>
                                    </div>
                                    <div class="form-text mt-2">
                                        <i class="bi bi-magic text-warning me-1"></i>
                                        使用高级编辑器可以分类填写角色、技能、Action、限制等关键点
                                    </div>

                                    <!-- 提示词质量指示器 -->
                                    <div class="mt-3">
                                        <div class="d-flex justify-content-between align-items-center mb-1">
                                            <small class="text-muted">提示词质量</small>
                                            <small class="text-muted" id="promptQualityText">待评估</small>
                                        </div>
                                        <div class="progress" style="height: 4px;">
                                            <div class="progress-bar" role="progressbar" style="width: 0%"
                                                 id="promptQualityBar"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- LLM配置和JSON模板区域 -->
                    <div class="row g-4 mt-3">
                        <!-- LLM配置 -->
                        <div class="col-lg-6">
                            <div class="card border-0 bg-light">
                                <div class="card-header bg-transparent border-0 pb-2">
                                    <h6 class="card-title mb-0 fw-bold text-dark">
                                        <i class="bi bi-cpu text-primary me-2"></i>LLM配置
                                    </h6>
                                    <small class="text-muted">选择语言模型和参数</small>
                                </div>
                                <div class="card-body pt-2">
                                    <div class="row g-3">
                                        <!-- 平台选择 -->
                                        <div class="col-6">
                                            <label class="form-label fw-semibold text-dark">平台</label>
                                            <select class="form-select" name="platform" id="createPlatformSelect"
                                                    onchange="onPlatformChange('create')">
                                                <option value="">请选择平台...</option>
                                            </select>
                                        </div>

                                        <!-- 模型选择 -->
                                        <div class="col-6">
                                            <label class="form-label fw-semibold text-dark">模型</label>
                                            <select class="form-select" name="model" id="createModelSelect">
                                                <option value="">请先选择平台...</option>
                                            </select>
                                        </div>

                                        <!-- Temperature -->
                                        <div class="col-6" id="createTemperatureDiv">
                                            <label class="form-label fw-semibold text-dark">
                                                Temperature
                                                <i class="bi bi-question-circle text-muted ms-1"
                                                   title="控制输出的随机性，值越高越随机"
                                                   data-bs-toggle="tooltip"></i>
                                            </label>
                                            <input type="number" class="form-control" name="temperature"
                                                   value="0.7" min="0" max="2" step="0.1">
                                        </div>

                                        <!-- Max Tokens -->
                                        <div class="col-6" id="createMaxTokensDiv">
                                            <label class="form-label fw-semibold text-dark">
                                                Max Tokens
                                                <i class="bi bi-question-circle text-muted ms-1"
                                                   title="最大输出长度"
                                                   data-bs-toggle="tooltip"></i>
                                            </label>
                                            <input type="number" class="form-control" name="maxTokens"
                                                   value="2000" min="100" max="4000" step="100">
                                        </div>

                                        <!-- Top P -->
                                        <div class="col-6" id="createTopPDiv">
                                            <label class="form-label fw-semibold text-dark">
                                                Top P
                                                <i class="bi bi-question-circle text-muted ms-1"
                                                   title="核采样参数，控制词汇选择范围"
                                                   data-bs-toggle="tooltip"></i>
                                            </label>
                                            <input type="number" class="form-control" name="topP"
                                                   value="0.9" min="0.1" max="1" step="0.1">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- JSON输出模板 -->
                        <div class="col-lg-6">
                            <div class="card border-0 bg-light">
                                <div class="card-header bg-transparent border-0 pb-2">
                                    <h6 class="card-title mb-0 fw-bold text-dark">
                                        <i class="bi bi-file-earmark-code text-primary me-2"></i>JSON输出模板
                                    </h6>
                                    <small class="text-muted">基于业务模板自动生成</small>
                                </div>
                                <div class="card-body pt-2">
                                    <div class="border rounded bg-white p-3" style="height: 240px; overflow-y: auto;">
                                        <pre id="selectedTemplateJson" class="mb-0 text-muted small"
                                             style="font-family: 'Consolas', 'Monaco', monospace; line-height: 1.4;">请先选择业务模板</pre>
                                    </div>
                                    <div class="form-text mt-2">
                                        <i class="bi bi-info-circle text-info me-1"></i>
                                        Agent将按照此JSON格式输出结果
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>

            <!-- 模态框底部 -->
            <div class="modal-footer bg-light border-0 px-4 py-3">
                <div class="d-flex justify-content-between align-items-center w-100">
                    <!-- 左侧提示信息 -->
                    <div class="text-muted small">
                        <i class="bi bi-info-circle me-1"></i>
                        创建后可在调试台进行测试和优化
                    </div>

                    <!-- 右侧操作按钮 -->
                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                            <i class="bi bi-x-lg me-2"></i>取消
                        </button>
                        <button type="button" class="btn btn-outline-primary" hidden="hidden" onclick="previewAgent()">
                            <i class="bi bi-eye me-2"></i>预览
                        </button>
                        <button type="button" class="btn btn-primary px-4" onclick="createAgentFromTemplate()">
                            <i class="bi bi-plus-lg me-2"></i>创建Agent
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 编辑Agent模态框 -->
<div class="modal fade" id="editAgentModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="bi bi-pencil me-2"></i>编辑Agent</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editAgentForm">
                    <input type="hidden" id="editAgentId">
                    <div class="row g-4">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Agent名称 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" name="agentName" id="editAgentName" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label fw-bold">Agent编码</label>
                                <input type="text" class="form-control" name="agentCode" id="editAgentCode" readonly>
                                <small class="text-muted">编码不可修改</small>
                            </div>
                            <div class="mb-3">
                                <label class="form-label fw-bold">描述</label>
                                <textarea class="form-control" name="description" id="editAgentDescription" rows="3"></textarea>
                            </div>
                            <div class="mb-3">
                                <label class="form-label fw-bold">业务模板 <span class="text-danger">*</span></label>
                                <select class="form-select" name="templateId" id="editAgentTemplateSelect" required onchange="loadSelectedEditTemplate()">
                                    <option value="">请选择业务模板</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label fw-bold">分类</label>
                                <select class="form-select" name="categoryId" id="editAgentCategorySelect">
                                    <option value="">请选择分类...</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <label class="form-label fw-bold mb-0">提示词模板 <span class="text-danger">*</span></label>
                                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="showEditPromptTemplateModal()">
                                        <i class="bi bi-pencil-square me-1"></i>高级编辑器
                                    </button>
                                </div>
                                <textarea class="form-control" name="promptTemplate" id="editPromptTemplate" rows="8" required></textarea>
                                <small class="text-muted">使用高级编辑器可以分类填写角色、技能、Action、限制等关键点</small>
                            </div>
                        </div>
                    </div>

                    <div class="row g-4 mt-2">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">LLM配置</label>
                                <div class="parameter-panel">
                                    <div class="row g-3">
                                        <div class="col-6">
                                            <label class="form-label small">平台</label>
                                            <select class="form-select form-select-sm" name="platform" id="editPlatformSelect" onchange="onPlatformChange('edit')">
                                                <option value="">请选择平台...</option>
                                            </select>
                                        </div>
                                        <div class="col-6">
                                            <label class="form-label small">模型</label>
                                            <select class="form-select form-select-sm" name="model" id="editModelSelect">
                                                <option value="">请先选择平台...</option>
                                            </select>
                                        </div>
                                        <div class="col-6" id="editTemperatureDiv">
                                            <label class="form-label small">Temperature</label>
                                            <input type="number" class="form-control form-control-sm" name="temperature" id="editTemperature"
                                                   value="0.7" min="0" max="2" step="0.1">
                                        </div>
                                        <div class="col-6" id="editMaxTokensDiv">
                                            <label class="form-label small">Max Tokens</label>
                                            <input type="number" class="form-control form-control-sm" name="maxTokens" id="editMaxTokens"
                                                   value="2000" min="100" max="4000" step="100">
                                        </div>
                                        <div class="col-6" id="editTopPDiv">
                                            <label class="form-label small">Top P</label>
                                            <input type="number" class="form-control form-control-sm" name="topP" id="editTopP"
                                                   value="0.9" min="0.1" max="1" step="0.1">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">JSON输出模板</label>
                                <div class="bg-light border rounded p-3" style="height: 200px; overflow-y: auto;">
                                    <pre id="selectedEditTemplateJson" class="mb-0 text-muted">请先选择业务模板</pre>
                                </div>
                                <small class="text-muted">基于选择的业务模板自动设置</small>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-outline-primary" onclick="previewEditAgent()">预览</button>
                <button type="button" class="btn btn-primary" onclick="updateAgent()">
                    <i class="bi bi-check-lg me-2"></i>保存修改
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Agent版本历史模态框 -->
<div class="modal fade" id="agentVersionHistoryModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-clock-history me-2"></i>版本历史
                    <span class="text-muted" id="agentVersionModalTitle"></span>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>
                        选择历史版本，点击“调试”可以测试该版本；点击"申请发布"，审批后可以将Agent切换到该版本；
                    </div>
                </div>
                <div id="agentVersionsList">
                    <div class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2 text-muted">正在加载版本历史...</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- 调试历史模态框 -->
<div class="modal fade" id="debugHistoryModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-bug me-2"></i>调试历史
                    <span class="text-muted" id="debugAgentName"></span>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between">
                                <h6 class="mb-0">调试记录</h6>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-primary" onclick="filterDebugHistory('all')">全部</button>
                                    <button class="btn btn-outline-success" onclick="filterDebugHistory('success')">成功</button>
                                    <button class="btn btn-outline-danger" onclick="filterDebugHistory('error')">失败</button>
                                </div>
                            </div>
                            <div class="card-body p-0">
                                <div class="table-responsive" style="max-height: 400px;">
                                    <table class="table table-sm mb-0">
                                        <thead class="table-light sticky-top">
                                            <tr>
                                                <th>时间</th>
                                                <th>版本</th>
                                                <th>模型</th>
                                                <th>状态</th>
                                                <th>耗时</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="debugHistoryTable">
                                            <!-- 调试历史将在这里动态加载 -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">性能统计</h6>
                            </div>
                            <div class="card-body">
                                <div class="row text-center">
                                    <div class="col-6">
                                        <div class="small text-muted">总调用</div>
                                        <div class="h4 mb-0" id="totalCalls">0</div>
                                    </div>
                                    <div class="col-6">
                                        <div class="small text-muted">成功率</div>
                                        <div class="h4 mb-0 text-success" id="successRate">0%</div>
                                    </div>
                                </div>
                                <hr>
                                <div class="row text-center">
                                    <div class="col-6">
                                        <div class="small text-muted">平均耗时</div>
                                        <div class="fw-bold" id="avgTime">0ms</div>
                                    </div>
                                    <div class="col-6">
                                        <div class="small text-muted">最快响应</div>
                                        <div class="fw-bold text-success" id="minTime">0ms</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Tab式提示词编辑器模态框 -->
<div class="modal fade" id="agentPromptTemplateModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-chat-quote me-2"></i>提示词模板编辑器
                </h5>
                <div class="d-flex gap-2">
                    <button type="button" class="btn btn-outline-info btn-sm" onclick="loadDefaultPromptTemplate(); updateAgentFinalPrompt();">
                        <i class="bi bi-file-earmark-text me-1"></i>加载示例模板
                    </button>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
            </div>
            <div class="modal-body">
                <!-- Tab导航 -->
                <ul class="nav nav-tabs" id="agentPromptTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="agent-role-tab" data-bs-toggle="tab" data-bs-target="#agent-role-pane"
                                type="button" role="tab" aria-controls="agent-role-pane" aria-selected="true">
                            <i class="bi bi-person-badge me-1"></i>角色
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="agent-skills-tab" data-bs-toggle="tab" data-bs-target="#agent-skills-pane"
                                type="button" role="tab" aria-controls="agent-skills-pane" aria-selected="false">
                            <i class="bi bi-gear me-1"></i>技能
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="agent-action-tab" data-bs-toggle="tab" data-bs-target="#agent-action-pane"
                                type="button" role="tab" aria-controls="agent-action-pane" aria-selected="false">
                            <i class="bi bi-play-circle me-1"></i>Action
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="agent-constraints-tab" data-bs-toggle="tab" data-bs-target="#agent-constraints-pane"
                                type="button" role="tab" aria-controls="agent-constraints-pane" aria-selected="false">
                            <i class="bi bi-shield-check me-1"></i>限制
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="agent-final-tab" data-bs-toggle="tab" data-bs-target="#agent-final-pane"
                                type="button" role="tab" aria-controls="agent-final-pane" aria-selected="false">
                            <i class="bi bi-file-text me-1"></i>最终提示词
                        </button>
                    </li>
                </ul>

                <!-- Tab内容 -->
                <div class="tab-content mt-3" id="agentPromptTabContent">
                    <!-- 角色Tab -->
                    <div class="tab-pane fade show active" id="agent-role-pane" role="tabpanel" aria-labelledby="agent-role-tab">
                        <div class="mb-3">
                            <label class="form-label fw-bold">角色定义</label>
                            <textarea class="form-control" id="agentRoleContent" rows="6"
                                      placeholder="请定义AI的角色和专业领域，例如：&#10;你是一位航空货运专家，精通航空主单、IATA-Cargo/ONE-Record标准"
                                      style="font-family: 'Consolas', 'Monaco', monospace;"
                                      oninput="updateAgentFinalPrompt()"></textarea>
                            <small class="text-muted">定义AI助手的身份、专业背景和核心能力</small>
                        </div>
                    </div>

                    <!-- 技能Tab -->
                    <div class="tab-pane fade" id="agent-skills-pane" role="tabpanel" aria-labelledby="agent-skills-tab">
                        <div class="mb-3">
                            <label class="form-label fw-bold">技能列表</label>
                            <textarea class="form-control" id="agentSkillsContent" rows="12"
                                      placeholder="请列出AI需要掌握的具体技能，例如：&#10;### 技能 1: 整理订单数据&#10;识别主运单的信息，使用IATA-Cargo/ONE-Record字段的说明将其对应填入以下JSON数据中..."
                                      style="font-family: 'Consolas', 'Monaco', monospace;"
                                      oninput="updateAgentFinalPrompt()"></textarea>
                            <small class="text-muted">详细描述AI需要具备的各项技能和能力</small>
                        </div>
                    </div>

                    <!-- Action Tab -->
                    <div class="tab-pane fade" id="agent-action-pane" role="tabpanel" aria-labelledby="agent-action-tab">
                        <div class="mb-3">
                            <label class="form-label fw-bold">执行动作</label>
                            <textarea class="form-control" id="agentActionContent" rows="6"
                                      placeholder="请描述AI需要执行的具体操作，例如：&#10;请使用 技能1 和技能2 对给定的航空主单内容进行格式化，并按制定的Json输出，不需要其他无关内容"
                                      style="font-family: 'Consolas', 'Monaco', monospace;"
                                      oninput="updateAgentFinalPrompt()"></textarea>
                            <small class="text-muted">明确AI需要执行的具体任务和操作步骤</small>
                        </div>
                    </div>

                    <!-- 限制Tab -->
                    <div class="tab-pane fade" id="agent-constraints-pane" role="tabpanel" aria-labelledby="agent-constraints-tab">
                        <div class="mb-3">
                            <label class="form-label fw-bold">约束条件</label>
                            <textarea class="form-control" id="agentConstraintsContent" rows="6"
                                      placeholder="请设定AI的约束条件和限制，例如：&#10;1. 严格按照JSON格式中规定的数据类型和长度要求进行整理。&#10;2. 不要添加任何额外的解释或说明文字。"
                                      style="font-family: 'Consolas', 'Monaco', monospace;"
                                      oninput="updateAgentFinalPrompt()"></textarea>
                            <small class="text-muted">设定AI的行为约束和输出要求</small>
                        </div>
                    </div>

                    <!-- 最终提示词Tab -->
                    <div class="tab-pane fade" id="agent-final-pane" role="tabpanel" aria-labelledby="agent-final-tab">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <label class="form-label fw-bold mb-0">最终提示词预览</label>
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="copyAgentFinalPrompt()">
                                <i class="bi bi-clipboard me-1"></i>复制到剪贴板
                            </button>
                        </div>
                        <div class="border rounded p-3" style="background-color: #f8f9fa; max-height: 400px; overflow-y: auto;">
                            <pre id="agentFinalPromptPreview" style="white-space: pre-wrap; margin: 0; font-family: 'Consolas', 'Monaco', monospace; font-size: 13px;"></pre>
                        </div>
                        <div class="alert alert-info mt-3">
                            <i class="bi bi-info-circle me-2"></i>
                            <strong>提示：</strong>这是根据各个Tab内容自动生成的完整提示词，您可以复制使用
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveAgentPromptTemplate()">
                    <i class="bi bi-check-lg"></i> 保存提示词
                </button>
            </div>
        </div>
    </div>
</div>

<script src="../assets/js/agents.js"></script>
</body>
</html>