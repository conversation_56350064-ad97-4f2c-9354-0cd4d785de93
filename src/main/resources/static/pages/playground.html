<!-- 智能调试台 -->
<div class="container-fluid">
    <!-- 上半部分：参数调节区域 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="bi bi-sliders me-2"></i>参数调节</h5>
                    <button class="btn btn-outline-secondary btn-sm" onclick="toggleParameterPanel()" id="parameterToggleBtn">
                        <i class="bi bi-chevron-up"></i> 收起
                    </button>
                </div>
                <div class="card-body" id="parameterPanelBody">
                    <div class="row g-4">
                        <!-- Agent和版本选择 -->
                        <div class="col-lg-3">
                            <div class="mb-3">
                                <label class="form-label fw-bold">选择Agent</label>
                                <select class="form-select" id="playgroundAgentSelect" onchange="onPlaygroundAgentChange()">
                                    <option value="">请选择Agent...</option>
                                </select>
                            </div>
                            <!-- 版本选择 -->
                            <div class="mb-3" id="versionSelectDiv" style="display: none;">
                                <label class="form-label fw-bold">选择版本</label>
                                <div class="d-flex gap-2">
                                    <select class="form-select" id="playgroundVersionSelect" onchange="onPlaygroundVersionChange()">
                                        <option value="">请先选择Agent...</option>
                                    </select>
                                    <button class="btn btn-outline-secondary btn-sm" onclick="refreshVersionList()" title="刷新版本列表">
                                        <i class="bi bi-arrow-clockwise"></i>
                                    </button>
                                </div>
                                <small class="text-muted">切换版本将自动回填对应的参数配置</small>
                            </div>
                        </div>

                        <!-- 平台和模型选择 -->
                        <div class="col-lg-3">
                            <div class="mb-3">
                                <label class="form-label fw-bold">选择平台</label>
                                <select class="form-select" id="playgroundPlatformSelect" onchange="onPlaygroundPlatformChange()">
                                    <option value="">请选择平台...</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label fw-bold">选择模型</label>
                                <select class="form-select" id="playgroundModelSelect" onchange="updateModel()">
                                    <option value="">请先选择平台...</option>
                                </select>
                            </div>
                        </div>

                        <!-- LLM参数调节 -->
                        <div class="col-lg-4">
                            <h6 class="fw-bold mb-3">LLM参数</h6>
                            <div class="row g-3" id="playgroundParametersDiv">
                                <div class="col-4">
                                    <label class="form-label small mb-1">Temperature</label>
                                    <div class="d-flex align-items-center">
                                        <div class="range-container me-2" style="flex: 1;">
                                            <input type="range" class="form-range" id="temperatureSlider"
                                                   min="0" max="2" step="0.1" value="0.7"
                                                   oninput="updateParameter('temperature', this.value)">
                                        </div>
                                        <span class="badge bg-primary small" id="temperatureValue">0.7</span>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <label class="form-label small mb-1">Max Tokens</label>
                                    <div class="d-flex align-items-center">
                                        <div class="range-container me-2" style="flex: 1;">
                                            <input type="range" class="form-range" id="maxTokensSlider"
                                                   min="100" max="4000" step="100" value="2000"
                                                   oninput="updateParameter('maxTokens', this.value)">
                                        </div>
                                        <span class="badge bg-primary small" id="maxTokensValue">2000</span>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <label class="form-label small mb-1">Top P</label>
                                    <div class="d-flex align-items-center">
                                        <div class="range-container me-2" style="flex: 1;">
                                            <input type="range" class="form-range" id="topPSlider"
                                                   min="0.1" max="1" step="0.1" value="0.9"
                                                   oninput="updateParameter('topP', this.value)">
                                        </div>
                                        <span class="badge bg-primary small" id="topPValue">0.9</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 功能按钮组 -->
                        <div class="col-lg-2">
                            <h6 class="fw-bold mb-3">快捷操作</h6>
                            <div class="d-grid gap-2">
                                <button class="btn btn-outline-secondary btn-sm" onclick="showVersionCompareModal()">
                                    <i class="bi bi-clock-history"></i> 版本对比
                                </button>
                                <button class="btn btn-outline-info btn-sm" onclick="showBusinessTemplateModal()">
                                    <i class="bi bi-file-code"></i> 业务模板
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 下半部分：三列布局 -->
    <div class="row g-4" id="mainWorkArea">
        <!-- 左列：文件上传区域 -->
        <div class="col-lg-3" id="fileUploadColumn">
            <div class="card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0"><i class="bi bi-cloud-upload me-2"></i>文件上传</h6>
                    <button class="btn btn-outline-secondary btn-sm" onclick="toggleFileUploadPanel()" id="fileToggleBtn">
                        <i class="bi bi-chevron-left"></i>
                    </button>
                    <!-- 收起状态下的展开按钮 -->
                    <button class="btn btn-outline-secondary btn-sm" onclick="toggleFileUploadPanel()" id="fileExpandBtn" style="display: none;">
                        <i class="bi bi-chevron-right"></i>
                    </button>
                </div>
                <div class="card-body d-flex flex-column h-100">
                    <div class="file-drop-zone border border-2 border-dashed rounded text-center"
                         id="playgroundFileZone"
                         style="height: 200px; padding: 20px; cursor: pointer; transition: all 0.3s ease;">
                        <i class="bi bi-cloud-upload fs-2 text-muted"></i>
                        <p class="mt-3 mb-2">拖拽文件到此处或点击选择</p>
                        <small class="text-muted">支持 JPG、PNG、PDF、TXT 格式<br>最大 5MB</small>
                        <input type="file" id="playgroundFileInput" style="display: none;" accept=".jpg,.jpeg,.png,.pdf,.txt">
                    </div>

                    <!-- 文件预览区域 -->
                    <div id="filePreviewArea" class="mt-3 flex-grow-1 d-flex flex-column d-none">
                        <div class="border rounded h-100 d-flex flex-column" style="background-color: var(--card-background); border-color: var(--border-color)!important;">
                            <div class="d-flex justify-content-between align-items-center p-2 border-bottom" style="background-color: var(--background-color); border-color: var(--border-color)!important; color: var(--dark-text);">
                                <small class="fw-bold" style="color: var(--dark-text);">文件预览</small>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-primary" onclick="showFilePreviewModal()" title="查看大图">
                                        <i class="bi bi-arrows-fullscreen"></i>
                                    </button>
                                    <button class="btn btn-outline-danger" onclick="clearFile()" title="删除文件">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </div>
                            </div>
                            <div id="filePreviewContent" class="flex-grow-1 p-3 overflow-auto"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 中列：提示词区域 -->
        <div class="col-lg-5" id="promptColumn">
            <div class="card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0"><i class="bi bi-chat-quote me-2"></i>提示词配置</h6>
                    <div class="d-flex gap-2">
                        <button class="btn btn-outline-primary" onclick="testWithParameters()" id="testBtn">
                            <i class="bi bi-play-fill me-1"></i>实时测试
                        </button>
                        <button class="btn btn-outline-success" onclick="saveAsNewVersion()" id="saveVersionBtn">
                            <i class="bi bi-plus-circle me-1"></i>保存版本
                        </button>
                        <button class="btn btn-outline-secondary" onclick="showPromptTemplateModal()" title="全屏编辑">
                            <i class="bi bi-arrows-fullscreen"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body d-flex flex-column">
                    <!-- 提示词模板 -->
                    <div class="flex-grow-1">
                        <textarea class="form-control h-100" id="promptTemplate"
                                  placeholder="请输入提示词模板..." onchange="updatePrompt()"></textarea>
                    </div>
                </div>
            </div>
        </div>

        <!-- 右列：输出结果区域 -->
        <div class="col-lg-4" id="resultColumn">
            <div class="card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0"><i class="bi bi-eye me-2"></i>输出结果</h6>
                    <button class="btn btn-outline-primary btn-sm" onclick="showResultCompareModal()" id="viewResultBtn" style="display: none;">
                        <i class="bi bi-arrows-fullscreen"></i> 查看
                    </button>
                </div>
                <div class="card-body d-flex flex-column">
                    <!-- 状态信息 - 隐藏重复的响应时间和Tokens -->
<!--                    <div hidden="hidden" class="d-flex justify-content-between align-items-center mb-3" id="resultStatusInfo" style="display: none;">-->
<!--                        <div class="d-flex gap-2">-->
<!--                            <span class="badge bg-success" id="responseTime">响应时间: -</span>-->
<!--                            <span class="badge bg-info" id="tokenCount">Tokens: -</span>-->
<!--                        </div>-->
<!--                    </div>-->

                    <!-- 结果显示区域 - 使用flex-grow-1实现动态高度 -->
                    <div id="playgroundResult" class="flex-grow-1" style="overflow-y: auto; min-height: 300px;">
                        <div class="text-center text-muted py-5">
                            <i class="bi bi-lightbulb fs-1"></i>
                            <p class="mt-3">调整参数并上传文件，点击实时测试查看效果</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 版本对比模态框 -->
<div class="modal fade" id="versionCompareModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-clock-history me-2"></i>历史版本对比
                    <span class="text-muted" id="versionAgentName"></span>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <label class="form-label fw-bold">当前版本</label>
                        <div class="card">
                            <div class="card-body">
                                <h6 class="card-title" id="currentVersionTitle">当前版本</h6>
                                <p class="card-text small" id="currentVersionInfo">
                                    版本号: v1.0<br>
                                    创建时间: 2024-01-15<br>
                                    状态: 当前使用
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label fw-bold">选择对比版本</label>
                        <select class="form-select mb-3" id="compareVersionSelect" onchange="loadVersionDetails()">
                            <option value="">选择历史版本...</option>
                        </select>
                        <div class="card" id="compareVersionCard" style="display: none;">
                            <div class="card-body">
                                <h6 class="card-title" id="compareVersionTitle">-</h6>
                                <p class="card-text small" id="compareVersionInfo">
                                    请选择版本进行对比
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="mt-3" id="versionDiffArea" style="display: none;">
                    <label class="form-label fw-bold">版本差异</label>
                    <div class="border rounded p-3 bg-light" style="max-height: 200px; overflow-y: auto;">
                        <div id="versionDiffContent">
                            选择版本后将显示差异对比
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- 业务模板查看模态框 -->
<div class="modal fade" id="businessTemplateModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-file-code me-2"></i>业务模板详情
                    <span class="text-muted" id="templateAgentName"></span>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label fw-bold">JSON模板结构</label>
                    <div class="border rounded p-3" style="background: #f8f9fa; max-height: 400px; overflow-y: auto;">
                        <pre class="mb-0" id="fullBusinessTemplate" style="font-size: 13px;">
{
  "loading": "请先选择Agent..."
}
                        </pre>
                    </div>
                </div>
                <div class="alert alert-info">
                    <i class="bi bi-info-circle me-2"></i>
                    <strong>说明：</strong>这是Agent输出的JSON格式模板，用于结构化数据提取
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- 保存新版本模态框 -->
<div class="modal fade" id="saveVersionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-plus-circle me-2"></i>保存为新版本
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label fw-bold">版本变更说明 <span class="text-danger">*</span></label>
                    <textarea class="form-control" id="versionChangeLog" rows="4"
                              placeholder="请详细描述本次版本的主要变更内容，如：优化了提示词模板，提高了识别准确率..."
                              maxlength="500" oninput="updateChangeLogCount()"></textarea>
                    <div class="form-text">
                        <span id="changeLogCount">0</span>/500 字符
                    </div>
                </div>
                <div class="alert alert-info">
                    <i class="bi bi-info-circle me-2"></i>
                    <strong>说明：</strong>新版本将保存为历史记录，不会影响当前正在使用的版本。
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="bi bi-x-lg me-1"></i>取消
                </button>
                <button type="button" class="btn btn-primary" onclick="confirmSaveVersion()" id="confirmSaveBtn">
                    <i class="bi bi-check-lg me-1"></i>确认保存
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 提示词模板编辑模态框 -->
<div class="modal fade" id="promptTemplateModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-chat-quote me-2"></i>提示词模板编辑器
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <!-- Tab导航 -->
                <ul class="nav nav-tabs" id="promptTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="role-tab" data-bs-toggle="tab" data-bs-target="#role-pane"
                                type="button" role="tab" aria-controls="role-pane" aria-selected="true">
                            <i class="bi bi-person-badge me-1"></i>角色
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="skills-tab" data-bs-toggle="tab" data-bs-target="#skills-pane"
                                type="button" role="tab" aria-controls="skills-pane" aria-selected="false">
                            <i class="bi bi-gear me-1"></i>技能
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="action-tab" data-bs-toggle="tab" data-bs-target="#action-pane"
                                type="button" role="tab" aria-controls="action-pane" aria-selected="false">
                            <i class="bi bi-play-circle me-1"></i>Action
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="constraints-tab" data-bs-toggle="tab" data-bs-target="#constraints-pane"
                                type="button" role="tab" aria-controls="constraints-pane" aria-selected="false">
                            <i class="bi bi-shield-check me-1"></i>限制
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="final-tab" data-bs-toggle="tab" data-bs-target="#final-pane"
                                type="button" role="tab" aria-controls="final-pane" aria-selected="false">
                            <i class="bi bi-file-text me-1"></i>最终提示词
                        </button>
                    </li>
                </ul>

                <!-- Tab内容 -->
                <div class="tab-content mt-3" id="promptTabContent">
                    <!-- 角色Tab -->
                    <div class="tab-pane fade show active" id="role-pane" role="tabpanel" aria-labelledby="role-tab">
                        <div class="mb-3">
                            <label class="form-label fw-bold">角色定义</label>
                            <textarea class="form-control" id="roleContent" rows="6"
                                      placeholder="请定义AI的角色和专业领域，例如：&#10;你是一位航空货运专家，精通航空主单、IATA-Cargo/ONE-Record标准"
                                      style="font-family: 'Consolas', 'Monaco', monospace;"
                                      oninput="updateFinalPrompt()"></textarea>
                            <small class="text-muted">定义AI助手的身份、专业背景和核心能力</small>
                        </div>
                    </div>

                    <!-- 技能Tab -->
                    <div class="tab-pane fade" id="skills-pane" role="tabpanel" aria-labelledby="skills-tab">
                        <div class="mb-3">
                            <label class="form-label fw-bold">技能列表</label>
                            <textarea class="form-control" id="skillsContent" rows="12"
                                      placeholder="请列出AI需要掌握的具体技能，例如：&#10;### 技能 1: 整理订单数据&#10;识别主运单的信息，使用IATA-Cargo/ONE-Record字段的说明将其对应填入以下JSON数据中..."
                                      style="font-family: 'Consolas', 'Monaco', monospace;"
                                      oninput="updateFinalPrompt()"></textarea>
                            <small class="text-muted">详细描述AI需要具备的各项技能和能力</small>
                        </div>
                    </div>

                    <!-- Action Tab -->
                    <div class="tab-pane fade" id="action-pane" role="tabpanel" aria-labelledby="action-tab">
                        <div class="mb-3">
                            <label class="form-label fw-bold">执行动作</label>
                            <textarea class="form-control" id="actionContent" rows="6"
                                      placeholder="请描述AI需要执行的具体操作，例如：&#10;请使用 技能1 和技能2 对给定的航空主单内容进行格式化，并按制定的Json输出，不需要其他无关内容"
                                      style="font-family: 'Consolas', 'Monaco', monospace;"
                                      oninput="updateFinalPrompt()"></textarea>
                            <small class="text-muted">明确AI需要执行的具体任务和操作步骤</small>
                        </div>
                    </div>

                    <!-- 限制Tab -->
                    <div class="tab-pane fade" id="constraints-pane" role="tabpanel" aria-labelledby="constraints-tab">
                        <div class="mb-3">
                            <label class="form-label fw-bold">约束条件</label>
                            <textarea class="form-control" id="constraintsContent" rows="6"
                                      placeholder="请设定AI的约束条件和限制，例如：&#10;1. 严格按照JSON格式中规定的数据类型和长度要求进行整理。&#10;2. 不要添加任何额外的解释或说明文字。"
                                      style="font-family: 'Consolas', 'Monaco', monospace;"
                                      oninput="updateFinalPrompt()"></textarea>
                            <small class="text-muted">设定AI的行为约束和输出要求</small>
                        </div>
                    </div>

                    <!-- 最终提示词Tab -->
                    <div class="tab-pane fade" id="final-pane" role="tabpanel" aria-labelledby="final-tab">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <label class="form-label fw-bold mb-0">最终提示词预览</label>
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="copyFinalPrompt()">
                                <i class="bi bi-clipboard me-1"></i>复制到剪贴板
                            </button>
                        </div>
                        <div class="border rounded p-3" style="background-color: #f8f9fa; max-height: 400px; overflow-y: auto;">
                            <pre id="finalPromptPreview" style="white-space: pre-wrap; margin: 0; font-family: 'Consolas', 'Monaco', monospace; font-size: 13px;"></pre>
                        </div>
                        <div class="alert alert-info mt-3">
                            <i class="bi bi-info-circle me-2"></i>
                            <strong>提示：</strong>这是根据各个Tab内容自动生成的完整提示词，您可以复制使用
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="savePromptTemplate()">
                    <i class="bi bi-check-lg"></i> 保存提示词
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 文件预览模态框 -->
<div class="modal fade" id="filePreviewModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-file-earmark me-2"></i>文件预览
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="filePreviewModalContent" class="text-center">
                    <p class="text-muted">暂无文件</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- 结果对比查看模态框 -->
<div class="modal fade" id="resultCompareModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-layout-split me-2"></i>文件与结果对比查看
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row g-4">
                    <!-- 左侧：上传的文件 -->
                    <div class="col-md-6">
                        <div class="card h-100">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="bi bi-file-earmark me-2"></i>上传的文件</h6>
                            </div>
                            <div class="card-body">
                                <div id="resultCompareFileContent" style="max-height: 500px; overflow-y: auto;">
                                    <p class="text-muted text-center">暂无文件</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- 右侧：输出结果 -->
                    <div class="col-md-6">
                        <div class="card h-100">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="bi bi-cpu me-2"></i>AI输出结果</h6>
                            </div>
                            <div class="card-body">
                                <div id="resultCompareOutputContent" style="max-height: 500px; overflow-y: auto;">
                                    <p class="text-muted text-center">暂无结果</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- 智能调试台样式 -->
<style>
/* 文件上传区域样式 */
.file-drop-zone {
    border-color: var(--border-color) !important;
    background-color: var(--background-color) !important;
    transition: all 0.3s ease;
    color: var(--dark-text) !important;
}

.file-drop-zone:hover {
    border-color: #009EF7 !important;
    background-color: var(--card-background) !important;
}

.file-drop-zone.dragover {
    border-color: #009EF7 !important;
    background-color: var(--card-background) !important;
    transform: scale(1.02);
}

/* 文件预览样式 */
.file-preview {
    text-align: center;
    background-color: var(--card-background) !important;
    color: var(--dark-text) !important;
}

.file-preview .preview-image img {
    border: 1px solid var(--border-color);
    box-shadow: var(--light-shadow);
}

.file-preview .preview-pdf iframe {
    border: 1px solid var(--border-color);
    box-shadow: var(--light-shadow);
}

.file-preview .preview-text textarea {
    background-color: var(--background-color) !important;
    border: 1px solid var(--border-color) !important;
    color: var(--dark-text) !important;
    resize: none;
}

/* 参数调节区域样式 */
.parameter-panel .form-range {
    height: 4px;
}

.parameter-panel .badge {
    min-width: 45px;
    font-size: 11px;
}

/* JSON输出样式 */
.json-result-container {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    overflow: hidden;
}

.json-output-wrapper {
    max-height: 400px;
    overflow-y: auto;
    transition: max-height 0.3s ease;
}

.json-output {
    margin: 0;
    padding: 16px;
    background: #ffffff;
    border: none;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.5;
    color: #333;
    white-space: pre-wrap;
    word-wrap: break-word;
    border-radius: 0;
}

/* JSON语法高亮样式 */
.json-key {
    color: #0066cc;
    font-weight: 600;
}

.json-string {
    color: #008000;
}

.json-number {
    color: #ff6600;
    font-weight: 500;
}

.json-boolean {
    color: #cc0066;
    font-weight: 600;
}

.json-null {
    color: #999999;
    font-style: italic;
}

/* 滚动条样式 */
.json-output-wrapper::-webkit-scrollbar,
#playgroundResult::-webkit-scrollbar {
    width: 8px;
}

.json-output-wrapper::-webkit-scrollbar-track,
#playgroundResult::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.json-output-wrapper::-webkit-scrollbar-thumb,
#playgroundResult::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.json-output-wrapper::-webkit-scrollbar-thumb:hover,
#playgroundResult::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 卡片等高样式 */
.card.h-100 {
    height: 100% !important;
}

/* 卡片标题统一高度 */
.card-header {
    min-height: 60px;
    padding: 0.75rem 1rem;
}

.card-header h6 {
    line-height: 1.5;
}

/* 按钮组优化 */
.card-header .btn-sm {
    white-space: nowrap;
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-width: 1px;
    line-height: 1.2;
}

.card-header .btn-sm i {
    font-size: 0.75rem;
}

/* 按钮间距优化 */
.card-header .d-flex.gap-1 {
    gap: 0.25rem !important;
}

/* 按钮悬停效果优化 */
.card-header .btn-outline-primary:hover {
    background-color: #009EF7;
    border-color: #009EF7;
    color: white;
}

.card-header .btn-outline-success:hover {
    background-color: #50CD89;
    border-color: #50CD89;
    color: white;
}

.card-header .btn-outline-secondary:hover {
    background-color: #5E6278;
    border-color: #5E6278;
    color: white;
}

/* 确保所有卡片标题按钮样式一致 */
.card-header .btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

/* 收放功能样式 */
.collapsed-panel {
    transition: all 0.3s ease;
}

.collapsed-panel .card-body {
    display: none;
}

/* 参数面板收起时的样式调整 */
.parameter-panel-collapsed #mainWorkArea {
    margin-top: -1rem;
    transition: all 0.3s ease;
}

.parameter-panel-collapsed .card.h-100 {
    height: calc(100vh - 120px) !important;
    transition: all 0.3s ease;
}

.parameter-panel-expanded .card.h-100 {
    height: auto !important;
    min-height: 500px;
    transition: all 0.3s ease;
}

/* 确保卡片内容区域正确填充 */
.parameter-panel-collapsed .card-body.d-flex.flex-column {
    height: calc(100% - 60px);
}

.parameter-panel-collapsed .card-body .flex-grow-1 {
    height: 100%;
}

.file-upload-collapsed {
    width: 60px !important;
    min-width: 60px !important;
    max-width: 60px !important;
    transition: all 0.3s ease;
}

.file-upload-collapsed .card-body {
    display: none !important;
}

.file-upload-collapsed .card-header h6 {
    display: none !important;
}

.file-upload-collapsed .card-header #fileToggleBtn {
    display: none !important;
}

.file-upload-collapsed .card-header {
    padding: 0.5rem;
    text-align: center;
    justify-content: center !important;
}

.file-upload-collapsed .card-header #fileExpandBtn {
    display: block !important;
}

/* 动态列宽调整 */
.prompt-expanded {
    transition: all 0.3s ease;
}

.result-expanded {
    transition: all 0.3s ease;
}

/* 提示词文本框样式优化 */
#promptTemplate {
    height: 100% !important;
    min-height: 400px;
    resize: none;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif !important;
    font-size: 14px !important;
    line-height: 1.6;
    border: 1px solid #E4E6EA;
    border-radius: 0.375rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out, height 0.3s ease;
    padding: 0.75rem;
    background-color: #FFFFFF;
}

#promptTemplate:focus {
    border-color: #009EF7;
    box-shadow: 0 0 0 0.2rem rgba(0, 158, 247, 0.25);
    outline: none;
}

#promptTemplate::placeholder {
    color: #A1A5B7;
    font-style: italic;
}

/* 参数面板收起时提示词文本框和输出结果高度调整 */
.parameter-panel-collapsed #promptTemplate {
    min-height: calc(100vh - 280px);
}

.parameter-panel-collapsed #playgroundResult {
    min-height: calc(100vh - 280px);
}

/* 确保文本框在flex容器中正确填充 */
.card-body.d-flex.flex-column .flex-grow-1 {
    display: flex;
    flex-direction: column;
}

.card-body.d-flex.flex-column .flex-grow-1 #promptTemplate {
    flex: 1;
}

/* 输出结果区域样式优化 */
#playgroundResult {
    transition: all 0.3s ease;
    border-radius: 0.375rem;
}

#playgroundResult.flex-grow-1 {
    display: flex !important;
    flex-direction: column;
}

/* 输出结果内容自适应高度 */
#playgroundResult .alert {
    margin-bottom: 0;
    flex: 1;
    display: flex;
    flex-direction: column;
}

#playgroundResult .json-result-container {
    flex: 1;
    display: flex;
    flex-direction: column;
}

#playgroundResult .json-output-wrapper {
    flex: 1;
    max-height: none;
    overflow-y: auto;
}

/* 文件上传区域样式优化 */
#fileUploadColumn .card-body {
    height: calc(100% - 60px);
}

/* 文件预览区域高度自适应 */
#filePreviewArea {
    min-height: 300px;
}

#filePreviewArea.flex-grow-1 {
    display: flex !important;
}

#filePreviewContent {
    max-height: none;
    overflow-y: auto;
    background-color: var(--card-background) !important;
    color: var(--dark-text) !important;
}

/* ==================== 深色主题适配 ==================== */

/* 业务模板模态框深色主题适配 */
.theme-dark #businessTemplateModal .modal-content {
    background-color: var(--card-background);
    color: var(--dark-text);
}

.theme-dark #businessTemplateModal .modal-header {
    background-color: var(--background-color);
    border-bottom-color: var(--border-color);
    color: var(--dark-text);
}

.theme-dark #businessTemplateModal .modal-body {
    background-color: var(--card-background);
    color: var(--dark-text);
}

.theme-dark #businessTemplateModal .border {
    border-color: var(--border-color) !important;
    background-color: var(--background-color) !important;
}

.theme-dark #businessTemplateModal pre {
    background-color: var(--background-color) !important;
    color: var(--dark-text) !important;
    border-color: var(--border-color) !important;
}

.theme-dark #businessTemplateModal .alert-info {
    background-color: rgba(114, 57, 234, 0.1);
    border-color: rgba(114, 57, 234, 0.2);
    color: var(--dark-text);
}

/* 输出结果区域深色主题适配 */
.theme-dark #playgroundResult {
    background-color: var(--card-background);
    color: var(--dark-text);
}

.theme-dark #playgroundResult .alert {
    background-color: var(--card-background);
    border-color: var(--border-color);
    color: var(--dark-text);
}

.theme-dark #playgroundResult .alert-success {
    background-color: rgba(80, 205, 137, 0.1);
    border-color: rgba(80, 205, 137, 0.2);
    color: var(--dark-text);
}

.theme-dark #playgroundResult .json-result-container {
    background-color: var(--card-background);
    color: var(--dark-text);
}

.theme-dark #playgroundResult .json-output-wrapper {
    background-color: var(--background-color);
    border-color: var(--border-color);
    color: var(--dark-text);
}

/* 提示词文本框深色主题适配 */
.theme-dark #promptTemplate {
    background-color: var(--card-background);
    border-color: var(--border-color);
    color: var(--dark-text);
}

.theme-dark #promptTemplate:focus {
    background-color: var(--card-background);
    border-color: var(--primary-color);
    color: var(--dark-text);
}

.theme-dark #promptTemplate::placeholder {
    color: var(--light-text);
}

/* 文件预览区域深色主题适配 */
.theme-dark #filePreviewArea {
    background-color: var(--card-background);
    color: var(--dark-text);
}

.theme-dark #filePreviewContent {
    background-color: var(--background-color) !important;
    color: var(--dark-text) !important;
    border-color: var(--border-color) !important;
}

.theme-dark #filePreviewContent textarea {
    background-color: var(--background-color);
    border-color: var(--border-color);
    color: var(--dark-text);
}

/* 卡片组件深色主题适配 */
.theme-dark .card {
    background-color: var(--card-background);
    border-color: var(--border-color);
    color: var(--dark-text);
}

.theme-dark .card-header {
    background-color: var(--background-color);
    border-bottom-color: var(--border-color);
    color: var(--dark-text);
}

.theme-dark .card-body {
    background-color: var(--card-background);
    color: var(--dark-text);
}

/* 文件预览内容样式优化 */
#filePreviewContent .file-preview {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    background-color: var(--card-background) !important;
    color: var(--dark-text) !important;
}

#filePreviewContent .preview-image img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

#filePreviewContent .preview-pdf iframe {
    width: 100%;
    height: 100%;
    min-height: 300px;
    border: none;
    border-radius: 4px;
}

#filePreviewContent .preview-text textarea {
    width: 100%;
    height: 100%;
    min-height: 200px;
    border: none;
    background-color: var(--background-color) !important;
    color: var(--dark-text) !important;
    resize: none;
}

#filePreviewContent .preview-generic {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-color: var(--card-background) !important;
    color: var(--dark-text) !important;
}

/* 文件预览区域主题适配 */
#filePreviewArea {
    background-color: var(--card-background) !important;
    color: var(--dark-text) !important;
}

#filePreviewArea .border {
    border-color: var(--border-color) !important;
}

#filePreviewArea .border-bottom {
    border-color: var(--border-color) !important;
}

/* 确保所有文件预览子元素都适配主题 */
.file-preview,
.file-preview * {
    background-color: inherit !important;
    color: var(--dark-text) !important;
}

.file-preview textarea,
.file-preview input {
    background-color: var(--background-color) !important;
    color: var(--dark-text) !important;
    border-color: var(--border-color) !important;
}
}

/* 参数面板收起时文件预览区域高度调整 */
.parameter-panel-collapsed #filePreviewArea {
    min-height: calc(100vh - 350px);
}

/* 响应式调整 */
@media (max-width: 992px) {
    .col-lg-3, .col-lg-4, .col-lg-5 {
        margin-bottom: 1rem;
    }

    #playgroundResult {
        height: 300px !important;
    }
}

@media (max-width: 768px) {
    .json-output {
        font-size: 12px;
        padding: 12px;
    }

    .json-output-wrapper {
        max-height: 300px;
    }

    .parameter-panel .col-4 {
        margin-bottom: 1rem;
    }

    .file-drop-zone {
        height: 150px !important;
    }
}
</style>
