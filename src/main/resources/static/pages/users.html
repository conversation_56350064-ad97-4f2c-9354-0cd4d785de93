<!-- 用户管理页面 -->
<div class="users-content">

    <!-- Tab导航 -->
    <ul class="nav nav-tabs mb-4" id="userTabs" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active" id="system-users-tab" data-bs-toggle="tab" data-bs-target="#system-users"
                    type="button" role="tab" aria-controls="system-users" aria-selected="true">
                <i class="bi bi-gear me-2"></i>系统用户
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="platform-users-tab" data-bs-toggle="tab" data-bs-target="#platform-users"
                    type="button" role="tab" aria-controls="platform-users" aria-selected="false">
                <i class="bi bi-globe me-2"></i>平台用户
                <span class="badge bg-warning ms-1" id="pendingApprovalBadge" style="display: none;">0</span>
            </button>
        </li>
    </ul>

    <!-- Tab内容 -->
    <div class="tab-content" id="userTabsContent">
        <!-- 系统用户Tab -->
        <div class="tab-pane fade show active" id="system-users" role="tabpanel" aria-labelledby="system-users-tab">
            <!-- 操作栏和搜索过滤 -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div class="d-flex gap-2">
                    <button class="btn btn-primary" onclick="showCreateUserModal()" data-permission="USER_CREATE">
                        <i class="bi bi-person-plus"></i> 添加系统用户
                    </button>
                    <select class="form-select" id="systemUserRoleFilter" onchange="filterSystemUsers()" style="width: 150px;">
                        <option value="">全部角色</option>
                    </select>
                    <select class="form-select" id="systemUserStatusFilter" onchange="filterSystemUsers()" style="width: 120px;">
                        <option value="">全部状态</option>
                        <option value="1">正常</option>
                        <option value="0">禁用</option>
                    </select>
                </div>
                <div class="d-flex gap-2">
                    <input type="text" class="form-control" placeholder="搜索用户..." id="systemUserSearchInput" style="width: 250px;">
                    <button class="btn btn-outline-secondary" onclick="searchSystemUsers()">
                        <i class="bi bi-search"></i>
                    </button>
                </div>
            </div>

            <!-- 统计卡片 -->
            <div class="row g-4 mb-4">
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="bi bi-people text-primary" style="font-size: 2rem;"></i>
                            <h5 class="mt-2" id="totalSystemUsers">-</h5>
                            <small class="text-muted">系统用户总数</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="bi bi-person-check text-success" style="font-size: 2rem;"></i>
                            <h5 class="mt-2" id="activeSystemUsers">-</h5>
                            <small class="text-muted">正常用户</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="bi bi-shield-check text-warning" style="font-size: 2rem;"></i>
                            <h5 class="mt-2" id="adminSystemUsers">-</h5>
                            <small class="text-muted">管理员</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="bi bi-clock text-info" style="font-size: 2rem;"></i>
                            <h5 class="mt-2" id="todaySystemLogins">-</h5>
                            <small class="text-muted">今日登录</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 系统用户列表 -->
            <div class="card">
                <div class="card-body p-0">
                    <div id="systemUsersTable">
                        <div class="text-center py-5">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                            <div class="mt-2 text-muted">正在加载系统用户列表...</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 平台用户Tab -->
        <div class="tab-pane fade" id="platform-users" role="tabpanel" aria-labelledby="platform-users-tab">
            <!-- 搜索和过滤 -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div class="d-flex gap-2">
                    <select class="form-select" id="platformUserApprovalFilter" onchange="filterPlatformUsers()" style="width: 150px;">
                        <option value="">全部状态</option>
                        <option value="0">待审核</option>
                        <option value="1">已通过</option>
                        <option value="2">已拒绝</option>
                    </select>
                    <select class="form-select" id="platformUserStatusFilter" onchange="filterPlatformUsers()" style="width: 120px;">
                        <option value="">全部状态</option>
                        <option value="1">正常</option>
                        <option value="0">禁用</option>
                    </select>
                </div>
                <div class="d-flex gap-2">
                    <input type="text" class="form-control" placeholder="搜索用户..." id="platformUserSearchInput" style="width: 250px;">
                    <button class="btn btn-outline-secondary" onclick="searchPlatformUsers()">
                        <i class="bi bi-search"></i>
                    </button>
                </div>
            </div>

            <!-- 统计卡片 -->
            <div class="row g-4 mb-4">
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="bi bi-globe text-primary" style="font-size: 2rem;"></i>
                            <h5 class="mt-2" id="totalPlatformUsers">-</h5>
                            <small class="text-muted">平台用户总数</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="bi bi-clock-history text-warning" style="font-size: 2rem;"></i>
                            <h5 class="mt-2" id="pendingPlatformUsers">-</h5>
                            <small class="text-muted">待审核</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="bi bi-check-circle text-success" style="font-size: 2rem;"></i>
                            <h5 class="mt-2" id="approvedPlatformUsers">-</h5>
                            <small class="text-muted">已通过</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="bi bi-x-circle text-danger" style="font-size: 2rem;"></i>
                            <h5 class="mt-2" id="rejectedPlatformUsers">-</h5>
                            <small class="text-muted">已拒绝</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 平台用户列表 -->
            <div class="card">
                <div class="card-body p-0">
                    <div id="platformUsersTable">
                        <div class="text-center py-5">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                            <div class="mt-2 text-muted">正在加载平台用户列表...</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 创建用户模态框 -->
<div class="modal fade" id="createUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-person-plus me-2"></i>添加新用户
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="createUserForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">用户名 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" name="username" required
                                       placeholder="请输入用户名">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">密码 <span class="text-danger">*</span></label>
                                <input type="password" class="form-control" name="password" required
                                       minlength="6" maxlength="20" placeholder="请输入密码">
                                <div class="form-text">密码长度必须在6-20个字符之间</div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">邮箱</label>
                                <input type="email" class="form-control" name="email"
                                       placeholder="请输入邮箱地址">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">手机号</label>
                                <input type="tel" class="form-control" name="phone"
                                       placeholder="请输入手机号">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">真实姓名</label>
                                <input type="text" class="form-control" name="realName"
                                       placeholder="请输入真实姓名">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">部门</label>
                                <input type="text" class="form-control" name="department"
                                       placeholder="请输入部门">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">状态</label>
                                <select class="form-select" name="status">
                                    <option value="1">正常</option>
                                    <option value="0">禁用</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">角色分配</label>
                                <div class="form-text">用户创建后可通过"分配角色"功能进行角色管理</div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="createUser()">
                    <i class="bi bi-check-lg"></i> 创建用户
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 编辑用户模态框 -->
<div class="modal fade" id="editUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-pencil me-2"></i>编辑用户
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editUserForm">
                    <input type="hidden" id="editUserId">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">邮箱</label>
                                <input type="email" class="form-control" name="email" id="editUserEmail"
                                       placeholder="请输入邮箱地址">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">手机号</label>
                                <input type="tel" class="form-control" name="phone" id="editUserPhone"
                                       placeholder="请输入手机号">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">真实姓名</label>
                                <input type="text" class="form-control" name="realName" id="editUserRealName"
                                       placeholder="请输入真实姓名">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">角色管理</label>
                                <div class="form-text">请使用"分配角色"功能进行角色管理</div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">部门</label>
                                <input type="text" class="form-control" name="department" id="editUserDepartment"
                                       placeholder="请输入部门">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">状态</label>
                                <select class="form-select" name="status" id="editUserStatus">
                                    <option value="1">正常</option>
                                    <option value="0">禁用</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="updateUser()">
                    <i class="bi bi-check-lg"></i> 保存修改
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 角色分配模态框 -->
<div class="modal fade" id="userRoleModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-person-badge me-2"></i>分配角色
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-4">
                        <h6>用户信息</h6>
                        <div class="card">
                            <div class="card-body">
                                <p><strong>用户名：</strong><span id="roleUserUsername"></span></p>
                                <p><strong>真实姓名：</strong><span id="roleUserRealName"></span></p>
                                <p><strong>邮箱：</strong><span id="roleUserEmail"></span></p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-8">
                        <h6>角色配置</h6>
                        <div class="role-list" id="userRoleList">
                            <!-- 角色列表将通过JavaScript动态生成 -->
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveUserRoles()">保存角色</button>
            </div>
        </div>
    </div>
</div>

<!-- 用户审核模态框 -->
<div class="modal fade" id="userApprovalModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-person-check me-2"></i>用户审核
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="userApprovalForm">
                    <input type="hidden" id="approvalUserId">

                    <!-- 用户信息 -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0">用户信息</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>用户名：</strong><span id="approvalUsername"></span></p>
                                    <p><strong>真实姓名：</strong><span id="approvalRealName"></span></p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>邮箱：</strong><span id="approvalEmail"></span></p>
                                    <p><strong>注册时间：</strong><span id="approvalCreatedTime"></span></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 审核操作 -->
                    <div class="mb-3">
                        <label class="form-label fw-bold">审核结果 <span class="text-danger">*</span></label>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="approvalStatus" value="1" id="approvePass" required>
                            <label class="form-check-label text-success" for="approvePass">
                                <i class="bi bi-check-circle me-1"></i>通过
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="approvalStatus" value="2" id="approveReject" required>
                            <label class="form-check-label text-danger" for="approveReject">
                                <i class="bi bi-x-circle me-1"></i>拒绝
                            </label>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label fw-bold">审核备注</label>
                        <textarea class="form-control" name="approvalRemark" rows="3"
                                  placeholder="请输入审核备注（可选）"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="submitUserApproval()">
                    <i class="bi bi-check-lg"></i> 提交审核
                </button>
            </div>
        </div>
    </div>
</div>


