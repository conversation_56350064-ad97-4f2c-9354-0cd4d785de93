<!-- 文档识别页面 -->
<div class="recognition-content">
    <div class="row">
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">文档识别测试</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label">选择Agent</label>
                        <select class="form-select" id="recognitionAgentSelect">
                            <option value="">请选择Agent...</option>
                        </select>
                    </div>
                    
                    <div class="file-drop-zone" id="fileDropZone" onclick="document.getElementById('fileInput').click()">
                        <i class="bi bi-cloud-upload fs-1 text-muted"></i>
                        <p class="mt-2 mb-0">点击选择文件或拖拽文件到此处</p>
                        <small class="text-muted">支持 JPG, PNG, PDF 格式</small>
                        <input type="file" id="fileInput" style="display: none;" accept=".jpg,.jpeg,.png,.pdf">
                    </div>
                    
                    <div class="mt-3">
                        <button class="btn btn-primary" onclick="startRecognition()" id="recognizeBtn" disabled>
                            <i class="bi bi-play"></i> 开始识别
                        </button>
                        <button class="btn btn-secondary ms-2" onclick="clearRecognition()">
                            <i class="bi bi-arrow-clockwise"></i> 重置
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">识别结果</h5>
                </div>
                <div class="card-body">
                    <div id="recognitionResult">
                        <div class="text-muted text-center py-4">
                            <i class="bi bi-file-earmark-text fs-1"></i>
                            <p class="mt-2">请上传文件并开始识别</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
