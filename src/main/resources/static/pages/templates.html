<!-- 业务模板管理页面 -->
<div class="templates-content">
    <!-- 操作栏 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <button class="btn btn-primary" onclick="showCreateTemplateModal()">
                <i class="bi bi-plus-lg"></i> 创建模板
            </button>
            <button class="btn btn-outline-primary ms-2" onclick="importTemplate()">
                <i class="bi bi-upload"></i> 导入模板
            </button>
            <div class="btn-group ms-2" id="batchOperations" style="display: none;">
                <button class="btn btn-outline-success btn-sm" onclick="batchEnable()">
                    <i class="bi bi-check-circle"></i> 批量启用
                </button>
                <button class="btn btn-outline-warning btn-sm" onclick="batchDisable()">
                    <i class="bi bi-x-circle"></i> 批量禁用
                </button>
                <button class="btn btn-outline-danger btn-sm" onclick="batchDelete()">
                    <i class="bi bi-trash"></i> 批量删除
                </button>
            </div>
        </div>
        <div class="d-flex gap-2">
            <select class="form-select" id="templateCategoryFilter" onchange="filterTemplates()" style="width: 150px;">
                <option value="">全部分类</option>
                <option value="finance">财务</option>
                <option value="logistics">物流</option>
                <option value="legal">法务</option>
                <option value="identity">身份</option>
                <option value="general">通用</option>
            </select>
            <input type="text" class="form-control" placeholder="搜索模板..." id="templateSearchInput" style="width: 250px;">
            <button class="btn btn-outline-secondary" onclick="searchTemplates()">
                <i class="bi bi-search"></i>
            </button>
        </div>
    </div>

    <!-- 统计卡片 -->
    <div class="row g-4 mb-4">
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <i class="bi bi-file-code text-primary" style="font-size: 2rem;"></i>
                    <h5 class="mt-2" id="totalTemplates">-</h5>
                    <small class="text-muted">总模板数</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <i class="bi bi-check-circle text-success" style="font-size: 2rem;"></i>
                    <h5 class="mt-2" id="activeTemplates">-</h5>
                    <small class="text-muted">启用模板</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <i class="bi bi-tags text-info" style="font-size: 2rem;"></i>
                    <h5 class="mt-2" id="templateCategories">-</h5>
                    <small class="text-muted">分类数量</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <i class="bi bi-robot text-warning" style="font-size: 2rem;"></i>
                    <h5 class="mt-2" id="templatesInUse">-</h5>
                    <small class="text-muted">使用中</small>
                </div>
            </div>
        </div>
    </div>

    <!-- 模板列表 -->
    <div class="card">
        <div class="card-body p-0">
            <div id="templatesTable">
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <div class="mt-2 text-muted">正在加载模板列表...</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 创建模板模态框 -->
<div class="modal fade" id="createTemplateModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-plus-circle me-2"></i>创建业务模板
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="createTemplateForm">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label class="form-label fw-bold">模板名称 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" name="templateName" required
                                   placeholder="例如：发票识别模板" onchange="generateTemplateCode()">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label fw-bold">模板编码 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" name="templateCode" required
                                   placeholder="INVOICE_RECOGNITION" style="text-transform: uppercase;">
                            <small class="text-muted">系统会根据模板名称自动生成</small>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label fw-bold">模板分类</label>
                            <select class="form-select" name="category">
                                <option value="finance">财务</option>
                                <option value="logistics">物流</option>
                                <option value="legal">法务</option>
                                <option value="identity">身份</option>
                                <option value="general">通用</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label fw-bold">状态</label>
                            <select class="form-select" name="status">
                                <option value="1">启用</option>
                                <option value="0">禁用</option>
                            </select>
                        </div>
                        <div class="col-12">
                            <label class="form-label fw-bold">描述</label>
                            <textarea class="form-control" name="description" rows="2"
                                      placeholder="描述这个模板的用途..."></textarea>
                        </div>
                        <div class="col-12">
                            <label class="form-label fw-bold">JSON模板 <span class="text-danger">*</span></label>
                            <textarea class="form-control" name="jsonTemplate" rows="8" required
                                      placeholder='{"field1": "字段1描述", "field2": "字段2描述"}'></textarea>
                            <small class="text-muted">请输入有效的JSON格式，这将作为Agent输出的数据结构模板</small>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="createTemplate()">
                    <i class="bi bi-check-lg"></i> 创建模板
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 编辑模板模态框 -->
<div class="modal fade" id="editTemplateModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-pencil me-2"></i>编辑业务模板
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editTemplateForm">
                    <input type="hidden" name="templateId">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label class="form-label fw-bold">模板名称 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" name="templateName" required
                                   placeholder="例如：发票识别模板">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label fw-bold">模板编码 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" name="templateCode" required
                                   placeholder="INVOICE_RECOGNITION" style="text-transform: uppercase;" readonly>
                            <small class="text-muted">模板编码不可修改</small>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label fw-bold">模板分类</label>
                            <select class="form-select" name="category">
                                <option value="finance">财务</option>
                                <option value="logistics">物流</option>
                                <option value="legal">法务</option>
                                <option value="identity">身份</option>
                                <option value="general">通用</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label fw-bold">状态</label>
                            <select class="form-select" name="status">
                                <option value="1">启用</option>
                                <option value="0">禁用</option>
                            </select>
                        </div>
                        <div class="col-12">
                            <label class="form-label fw-bold">描述</label>
                            <textarea class="form-control" name="description" rows="2"
                                      placeholder="描述这个模板的用途..."></textarea>
                        </div>
                        <div class="col-12">
                            <label class="form-label fw-bold">JSON模板 <span class="text-danger">*</span></label>
                            <textarea class="form-control" name="jsonTemplate" rows="8" required
                                      placeholder='{"field1": "字段1描述", "field2": "字段2描述"}'></textarea>
                            <small class="text-muted">请输入有效的JSON格式，这将作为Agent输出的数据结构模板</small>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="updateTemplate()">
                    <i class="bi bi-check-lg"></i> 保存修改
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 复制模板模态框 -->
<div class="modal fade" id="copyTemplateModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-files me-2"></i>复制业务模板
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="bi bi-info-circle me-2"></i>
                    <strong>提示：</strong>将基于现有模板创建一个副本，您可以修改名称和编码
                </div>
                <form id="copyTemplateForm">
                    <input type="hidden" name="originalId">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label class="form-label fw-bold">新模板名称 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" name="templateName" required
                                   placeholder="例如：发票识别模板 - 副本">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label fw-bold">新模板编码 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" name="templateCode" required
                                   placeholder="INVOICE_RECOGNITION_COPY" style="text-transform: uppercase;">
                            <small class="text-muted">请确保编码唯一</small>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label fw-bold">模板分类</label>
                            <select class="form-select" name="category">
                                <option value="finance">财务</option>
                                <option value="logistics">物流</option>
                                <option value="legal">法务</option>
                                <option value="identity">身份</option>
                                <option value="general">通用</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label fw-bold">状态</label>
                            <select class="form-select" name="status">
                                <option value="1">启用</option>
                                <option value="0">禁用</option>
                            </select>
                        </div>
                        <div class="col-12">
                            <label class="form-label fw-bold">描述</label>
                            <textarea class="form-control" name="description" rows="2"
                                      placeholder="描述这个模板的用途..."></textarea>
                        </div>
                        <div class="col-12">
                            <label class="form-label fw-bold">JSON模板 <span class="text-danger">*</span></label>
                            <textarea class="form-control" name="jsonTemplate" rows="6" required readonly
                                      style="background-color: #f8f9fa;"></textarea>
                            <small class="text-muted">JSON模板将从原模板复制，如需修改请在创建后编辑</small>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-success" onclick="confirmCopyTemplate()">
                    <i class="bi bi-files"></i> 创建副本
                </button>
            </div>
        </div>
    </div>
</div>
