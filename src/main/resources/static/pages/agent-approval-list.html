<!-- Agent审核列表 -->
<style>
    /* CSS变量定义 - 支持主题切换 */
    :root {
        /* 主色调 */
        --primary-color: #009EF7;
        --success-color: #50CD89;
        --warning-color: #FFC700;
        --danger-color: #F1416C;
        --info-color: #7239EA;

        /* 中性色调 */
        --dark-text: #181C32;
        --medium-text: #5E6278;
        --light-text: #A1A5B7;
        --border-color: #E4E6EA;
        --background-color: #F9F9F9;
        --card-background: #FFFFFF;

        /* 阴影系统 */
        --light-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        --normal-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        --deep-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
        --hover-shadow: 0 0.5rem 2rem rgba(0, 0, 0, 0.1);

        /* 圆角系统 */
        --small-radius: 0.375rem;
        --medium-radius: 0.5rem;
        --large-radius: 0.75rem;

        /* 间距系统 */
        --spacing-xs: 0.25rem;
        --spacing-sm: 0.5rem;
        --spacing-md: 1rem;
        --spacing-lg: 1.5rem;
        --spacing-xl: 3rem;

        /* 过渡效果 */
        --theme-transition: all 0.3s ease;
    }

    /* 深色主题变量 */
    .theme-dark {
        --dark-text: #FFFFFF;
        --medium-text: #A1A5B7;
        --light-text: #5E6278;
        --border-color: #2B2B40;
        --background-color: #1B1B29;
        --card-background: #1E1E2D;

        --light-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.3);
        --normal-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.4);
        --deep-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.5);
        --hover-shadow: 0 0.5rem 2rem rgba(0, 0, 0, 0.3);
    }

    /* 搜索卡片 */
    .search-card {
        background-color: var(--card-background);
        border-radius: var(--medium-radius);
        margin-bottom: var(--spacing-lg);
        border: 1px solid var(--border-color);
        box-shadow: var(--light-shadow);
        transition: var(--theme-transition);
    }

    /* 表格容器 */
    .table-responsive {
        min-height: 400px;
        background-color: var(--card-background);
        border-radius: var(--medium-radius);
        border: 1px solid var(--border-color);
        box-shadow: var(--light-shadow);
        transition: var(--theme-transition);
        overflow: hidden;
    }

    /* 表格样式 */
    .table {
        color: var(--dark-text);
        margin-bottom: 0;
        border-collapse: separate;
        border-spacing: 0;
    }

    .table thead th {
        background-color: var(--background-color);
        color: var(--dark-text);
        font-weight: 600;
        font-size: 0.875rem;
        border-bottom: 2px solid var(--border-color);
        border-top: none;
        padding: var(--spacing-md);
        text-align: left;
        vertical-align: middle;
        position: sticky;
        top: 0;
        z-index: 10;
        transition: var(--theme-transition);
    }

    .table thead th:first-child {
        border-top-left-radius: var(--medium-radius);
    }

    .table thead th:last-child {
        border-top-right-radius: var(--medium-radius);
    }

    /* 表格头部排序样式 */
    .table thead th.sortable {
        cursor: pointer;
        user-select: none;
        position: relative;
    }

    .table thead th.sortable:hover {
        background-color: var(--border-color);
    }

    .table thead th.sortable::after {
        content: '\f0dc';
        font-family: 'bootstrap-icons';
        position: absolute;
        right: 8px;
        top: 50%;
        transform: translateY(-50%);
        opacity: 0.5;
        font-size: 0.75rem;
    }

    .table thead th.sortable.asc::after {
        content: '\f0d8';
        opacity: 1;
        color: var(--primary-color);
    }

    .table thead th.sortable.desc::after {
        content: '\f0d7';
        opacity: 1;
        color: var(--primary-color);
    }

    /* 表格标题样式 - 符合UI规范 */
    .table-responsive .table thead th {
        background-color: #F9F9F9;
        color: var(--dark-text);
        font-weight: 600;
        font-size: 0.875rem;
        padding: 0.75rem 1rem;
        border-bottom: 1px solid var(--border-color);
        border-top: none;
        text-align: left;
        vertical-align: middle;
        transition: var(--theme-transition);
    }

    /* 深色主题下的表格标题 */
    .theme-dark .table-responsive .table thead th {
        background-color: #2B2B40;
        color: var(--dark-text);
    }

    /* 可排序的表格标题样式 */
    .table-responsive .table thead th.sortable {
        cursor: pointer;
        user-select: none;
        position: relative;
        transition: background-color 0.2s ease;
    }

    .table-responsive .table thead th.sortable:hover {
        background-color: #E9ECEF;
    }

    .theme-dark .table-responsive .table thead th.sortable:hover {
        background-color: #3A3A52;
    }

    /* 排序图标 */
    .table-responsive .table thead th.sortable::after {
        content: '\f0dc';
        font-family: 'bootstrap-icons';
        position: absolute;
        right: 0.5rem;
        top: 50%;
        transform: translateY(-50%);
        opacity: 0.5;
        font-size: 0.75rem;
        transition: opacity 0.2s ease;
    }

    .table-responsive .table thead th.sortable:hover::after {
        opacity: 0.8;
    }

    .table-responsive .table thead th.sortable.asc::after {
        content: '\f0d8';
        opacity: 1;
        color: var(--primary-color);
    }

    .table-responsive .table thead th.sortable.desc::after {
        content: '\f0d7';
        opacity: 1;
        color: var(--primary-color);
    }

    .table tbody td {
        padding: var(--spacing-md);
        border-bottom: 1px solid var(--border-color);
        vertical-align: middle;
        transition: var(--theme-transition);
        height: 48px; /* 符合UI规范的行高 */
    }

    .table tbody tr {
        transition: var(--theme-transition);
    }

    .table tbody tr:hover {
        background-color: var(--background-color);
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .table tbody tr:nth-child(even) {
        background-color: rgba(0, 0, 0, 0.02);
    }

    .theme-dark .table tbody tr:nth-child(even) {
        background-color: rgba(255, 255, 255, 0.02);
    }

    /* 状态徽章 */
    .status-badge {
        display: inline-flex;
        align-items: center;
        gap: var(--spacing-xs);
        padding: var(--spacing-xs) var(--spacing-md);
        border-radius: var(--small-radius);
        font-weight: 500;
        font-size: 0.75rem;
    }

    .status-pending {
        background-color: #fff3cd;
        color: #856404;
        border: 1px solid #ffeaa7;
    }

    .status-approved {
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }

    .status-rejected {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }

    /* 版本徽章样式 - 企业风格浅色系设计 */
    .version-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.25rem 0.75rem;
        border-radius: var(--small-radius);
        font-size: 0.75rem;
        font-weight: 500;
        background-color: #F8F9FA;
        color: #495057;
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        border: 1px solid #DEE2E6;
        transition: var(--theme-transition);
        box-shadow: var(--light-shadow);
    }

    /* 版本徽章悬停效果 */
    .version-badge:hover {
        background-color: #E9ECEF;
        border-color: #CED4DA;
        transform: translateY(-1px);
        box-shadow: var(--normal-shadow);
    }

    /* 深色主题下的版本徽章 */
    .theme-dark .version-badge {
        background-color: #2B2B40;
        color: #A1A5B7;
        border-color: #3F3F56;
    }

    .theme-dark .version-badge:hover {
        background-color: #3F3F56;
        border-color: #4F4F6A;
        color: #FFFFFF;
    }

    /* 按钮样式 */
    .btn-primary {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
        color: white;
        font-weight: 500;
        transition: var(--theme-transition);
    }

    .btn-primary:hover {
        background-color: #0085d1;
        border-color: #0085d1;
        transform: translateY(-1px);
        box-shadow: var(--hover-shadow);
    }

    .btn-outline-secondary {
        color: var(--medium-text);
        border-color: var(--border-color);
        background-color: transparent;
    }

    .btn-outline-secondary:hover {
        background-color: var(--background-color);
        color: var(--dark-text);
        border-color: var(--medium-text);
    }

    .action-btn {
        margin-right: var(--spacing-sm);
        font-size: 0.875rem;
        padding: var(--spacing-xs) var(--spacing-sm);
    }

    /* 分页样式 */
    .pagination {
        justify-content: center;
        margin-top: var(--spacing-lg);
    }

    .page-link {
        color: var(--primary-color);
        background-color: var(--card-background);
        border-color: var(--border-color);
        transition: var(--theme-transition);
    }

    .page-link:hover {
        color: var(--dark-text);
        background-color: var(--background-color);
        border-color: var(--border-color);
    }

    .page-item.active .page-link {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
    }

    /* Agent信息样式 */
    .agent-info {
        line-height: 1.4;
    }

    .agent-name {
        font-weight: 600;
        color: var(--dark-text);
        margin-bottom: var(--spacing-xs);
        font-size: 0.875rem;
    }

    .agent-name-link {
        color: var(--primary-color);
        text-decoration: none;
        font-weight: 600;
        font-size: 0.875rem;
        transition: var(--theme-transition);
    }

    .agent-name-link:hover {
        color: var(--dark-text);
        text-decoration: underline;
    }

    .agent-code {
        font-size: 0.75rem;
        color: var(--medium-text);
        font-family: "JetBrains Mono", "Fira Code", Consolas, monospace;
    }

    .description-tooltip {
        cursor: help;
        text-decoration: underline;
        text-decoration-style: dotted;
        color: var(--medium-text);
    }

    /* 表单控件样式 */
    .form-control,
    .form-select {
        background-color: var(--card-background);
        border-color: var(--border-color);
        color: var(--dark-text);
        transition: var(--theme-transition);
    }

    .form-control:focus,
    .form-select:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.2rem rgba(0, 158, 247, 0.25);
        background-color: var(--card-background);
        color: var(--dark-text);
    }

    .form-label {
        color: var(--dark-text);
        font-weight: 500;
        font-size: 0.875rem;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .table-responsive {
            font-size: 0.875rem;
        }

        .table thead th {
            padding: var(--spacing-sm);
            font-size: 0.75rem;
        }

        .table thead th {
            padding: 0.5rem 0.75rem;
            font-size: 0.75rem;
        }

        .table tbody td {
            padding: var(--spacing-sm);
        }

        .agent-name {
            font-size: 0.8rem;
        }

        .agent-code {
            font-size: 0.7rem;
        }

        .action-btn {
            font-size: 0.75rem;
            padding: var(--spacing-xs);
        }

        .status-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }

        .table-responsive .table thead th.sortable::after {
            right: 0.25rem;
            font-size: 0.65rem;
        }

        .table thead th.sortable::after {
            right: 4px;
            font-size: 0.65rem;
        }
    }
</style>


<!-- 搜索条件 -->
<div class="card search-card p-3 mb-4">
    <form id="searchForm">
        <div class="row g-3">
            <div class="col-md-3">
                <label for="agentName" class="form-label">Agent名称</label>
                <input type="text" class="form-control" id="agentName" placeholder="请输入Agent名称">
            </div>
            <div class="col-md-3">
                <label for="agentCode" class="form-label">Agent编码</label>
                <input type="text" class="form-control" id="agentCode" placeholder="请输入Agent编码">
            </div>
            <div class="col-md-2">
                <label for="status" class="form-label">审批状态</label>
                <select class="form-select" id="status">
                    <option value="">全部</option>
                    <option value="1">审批中</option>
                    <option value="2">审批通过</option>
                    <option value="3">审批不通过</option>
                </select>
            </div>
            <div class="col-md-2">
                <label for="categoryId" class="form-label">分类</label>
                <select class="form-select" id="categoryId">
                    <option value="">全部分类</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-flex gap-2">
                    <button type="button" class="btn btn-primary" id="searchBtn">
                        <i class="bi bi-search"></i> 查询
                    </button>
                    <button type="button" class="btn btn-outline-secondary" id="resetBtn">
                        <i class="bi bi-arrow-clockwise"></i> 重置
                    </button>
                </div>
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-md-3">
                <label for="startTime" class="form-label">开始时间</label>
                <input type="date" class="form-control" id="startTime">
            </div>
            <div class="col-md-3">
                <label for="endTime" class="form-label">结束时间</label>
                <input type="date" class="form-control" id="endTime">
            </div>
            <div class="col-md-2">
                <label for="pageSize" class="form-label">每页显示</label>
                <select class="form-select" id="pageSize">
                    <option value="10">10条</option>
                    <option value="20">20条</option>
                    <option value="50">50条</option>
                    <option value="100">100条</option>
                </select>
            </div>
        </div>
    </form>
</div>

<!-- 数据表格 -->
<div class="card">
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead>
                    <tr>
                        <th>Agent信息</th>
                        <th>描述</th>
                        <th style="width: 110px;">审批状态</th>
                        <th style="width: 100px;">分类</th>
                        <th>版本号</th>
                        <th style="width: 200px;">使用场景</th>
                        <th style="width: 100px;">订阅数</th>
                        <th>提交时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="tableBody">
                    <!-- 数据将通过JavaScript动态加载 -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 分页和统计信息 -->
<div class="d-flex justify-content-between align-items-center mt-4">
    <div class="text-muted">
        共 <span id="totalCount">0</span> 条记录
    </div>
    <nav aria-label="分页导航">
        <ul class="pagination mb-0">
            <!-- 分页内容将通过JavaScript动态生成 -->
        </ul>
    </nav>
</div>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<!-- 通用工具函数 -->
<script src="/assets/js/common.js"></script>

<!-- Agent审批列表页面脚本 -->
<script src="/assets/js/agent-approval-list.js"></script>

<script>
// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 检查是否为Agent审批列表页面
    if (document.getElementById('agentName') && document.getElementById('tableBody')) {
        console.log('DOM加载完成，开始初始化Agent审批列表页面');
        if (typeof initAgentApprovalListPage === 'function') {
            initAgentApprovalListPage();
        } else {
            console.warn('initAgentApprovalListPage函数未找到');
        }
    } else {
        console.log('不是Agent审批列表页面，跳过初始化');
    }
});
</script>
