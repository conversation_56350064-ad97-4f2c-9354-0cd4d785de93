<!-- 仪表板页面 -->
<div class="dashboard-content">
    <!-- 统计卡片 -->
    <div class="row g-4 mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="stats-card fade-in">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h3 class="mb-1" id="totalAgents">-</h3>
                        <p class="mb-0">总Agent数</p>
                    </div>
                    <i class="bi bi-robot fs-1 opacity-50"></i>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="stats-card success fade-in">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h3 class="mb-1" id="publishedAgents">-</h3>
                        <p class="mb-0">已发布Agent</p>
                    </div>
                    <i class="bi bi-rocket fs-1 opacity-50"></i>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="stats-card warning fade-in">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h3 class="mb-1" id="todayCalls">-</h3>
                        <p class="mb-0">今日调用</p>
                    </div>
                    <i class="bi bi-graph-up fs-1 opacity-50"></i>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="stats-card info fade-in">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h3 class="mb-1" id="successRate">-</h3>
                        <p class="mb-0">成功率</p>
                    </div>
                    <i class="bi bi-check-circle fs-1 opacity-50"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- 图表和信息面板 -->
    <div class="row g-4">
        <!-- 系统状态 -->
        <div class="col-lg-4">
            <div class="card h-100">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="bi bi-activity me-2"></i>系统状态
                    </h6>
                </div>
                <div class="card-body">
                    <div id="systemStatus">
                        <div class="text-center py-3">
                            <div class="spinner-border spinner-border-sm text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 最近活动 -->
        <div class="col-lg-4">
            <div class="card h-100">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="bi bi-clock-history me-2"></i>最近Agent
                    </h6>
                </div>
                <div class="card-body">
                    <div id="recentAgents">
                        <div class="text-center py-3">
                            <div class="spinner-border spinner-border-sm text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 快速操作 -->
        <div class="col-lg-4">
            <div class="card h-100">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="bi bi-lightning me-2"></i>快速操作
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button class="btn btn-primary" onclick="loadPage('agents')">
                            <i class="bi bi-plus-lg me-2"></i>创建新Agent
                        </button>
                        <button class="btn btn-outline-primary" onclick="loadPage('playground')">
                            <i class="bi bi-play-circle me-2"></i>智能调试台
                        </button>
                        <button class="btn btn-outline-secondary" onclick="loadPage('templates')">
                            <i class="bi bi-file-code me-2"></i>业务模板
                        </button>
                        <button class="btn btn-outline-info" onclick="loadPage('recognition')">
                            <i class="bi bi-file-earmark-text me-2"></i>文档识别
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 使用统计图表 -->
    <div class="row g-4 mt-2">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="bi bi-bar-chart me-2"></i>使用统计
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <canvas id="usageChart" width="400" height="200"></canvas>
                        </div>
                        <div class="col-md-4">
                            <h6>今日概览</h6>
                            <div class="mb-3">
                                <div class="d-flex justify-content-between">
                                    <span>API调用</span>
                                    <strong>1,234</strong>
                                </div>
                                <div class="progress" style="height: 4px;">
                                    <div class="progress-bar bg-primary" style="width: 75%"></div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="d-flex justify-content-between">
                                    <span>成功率</span>
                                    <strong>98.5%</strong>
                                </div>
                                <div class="progress" style="height: 4px;">
                                    <div class="progress-bar bg-success" style="width: 98.5%"></div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="d-flex justify-content-between">
                                    <span>平均响应时间</span>
                                    <strong>245ms</strong>
                                </div>
                                <div class="progress" style="height: 4px;">
                                    <div class="progress-bar bg-info" style="width: 60%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
