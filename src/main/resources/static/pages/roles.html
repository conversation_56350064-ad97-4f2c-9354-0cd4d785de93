<!-- 角色管理页面内容 -->
<style>
    /* 角色权限树样式 */
    .permission-tree {
        max-height: 500px;
        overflow-y: auto;
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
        padding: 15px;
    }
    .permission-node {
        padding: 8px;
        border: 1px solid #e9ecef;
        border-radius: 4px;
        margin-bottom: 8px;
        background-color: #f8f9fa;
        position: relative;
    }
    .permission-node-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        cursor: pointer;
    }
    .permission-node-content {
        display: flex;
        align-items: center;
        flex: 1;
    }
    .permission-toggle {
        width: 20px;
        height: 20px;
        border: none;
        background: none;
        color: #6c757d;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 3px;
        transition: all 0.2s ease;
        margin-right: 8px;
    }
    .permission-toggle:hover {
        background-color: #e9ecef;
        color: #495057;
    }
    .permission-toggle.collapsed {
        transform: rotate(-90deg);
    }
    .permission-toggle.no-children {
        visibility: hidden;
    }
    .permission-children {
        margin-left: 20px;
        margin-top: 8px;
        overflow: hidden;
        transition: all 0.3s ease;
    }
    .permission-children.collapsed {
        display: none;
    }
    .permission-checkbox {
        margin-right: 8px;
    }
    .permission-info {
        margin-left: 8px;
    }
    .permission-node.level-0 {
        background-color: #e3f2fd;
        border-color: #2196f3;
    }
    .permission-node.level-1 {
        background-color: #f3e5f5;
        border-color: #9c27b0;
    }
    .permission-node.level-2 {
        background-color: #e8f5e8;
        border-color: #4caf50;
    }
</style>

<div class="roles-content">
    <!-- 操作栏 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <button class="btn btn-primary" onclick="showCreateRoleModal()" data-permission="ROLE_CREATE">
                <i class="bi bi-plus-lg"></i> 新增角色
            </button>
            <button class="btn btn-outline-primary ms-2" onclick="refreshRoleList()">
                <i class="bi bi-arrow-clockwise"></i> 刷新
            </button>
        </div>
        <div class="d-flex gap-2">
            <select class="form-select" id="statusFilter" onchange="searchRoles()" style="width: 150px;">
                <option value="">全部状态</option>
                <option value="1">启用</option>
                <option value="0">禁用</option>
            </select>
            <input type="text" class="form-control" placeholder="搜索角色..." id="searchKeyword" style="width: 250px;">
            <button class="btn btn-outline-secondary" onclick="searchRoles()">
                <i class="bi bi-search"></i>
            </button>
        </div>
    </div>

    <!-- 角色列表 -->
    <div class="card">
        <div class="card-body p-0">
            <div id="roleTable">
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <div class="mt-2 text-muted">正在加载角色列表...</div>
                </div>
            </div>
        </div>
    </div>
</div>

    <!-- 创建/编辑角色模态框 -->
    <div class="modal fade" id="roleModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="roleModalTitle">新增角色</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="roleForm">
                        <input type="hidden" id="roleId">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="roleName" class="form-label">角色名称 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="roleName" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="roleCode" class="form-label">角色编码 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="roleCode" required>
                                    <div class="form-text">角色编码用于系统内部识别，建议使用大写字母和下划线</div>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="roleDescription" class="form-label">角色描述</label>
                            <textarea class="form-control" id="roleDescription" rows="3"></textarea>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="roleStatus" class="form-label">状态</label>
                                    <select class="form-select" id="roleStatus">
                                        <option value="1">启用</option>
                                        <option value="0">禁用</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="sortOrder" class="form-label">排序</label>
                                    <input type="number" class="form-control" id="sortOrder" value="0">
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveRole()">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 权限分配模态框 -->
    <div class="modal fade" id="permissionModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">权限分配</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-4">
                            <h6>角色信息</h6>
                            <div class="card">
                                <div class="card-body">
                                    <p><strong>角色名称：</strong><span id="permissionRoleName"></span></p>
                                    <p><strong>角色编码：</strong><span id="permissionRoleCode"></span></p>
                                    <p><strong>描述：</strong><span id="permissionRoleDescription"></span></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-8">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6 class="mb-0">权限配置</h6>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-secondary" onclick="expandAllRolePermissions()" title="展开所有">
                                        <i class="bi bi-arrows-expand"></i> 展开所有
                                    </button>
                                    <button class="btn btn-outline-secondary" onclick="collapseAllRolePermissions()" title="折叠所有">
                                        <i class="bi bi-arrows-collapse"></i> 折叠所有
                                    </button>
                                    <button class="btn btn-outline-primary" onclick="selectAllPermissions()" title="全选">
                                        <i class="bi bi-check-all"></i> 全选
                                    </button>
                                    <button class="btn btn-outline-warning" onclick="clearAllPermissions()" title="清空">
                                        <i class="bi bi-x-square"></i> 清空
                                    </button>
                                </div>
                            </div>
                            <div class="permission-tree" id="permissionTree">
                                <!-- 权限树将通过JavaScript动态生成 -->
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveRolePermissions()">保存权限</button>
                </div>
            </div>
        </div>
    </div>


