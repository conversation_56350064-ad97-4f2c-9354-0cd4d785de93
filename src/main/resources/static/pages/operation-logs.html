<!-- 操作日志管理 -->
<!-- 查询条件区域 -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="bi bi-search me-2"></i>查询条件
        </h5>
    </div>
    <div class="card-body">
        <form id="searchForm">
            <div class="row g-3">
                <div class="col-md-3">
                    <label class="form-label">用户ID</label>
                    <input type="number" class="form-control" id="userId" placeholder="请输入用户ID">
                </div>
                <div class="col-md-3">
                    <label class="form-label">用户名</label>
                    <input type="text" class="form-control" id="username" placeholder="请输入用户名">
                </div>
                <div class="col-md-3">
                    <label class="form-label">模块</label>
                    <select class="form-select" id="module">
                        <option value="">全部模块</option>
                        <option value="Agent管理">Agent管理</option>
                        <option value="用户管理">用户管理</option>
                        <option value="角色管理">角色管理</option>
                        <option value="权限管理">权限管理</option>
                        <option value="业务模板">业务模板</option>
                        <option value="页面绑定">页面绑定</option>
                        <option value="文档识别">文档识别</option>
                        <option value="日志管理">日志管理</option>
                        <option value="系统管理">系统管理</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">操作类型</label>
                    <select class="form-select" id="operationType">
                        <option value="">全部类型</option>
                        <option value="CREATE">创建</option>
                        <option value="UPDATE">更新</option>
                        <option value="DELETE">删除</option>
                        <option value="QUERY">查询</option>
                        <option value="LOGIN">登录</option>
                        <option value="LOGOUT">登出</option>
                        <option value="UPLOAD">上传</option>
                        <option value="DOWNLOAD">下载</option>
                        <option value="EXPORT">导出</option>
                        <option value="IMPORT">导入</option>
                        <option value="RECOGNITION">识别</option>
                        <option value="AGENT_TEST">Agent测试</option>
                        <option value="AGENT_PUBLISH">Agent发布</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">状态</label>
                    <select class="form-select" id="status">
                        <option value="">全部状态</option>
                        <option value="SUCCESS">成功</option>
                        <option value="FAILED">失败</option>
                        <option value="ERROR">错误</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">开始时间</label>
                    <input type="datetime-local" class="form-control" id="startTime">
                </div>
                <div class="col-md-3">
                    <label class="form-label">结束时间</label>
                    <input type="datetime-local" class="form-control" id="endTime">
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <div class="btn-group w-100">
                        <button type="button" class="btn btn-primary" onclick="searchLogs()">
                            <i class="bi bi-search me-1"></i>查询
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="resetSearch()">
                            <i class="bi bi-arrow-counterclockwise me-1"></i>重置
                        </button>
                        <button type="button" class="btn btn-success" onclick="exportLogs()">
                            <i class="bi bi-file-excel me-1"></i>导出
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- 数据表格区域 -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">
            <i class="bi bi-table me-2"></i>操作日志列表
        </h5>
        <div class="d-flex align-items-center">
            <button class="btn btn-outline-info btn-sm me-2" onclick="loadRecentLogs()">
                <i class="bi bi-clock-history me-1"></i>最近日志
            </button>
            <button class="btn btn-outline-warning btn-sm" onclick="loadSlowLogs()">
                <i class="bi bi-hourglass-split me-1"></i>慢操作
            </button>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>用户名</th>
                        <th>模块</th>
                        <th>操作类型</th>
                        <th>操作描述</th>
                        <th>请求方法</th>
                        <th>状态</th>
                        <th>执行时间(ms)</th>
                        <th>操作时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="logsTableBody">
                    <!-- 数据将通过JavaScript动态加载 -->
                </tbody>
            </table>
        </div>

        <!-- 分页控件 -->
        <div class="d-flex justify-content-between align-items-center mt-4">
            <div class="pagination-info">
                共 <span id="totalCount">0</span> 条记录，第 <span id="currentPage">1</span> / <span id="totalPages">1</span> 页
            </div>
            <div class="d-flex align-items-center">
                <select id="pageSize" class="form-select form-select-sm me-2" style="width: auto;" onchange="changePageSize()">
                    <option value="10">10条/页</option>
                    <option value="20" selected>20条/页</option>
                    <option value="50">50条/页</option>
                    <option value="100">100条/页</option>
                </select>
                <nav>
                    <ul class="pagination pagination-sm mb-0" id="pagination">
                        <!-- 分页将通过JavaScript动态生成 -->
                    </ul>
                </nav>
            </div>
        </div>
    </div>
</div>

<!-- 日志详情模态框 -->
<div class="modal fade" id="logDetailModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-info-circle me-2"></i>日志详情
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="logDetailContent">
                <!-- 详情内容将动态加载 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
