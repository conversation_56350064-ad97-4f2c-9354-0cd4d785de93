<!-- 权限管理页面内容 -->
<style>
    .permission-tree {
        max-height: 600px;
        overflow-y: auto;
    }
    .permission-node {
        padding: 8px;
        border: 1px solid var(--border-color);
        border-radius: 4px;
        margin-bottom: 8px;
        background-color: var(--background-color);
        color: var(--dark-text);
        position: relative;
    }
    .permission-node-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        cursor: pointer;
    }
    .permission-node-content {
        display: flex;
        align-items: center;
        flex: 1;
    }
    .permission-toggle {
        width: 20px;
        height: 20px;
        border: none;
        background: none;
        color: #6c757d;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 3px;
        transition: all 0.2s ease;
        margin-right: 8px;
    }
    .permission-toggle:hover {
        background-color: #e9ecef;
        color: #495057;
    }
    .permission-toggle.collapsed {
        transform: rotate(-90deg);
    }
    .permission-toggle.no-children {
        visibility: hidden;
    }
    .permission-children {
        margin-left: 20px;
        margin-top: 8px;
        overflow: hidden;
        transition: all 0.3s ease;
    }
    .permission-children.collapsed {
        display: none;
    }
    .permission-type-badge {
        font-size: 0.75rem;
    }
    .permission-actions {
        display: flex;
        gap: 5px;
    }
    .permission-node.level-0 {
        background-color: rgba(33, 150, 243, 0.1);
        border-color: #2196f3;
    }
    .permission-node.level-1 {
        background-color: rgba(156, 39, 176, 0.1);
        border-color: #9c27b0;
    }
    .permission-node.level-2 {
        background-color: rgba(76, 175, 80, 0.1);
        border-color: #4caf50;
    }
    .permission-info {
        margin-left: 8px;
    }
</style>

<div class="permissions-content">
    <!-- 操作栏 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <button class="btn btn-primary" onclick="showCreatePermissionModal()" data-permission="PERMISSION_CREATE">
                <i class="bi bi-plus-lg"></i> 新增权限
            </button>
            <button class="btn btn-outline-primary ms-2" onclick="refreshPermissionTree()">
                <i class="bi bi-arrow-clockwise"></i> 刷新
            </button>
        </div>
        <div class="d-flex gap-2">
            <select class="form-select" id="typeFilter" onchange="filterPermissions()" style="width: 150px;">
                <option value="">全部类型</option>
                <option value="1">菜单权限</option>
                <option value="2">按钮权限</option>
                <option value="3">接口权限</option>
            </select>
            <select class="form-select" id="statusFilter" onchange="filterPermissions()" style="width: 150px;">
                <option value="">全部状态</option>
                <option value="1">启用</option>
                <option value="0">禁用</option>
            </select>
            <input type="text" class="form-control" placeholder="搜索权限..." id="searchKeyword" style="width: 200px;">
            <button class="btn btn-outline-secondary" onclick="filterPermissions()">
                <i class="bi bi-search"></i>
            </button>
        </div>
    </div>

    <!-- 权限树 -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">权限树</h5>
            <div class="btn-group btn-group-sm">
                <button class="btn btn-outline-secondary" onclick="expandAllPermissions()" title="展开所有">
                    <i class="bi bi-arrows-expand"></i> 展开所有
                </button>
                <button class="btn btn-outline-secondary" onclick="collapseAllPermissions()" title="折叠所有">
                    <i class="bi bi-arrows-collapse"></i> 折叠所有
                </button>
            </div>
        </div>
        <div class="card-body">
            <div class="permission-tree" id="permissionTreeContainer">
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <div class="mt-2 text-muted">正在加载权限树...</div>
                </div>
            </div>
        </div>
    </div>
</div>

    <!-- 创建/编辑权限模态框 -->
    <div class="modal fade" id="permissionModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="permissionModalTitle">新增权限</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="permissionForm">
                        <input type="hidden" id="permissionId">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="permissionName" class="form-label">权限名称 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="permissionName" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="permissionCode" class="form-label">权限编码 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="permissionCode" required>
                                    <div class="form-text">权限编码用于系统内部识别，如：system:user:view</div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="permissionType" class="form-label">权限类型 <span class="text-danger">*</span></label>
                                    <select class="form-select" id="permissionType" required onchange="handleTypeChange()">
                                        <option value="">请选择</option>
                                        <option value="1">菜单权限</option>
                                        <option value="2">按钮权限</option>
                                        <option value="3">接口权限</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="parentId" class="form-label">父权限</label>
                                    <select class="form-select" id="parentId">
                                        <option value="0">顶级权限</option>
                                        <!-- 父权限选项将动态加载 -->
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row" id="menuFields" style="display: none;">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="path" class="form-label">路由路径</label>
                                    <input type="text" class="form-control" id="path" placeholder="/system/users">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="component" class="form-label">组件路径</label>
                                    <input type="text" class="form-control" id="component" placeholder="users.html">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="icon" class="form-label">图标</label>
                                    <input type="text" class="form-control" id="icon" placeholder="bi-people">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="sortOrder" class="form-label">排序</label>
                                    <input type="number" class="form-control" id="sortOrder" value="0">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="permissionStatus" class="form-label">状态</label>
                                    <select class="form-select" id="permissionStatus">
                                        <option value="1">启用</option>
                                        <option value="0">禁用</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="permissionDescription" class="form-label">权限描述</label>
                            <textarea class="form-control" id="permissionDescription" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="savePermission()">保存</button>
                </div>
            </div>
        </div>
    </div>


