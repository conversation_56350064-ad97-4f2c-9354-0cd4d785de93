<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能体矩阵 - 智能文档识别与自动填写平台</title>
    <link href="assets/css/bootstrap.min.css" rel="stylesheet">
    <link href="assets/css/bootstrap-icons.css" rel="stylesheet">
    <link href="assets/css/google-fonts-inter.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2563eb;
            --primary-dark: #1d4ed8;
            --secondary-color: #64748b;
            --success-color: #059669;
            --warning-color: #d97706;
            --danger-color: #dc2626;
            --dark-color: #1e293b;
            --light-color: #f8fafc;
            --border-color: #e2e8f0;
        }

        * {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }

        body {
            background-color: #f8fafc;
        }

        .hero-section {
            background: linear-gradient(135deg, #1e293b 0%, #334155 50%, #475569 100%);
            color: white;
            padding: 120px 0;
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="%23ffffff" stop-opacity="0.1"/><stop offset="100%" stop-color="%23ffffff" stop-opacity="0"/></radialGradient></defs><circle cx="200" cy="200" r="100" fill="url(%23a)"/><circle cx="800" cy="300" r="150" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/></svg>');
            opacity: 0.3;
        }

        .hero-content {
            position: relative;
            z-index: 1;
        }

        .feature-card {
            transition: all 0.3s ease;
            border: none;
            border-radius: 16px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            background: white;
        }

        .feature-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        .feature-icon {
            width: 80px;
            height: 80px;
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 24px;
            font-size: 2rem;
        }

        .feature-icon.primary {
            background: linear-gradient(135deg, var(--primary-color), #3b82f6);
            color: white;
        }

        .feature-icon.success {
            background: linear-gradient(135deg, var(--success-color), #10b981);
            color: white;
        }

        .feature-icon.warning {
            background: linear-gradient(135deg, var(--warning-color), #f59e0b);
            color: white;
        }

        .feature-icon.info {
            background: linear-gradient(135deg, #0ea5e9, #06b6d4);
            color: white;
        }

        .btn {
            border-radius: 12px;
            font-weight: 500;
            padding: 12px 24px;
            transition: all 0.3s ease;
            border: none;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), #3b82f6);
            box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(37, 99, 235, 0.4);
        }

        .btn-outline-primary {
            border: 2px solid var(--primary-color);
            color: var(--primary-color);
        }

        .btn-outline-primary:hover {
            background: var(--primary-color);
            border-color: var(--primary-color);
            transform: translateY(-2px);
        }

        .btn-success {
            background: linear-gradient(135deg, var(--success-color), #10b981);
        }

        .btn-warning {
            background: linear-gradient(135deg, var(--warning-color), #f59e0b);
        }

        .navbar {
            background: rgba(30, 41, 59, 0.95) !important;
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            background: linear-gradient(135deg, #3b82f6, #8b5cf6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stats-section {
            background: white;
            border-radius: 20px;
            padding: 40px;
            margin: 60px auto 0;
            position: relative;
            z-index: 2;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        }

        .stat-item {
            text-align: center;
            padding: 20px;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 8px;
        }

        .stat-label {
            color: var(--secondary-color);
            font-weight: 500;
        }

        .section-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--dark-color);
            margin-bottom: 1rem;
        }

        .section-subtitle {
            font-size: 1.25rem;
            color: var(--secondary-color);
            margin-bottom: 3rem;
        }

        .download-card {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-radius: 16px;
            padding: 40px;
            text-align: center;
            border: 1px solid var(--border-color);
        }

        .download-icon {
            width: 100px;
            height: 100px;
            background: linear-gradient(135deg, var(--primary-color), #3b82f6);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 24px;
            font-size: 3rem;
            color: white;
        }

        .footer {
            background: var(--dark-color);
            color: white;
            padding: 60px 0 30px;
        }

        .footer-link {
            color: #cbd5e1;
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .footer-link:hover {
            color: white;
        }

        .fade-in {
            animation: fadeIn 0.8s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .floating-elements {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            pointer-events: none;
        }

        .floating-element {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark fixed-top">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="bi bi-robot me-2"></i>
                <strong>智能体矩阵</strong>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#features">功能特性</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#download">插件下载</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/doc.html" target="_blank">API文档</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="login.html">
                            <i class="bi bi-box-arrow-in-right me-1"></i>登录
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="register.html">
                            <i class="bi bi-person-plus me-1"></i>注册
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主页横幅 -->
    <section class="hero-section text-center">
        <div class="floating-elements">
            <div class="floating-element" style="width: 60px; height: 60px; top: 10%; left: 10%; animation-delay: 0s;"></div>
            <div class="floating-element" style="width: 80px; height: 80px; top: 20%; right: 15%; animation-delay: 2s;"></div>
            <div class="floating-element" style="width: 40px; height: 40px; bottom: 30%; left: 20%; animation-delay: 4s;"></div>
            <div class="floating-element" style="width: 100px; height: 100px; bottom: 20%; right: 10%; animation-delay: 1s;"></div>
        </div>
        <div class="container hero-content">
            <div class="fade-in">
                <h1 class="display-3 fw-bold mb-4">
                    智能文档识别<br>
                    <span style="background: linear-gradient(135deg, #3b82f6, #8b5cf6); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">自动填写平台</span>
                </h1>
                <p class="lead mb-4 fs-4">
                    基于大语言模型的文档智能识别，专为航空货运代理行业设计
                </p>
                <p class="fs-5 mb-5 text-white-50">
                    支持订单、发票、制单等多种业务场景，通过AI Agent技术实现文档解析和网页自动回填<br>
                    提升90%的业务处理效率，减少人为错误，符合IATA规范
                </p>
                <div class="row justify-content-center mb-5">
                    <div class="col-md-10">
                        <div class="d-grid gap-3 d-md-flex justify-content-md-center">
                            <a href="login.html" class="btn btn-primary btn-lg px-5">
                                <i class="bi bi-rocket-takeoff me-2"></i>立即开始
                            </a>
                            <a href="#features" class="btn btn-outline-light btn-lg px-5">
                                <i class="bi bi-play-circle me-2"></i>了解更多
                            </a>
                            <a href="#download" class="btn btn-success btn-lg px-5">
                                <i class="bi bi-download me-2"></i>下载插件
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 统计数据卡片 -->
        <div class="container">
            <div class="stats-section">
                <div class="row">
                    <div class="col-md-3">
                        <div class="stat-item">
                            <div class="stat-number">50+</div>
                            <div class="stat-label">可用Agent</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-item">
                            <div class="stat-number">10K+</div>
                            <div class="stat-label">文档处理</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-item">
                            <div class="stat-number">99.5%</div>
                            <div class="stat-label">识别准确率</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-item">
                            <div class="stat-number">90%</div>
                            <div class="stat-label">效率提升</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 功能特性 -->
    <section id="features" class="py-5" style="padding-top: 120px !important;">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="section-title">核心功能</h2>
                <p class="section-subtitle">为航空货运代理行业量身定制的智能解决方案</p>
            </div>
            <div class="row g-4">
                <div class="col-lg-3 col-md-6">
                    <div class="card feature-card h-100">
                        <div class="card-body text-center p-4">
                            <div class="feature-icon primary">
                                <i class="bi bi-robot"></i>
                            </div>
                            <h5 class="card-title fw-bold mb-3">Agent管理</h5>
                            <p class="card-text text-muted">
                                创建、编辑、测试和发布AI Agent，支持多种LLM提供商，灵活配置提示词和输出格式。
                            </p>
                            <div class="mt-3">
                                <span class="badge bg-primary bg-opacity-10 text-primary me-1">创建</span>
                                <span class="badge bg-primary bg-opacity-10 text-primary me-1">测试</span>
                                <span class="badge bg-primary bg-opacity-10 text-primary">发布</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="card feature-card h-100">
                        <div class="card-body text-center p-4">
                            <div class="feature-icon success">
                                <i class="bi bi-file-earmark-text"></i>
                            </div>
                            <h5 class="card-title fw-bold mb-3">文档识别</h5>
                            <p class="card-text text-muted">
                                支持图片、PDF等多种格式文档的智能识别，提取关键信息并转换为结构化JSON数据。
                            </p>
                            <div class="mt-3">
                                <span class="badge bg-success bg-opacity-10 text-success me-1">JPG</span>
                                <span class="badge bg-success bg-opacity-10 text-success me-1">PNG</span>
                                <span class="badge bg-success bg-opacity-10 text-success">PDF</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="card feature-card h-100">
                        <div class="card-body text-center p-4">
                            <div class="feature-icon warning">
                                <i class="bi bi-magic"></i>
                            </div>
                            <h5 class="card-title fw-bold mb-3">自动回填</h5>
                            <p class="card-text text-muted">
                                通过脚本引擎自动将识别结果回填到目标网页，支持双向调用和Chrome插件集成。
                            </p>
                            <div class="mt-3">
                                <span class="badge bg-warning bg-opacity-10 text-warning me-1">双向调用</span>
                                <span class="badge bg-warning bg-opacity-10 text-warning">插件集成</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="card feature-card h-100">
                        <div class="card-body text-center p-4">
                            <div class="feature-icon info">
                                <i class="bi bi-shield-check"></i>
                            </div>
                            <h5 class="card-title fw-bold mb-3">安全可靠</h5>
                            <p class="card-text text-muted">
                                企业级安全保障，完整的权限管理，数据加密传输，符合IATA规范要求。
                            </p>
                            <div class="mt-3">
                                <span class="badge bg-info bg-opacity-10 text-info me-1">权限管理</span>
                                <span class="badge bg-info bg-opacity-10 text-info">数据加密</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 插件下载 -->
    <section id="download" class="py-5" style="background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);">
        <div class="container">
            <div class="text-center mb-5">
                <h2 class="section-title">Chrome插件下载</h2>
                <p class="section-subtitle">在任何网页上使用SinoairAgent，实现文档识别和自动填写</p>
            </div>

            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="download-card">
                        <div class="download-icon">
                            <i class="bi bi-puzzle"></i>
                        </div>
                        <h3 class="fw-bold mb-3">智能体矩阵 Chrome插件</h3>
                        <p class="text-muted mb-4 fs-5">
                            安装Chrome插件后，您可以在任何网页上：<br>
                            • 右键菜单快速调用Agent<br>
                            • 拖拽文件进行文档识别<br>
                            • 自动填写表单字段<br>
                            • 缓存识别结果
                        </p>

                        <div class="row g-3 mb-4">
                            <div class="col-md-4">
                                <div class="d-flex align-items-center justify-content-center p-3 bg-white rounded-3">
                                    <i class="bi bi-mouse2 text-primary me-2"></i>
                                    <span class="fw-medium">右键调用</span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="d-flex align-items-center justify-content-center p-3 bg-white rounded-3">
                                    <i class="bi bi-file-arrow-up text-success me-2"></i>
                                    <span class="fw-medium">拖拽识别</span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="d-flex align-items-center justify-content-center p-3 bg-white rounded-3">
                                    <i class="bi bi-lightning text-warning me-2"></i>
                                    <span class="fw-medium">自动填写</span>
                                </div>
                            </div>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                            <a href="/chrome-extension.zip" class="btn btn-primary btn-lg px-5" download>
                                <i class="bi bi-download me-2"></i>下载插件
                            </a>
                            <a href="#install-guide" class="btn btn-outline-primary btn-lg px-5">
                                <i class="bi bi-question-circle me-2"></i>安装指南
                            </a>
                        </div>

                        <div class="mt-4 text-muted">
                            <small>
                                <i class="bi bi-info-circle me-1"></i>
                                支持Chrome 88+版本，需要开启开发者模式
                            </small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 安装指南 -->
            <div id="install-guide" class="row justify-content-center mt-5">
                <div class="col-lg-10">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0"><i class="bi bi-gear me-2"></i>安装指南</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="fw-bold text-primary">步骤1：下载插件</h6>
                                    <p class="text-muted mb-3">点击上方"下载插件"按钮，下载chrome-extension.zip文件</p>

                                    <h6 class="fw-bold text-primary">步骤2：解压文件</h6>
                                    <p class="text-muted mb-3">将下载的zip文件解压到任意文件夹</p>

                                    <h6 class="fw-bold text-primary">步骤3：打开Chrome扩展</h6>
                                    <p class="text-muted mb-3">在Chrome浏览器中访问 <code>chrome://extensions/</code></p>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="fw-bold text-primary">步骤4：开启开发者模式</h6>
                                    <p class="text-muted mb-3">在扩展页面右上角开启"开发者模式"</p>

                                    <h6 class="fw-bold text-primary">步骤5：加载插件</h6>
                                    <p class="text-muted mb-3">点击"加载已解压的扩展程序"，选择解压后的文件夹</p>

                                    <h6 class="fw-bold text-primary">步骤6：开始使用</h6>
                                    <p class="text-muted mb-3">插件安装成功后，在任意网页右键即可看到SinoairAgent菜单</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 页脚 -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 mb-4">
                    <h5 class="fw-bold mb-3">
                        <i class="bi bi-robot me-2"></i>智能体矩阵
                    </h5>
                    <p class="text-muted">
                        专为航空货运代理行业设计的智能文档识别与自动填写平台，
                        基于大语言模型技术，提升业务处理效率。
                    </p>
                    <div class="d-flex gap-3">
                        <a href="#" class="footer-link">
                            <i class="bi bi-github fs-5"></i>
                        </a>
                        <a href="#" class="footer-link">
                            <i class="bi bi-twitter fs-5"></i>
                        </a>
                        <a href="#" class="footer-link">
                            <i class="bi bi-linkedin fs-5"></i>
                        </a>
                    </div>
                </div>
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="fw-bold mb-3">产品</h6>
                    <ul class="list-unstyled">
                        <li class="mb-2"><a href="#features" class="footer-link">功能特性</a></li>
                        <li class="mb-2"><a href="#download" class="footer-link">插件下载</a></li>
                        <li class="mb-2"><a href="/doc.html" class="footer-link" target="_blank">API文档</a></li>
                        <li class="mb-2"><a href="login.html" class="footer-link">管理平台</a></li>
                    </ul>
                </div>
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="fw-bold mb-3">支持</h6>
                    <ul class="list-unstyled">
                        <li class="mb-2"><a href="#install-guide" class="footer-link">安装指南</a></li>
                        <li class="mb-2"><a href="#" class="footer-link">使用教程</a></li>
                        <li class="mb-2"><a href="#" class="footer-link">常见问题</a></li>
                        <li class="mb-2"><a href="#" class="footer-link">技术支持</a></li>
                    </ul>
                </div>
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="fw-bold mb-3">公司</h6>
                    <ul class="list-unstyled">
                        <li class="mb-2"><a href="#" class="footer-link">关于我们</a></li>
                        <li class="mb-2"><a href="#" class="footer-link">联系我们</a></li>
                        <li class="mb-2"><a href="#" class="footer-link">隐私政策</a></li>
                        <li class="mb-2"><a href="#" class="footer-link">服务条款</a></li>
                    </ul>
                </div>
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="fw-bold mb-3">系统</h6>
                    <ul class="list-unstyled">
                        <li class="mb-2"><a href="/actuator/health" class="footer-link" target="_blank">系统状态</a></li>
                        <li class="mb-2"><a href="login.html" class="footer-link">用户登录</a></li>
                        <li class="mb-2"><a href="register.html" class="footer-link">用户注册</a></li>
                        <li class="mb-2"><a href="/admin.html" class="footer-link">管理后台</a></li>
                    </ul>
                </div>
            </div>
            <hr class="my-4" style="border-color: #475569;">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0 text-muted">
                        &copy; 2024 智能体矩阵. 智能文档识别与自动填写解决方案.
                    </p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0 text-muted">
                        基于大语言模型 | 符合IATA规范 | 企业级安全
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <script src="assets/js/bootstrap.bundle.min.js"></script>
    <script>
        // 平滑滚动
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // 导航栏背景变化
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 50) {
                navbar.style.background = 'rgba(30, 41, 59, 0.98)';
            } else {
                navbar.style.background = 'rgba(30, 41, 59, 0.95)';
            }
        });

        // 添加动画效果
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('fade-in');
                }
            });
        }, observerOptions);

        // 观察所有卡片元素
        document.querySelectorAll('.feature-card, .download-card').forEach(card => {
            observer.observe(card);
        });
    </script>
</body>
</html>
