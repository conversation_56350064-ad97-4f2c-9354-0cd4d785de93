<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能体矩阵 - 客户门户</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        html {
            scroll-behavior: smooth;
        }
        body {
            padding-top: 76px; /* 为固定导航栏留出空间 */
        }
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 100px 0;
        }
        .feature-card {
            transition: transform 0.3s ease;
            border: none;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .feature-card:hover {
            transform: translateY(-5px);
        }
        .api-key-card {
            background: #f8f9fa;
            border-left: 4px solid #007bff;
        }
        .usage-stats {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
        }
        .navbar-brand {
            font-weight: bold;
            font-size: 1.5rem;
        }
        .btn-gradient {
            background: linear-gradient(45deg, #007bff, #0056b3);
            border: none;
            color: white;
        }
        .btn-gradient:hover {
            background: linear-gradient(45deg, #0056b3, #004085);
            color: white;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="bi bi-robot"></i> 智能体矩阵
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#features">功能特性</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#pricing">价格方案</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#docs">API文档</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item" id="loginNav">
                        <a class="nav-link" href="#" onclick="showLoginModal()">登录</a>
                    </li>
                    <li class="nav-item" id="registerNav">
                        <a class="nav-link" href="#" onclick="showRegisterModal()">注册</a>
                    </li>
                    <li class="nav-item dropdown d-none" id="userNav">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle"></i> <span id="username"></span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="showDashboard()">控制台</a></li>
                            <li><a class="dropdown-item" href="#" onclick="logout()">退出登录</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主页内容 -->
    <div id="homePage">
        <!-- Hero Section -->
        <section class="hero-section text-center">
            <div class="container">
                <h1 class="display-4 mb-4">智能文档识别与自动填写平台</h1>
                <p class="lead mb-5">基于AI技术的文档解析服务，支持PDF、图片等多种格式，提供高精度的内容识别和结构化数据输出</p>
                <div class="row justify-content-center">
                    <div class="col-md-8">
                        <div class="d-flex justify-content-center gap-3">
                            <button class="btn btn-light btn-lg" onclick="showRegisterModal()">
                                <i class="bi bi-rocket"></i> 免费开始
                            </button>
                            <button class="btn btn-outline-light btn-lg" onclick="showApiDocs()">
                                <i class="bi bi-book"></i> 查看文档
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 功能特性 -->
        <section id="features" class="py-5" style="scroll-margin-top: 100px;">
            <div class="container">
                <div class="text-center mb-5">
                    <h2>强大的功能特性</h2>
                    <p class="text-muted">为开发者提供简单易用的API接口</p>
                </div>
                <div class="row">
                    <div class="col-md-4 mb-4">
                        <div class="card feature-card h-100">
                            <div class="card-body text-center">
                                <i class="bi bi-file-earmark-pdf text-primary" style="font-size: 3rem;"></i>
                                <h5 class="card-title mt-3">多格式支持</h5>
                                <p class="card-text">支持PDF、JPG、PNG等多种文件格式的智能识别和解析</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-4">
                        <div class="card feature-card h-100">
                            <div class="card-body text-center">
                                <i class="bi bi-lightning text-warning" style="font-size: 3rem;"></i>
                                <h5 class="card-title mt-3">高速处理</h5>
                                <p class="card-text">基于先进的AI算法，提供毫秒级的文档处理速度</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-4">
                        <div class="card feature-card h-100">
                            <div class="card-body text-center">
                                <i class="bi bi-shield-check text-success" style="font-size: 3rem;"></i>
                                <h5 class="card-title mt-3">安全可靠</h5>
                                <p class="card-text">企业级安全保障，数据传输加密，隐私保护</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 价格方案 -->
        <section id="pricing" class="py-5 bg-light" style="scroll-margin-top: 100px;">
            <div class="container">
                <div class="text-center mb-5">
                    <h2>选择适合您的方案</h2>
                    <p class="text-muted">灵活的定价策略，满足不同规模的业务需求</p>
                </div>
                <div class="row justify-content-center">
                    <div class="col-md-3 mb-4">
                        <div class="card">
                            <div class="card-header text-center">
                                <h5>免费版</h5>
                            </div>
                            <div class="card-body text-center">
                                <h2>¥0<small class="text-muted">/月</small></h2>
                                <ul class="list-unstyled mt-3">
                                    <li>1,000次/月 API调用</li>
                                    <li>基础文档识别</li>
                                    <li>邮件支持</li>
                                </ul>
                                <button class="btn btn-outline-primary" onclick="showRegisterModal()">开始使用</button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-4">
                        <div class="card border-primary">
                            <div class="card-header text-center bg-primary text-white">
                                <h5>基础版</h5>
                            </div>
                            <div class="card-body text-center">
                                <h2>¥99<small class="text-muted">/月</small></h2>
                                <ul class="list-unstyled mt-3">
                                    <li>10,000次/月 API调用</li>
                                    <li>高级文档识别</li>
                                    <li>优先技术支持</li>
                                </ul>
                                <button class="btn btn-primary" onclick="showRegisterModal()">立即购买</button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-4">
                        <div class="card">
                            <div class="card-header text-center">
                                <h5>专业版</h5>
                            </div>
                            <div class="card-body text-center">
                                <h2>¥299<small class="text-muted">/月</small></h2>
                                <ul class="list-unstyled mt-3">
                                    <li>50,000次/月 API调用</li>
                                    <li>全功能文档识别</li>
                                    <li>专属客服支持</li>
                                </ul>
                                <button class="btn btn-outline-primary" onclick="showRegisterModal()">立即购买</button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-4">
                        <div class="card">
                            <div class="card-header text-center">
                                <h5>企业版</h5>
                            </div>
                            <div class="card-body text-center">
                                <h2>定制<small class="text-muted">价格</small></h2>
                                <ul class="list-unstyled mt-3">
                                    <li>无限制 API调用</li>
                                    <li>定制化功能开发</li>
                                    <li>专属技术团队</li>
                                </ul>
                                <button class="btn btn-outline-primary" onclick="contactSales()">联系销售</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- API文档 -->
        <section id="docs" class="py-5" style="scroll-margin-top: 100px;">
            <div class="container">
                <div class="text-center mb-5">
                    <h2>API文档</h2>
                    <p class="text-muted">完整的API接口文档和使用指南</p>
                </div>
                <div class="row justify-content-center">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-body text-center">
                                <i class="bi bi-book text-primary" style="font-size: 4rem;"></i>
                                <h4 class="mt-3">在线API文档</h4>
                                <p class="text-muted mb-4">
                                    查看完整的API接口文档，包括认证方式、请求参数、响应格式等详细信息
                                </p>
                                <div class="d-flex justify-content-center gap-3">
                                    <button class="btn btn-primary" onclick="showApiDocs()">
                                        <i class="bi bi-book"></i> 查看API文档
                                    </button>
                                    <button class="btn btn-outline-primary" onclick="downloadApiDoc()">
                                        <i class="bi bi-download"></i> 下载文档
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <!-- 用户控制台 -->
    <div id="dashboardPage" class="d-none">
        <div class="container-fluid mt-4">
            <div class="row">
                <!-- 侧边栏 -->
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-header">
                            <h6><i class="bi bi-speedometer2"></i> 控制台</h6>
                        </div>
                        <div class="list-group list-group-flush">
                            <a href="#" class="list-group-item list-group-item-action active" onclick="showApiKeys()">
                                <i class="bi bi-key"></i> API Keys
                            </a>
                            <a href="#" class="list-group-item list-group-item-action" onclick="showUsageStats()">
                                <i class="bi bi-graph-up"></i> 使用统计
                            </a>
                            <a href="#" class="list-group-item list-group-item-action" onclick="showProfile()">
                                <i class="bi bi-person"></i> 个人资料
                            </a>
                            <a href="#" class="list-group-item list-group-item-action" onclick="showBilling()">
                                <i class="bi bi-credit-card"></i> 账单管理
                            </a>
                        </div>
                    </div>
                </div>

                <!-- 主内容区 -->
                <div class="col-md-9">
                    <!-- API Keys 管理 -->
                    <div id="apiKeysSection">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h4><i class="bi bi-key"></i> API Keys 管理</h4>
                            <button class="btn btn-primary" onclick="showCreateApiKeyModal()">
                                <i class="bi bi-plus"></i> 创建 API Key
                            </button>
                        </div>

                        <!-- API Keys 列表 -->
                        <div class="card">
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>名称</th>
                                                <th>Key ID</th>
                                                <th>状态</th>
                                                <th>创建时间</th>
                                                <th>最后使用</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="apiKeysTable">
                                            <!-- API Keys 数据将通过JavaScript加载 -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 使用统计 -->
                    <div id="usageStatsSection" class="d-none">
                        <h4><i class="bi bi-graph-up"></i> 使用统计</h4>
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <div class="card usage-stats">
                                    <div class="card-body text-center">
                                        <h3 id="todayUsage">0</h3>
                                        <p class="mb-0">今日调用</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card usage-stats">
                                    <div class="card-body text-center">
                                        <h3 id="monthlyUsage">0</h3>
                                        <p class="mb-0">本月调用</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card usage-stats">
                                    <div class="card-body text-center">
                                        <h3 id="totalUsage">0</h3>
                                        <p class="mb-0">总调用次数</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card usage-stats">
                                    <div class="card-body text-center">
                                        <h3 id="remainingQuota">0</h3>
                                        <p class="mb-0">剩余配额</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 登录模态框 -->
    <div class="modal fade" id="loginModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">用户登录</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="loginForm">
                        <div class="mb-3">
                            <label for="loginUsername" class="form-label">用户名</label>
                            <input type="text" class="form-control" id="loginUsername" required>
                        </div>
                        <div class="mb-3">
                            <label for="loginPassword" class="form-label">密码</label>
                            <input type="password" class="form-control" id="loginPassword" required>
                        </div>
                        <button type="submit" class="btn btn-primary w-100">登录</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- 注册模态框 -->
    <div class="modal fade" id="registerModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">用户注册</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="registerForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="registerUsername" class="form-label">用户名</label>
                                    <input type="text" class="form-control" id="registerUsername" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="registerEmail" class="form-label">邮箱</label>
                                    <input type="email" class="form-control" id="registerEmail" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="registerPassword" class="form-label">密码</label>
                                    <input type="password" class="form-control" id="registerPassword" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="confirmPassword" class="form-label">确认密码</label>
                                    <input type="password" class="form-control" id="confirmPassword" required>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="companyName" class="form-label">公司名称</label>
                            <input type="text" class="form-control" id="companyName">
                        </div>
                        <div class="mb-3">
                            <label for="contactPhone" class="form-label">联系电话</label>
                            <input type="tel" class="form-control" id="contactPhone">
                        </div>
                        <button type="submit" class="btn btn-primary w-100">注册</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- 创建API Key模态框 -->
    <div class="modal fade" id="createApiKeyModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">创建 API Key</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="createApiKeyForm">
                        <div class="mb-3">
                            <label for="apiKeyName" class="form-label">API Key 名称</label>
                            <input type="text" class="form-control" id="apiKeyName" required>
                        </div>
                        <div class="mb-3">
                            <label for="apiKeyDescription" class="form-label">描述</label>
                            <textarea class="form-control" id="apiKeyDescription" rows="3"></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="expiresAt" class="form-label">过期时间（可选）</label>
                            <input type="datetime-local" class="form-control" id="expiresAt">
                        </div>
                        <button type="submit" class="btn btn-primary w-100">创建</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="customer-portal.js"></script>
</body>
</html>
