// 客户门户JavaScript代码

// 全局变量
let currentUser = null;
let apiKeys = [];

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    checkLoginStatus();
    initEventListeners();
    initSmoothScrolling();
});

// 检查登录状态
function checkLoginStatus() {
    const token = localStorage.getItem('token');
    if (token) {
        // 验证token有效性
        fetch('/api/v1/auth/verify', {
            method: 'POST',
            headers: {
                'Authorization': 'Bearer ' + token,
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.code === 200) {
                currentUser = data.data;
                showUserLoggedIn();
            } else {
                localStorage.removeItem('token');
                showUserLoggedOut();
            }
        })
        .catch(error => {
            console.error('验证token失败:', error);
            localStorage.removeItem('token');
            showUserLoggedOut();
        });
    } else {
        showUserLoggedOut();
    }
}

// 显示用户已登录状态
function showUserLoggedIn() {
    document.getElementById('loginNav').classList.add('d-none');
    document.getElementById('registerNav').classList.add('d-none');
    document.getElementById('userNav').classList.remove('d-none');
    document.getElementById('username').textContent = currentUser.username;
}

// 显示用户未登录状态
function showUserLoggedOut() {
    document.getElementById('loginNav').classList.remove('d-none');
    document.getElementById('registerNav').classList.remove('d-none');
    document.getElementById('userNav').classList.add('d-none');
    document.getElementById('homePage').classList.remove('d-none');
    document.getElementById('dashboardPage').classList.add('d-none');
}

// 初始化事件监听器
function initEventListeners() {
    // 登录表单提交
    document.getElementById('loginForm').addEventListener('submit', function(e) {
        e.preventDefault();
        handleLogin();
    });

    // 注册表单提交
    document.getElementById('registerForm').addEventListener('submit', function(e) {
        e.preventDefault();
        handleRegister();
    });

    // 创建API Key表单提交
    document.getElementById('createApiKeyForm').addEventListener('submit', function(e) {
        e.preventDefault();
        handleCreateApiKey();
    });
}

// 初始化平滑滚动
function initSmoothScrolling() {
    // 为所有锚点链接添加平滑滚动
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            const target = document.querySelector(targetId);

            console.log('点击导航链接:', targetId, '目标元素:', target); // 调试信息

            if (target) {
                // 计算滚动位置，考虑固定导航栏的高度
                const navbarHeight = 76;
                const targetPosition = target.getBoundingClientRect().top + window.pageYOffset - navbarHeight;

                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });
            } else {
                console.warn('未找到目标元素:', targetId);
            }
        });
    });
}

// 显示登录模态框
function showLoginModal() {
    const modal = new bootstrap.Modal(document.getElementById('loginModal'));
    modal.show();
}

// 显示注册模态框
function showRegisterModal() {
    const modal = new bootstrap.Modal(document.getElementById('registerModal'));
    modal.show();
}

// 处理用户登录
function handleLogin() {
    const username = document.getElementById('loginUsername').value;
    const password = document.getElementById('loginPassword').value;

    fetch('/api/v1/auth/login', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            username: username,
            password: password
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 200) {
            localStorage.setItem('token', data.data.token);
            currentUser = data.data.userInfo;
            showUserLoggedIn();
            bootstrap.Modal.getInstance(document.getElementById('loginModal')).hide();
            showMessage('登录成功', 'success');
        } else {
            showMessage(data.message || '登录失败', 'error');
        }
    })
    .catch(error => {
        console.error('登录失败:', error);
        showMessage('登录失败，请稍后重试', 'error');
    });
}

// 处理用户注册
function handleRegister() {
    const username = document.getElementById('registerUsername').value;
    const email = document.getElementById('registerEmail').value;
    const password = document.getElementById('registerPassword').value;
    const confirmPassword = document.getElementById('confirmPassword').value;
    const companyName = document.getElementById('companyName').value;
    const contactPhone = document.getElementById('contactPhone').value;

    if (password !== confirmPassword) {
        showMessage('两次输入的密码不一致', 'error');
        return;
    }

    fetch('/api/v1/auth/register', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            username: username,
            email: email,
            password: password,
            companyName: companyName,
            contactPhone: contactPhone
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 200) {
            bootstrap.Modal.getInstance(document.getElementById('registerModal')).hide();
            showMessage('注册成功，请登录', 'success');
            showLoginModal();
        } else {
            showMessage(data.message || '注册失败', 'error');
        }
    })
    .catch(error => {
        console.error('注册失败:', error);
        showMessage('注册失败，请稍后重试', 'error');
    });
}

// 用户登出
function logout() {
    localStorage.removeItem('token');
    currentUser = null;
    showUserLoggedOut();
    showMessage('已退出登录', 'success');
}

// 显示控制台
function showDashboard() {
    document.getElementById('homePage').classList.add('d-none');
    document.getElementById('dashboardPage').classList.remove('d-none');
    loadApiKeys();
}

// 显示API Keys管理
function showApiKeys() {
    document.getElementById('apiKeysSection').classList.remove('d-none');
    document.getElementById('usageStatsSection').classList.add('d-none');
    loadApiKeys();
}

// 显示使用统计
function showUsageStats() {
    document.getElementById('apiKeysSection').classList.add('d-none');
    document.getElementById('usageStatsSection').classList.remove('d-none');
    loadUsageStats();
}

// 显示个人资料
function showProfile() {
    showMessage('个人资料功能开发中', 'info');
}

// 显示账单管理
function showBilling() {
    showMessage('账单管理功能开发中', 'info');
}

// 加载API Keys列表
function loadApiKeys() {
    const token = localStorage.getItem('token');
    if (!token) return;

    fetch('/api/v1/api-keys/my', {
        headers: {
            'Authorization': 'Bearer ' + token
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 200) {
            apiKeys = data.data.records;
            renderApiKeysTable();
        } else {
            showMessage('加载API Keys失败: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('加载API Keys失败:', error);
        showMessage('加载API Keys失败', 'error');
    });
}

// 渲染API Keys表格
function renderApiKeysTable() {
    const tbody = document.getElementById('apiKeysTable');
    tbody.innerHTML = '';

    if (apiKeys.length === 0) {
        tbody.innerHTML = '<tr><td colspan="6" class="text-center text-muted">暂无API Key</td></tr>';
        return;
    }

    apiKeys.forEach(apiKey => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${apiKey.keyName}</td>
            <td><code>${apiKey.keyId}</code></td>
            <td>
                <span class="badge ${apiKey.status === 1 ? 'bg-success' : 'bg-danger'}">
                    ${apiKey.status === 1 ? '启用' : '禁用'}
                </span>
            </td>
            <td>${formatDateTime(apiKey.createdTime)}</td>
            <td>${apiKey.lastUsedAt ? formatDateTime(apiKey.lastUsedAt) : '从未使用'}</td>
            <td>
                <button class="btn btn-sm btn-outline-primary" onclick="viewApiKey(${apiKey.id})">
                    <i class="bi bi-eye"></i>
                </button>
                <button class="btn btn-sm btn-outline-danger" onclick="deleteApiKey(${apiKey.id})">
                    <i class="bi bi-trash"></i>
                </button>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// 显示创建API Key模态框
function showCreateApiKeyModal() {
    const modal = new bootstrap.Modal(document.getElementById('createApiKeyModal'));
    modal.show();
}

// 处理创建API Key
function handleCreateApiKey() {
    const token = localStorage.getItem('token');
    if (!token) return;

    const keyName = document.getElementById('apiKeyName').value;
    const description = document.getElementById('apiKeyDescription').value;
    const expiresAt = document.getElementById('expiresAt').value;

    fetch('/api/v1/api-keys', {
        method: 'POST',
        headers: {
            'Authorization': 'Bearer ' + token,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            keyName: keyName,
            description: description,
            expiresAt: expiresAt || null
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 200) {
            bootstrap.Modal.getInstance(document.getElementById('createApiKeyModal')).hide();
            showApiKeyCreatedModal(data.data);
            loadApiKeys();
        } else {
            showMessage('创建API Key失败: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('创建API Key失败:', error);
        showMessage('创建API Key失败', 'error');
    });
}

// 显示API Key创建成功模态框
function showApiKeyCreatedModal(apiKey) {
    const fullApiKey = `${apiKey.keyId}.${apiKey.keySecret}`;
    
    const modalHtml = `
        <div class="modal fade" id="apiKeyCreatedModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header bg-success text-white">
                        <h5 class="modal-title">API Key 创建成功</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle"></i>
                            <strong>重要提示：</strong>请立即复制并保存您的API Key，关闭此窗口后将无法再次查看完整密钥。
                        </div>
                        <div class="mb-3">
                            <label class="form-label">您的API Key：</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="fullApiKey" value="${fullApiKey}" readonly>
                                <button class="btn btn-outline-secondary" onclick="copyApiKey()">
                                    <i class="bi bi-clipboard"></i> 复制
                                </button>
                            </div>
                        </div>
                        <div class="mb-3">
                            <small class="text-muted">
                                使用方法：在请求头中添加 <code>X-API-Key: ${fullApiKey}</code>
                            </small>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" data-bs-dismiss="modal">我已保存</button>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    document.body.insertAdjacentHTML('beforeend', modalHtml);
    const modal = new bootstrap.Modal(document.getElementById('apiKeyCreatedModal'));
    modal.show();
    
    // 模态框关闭后移除DOM元素
    document.getElementById('apiKeyCreatedModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

// 复制API Key
function copyApiKey() {
    const apiKeyInput = document.getElementById('fullApiKey');
    apiKeyInput.select();
    document.execCommand('copy');
    showMessage('API Key已复制到剪贴板', 'success');
}

// 删除API Key
function deleteApiKey(id) {
    if (!confirm('确定要删除这个API Key吗？此操作不可撤销。')) {
        return;
    }

    const token = localStorage.getItem('token');
    if (!token) return;

    fetch(`/api/v1/api-keys/${id}`, {
        method: 'DELETE',
        headers: {
            'Authorization': 'Bearer ' + token
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 200) {
            showMessage('API Key删除成功', 'success');
            loadApiKeys();
        } else {
            showMessage('删除失败: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('删除API Key失败:', error);
        showMessage('删除失败', 'error');
    });
}

// 查看API Key详情
function viewApiKey(id) {
    showMessage('查看API Key详情功能开发中', 'info');
}

// 加载使用统计
function loadUsageStats() {
    // 这里应该调用实际的API来获取使用统计
    // 暂时使用模拟数据
    document.getElementById('todayUsage').textContent = '156';
    document.getElementById('monthlyUsage').textContent = '2,847';
    document.getElementById('totalUsage').textContent = '15,632';
    document.getElementById('remainingQuota').textContent = '7,153';
}

// 显示API文档
function showApiDocs() {
    window.open('/doc.html', '_blank');
}

// 下载API文档
function downloadApiDoc() {
    // 创建一个临时链接来下载文档
    const link = document.createElement('a');
    link.href = '/api/v1/docs/download'; // 假设有这个下载接口
    link.download = 'SinoairAgent-API-Documentation.pdf';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    showMessage('API文档下载已开始', 'success');
}

// 联系销售
function contactSales() {
    showMessage('请发送邮件至 <EMAIL> 联系我们的销售团队', 'info');
}

// 显示消息提示
function showMessage(message, type = 'info') {
    const alertClass = {
        'success': 'alert-success',
        'error': 'alert-danger',
        'warning': 'alert-warning',
        'info': 'alert-info'
    }[type] || 'alert-info';

    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show position-fixed" 
             style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    document.body.insertAdjacentHTML('beforeend', alertHtml);
    
    // 3秒后自动消失
    setTimeout(() => {
        const alerts = document.querySelectorAll('.alert');
        if (alerts.length > 0) {
            alerts[alerts.length - 1].remove();
        }
    }, 3000);
}

// 格式化日期时间
function formatDateTime(dateTimeStr) {
    if (!dateTimeStr) return '';
    const date = new Date(dateTimeStr);
    return date.toLocaleString('zh-CN');
}
