<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>注册 - SinoairAgent</title>
    <link href="assets/css/bootstrap.min.css" rel="stylesheet">
    <link href="assets/css/bootstrap-icons.css" rel="stylesheet">
    <link href="assets/css/google-fonts-inter.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2563eb;
            --primary-dark: #1d4ed8;
            --secondary-color: #64748b;
            --success-color: #059669;
            --dark-color: #1e293b;
            --light-color: #f8fafc;
            --border-color: #e2e8f0;
        }

        * {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }

        body {
            background: linear-gradient(135deg, #1e293b 0%, #334155 50%, #475569 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px 0;
        }

        .register-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            overflow: hidden;
            max-width: 1000px;
            width: 100%;
            margin: 20px;
        }

        .register-left {
            background: linear-gradient(135deg, var(--success-color), #10b981);
            color: white;
            padding: 60px 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .register-left::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
        }

        .register-right {
            padding: 60px 40px;
        }

        .brand-logo {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }

        .form-control {
            border-radius: 12px;
            border: 2px solid var(--border-color);
            padding: 12px 16px;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .form-select {
            border-radius: 12px;
            border: 2px solid var(--border-color);
            padding: 12px 16px;
            font-size: 16px;
        }

        .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), #3b82f6);
            border: none;
            border-radius: 12px;
            padding: 12px 24px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(37, 99, 235, 0.4);
        }

        .floating-elements {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            pointer-events: none;
        }

        .floating-element {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        .alert {
            border-radius: 12px;
            border: none;
        }

        .form-check-input:checked {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .text-link {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
        }

        .text-link:hover {
            color: var(--primary-dark);
            text-decoration: underline;
        }

        .password-strength {
            height: 4px;
            border-radius: 2px;
            background-color: #e2e8f0;
            margin-top: 8px;
            overflow: hidden;
        }

        .password-strength-bar {
            height: 100%;
            transition: all 0.3s ease;
            border-radius: 2px;
        }

        .strength-weak { background-color: #ef4444; width: 33%; }
        .strength-medium { background-color: #f59e0b; width: 66%; }
        .strength-strong { background-color: #10b981; width: 100%; }

        @media (max-width: 768px) {
            .register-left {
                padding: 40px 30px;
                text-align: center;
            }
            
            .register-right {
                padding: 40px 30px;
            }
        }
    </style>
</head>
<body>
    <div class="register-container">
        <div class="row g-0">
            <!-- 左侧品牌区域 -->
            <div class="col-lg-5">
                <div class="register-left h-100">
                    <div class="floating-elements">
                        <div class="floating-element" style="width: 60px; height: 60px; top: 10%; left: 10%; animation-delay: 0s;"></div>
                        <div class="floating-element" style="width: 40px; height: 40px; top: 60%; right: 15%; animation-delay: 2s;"></div>
                        <div class="floating-element" style="width: 80px; height: 80px; bottom: 20%; left: 20%; animation-delay: 4s;"></div>
                    </div>
                    
                    <div style="position: relative; z-index: 1;">
                        <div class="brand-logo">
                            <i class="bi bi-robot me-2"></i>SinoairAgent
                        </div>
                        <h3 class="mb-4">加入我们的平台</h3>
                        <p class="mb-4 opacity-75">
                            注册SinoairAgent账号，开始体验智能文档识别的强大功能。
                            专为航空货运代理行业设计，提升您的工作效率。
                        </p>
                        
                        <div class="d-flex align-items-center mb-3">
                            <i class="bi bi-shield-check me-3 fs-5"></i>
                            <span>企业级安全保障</span>
                        </div>
                        <div class="d-flex align-items-center mb-3">
                            <i class="bi bi-lightning-charge me-3 fs-5"></i>
                            <span>快速部署使用</span>
                        </div>
                        <div class="d-flex align-items-center mb-4">
                            <i class="bi bi-headset me-3 fs-5"></i>
                            <span>7x24技术支持</span>
                        </div>
                        
                        <div class="mt-5">
                            <small class="opacity-75">
                                已有账号？ 
                                <a href="login.html" class="text-white fw-bold text-decoration-underline">立即登录</a>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 右侧注册表单 -->
            <div class="col-lg-7">
                <div class="register-right">
                    <div class="text-center mb-4">
                        <h2 class="fw-bold text-dark mb-2">创建账号</h2>
                        <p class="text-muted">填写以下信息完成注册</p>
                    </div>
                    
                    <div id="alertContainer"></div>
                    
                    <form id="registerForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="username" class="form-label fw-medium">用户名 <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <span class="input-group-text bg-light border-end-0" style="border-radius: 12px 0 0 12px; border-color: var(--border-color);">
                                        <i class="bi bi-person text-muted"></i>
                                    </span>
                                    <input type="text" class="form-control border-start-0" id="username" 
                                           placeholder="请输入用户名" required
                                           style="border-radius: 0 12px 12px 0;">
                                </div>
                                <small class="text-muted">用户名长度3-20位，支持字母、数字、下划线</small>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="realName" class="form-label fw-medium">真实姓名 <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <span class="input-group-text bg-light border-end-0" style="border-radius: 12px 0 0 12px; border-color: var(--border-color);">
                                        <i class="bi bi-person-badge text-muted"></i>
                                    </span>
                                    <input type="text" class="form-control border-start-0" id="realName" 
                                           placeholder="请输入真实姓名" required
                                           style="border-radius: 0 12px 12px 0;">
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="email" class="form-label fw-medium">邮箱地址 <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <span class="input-group-text bg-light border-end-0" style="border-radius: 12px 0 0 12px; border-color: var(--border-color);">
                                    <i class="bi bi-envelope text-muted"></i>
                                </span>
                                <input type="email" class="form-control border-start-0" id="email" 
                                       placeholder="请输入邮箱地址" required
                                       style="border-radius: 0 12px 12px 0;">
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="password" class="form-label fw-medium">密码 <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <span class="input-group-text bg-light border-end-0" style="border-radius: 12px 0 0 12px; border-color: var(--border-color);">
                                        <i class="bi bi-lock text-muted"></i>
                                    </span>
                                    <input type="password" class="form-control border-start-0 border-end-0" id="password" 
                                           placeholder="请输入密码" required
                                           style="border-radius: 0;">
                                    <button class="btn btn-outline-secondary border-start-0" type="button" 
                                            style="border-radius: 0 12px 12px 0; border-color: var(--border-color);"
                                            onclick="togglePassword('password')">
                                        <i class="bi bi-eye" id="passwordToggle"></i>
                                    </button>
                                </div>
                                <div class="password-strength">
                                    <div class="password-strength-bar" id="passwordStrengthBar"></div>
                                </div>
                                <small class="text-muted" id="passwordStrengthText">密码强度：弱</small>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="confirmPassword" class="form-label fw-medium">确认密码 <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <span class="input-group-text bg-light border-end-0" style="border-radius: 12px 0 0 12px; border-color: var(--border-color);">
                                        <i class="bi bi-lock-fill text-muted"></i>
                                    </span>
                                    <input type="password" class="form-control border-start-0 border-end-0" id="confirmPassword" 
                                           placeholder="请再次输入密码" required
                                           style="border-radius: 0;">
                                    <button class="btn btn-outline-secondary border-start-0" type="button" 
                                            style="border-radius: 0 12px 12px 0; border-color: var(--border-color);"
                                            onclick="togglePassword('confirmPassword')">
                                        <i class="bi bi-eye" id="confirmPasswordToggle"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="company" class="form-label fw-medium">公司名称</label>
                            <div class="input-group">
                                <span class="input-group-text bg-light border-end-0" style="border-radius: 12px 0 0 12px; border-color: var(--border-color);">
                                    <i class="bi bi-building text-muted"></i>
                                </span>
                                <input type="text" class="form-control border-start-0" id="company" 
                                       placeholder="请输入公司名称（可选）"
                                       style="border-radius: 0 12px 12px 0;">
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="agreeTerms" required>
                                <label class="form-check-label text-muted" for="agreeTerms">
                                    我已阅读并同意 
                                    <a href="#" class="text-link">服务条款</a> 和 
                                    <a href="#" class="text-link">隐私政策</a>
                                </label>
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-primary w-100 mb-3" id="registerBtn">
                            <i class="bi bi-person-plus me-2"></i>创建账号
                        </button>
                        
                        <div class="text-center">
                            <small class="text-muted">
                                已有账号？ 
                                <a href="login.html" class="text-link">立即登录</a>
                            </small>
                        </div>
                    </form>
                    
                    <hr class="my-4">
                    
                    <div class="text-center">
                        <small class="text-muted">
                            <a href="index.html" class="text-link me-3">
                                <i class="bi bi-house me-1"></i>返回首页
                            </a>
                            <a href="/doc.html" class="text-link" target="_blank">
                                <i class="bi bi-book me-1"></i>API文档
                            </a>
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="assets/js/bootstrap.bundle.min.js"></script>
    <script>
        // 切换密码显示
        function togglePassword(inputId) {
            const passwordInput = document.getElementById(inputId);
            const passwordToggle = document.getElementById(inputId + 'Toggle');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                passwordToggle.className = 'bi bi-eye-slash';
            } else {
                passwordInput.type = 'password';
                passwordToggle.className = 'bi bi-eye';
            }
        }

        // 密码强度检测
        function checkPasswordStrength(password) {
            let strength = 0;
            let text = '弱';
            let className = 'strength-weak';
            
            if (password.length >= 8) strength++;
            if (/[a-z]/.test(password)) strength++;
            if (/[A-Z]/.test(password)) strength++;
            if (/[0-9]/.test(password)) strength++;
            if (/[^A-Za-z0-9]/.test(password)) strength++;
            
            if (strength >= 3) {
                text = '中';
                className = 'strength-medium';
            }
            if (strength >= 4) {
                text = '强';
                className = 'strength-strong';
            }
            
            const strengthBar = document.getElementById('passwordStrengthBar');
            const strengthText = document.getElementById('passwordStrengthText');
            
            strengthBar.className = 'password-strength-bar ' + className;
            strengthText.textContent = '密码强度：' + text;
        }

        // 监听密码输入
        document.getElementById('password').addEventListener('input', function() {
            checkPasswordStrength(this.value);
        });

        // 显示提示信息
        function showAlert(message, type = 'danger') {
            const alertContainer = document.getElementById('alertContainer');
            alertContainer.innerHTML = `
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    <i class="bi bi-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
        }

        // 注册表单提交
        document.getElementById('registerForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const realName = document.getElementById('realName').value;
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            const company = document.getElementById('company').value;
            const registerBtn = document.getElementById('registerBtn');
            
            // 验证密码
            if (password !== confirmPassword) {
                showAlert('两次输入的密码不一致');
                return;
            }
            
            if (password.length < 6) {
                showAlert('密码长度至少6位');
                return;
            }
            
            // 显示加载状态
            registerBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>注册中...';
            registerBtn.disabled = true;
            
            try {
                // 这里应该调用实际的注册API
                // const response = await fetch('/api/v1/auth/register', {
                //     method: 'POST',
                //     headers: {
                //         'Content-Type': 'application/json'
                //     },
                //     body: JSON.stringify({ username, realName, email, password, company })
                // });

                // 模拟注册成功
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                showAlert('注册成功！请登录您的账号', 'success');
                
                setTimeout(() => {
                    window.location.href = 'login.html';
                }, 2000);
                
            } catch (error) {
                showAlert('注册失败：' + error.message);
            } finally {
                // 恢复按钮状态
                registerBtn.innerHTML = '<i class="bi bi-person-plus me-2"></i>创建账号';
                registerBtn.disabled = false;
            }
        });
    </script>
</body>
</html>
