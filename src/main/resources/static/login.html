<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - 智能体矩阵</title>
    <link href="assets/css/bootstrap.min.css" rel="stylesheet">
    <link href="assets/css/bootstrap-icons.css" rel="stylesheet">
    <link href="assets/css/google-fonts-inter.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2563eb;
            --primary-dark: #1d4ed8;
            --secondary-color: #64748b;
            --dark-color: #1e293b;
            --light-color: #f8fafc;
            --border-color: #e2e8f0;
        }

        * {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }

        body {
            background: linear-gradient(135deg, #1e293b 0%, #334155 50%, #475569 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .login-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            overflow: hidden;
            max-width: 900px;
            width: 100%;
            margin: 20px;
        }

        .login-left {
            background: linear-gradient(135deg, var(--primary-color), #3b82f6);
            color: white;
            padding: 60px 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .login-left::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
        }

        .login-right {
            padding: 60px 40px;
        }

        .brand-logo {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }

        .form-control {
            border-radius: 12px;
            border: 2px solid var(--border-color);
            padding: 12px 16px;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), #3b82f6);
            border: none;
            border-radius: 12px;
            padding: 12px 24px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(37, 99, 235, 0.4);
        }

        .floating-elements {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            pointer-events: none;
        }

        .floating-element {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        .alert {
            border-radius: 12px;
            border: none;
        }

        .form-check-input:checked {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .text-link {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
        }

        .text-link:hover {
            color: var(--primary-dark);
            text-decoration: underline;
        }

        @media (max-width: 768px) {
            .login-left {
                padding: 40px 30px;
                text-align: center;
            }
            
            .login-right {
                padding: 40px 30px;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="row g-0">
            <!-- 左侧品牌区域 -->
            <div class="col-lg-5">
                <div class="login-left h-100">
                    <div class="floating-elements">
                        <div class="floating-element" style="width: 60px; height: 60px; top: 10%; left: 10%; animation-delay: 0s;"></div>
                        <div class="floating-element" style="width: 40px; height: 40px; top: 60%; right: 15%; animation-delay: 2s;"></div>
                        <div class="floating-element" style="width: 80px; height: 80px; bottom: 20%; left: 20%; animation-delay: 4s;"></div>
                    </div>
                    
                    <div style="position: relative; z-index: 1;">
                        <div class="brand-logo">
                            <i class="bi bi-robot me-2"></i>智能体矩阵
                        </div>
                        <h3 class="mb-4">智能文档识别平台</h3>
                        <p class="mb-4 opacity-75">
                            基于大语言模型的文档智能识别，专为航空货运代理行业设计。
                            支持订单、发票、制单等多种业务场景。
                        </p>
                        
                        <div class="d-flex align-items-center mb-3">
                            <i class="bi bi-check-circle-fill me-3 fs-5"></i>
                            <span>99.5% 识别准确率</span>
                        </div>
                        <div class="d-flex align-items-center mb-3">
                            <i class="bi bi-check-circle-fill me-3 fs-5"></i>
                            <span>90% 效率提升</span>
                        </div>
                        <div class="d-flex align-items-center mb-4">
                            <i class="bi bi-check-circle-fill me-3 fs-5"></i>
                            <span>符合IATA规范</span>
                        </div>
                        
                        <div class="mt-5">
                            <small class="opacity-75">
                                还没有账号？ 
                                <a href="register.html" class="text-white fw-bold text-decoration-underline">立即注册</a>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 右侧登录表单 -->
            <div class="col-lg-7">
                <div class="login-right">
                    <div class="text-center mb-4">
                        <h2 class="fw-bold text-dark mb-2">欢迎回来</h2>
                        <p class="text-muted">请登录您的账号以继续使用</p>
                    </div>
                    
                    <div id="alertContainer"></div>
                    
                    <form id="loginForm">
                        <!-- 快速选择用户 -->
                        <div class="mb-3">
                            <label for="userSelect" class="form-label fw-medium">快速选择用户</label>
                            <select class="form-select" id="userSelect" style="border-radius: 12px; border-color: var(--border-color);">
                                <option value="">-- 选择预设用户或手动输入 --</option>
                                <option value="admin:password123">admin (系统管理员)</option>
                                <option value="agent_admin:password123">agent_admin (Agent管理员)</option>
                                <option value="user1:password123">user1 (普通用户)</option>
                                <option value="reviewer:password123">reviewer (审核员)</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="username" class="form-label fw-medium">用户名</label>
                            <div class="input-group">
                                <span class="input-group-text bg-light border-end-0" style="border-radius: 12px 0 0 12px; border-color: var(--border-color);">
                                    <i class="bi bi-person text-muted"></i>
                                </span>
                                <input type="text" class="form-control border-start-0" id="username"
                                       placeholder="请输入用户名" value="" required
                                       style="border-radius: 0 12px 12px 0;">
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="password" class="form-label fw-medium">密码</label>
                            <div class="input-group">
                                <span class="input-group-text bg-light border-end-0" style="border-radius: 12px 0 0 12px; border-color: var(--border-color);">
                                    <i class="bi bi-lock text-muted"></i>
                                </span>
                                <input type="password" class="form-control border-start-0 border-end-0" id="password"
                                       placeholder="请输入密码" value="" required
                                       style="border-radius: 0;">
                                <button class="btn btn-outline-secondary border-start-0" type="button" 
                                        style="border-radius: 0 12px 12px 0; border-color: var(--border-color);"
                                        onclick="togglePassword()">
                                    <i class="bi bi-eye" id="passwordToggle"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="row mb-4">
                            <div class="col-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="rememberMe">
                                    <label class="form-check-label text-muted" for="rememberMe">
                                        记住我
                                    </label>
                                </div>
                            </div>
                            <div class="col-6 text-end">
                                <a href="#" class="text-link small">忘记密码？</a>
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-primary w-100 mb-3" id="loginBtn">
                            <i class="bi bi-box-arrow-in-right me-2"></i>登录
                        </button>
                        
                        <div class="text-center">
                            <small class="text-muted">
                                还没有账号？ 
                                <a href="register.html" class="text-link">立即注册</a>
                            </small>
                        </div>
                    </form>
                    
                    <hr class="my-4">
                    
                    <div class="text-center">
                        <small class="text-muted">
                            <a href="index.html" class="text-link me-3">
                                <i class="bi bi-house me-1"></i>返回首页
                            </a>
                            <a href="/doc.html" class="text-link" target="_blank">
                                <i class="bi bi-book me-1"></i>API文档
                            </a>
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="assets/js/bootstrap.bundle.min.js"></script>
    <script>
        // 用户选择处理
        document.getElementById('userSelect').addEventListener('change', function() {
            const selectedValue = this.value;
            if (selectedValue) {
                const [username, password] = selectedValue.split(':');
                document.getElementById('username').value = username;
                document.getElementById('password').value = password;
            }
        });

        // 切换密码显示
        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const passwordToggle = document.getElementById('passwordToggle');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                passwordToggle.className = 'bi bi-eye-slash';
            } else {
                passwordInput.type = 'password';
                passwordToggle.className = 'bi bi-eye';
            }
        }

        // 显示提示信息
        function showAlert(message, type = 'danger') {
            const alertContainer = document.getElementById('alertContainer');
            alertContainer.innerHTML = `
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    <i class="bi bi-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
        }

        // 登录表单提交
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const loginBtn = document.getElementById('loginBtn');
            
            // 显示加载状态
            loginBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>登录中...';
            loginBtn.disabled = true;
            
            try {
                const response = await fetch('/api/v1/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });

                const result = await response.json();
                
                if (result.code === 200) {
                    localStorage.setItem('authToken', result.data.token);
                    showAlert('登录成功！正在跳转到管理界面...', 'success');
                    
                    setTimeout(() => {
                        window.location.href = '/admin.html';
                    }, 1500);
                } else {
                    showAlert('登录失败：' + result.message);
                }
            } catch (error) {
                showAlert('登录请求失败：' + error.message);
            } finally {
                // 恢复按钮状态
                loginBtn.innerHTML = '<i class="bi bi-box-arrow-in-right me-2"></i>登录';
                loginBtn.disabled = false;
            }
        });

        // 检查是否已登录（注释掉自动跳转，方便测试不同用户）
        // if (localStorage.getItem('authToken')) {
        //     showAlert('您已登录，正在跳转到管理界面...', 'success');
        //     setTimeout(() => {
        //         window.location.href = '/admin.html';
        //     }, 1000);
        // }
    </script>
</body>
</html>
