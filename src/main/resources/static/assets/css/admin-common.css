/* SinoairAgent 管理后台公共样式 */

:root {
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --secondary-color: #64748b;
    --success-color: #059669;
    --warning-color: #d97706;
    --danger-color: #dc2626;
    --dark-color: #1e293b;
    --light-color: #f8fafc;
    --border-color: #e2e8f0;
    --sidebar-bg: var(--sidebar-background, #FFFFFF);
    --card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --card-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

    /* 主题变量 */
    --dark-text: #181C32;
    --medium-text: #5E6278;
    --light-text: #A1A5B7;
    --background-color: #F9F9F9;
    --card-background: #FFFFFF;

    /* 阴影系统 */
    --light-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --normal-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --deep-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
    --hover-shadow: 0 0.5rem 2rem rgba(0, 0, 0, 0.1);

    /* 主题过渡 */
    --theme-transition: all 0.3s ease;
}

* {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* 深色主题 */
.theme-dark {
    --dark-text: #FFFFFF;
    --medium-text: #A1A5B7;
    --light-text: #5E6278;
    --border-color: #2B2B40;
    --background-color: #1B1B29;
    --card-background: #1E1E2D;

    --light-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.3);
    --normal-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.4);
    --deep-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.5);
    --hover-shadow: 0 0.5rem 2rem rgba(0, 0, 0, 0.3);
}

body {
    background-color: var(--background-color, #f1f5f9);
    color: var(--dark-text, #181C32);
    font-size: 14px;
    margin: 0;
    padding: 0;
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* 主容器布局 */
.admin-container {
    display: flex;
    min-height: 100vh;
}

/* 侧边栏样式 */
.sidebar {
    width: 280px;
    min-height: 100vh;
    background: var(--sidebar-bg);
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
    position: relative;
    flex-shrink: 0;
    transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
}

/* 防止动画冲突 */
.sidebar.transitioning {
    pointer-events: none;
}

/* 文件上传和预览样式 */
.file-drop-zone {
    border-color: var(--border-color) !important;
    background-color: var(--background-color);
    transition: all 0.3s ease;
}

.file-drop-zone:hover {
    border-color: var(--primary-color) !important;
    background-color: var(--card-background);
}

.file-drop-zone.dragover {
    border-color: var(--primary-color) !important;
    background-color: var(--card-background);
    transform: scale(1.02);
}

.file-preview {
    text-align: center;
}

.file-preview .preview-image img {
    border: 1px solid #e5e7eb;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.file-preview .preview-pdf iframe {
    border: 1px solid #e5e7eb;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.file-preview .preview-text textarea {
    background-color: var(--background-color);
    border: 1px solid var(--border-color);
    resize: none;
}

.file-preview .file-info {
    padding: 8px 0;
}

.file-preview .file-actions {
    display: flex;
    justify-content: center;
    gap: 8px;
}

.file-preview .file-actions .btn {
    font-size: 12px;
    padding: 4px 8px;
}

/* 侧边栏收起状态 */
.sidebar.collapsed {
    width: 80px;
}

/* 保持原有的header区域文本处理方式 */
.sidebar.collapsed .sidebar-header .sidebar-text {
    opacity: 0;
    visibility: hidden;
    transform: translateX(-20px);
    width: 0;
    overflow: hidden;
}

/* 只对导航区域的sidebar-text使用display控制 */
.sidebar.collapsed .sidebar-nav .sidebar-text {
    display: none !important;
}



/* 统一的侧边栏项目样式 */
.sidebar-item {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    padding: 8px 20px;
    border-radius: 8px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar-item-icon {
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    flex-shrink: 0;
    transition: justify-content 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar-item-text {
    margin-left: 12px;
    flex: 1;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Logo区域特殊样式 */
.logo-section {
    margin-bottom: 8px;
    transition: margin 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.logo-section .sidebar-item-icon {
    background: linear-gradient(135deg, var(--primary-color), #3b82f6);
    border-radius: 12px;
    color: white;
    font-size: 24px;
    justify-content: center;
    transition: justify-content 0.3s cubic-bezier(0.4, 0, 0.2, 1), border-radius 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 状态区域特殊样式 */
.status-section {
    margin-bottom: 4px;
    transition: margin 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.status-section .sidebar-item-icon {
    justify-content: flex-start;
    padding-left: 20px;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
}

/* 收起状态样式 - 只应用于header中的sidebar-item */
.sidebar.collapsed .sidebar-header .sidebar-item {
    justify-content: center;
    padding: 2px;
    margin: 1px 8px;
}

.sidebar.collapsed .sidebar-header .sidebar-item-icon {
    margin: 0;
    justify-content: center;
    width: 28px;
    height: 28px;
}

/* 侧边栏收起时只隐藏导航区域的文本元素 */
.sidebar.collapsed .sidebar-nav .sidebar-item-text {
    display: none !important;
}

/* 收起状态下的Logo - 保持结构一致 */
.sidebar.collapsed .logo-section {
    margin-bottom: 2px;
    justify-content: center;
}

.sidebar.collapsed .logo-section .sidebar-item-icon {
    width: 32px;
    height: 32px;
    font-size: 16px;
    border-radius: 8px;
    justify-content: center;
}

/* 收起状态下的状态区域 - 保持结构一致 */
.sidebar.collapsed .status-section {
    margin-bottom: 2px;
    justify-content: center;
}

.sidebar.collapsed .status-section .sidebar-item-icon {
    width: 32px;
    height: 32px;
    padding-left: 0;
    justify-content: center;
}

.sidebar.collapsed .status-indicator {
    width: 8px;
    height: 8px;
    animation: pulse 2s infinite;
}



@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.7); }
    70% { box-shadow: 0 0 0 6px rgba(34, 197, 94, 0); }
    100% { box-shadow: 0 0 0 0 rgba(34, 197, 94, 0); }
}



/* 悬停提示优化 */
.sidebar.collapsed .nav-link::after {
    content: attr(title);
    position: absolute;
    left: calc(100% + 12px);
    top: 50%;
    transform: translateY(-50%);
    background: rgba(30, 41, 59, 0.95);
    color: white;
    padding: 8px 12px;
    border-radius: 8px;
    font-size: 13px;
    font-weight: 500;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.2s ease;
    z-index: 1000;
    pointer-events: none;
    backdrop-filter: blur(8px);
}

.sidebar.collapsed .nav-link::before {
    content: '';
    position: absolute;
    left: calc(100% + 6px);
    top: 50%;
    transform: translateY(-50%);
    border: 5px solid transparent;
    border-right-color: rgba(30, 41, 59, 0.95);
    opacity: 0;
    visibility: hidden;
    transition: all 0.2s ease;
    z-index: 1000;
}

.sidebar.collapsed .nav-link:hover::after,
.sidebar.collapsed .nav-link:hover::before {
    opacity: 1;
    visibility: visible;
}

.sidebar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--primary-color);
    opacity: 0.6;
    transition: opacity 0.3s ease;
}

.sidebar-header {
    padding: 16px 20px 8px 20px;
    transition: padding 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    flex-direction: column;
    gap: 8px;
}

/* 展开状态的header样式 */
.sidebar:not(.collapsed) .sidebar-header {
    display: flex !important;
    flex-direction: column !important;
    gap: 8px !important;
    padding: 16px 20px 8px 20px !important;
}

.sidebar:not(.collapsed) .logo-section {
    display: flex !important;
    align-items: center !important;
    margin-bottom: 4px !important;
}

.sidebar:not(.collapsed) .status-section {
    display: flex !important;
    align-items: center !important;
    margin-bottom: 6px !important;
}

.sidebar.collapsed .sidebar-header {
    padding: 4px 0 0px 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0px;
}

.logo-text {
    flex: 1;
    margin-left: 12px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    color: var(--sidebar-text, #181C32);
    font-weight: 600;
}

.status-text {
    margin-left: 8px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.logo-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, var(--primary-color), #3b82f6);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
}



/* 导航菜单容器 */
.sidebar-nav {
    flex: 1;
    padding-top: 2px;
    transition: padding 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar .nav-link {
    color: var(--sidebar-text, #181C32);
    padding: 0;
    margin: 2px 20px;
    border-radius: 8px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-weight: 500;
    text-decoration: none;
    font-size: 14px;
}

/* 展开状态的导航链接样式 */
.sidebar:not(.collapsed) .nav-link.sidebar-item {
    padding: 10px 20px !important;
    margin: 2px 20px !important;
    display: flex !important;
    align-items: center !important;
}

.sidebar .nav-link.sidebar-item {
    padding: 10px 20px;
    margin: 2px 20px;
}

.sidebar .nav-link .sidebar-item-icon {
    width: 48px;
    height: 40px;
    font-size: 18px;
}

.sidebar .nav-link .sidebar-item-icon i {
    width: 20px;
    text-align: left;
}

.sidebar .nav-link:hover {
    color: var(--primary-color);
    background-color: var(--sidebar-hover, rgba(255, 255, 255, 0.1));
    transform: translateX(4px);
}

.sidebar .nav-link.active {
    color: #fff;
    background: linear-gradient(135deg, var(--primary-color), #3b82f6);
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
}

/* 收起状态下的导航样式 */
.sidebar.collapsed .sidebar-nav {
    padding-top: 4px;
    margin-top: 0;
}

/* 重置Bootstrap默认样式 */
.sidebar.collapsed .nav {
    margin: 0 !important;
    padding: 0 !important;
}

.sidebar.collapsed .flex-column {
    gap: 0 !important;
}

.sidebar.collapsed .nav-item {
    margin: 0 !important;
    padding: 0 !important;
    line-height: 1 !important;
}

.sidebar.collapsed .nav-link {
    margin: 0 !important;
    padding: 0 !important;
    line-height: 1 !important;
    display: flex !important;
    justify-content: center !important;
}

.sidebar.collapsed .nav-link.sidebar-item {
    padding: 0 !important;
    justify-content: center !important;
    align-items: center !important;
    min-height: auto !important;
    height: 32px !important;
    width: 32px !important;
    margin: 2px auto !important;
    line-height: 1 !important;
    display: flex !important;
}

.sidebar.collapsed .nav-link:hover {
    transform: none;
    background-color: var(--sidebar-hover, rgba(255, 255, 255, 0.15));
    border-radius: 8px;
}

.sidebar.collapsed .nav-link.active {
    background: linear-gradient(135deg, var(--primary-color), #3b82f6);
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(37, 99, 235, 0.3);
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

.sidebar.collapsed .nav-link .sidebar-item-icon {
    width: 24px !important;
    height: 24px !important;
    margin: 0 !important;
    font-size: 16px;
    min-height: auto !important;
    justify-content: center !important;
    line-height: 1 !important;
    display: flex !important;
    align-items: center !important;
}

.sidebar.collapsed .nav-link .sidebar-item-icon i {
    width: auto !important;
    text-align: center !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

/* 侧边栏文本样式 */
.sidebar-text {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
}

/* 侧边栏展开时显示导航区域的文本元素 */
.sidebar:not(.collapsed) .sidebar-nav .sidebar-item-text {
    display: block !important;
}

/* 展开时header区域文本恢复原有样式 */
.sidebar:not(.collapsed) .sidebar-header .sidebar-text,
.sidebar:not(.collapsed) .logo-text,
.sidebar:not(.collapsed) .status-text {
    opacity: 1 !important;
    visibility: visible !important;
    transform: translateX(0) !important;
    width: auto !important;
}

/* 展开时导航区域文本显示 */
.sidebar:not(.collapsed) .sidebar-nav .sidebar-text {
    display: inline !important;
}



/* 顶部侧边栏切换按钮 */
.sidebar-toggle {
    color: var(--primary-color);
    padding: 8px 12px;
    margin-right: 8px;
    border: none;
    background: none;
    transition: all 0.3s ease;
    border-radius: 6px;
}

.sidebar-toggle:hover {
    color: var(--primary-dark);
    background-color: rgba(37, 99, 235, 0.1);
}

.sidebar-toggle i {
    font-size: 18px;
    transition: transform 0.3s ease;
}



/* 主内容区域 */
.main-content {
    flex: 1;
    min-height: 100vh;
    background-color: var(--background-color);
    display: flex;
    flex-direction: column;
}

/* 顶部导航栏 */
.top-navbar {
    background: var(--card-background);
    border-bottom: 1px solid var(--border-color);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    padding: 16px 24px;
    flex-shrink: 0;
    position: relative;
    z-index: 1030;
}

/* 下拉菜单样式优化 */
.top-navbar .dropdown-menu {
    border: none;
    border-radius: 12px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    padding: 8px 0;
    margin-top: 8px;
    z-index: 1040;
}

.top-navbar .dropdown-item {
    padding: 12px 20px;
    transition: all 0.2s ease;
    border-radius: 8px;
    margin: 2px 8px;
}

.top-navbar .dropdown-item:hover {
    background: linear-gradient(135deg, var(--primary-color), #3b82f6);
    color: white;
    transform: translateX(4px);
}

.top-navbar .dropdown-item i {
    width: 16px;
    margin-right: 8px;
}

/* 内容容器 */
.content-container {
    flex: 1;
    padding: 24px;
    overflow-y: auto;
}

/* 卡片样式 */
.card {
    border: none;
    border-radius: 12px;
    box-shadow: var(--card-shadow);
    transition: all 0.3s ease;
    background: var(--card-background);
}

.card:hover {
    box-shadow: var(--card-shadow-lg);
    transform: translateY(-2px);
}

.card-header {
    background: var(--background-color);
    border-bottom: 1px solid var(--border-color);
    border-radius: 12px 12px 0 0 !important;
    padding: 20px;
    font-weight: 600;
}

/* 统计卡片 */
.stats-card {
    background: linear-gradient(135deg, var(--primary-color), #3b82f6);
    color: white;
    border-radius: 16px;
    padding: 24px;
    position: relative;
    overflow: hidden;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
}

.stats-card.success {
    background: linear-gradient(135deg, var(--success-color), #10b981);
}

.stats-card.warning {
    background: linear-gradient(135deg, var(--warning-color), #f59e0b);
}

.stats-card.info {
    background: linear-gradient(135deg, #0ea5e9, #06b6d4);
}

/* 按钮样式 */
.btn {
    border-radius: 8px;
    font-weight: 500;
    padding: 10px 20px;
    transition: all 0.3s ease;
    border: none;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), #3b82f6);
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(37, 99, 235, 0.4);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color), #10b981);
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning-color), #f59e0b);
}

.btn-danger {
    background: linear-gradient(135deg, var(--danger-color), #ef4444);
}

/* 表单样式 */
.form-control, .form-select {
    border-radius: 8px;
    border: 1px solid var(--border-color);
    padding: 12px 16px;
    transition: all 0.3s ease;
    background-color: var(--card-background);
    color: var(--dark-text);
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    background-color: var(--card-background);
    color: var(--dark-text);
}

.form-control::placeholder {
    color: var(--light-text);
}

/* 深色主题下的表单控件 */
.theme-dark .form-control,
.theme-dark .form-select {
    background-color: var(--card-background);
    border-color: var(--border-color);
    color: var(--dark-text);
}

.theme-dark .form-control:focus,
.theme-dark .form-select:focus {
    background-color: var(--card-background);
    border-color: var(--primary-color);
    color: var(--dark-text);
}

/* 模态框样式 */
.modal-content {
    border-radius: 16px;
    border: none;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    background-color: var(--card-background);
    color: var(--dark-text);
    transition: var(--theme-transition);
}

.modal-header {
    background: var(--background-color);
    border-bottom: 1px solid var(--border-color);
    border-radius: 16px 16px 0 0;
    color: var(--dark-text);
}

.modal-body {
    background-color: var(--card-background);
    color: var(--dark-text);
}

.modal-footer {
    background-color: var(--card-background);
    border-top: 1px solid var(--border-color);
    color: var(--dark-text);
}

/* 徽章样式 */
.badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-weight: 500;
    font-size: 11px;
}



/* 状态指示器基础样式 */
.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.status-indicator.online {
    background-color: var(--success-color);
    box-shadow: 0 0 8px rgba(5, 150, 105, 0.5);
}

.status-indicator.offline {
    background-color: var(--danger-color);
    box-shadow: 0 0 8px rgba(220, 38, 38, 0.5);
}

/* 动画效果 */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Agent卡片样式 */
.agent-card {
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
}

.agent-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.agent-card .card-header {
    background: var(--background-color);
    border-bottom: 1px solid var(--border-color);
    border-radius: 12px 12px 0 0;
}

.agent-card .card-title {
    color: #2c3e50;
    font-size: 1rem;
}

/* 根据状态设置卡片头部颜色 - 使用更简洁的方式 */
.agent-card.status-0 .card-header {
    background: var(--background-color); /* 已删除 */
    opacity: 0.7;
}

.agent-card.status-1 .card-header {
    background: var(--background-color); /* 草稿 */
}

.agent-card.status-2 .card-header {
    background: rgba(255, 199, 0, 0.1); /* 测试中 - 黄色 */
}

.agent-card.status-3 .card-header {
    background: rgba(0, 158, 247, 0.1); /* 已发布 - 蓝色 */
}

.agent-card.status-4 .card-header {
    background: rgba(241, 65, 108, 0.1); /* 已下线 - 红色 */
}

.agent-card .card-text {
    font-size: 0.875rem;
    line-height: 1.4;
}

.agent-card .badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
}

.agent-card .btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}

/* 状态颜色 */
.border-success {
    border-color: #28a745 !important;
}

.border-warning {
    border-color: #ffc107 !important;
}

.border-secondary {
    border-color: #6c757d !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .admin-container {
        flex-direction: column;
    }

    .sidebar {
        width: 100%;
        min-height: auto;
        position: fixed;
        top: 0;
        left: -100%;
        z-index: 1050;
        transition: left 0.3s ease;
    }

    .sidebar.show {
        left: 0;
    }

    .sidebar.collapsed {
        width: 100%;
    }

    .main-content {
        min-height: calc(100vh - 200px);
        width: 100%;
    }

    .agent-card {
        margin-bottom: 1rem;
    }

    .sidebar-toggle-btn {
        display: block;
    }
}

@media (min-width: 769px) {
    .sidebar-toggle-btn {
        display: block;
    }
}

/* 文件上传区域样式 */
.file-drop-zone {
    border: 2px dashed var(--border-color);
    border-radius: 16px;
    padding: 48px;
    text-align: center;
    background: var(--background-color);
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.file-drop-zone::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(37, 99, 235, 0.1), transparent);
    transition: left 0.5s ease;
}

.file-drop-zone:hover::before {
    left: 100%;
}

.file-drop-zone:hover {
    border-color: var(--primary-color);
    background: var(--card-background);
    transform: scale(1.02);
}

.file-drop-zone.dragover {
    border-color: var(--primary-color);
    background: var(--card-background);
    box-shadow: 0 0 20px rgba(37, 99, 235, 0.2);
}

/* 结果显示样式 */
.result-json {
    background: var(--card-background);
    color: var(--dark-text);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 1rem;
    font-family: 'Consolas', 'Monaco', monospace;
    font-size: 0.875rem;
    line-height: 1.5;
}

.real-time-preview {
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 1rem;
    background: var(--card-background);
}

/* 权限管理样式 */
.permission-tree {
    max-height: 600px;
    overflow-y: auto;
}

.permission-node {
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    margin-bottom: 8px;
    background-color: var(--background-color);
    transition: all 0.2s ease;
}

.permission-node:hover {
    background-color: var(--card-background);
    border-color: var(--border-color);
}

.permission-children {
    margin-left: 20px;
    margin-top: 8px;
    padding-left: 15px;
    border-left: 2px solid #dee2e6;
}

.permission-type-badge {
    font-size: 0.75rem;
}

.role-list .form-check {
    padding: 10px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background-color: var(--background-color);
    transition: all 0.2s ease;
    margin-bottom: 8px;
}

.role-list .form-check:hover {
    background-color: var(--card-background);
    border-color: var(--border-color);
}

.role-list .form-check-input:checked + .form-check-label {
    color: #0d6efd;
    font-weight: 500;
}

/* 权限树复选框样式 */
.permission-tree-container .form-check-input:indeterminate {
    background-color: #6c757d;
    border-color: #6c757d;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10h8'/%3e%3c/svg%3e");
}



/* 视图切换按钮样式 */
.btn-group .btn.active {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.btn-group .btn:not(.active) {
    background-color: white;
    border-color: #dee2e6;
    color: #6c757d;
}

.btn-group .btn:not(.active):hover {
    background-color: var(--background-color);
    border-color: var(--border-color);
    color: var(--dark-text);
}

/* 权限分配模态框样式 */
.permission-tree-container {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 15px;
    background-color: var(--background-color);
}

/* 审批页面样式 */
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: var(--border-color);
}

.timeline-item {
    position: relative;
    margin-bottom: 30px;
}

.timeline-marker {
    position: absolute;
    left: -23px;
    top: 5px;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: var(--medium-text);
    border: 3px solid var(--card-background);
    box-shadow: 0 0 0 2px var(--border-color);
}

.timeline-item-current .timeline-marker {
    background: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-color);
}

.timeline-content {
    background: var(--background-color);
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid var(--border-color);
}

.timeline-item-current .timeline-content {
    border-left-color: #0d6efd;
}

/* 审批状态徽章样式 */
.badge {
    font-size: 0.75em;
    padding: 0.5em 0.75em;
}

/* 审批卡片统计样式 */
.card .card-title {
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.card .card-text {
    font-size: 0.875rem;
    color: #6c757d;
    margin-bottom: 0;
}

/* 审批代码块样式 */
pre code {
    font-size: 0.875rem;
    color: #495057;
}



.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

/* 审批模态框样式 */
.modal-xl {
    max-width: 1200px;
}

.modal-header {
    background-color: var(--background-color);
    border-bottom: 1px solid var(--border-color);
}

.modal-title {
    font-weight: 600;
    color: #495057;
}

/* 响应式权限管理 */
@media (max-width: 768px) {
    .permission-children {
        margin-left: 10px;
        padding-left: 10px;
    }

    .permission-node {
        padding: 8px;
    }

    .role-list .form-check {
        padding: 8px;
    }

    .timeline {
        padding-left: 20px;
    }

    .timeline-marker {
        left: -18px;
        width: 12px;
        height: 12px;
    }
}

/* 菜单权限控制优化 */
.sidebar .nav-item {
    /* 确保菜单项在权限加载期间保持可见 */
    display: block !important;
    opacity: 1;
    transition: opacity 0.3s ease, display 0s;
}

/* 权限加载中的菜单项样式 */
.sidebar .nav-item.permission-loading {
    opacity: 0.7;
}

/* 无权限的菜单项隐藏样式 */
.sidebar .nav-item.permission-hidden {
    display: none !important;
}

/* 确保菜单在初始化时可见 */
.sidebar .nav-link {
    opacity: 1;
    transition: all 0.3s ease;
}

/* 防止菜单闪烁 */
.sidebar-nav {
    opacity: 1;
    transition: opacity 0.3s ease;
}

@media (min-width: 992px) {
    .modal-xl {
        max-width: 90%;
    }
}

@media (min-width: 1200px) {
    .modal-xl {
        max-width: 1200px;
    }
}

/* ==================== 深色主题适配 ==================== */

/* 卡片组件深色主题 */
.theme-dark .card {
    background-color: var(--card-background);
    border-color: var(--border-color);
    color: var(--dark-text);
}

.theme-dark .card-header {
    background-color: var(--background-color);
    border-bottom-color: var(--border-color);
    color: var(--dark-text);
}

.theme-dark .card-body {
    background-color: var(--card-background);
    color: var(--dark-text);
}

.theme-dark .card-footer {
    background-color: var(--background-color);
    border-top-color: var(--border-color);
    color: var(--dark-text);
}

/* 文本颜色适配 */
.theme-dark .text-muted {
    color: var(--light-text) !important;
}

.theme-dark .text-secondary {
    color: var(--medium-text) !important;
}

/* 边框颜色适配 */
.theme-dark .border {
    border-color: var(--border-color) !important;
}

.theme-dark .border-top {
    border-top-color: var(--border-color) !important;
}

.theme-dark .border-bottom {
    border-bottom-color: var(--border-color) !important;
}

.theme-dark .border-left {
    border-left-color: var(--border-color) !important;
}

.theme-dark .border-right {
    border-right-color: var(--border-color) !important;
}

/* 背景颜色适配 */
.theme-dark .bg-light {
    background-color: var(--background-color) !important;
    color: var(--dark-text) !important;
}

.theme-dark .bg-white {
    background-color: var(--card-background) !important;
    color: var(--dark-text) !important;
}

/* Alert组件深色主题 */
.theme-dark .alert {
    background-color: var(--card-background);
    border-color: var(--border-color);
    color: var(--dark-text);
}

.theme-dark .alert-info {
    background-color: rgba(114, 57, 234, 0.1);
    border-color: rgba(114, 57, 234, 0.2);
    color: var(--dark-text);
}

.theme-dark .alert-success {
    background-color: rgba(80, 205, 137, 0.1);
    border-color: rgba(80, 205, 137, 0.2);
    color: var(--dark-text);
}

.theme-dark .alert-warning {
    background-color: rgba(255, 199, 0, 0.1);
    border-color: rgba(255, 199, 0, 0.2);
    color: var(--dark-text);
}

.theme-dark .alert-danger {
    background-color: rgba(241, 65, 108, 0.1);
    border-color: rgba(241, 65, 108, 0.2);
    color: var(--dark-text);
}

/* Pre和Code元素深色主题 */
.theme-dark pre {
    background-color: var(--background-color);
    border: 1px solid var(--border-color);
    color: var(--dark-text);
}

.theme-dark code {
    background-color: var(--background-color);
    color: var(--dark-text);
}

/* Textarea深色主题 */
.theme-dark textarea {
    background-color: var(--card-background);
    border-color: var(--border-color);
    color: var(--dark-text);
}

.theme-dark textarea:focus {
    background-color: var(--card-background);
    border-color: var(--primary-color);
    color: var(--dark-text);
}