/* Keen主题额外样式 */

/* ==================== Keen风格动画效果 ==================== */

/* 淡入动画 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeInUp 0.6s ease-out;
}

/* 悬停效果 */
.hover-scale {
    transition: all 0.3s ease;
}

.hover-scale:hover {
    transform: translateY(-2px);
}

/* ==================== Keen风格符号组件 ==================== */

.symbol {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    position: relative;
    flex-shrink: 0;
}

.symbol-50px {
    width: 50px;
    height: 50px;
}

.symbol-60px {
    width: 60px;
    height: 60px;
}

.symbol-circle {
    border-radius: 50%;
}

.symbol-label {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--kt-border-radius);
    font-weight: var(--kt-font-weight-bold);
}

/* 浅色背景变体 */
.bg-light-primary {
    background-color: var(--kt-primary-light) !important;
}

.bg-light-success {
    background-color: var(--kt-success-light) !important;
}

.bg-light-warning {
    background-color: var(--kt-warning-light) !important;
}

.bg-light-danger {
    background-color: var(--kt-danger-light) !important;
}

.bg-light-info {
    background-color: var(--kt-info-light) !important;
}

.bg-light-secondary {
    background-color: var(--kt-gray-100) !important;
}

/* ==================== Keen风格按钮变体 ==================== */

.btn-light-primary {
    background-color: var(--kt-primary-light) !important;
    border-color: var(--kt-primary-light) !important;
    color: var(--kt-primary) !important;
}

.btn-light-primary:hover {
    background-color: var(--kt-primary) !important;
    border-color: var(--kt-primary) !important;
    color: #FFFFFF !important;
}

.btn-light-success {
    background-color: var(--kt-success-light) !important;
    border-color: var(--kt-success-light) !important;
    color: var(--kt-success) !important;
}

.btn-light-success:hover {
    background-color: var(--kt-success) !important;
    border-color: var(--kt-success) !important;
    color: #FFFFFF !important;
}

.btn-light-warning {
    background-color: var(--kt-warning-light) !important;
    border-color: var(--kt-warning-light) !important;
    color: var(--kt-warning) !important;
}

.btn-light-warning:hover {
    background-color: var(--kt-warning) !important;
    border-color: var(--kt-warning) !important;
    color: var(--kt-text-dark) !important;
}

.btn-light-info {
    background-color: var(--kt-info-light) !important;
    border-color: var(--kt-info-light) !important;
    color: var(--kt-info) !important;
}

.btn-light-info:hover {
    background-color: var(--kt-info) !important;
    border-color: var(--kt-info) !important;
    color: #FFFFFF !important;
}

.btn-light-secondary {
    background-color: var(--kt-gray-100) !important;
    border-color: var(--kt-gray-100) !important;
    color: var(--kt-text-muted) !important;
}

.btn-light-secondary:hover {
    background-color: var(--kt-gray-200) !important;
    border-color: var(--kt-gray-200) !important;
    color: var(--kt-text-dark) !important;
}

/* ==================== Keen风格字体大小 ==================== */

.fs-1 {
    font-size: var(--kt-font-size-4xl) !important;
}

.fs-2 {
    font-size: var(--kt-font-size-3xl) !important;
}

.fs-2x {
    font-size: 1.75rem !important;
}

.fs-3 {
    font-size: var(--kt-font-size-2xl) !important;
}

.fs-4 {
    font-size: var(--kt-font-size-xl) !important;
}

.fs-5 {
    font-size: var(--kt-font-size-lg) !important;
}

.fs-6 {
    font-size: var(--kt-font-size-sm) !important;
}

.fs-7 {
    font-size: var(--kt-font-size-xs) !important;
}

/* ==================== Keen风格页面标题 ==================== */

.page-title-wrapper {
    flex: 1;
}

.page-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--kt-primary-light), rgba(0, 158, 247, 0.1));
    border-radius: var(--kt-border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 0.25rem 0.75rem rgba(0, 158, 247, 0.15);
}

.page-title {
    color: var(--kt-text-dark) !important;
    font-weight: 700 !important;
    letter-spacing: -0.02em;
}

.page-subtitle {
    color: var(--kt-text-muted) !important;
    font-weight: 500 !important;
}

/* ==================== Keen风格面包屑 ==================== */

.breadcrumb {
    background: none;
    padding: 0;
    margin: 0;
    font-size: var(--kt-font-size-sm);
}

.breadcrumb-item {
    color: var(--kt-text-muted);
    font-weight: 500;
}

.breadcrumb-item.active {
    color: var(--kt-text-dark);
    font-weight: 600;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: "/";
    color: var(--kt-text-gray-400);
    margin: 0 0.5rem;
}

.breadcrumb-item a {
    color: var(--kt-text-muted);
    text-decoration: none;
    transition: var(--kt-transition);
}

.breadcrumb-item a:hover {
    color: var(--kt-primary);
}

/* ==================== Keen风格卡片标题 ==================== */

.card-title {
    font-size: var(--kt-font-size-lg) !important;
    font-weight: 700 !important;
    color: var(--kt-text-dark) !important;
    margin: 0 !important;
}

.card-toolbar {
    margin-left: auto;
}

/* ==================== 系统状态样式 ==================== */

.system-status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--kt-space-3) 0;
    border-bottom: 1px solid var(--kt-border-color);
}

.system-status-item:last-child {
    border-bottom: none;
}

.system-status-label {
    font-weight: var(--kt-font-weight-semibold);
    color: var(--kt-text-dark);
    font-size: var(--kt-font-size-sm);
}

.system-status-badge {
    font-size: var(--kt-font-size-xs);
    font-weight: var(--kt-font-weight-bold);
    padding: 0.25rem 0.5rem;
    border-radius: var(--kt-border-radius-sm);
}

/* ==================== 最近Agent样式 ==================== */

.recent-agent-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--kt-space-3) 0;
    border-bottom: 1px solid var(--kt-border-color);
    transition: var(--kt-transition);
}

.recent-agent-item:last-child {
    border-bottom: none;
}

.recent-agent-item:hover {
    background-color: var(--kt-gray-100);
    margin: 0 calc(-1 * var(--kt-space-4));
    padding: var(--kt-space-3) var(--kt-space-4);
    border-radius: var(--kt-border-radius);
}

.recent-agent-info {
    flex: 1;
}

.recent-agent-name {
    font-weight: var(--kt-font-weight-semibold);
    color: var(--kt-text-dark);
    font-size: var(--kt-font-size-sm);
    margin-bottom: 0.25rem;
}

.recent-agent-code {
    font-size: var(--kt-font-size-xs);
    color: var(--kt-text-muted);
    font-family: 'Consolas', 'Monaco', monospace;
}

/* ==================== 页面加载动画 ==================== */

@keyframes slideInFromLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInFromRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInFromBottom {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 页面元素动画类 */
.animate-slide-in-left {
    animation: slideInFromLeft 0.6s ease-out;
}

.animate-slide-in-right {
    animation: slideInFromRight 0.6s ease-out;
}

.animate-slide-in-bottom {
    animation: slideInFromBottom 0.6s ease-out;
}

/* 延迟动画 */
.animate-delay-1 {
    animation-delay: 0.1s;
    opacity: 0;
    animation-fill-mode: forwards;
}

.animate-delay-2 {
    animation-delay: 0.2s;
    opacity: 0;
    animation-fill-mode: forwards;
}

.animate-delay-3 {
    animation-delay: 0.3s;
    opacity: 0;
    animation-fill-mode: forwards;
}

.animate-delay-4 {
    animation-delay: 0.4s;
    opacity: 0;
    animation-fill-mode: forwards;
}

/* ==================== 脉冲动画 ==================== */

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(0, 158, 247, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(0, 158, 247, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(0, 158, 247, 0);
    }
}

.pulse-primary {
    animation: pulse 2s infinite;
}

/* ==================== 数字计数动画 ==================== */

.counter-number {
    font-variant-numeric: tabular-nums;
    transition: all 0.3s ease;
}

/* ==================== 加载状态优化 ==================== */

.loading-shimmer {
    background: linear-gradient(90deg,
        var(--kt-gray-200) 25%,
        var(--kt-gray-100) 50%,
        var(--kt-gray-200) 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

/* ==================== 工具提示增强 ==================== */

.tooltip-enhanced {
    position: relative;
    cursor: help;
}

.tooltip-enhanced::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--kt-dark);
    color: white;
    padding: 0.5rem 0.75rem;
    border-radius: var(--kt-border-radius);
    font-size: var(--kt-font-size-xs);
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1000;
}

.tooltip-enhanced:hover::after {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) translateY(-5px);
}

/* ==================== Keen风格表格 ==================== */

.table {
    border-collapse: separate;
    border-spacing: 0;
}

.table thead th {
    background-color: var(--kt-gray-100);
    border-bottom: 1px solid var(--kt-border-color);
    font-weight: var(--kt-font-weight-semibold);
    font-size: var(--kt-font-size-sm);
    color: var(--kt-text-muted);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    padding: var(--kt-space-4) var(--kt-space-6);
}

.table tbody td {
    padding: var(--kt-space-4) var(--kt-space-6);
    border-bottom: 1px solid var(--kt-border-color);
    vertical-align: middle;
}

.table tbody tr:hover {
    background-color: var(--kt-gray-100);
}

/* ==================== Keen风格表单 ==================== */

.form-control {
    border: 1px solid var(--kt-border-color);
    border-radius: var(--kt-border-radius);
    padding: var(--kt-space-3) var(--kt-space-4);
    font-size: var(--kt-font-size-sm);
    transition: var(--kt-transition);
    background-color: var(--kt-card-bg);
    color: var(--kt-text-dark);
}

.form-control:focus {
    border-color: var(--kt-primary);
    box-shadow: 0 0 0 0.2rem rgba(0, 158, 247, 0.25);
    background-color: var(--kt-card-bg);
    color: var(--kt-text-dark);
}

.form-control-solid {
    background-color: var(--kt-gray-100);
    border-color: var(--kt-gray-100);
    color: var(--kt-text-dark);
}

.form-control-solid:focus {
    background-color: var(--kt-gray-100);
    border-color: var(--kt-primary);
    box-shadow: 0 0 0 0.2rem rgba(0, 158, 247, 0.25);
}

.form-select-solid {
    background-color: var(--kt-gray-100);
    border-color: var(--kt-gray-100);
    color: var(--kt-text-dark);
}

.form-select-solid:focus {
    background-color: var(--kt-gray-100);
    border-color: var(--kt-primary);
    box-shadow: 0 0 0 0.2rem rgba(0, 158, 247, 0.25);
}

.form-label {
    font-weight: var(--kt-font-weight-semibold);
    color: var(--kt-text-dark);
    margin-bottom: var(--kt-space-2);
    font-size: var(--kt-font-size-sm);
}

.form-select {
    border: 1px solid var(--kt-border-color);
    border-radius: var(--kt-border-radius);
    padding: var(--kt-space-3) var(--kt-space-4);
    font-size: var(--kt-font-size-sm);
    background-color: var(--kt-card-bg);
    color: var(--kt-text-dark);
}

/* ==================== Keen风格模态框 ==================== */

.modal-content {
    border: none;
    border-radius: var(--kt-border-radius-lg);
    box-shadow: var(--kt-box-shadow-lg);
}

.modal-header {
    border-bottom: 1px solid var(--kt-border-color);
    padding: var(--kt-space-6) var(--kt-space-8);
    background-color: var(--kt-card-bg);
}

.modal-title {
    font-weight: var(--kt-font-weight-bold);
    color: var(--kt-text-dark);
    font-size: var(--kt-font-size-lg);
}

.modal-body {
    padding: var(--kt-space-8);
    background-color: var(--kt-card-bg);
}

.modal-footer {
    border-top: 1px solid var(--kt-border-color);
    padding: var(--kt-space-6) var(--kt-space-8);
    background-color: var(--kt-gray-100);
}

/* ==================== Keen风格警告框 ==================== */

.alert {
    border: none;
    border-radius: var(--kt-border-radius);
    padding: var(--kt-space-4) var(--kt-space-6);
    font-size: var(--kt-font-size-sm);
}

.alert-primary {
    background-color: var(--kt-primary-light);
    color: var(--kt-primary);
}

.alert-success {
    background-color: var(--kt-success-light);
    color: var(--kt-success);
}

.alert-warning {
    background-color: var(--kt-warning-light);
    color: var(--kt-warning);
}

.alert-danger {
    background-color: var(--kt-danger-light);
    color: var(--kt-danger);
}

.alert-info {
    background-color: var(--kt-info-light);
    color: var(--kt-info);
}

/* ==================== Keen风格进度条 ==================== */

.progress {
    height: 8px;
    background-color: var(--kt-gray-200);
    border-radius: var(--kt-border-radius);
    overflow: hidden;
}

.progress-bar {
    background-color: var(--kt-primary);
    border-radius: var(--kt-border-radius);
    transition: var(--kt-transition);
}

.progress-bar.bg-success {
    background-color: var(--kt-success);
}

.progress-bar.bg-warning {
    background-color: var(--kt-warning);
}

.progress-bar.bg-danger {
    background-color: var(--kt-danger);
}

.progress-bar.bg-info {
    background-color: var(--kt-info);
}

/* ==================== Keen风格模态框 ==================== */

.modal-content {
    border: none;
    border-radius: var(--kt-border-radius-lg);
    box-shadow: var(--kt-box-shadow-lg);
}

.modal-header {
    border-bottom: 1px solid var(--kt-border-color);
    padding: var(--kt-space-6) var(--kt-space-8);
}

.modal-title {
    font-size: var(--kt-font-size-lg);
    font-weight: var(--kt-font-weight-bold);
    color: var(--kt-text-dark);
}

.modal-body {
    padding: var(--kt-space-8);
}

.modal-footer {
    border-top: 1px solid var(--kt-border-color);
    padding: var(--kt-space-6) var(--kt-space-8);
    gap: var(--kt-space-3);
}

/* ==================== Keen风格分页 ==================== */

.pagination {
    gap: var(--kt-space-1);
}

.page-link {
    border: 1px solid var(--kt-border-color);
    border-radius: var(--kt-border-radius);
    padding: var(--kt-space-2) var(--kt-space-3);
    color: var(--kt-text-muted);
    background-color: var(--kt-card-bg);
    font-size: var(--kt-font-size-sm);
    transition: var(--kt-transition);
}

.page-link:hover {
    background-color: var(--kt-gray-100);
    color: var(--kt-text-dark);
    border-color: var(--kt-border-color);
}

.page-item.active .page-link {
    background-color: var(--kt-primary);
    border-color: var(--kt-primary);
    color: #FFFFFF;
}

/* ==================== Keen风格工具提示 ==================== */

.tooltip {
    font-size: var(--kt-font-size-xs);
}

.tooltip-inner {
    background-color: var(--kt-dark);
    border-radius: var(--kt-border-radius);
    padding: var(--kt-space-2) var(--kt-space-3);
}

/* ==================== Keen风格面包屑 ==================== */

.breadcrumb {
    background-color: transparent;
    padding: 0;
    margin: 0;
    font-size: var(--kt-font-size-sm);
}

.breadcrumb-item {
    color: var(--kt-text-muted);
}

.breadcrumb-item.active {
    color: var(--kt-text-dark);
    font-weight: var(--kt-font-weight-semibold);
}

.breadcrumb-item + .breadcrumb-item::before {
    content: "/";
    color: var(--kt-text-gray-400);
}

/* ==================== Keen风格列表组 ==================== */

.list-group-item {
    border: 1px solid var(--kt-border-color);
    background-color: var(--kt-card-bg);
    color: var(--kt-text-dark);
    padding: var(--kt-space-4) var(--kt-space-6);
}

.list-group-item:hover {
    background-color: var(--kt-gray-100);
}

.list-group-item.active {
    background-color: var(--kt-primary);
    border-color: var(--kt-primary);
    color: #FFFFFF;
}

/* ==================== Keen风格加载状态 ==================== */

.spinner-border {
    width: 2rem;
    height: 2rem;
    border-width: 0.25em;
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
    border-width: 0.125em;
}

.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    border-radius: var(--kt-border-radius);
}

.theme-dark .loading-overlay {
    background-color: rgba(30, 30, 45, 0.8);
}
