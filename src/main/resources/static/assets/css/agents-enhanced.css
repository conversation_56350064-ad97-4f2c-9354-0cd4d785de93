/* Agent管理页面样式 - 专业UI设计师优化版本 */

/* ==================== 全局变量 ==================== */
:root {
    /* 主色调 - 企业友好的蓝色主题 */
    --primary-color: #2563eb;        /* 蓝色主色调 */
    --primary-light: #93c5fd;        /* 浅蓝色 */
    --primary-dark: #1d4ed8;         /* 深蓝色 */
    --secondary-color: #3b82f6;      /* 辅助蓝色 */
    --accent-color: #06b6d4;         /* 青色强调色 */

    /* 状态色彩 */
    --success-color: #10b981;        /* 绿色 */
    --warning-color: #f59e0b;        /* 橙色 */
    --danger-color: #ef4444;         /* 红色 */
    --info-color: #0ea5e9;           /* 信息蓝色 */

    /* 中性色调 */
    --text-primary: #1f2937;         /* 主要文字 */
    --text-secondary: #6b7280;       /* 次要文字 */
    --text-muted: #9ca3af;           /* 辅助文字 */

    /* 背景色系 */
    --bg-primary: #ffffff;           /* 主背景 */
    --bg-secondary: #f8fafc;         /* 次要背景 */
    --bg-card: #ffffff;              /* 卡片背景 */
    --bg-blue-light: #eff6ff;        /* 浅蓝背景 */
    --bg-gradient: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);

    /* 边框色 */
    --border-light: #e5e7eb;         /* 浅边框 */
    --border-medium: #d1d5db;        /* 中等边框 */
    --border-blue: #bfdbfe;          /* 蓝色边框 */

    /* 阴影系统 */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-blue: 0 4px 14px 0 rgba(37, 99, 235, 0.15);

    /* 字体系统 */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;

    /* 过渡效果 */
    --transition-fast: all 0.15s ease-in-out;
    --transition-normal: all 0.2s ease-in-out;
    --transition-slow: all 0.3s ease-in-out;
}

/* ==================== 基础样式 ==================== */
* {
    font-family: var(--font-family);
}

body {
    background-color: var(--bg-secondary);
    font-size: var(--font-size-base);
    color: var(--text-primary);
}

/* ==================== 页面布局 ==================== */
.agents-container {
    max-width: 100%;
    margin: 0;
    padding: 0.75rem;
}

/* ==================== 工具栏样式 ==================== */
.toolbar-card {
    background: white;
    border: 1px solid var(--border-light);
    border-radius: 0.375rem;
    box-shadow: var(--shadow-sm);
    margin-bottom: 0.5rem;
}

.toolbar-card .card-body {
    padding: 0.75rem;
}

.search-input-group .form-control {
    border-right: 0;
    font-size: 0.875rem;
}

.search-input-group .input-group-text {
    background: white;
    border-left: 0;
    border-right: 0;
}

.search-input-group .btn {
    font-size: 0.875rem;
    padding: 0.375rem 0.75rem;
}

/* ==================== 统计卡片 ==================== */
.stats-card {
    background: white;
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    padding: 1.5rem;
    box-shadow: var(--card-shadow);
    transition: var(--transition);
    text-align: center;
}

.stats-card:hover {
    box-shadow: var(--card-shadow-lg);
}

.stats-card.success {
    border-left: 4px solid var(--success-color);
}

.stats-card.warning {
    border-left: 4px solid var(--warning-color);
}

.stats-card.danger {
    border-left: 4px solid var(--danger-color);
}

.stats-card h3 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: var(--dark-color);
}

.stats-card p {
    color: var(--secondary-color);
    font-size: 0.875rem;
    margin-bottom: 0;
}

/* ==================== Agent卡片 - 企业蓝色主题设计 ==================== */
.agent-card {
    background: var(--bg-card);
    border: 1px solid var(--border-blue);
    border-radius: 0.5rem;
    box-shadow: var(--shadow-sm);
    transition: var(--transition-normal);
    height: 100%;
    position: relative;
    overflow: hidden;
    margin-bottom: 0.75rem;
}

.agent-card:hover {
    box-shadow: var(--shadow-blue);
    transform: translateY(-1px);
    border-color: var(--primary-light);
}

.agent-card-header {
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    border-bottom: 1px solid var(--border-blue);
    padding: 0.75rem;
    position: relative;
}

.agent-card-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    line-height: 1.4;
}

.agent-card-code {
    font-size: var(--font-size-xs);
    color: var(--text-muted);
    font-family: 'Monaco', 'Menlo', 'Consolas', monospace;
    background: rgba(37, 99, 235, 0.1);
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    display: inline-block;
}

.agent-status-badge {
    position: absolute;
    top: 0.75rem;
    right: 0.75rem;
    font-size: var(--font-size-xs);
    font-weight: 500;
    padding: 0.25rem 0.5rem;
    border-radius: 0.75rem;
    border: 1px solid;
}

.agent-status-badge.status-published {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
    border-color: rgba(16, 185, 129, 0.2);
}

.agent-status-badge.status-testing {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
    border-color: rgba(245, 158, 11, 0.2);
}

.agent-status-badge.status-draft {
    background: rgba(107, 114, 128, 0.1);
    color: var(--text-secondary);
    border-color: rgba(107, 114, 128, 0.2);
}

.agent-card-body {
    padding: 0.75rem;
}

.agent-description {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    line-height: 1.4;
    margin-bottom: 0.5rem;
    min-height: 2rem;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.agent-meta {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.375rem;
    margin-bottom: 0.5rem;
}

.agent-meta-item {
    text-align: center;
    padding: 0.375rem;
    background: rgba(37, 99, 235, 0.05);
    border: 1px solid rgba(37, 99, 235, 0.1);
    border-radius: 0.25rem;
    transition: var(--transition-fast);
}

.agent-meta-item:hover {
    background: rgba(37, 99, 235, 0.08);
    border-color: rgba(37, 99, 235, 0.15);
}

.agent-meta-label {
    font-size: var(--font-size-xs);
    color: var(--text-muted);
    margin-bottom: 0.25rem;
    font-weight: 500;
}

.agent-meta-value {
    font-weight: 600;
    color: var(--text-primary);
    font-size: var(--font-size-sm);
}

.agent-meta-value.text-primary {
    color: var(--primary-color);
}

.agent-meta-value.text-success {
    color: var(--success-color);
}

.agent-card-footer {
    padding: 0.75rem;
    background: rgba(248, 250, 252, 0.5);
    border-top: 1px solid var(--border-light);
}

/* ==================== 表格样式 - 企业蓝色主题设计 ==================== */
.agents-table {
    background: var(--bg-card);
    border: 1px solid var(--border-light);
    border-radius: 0.5rem;
    box-shadow: var(--shadow-sm);
    overflow: hidden;
}

.table {
    margin-bottom: 0;
    font-size: var(--font-size-sm);
}

.table th {
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    border-bottom: 1px solid var(--border-blue);
    font-weight: 600;
    color: var(--text-primary);
    font-size: var(--font-size-sm);
    padding: 0.75rem 0.5rem;
    white-space: nowrap;
}

.table td {
    padding: 0.75rem 0.5rem;
    vertical-align: middle;
    border-bottom: 1px solid var(--border-light);
}

.table tbody tr {
    transition: var(--transition-fast);
}

.table tbody tr:hover {
    background: rgba(37, 99, 235, 0.03);
}

.agent-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.agent-avatar {
    width: 36px;
    height: 36px;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    border-radius: 0.375rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: var(--font-size-sm);
    box-shadow: var(--shadow-sm);
}

.agent-name {
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 0.25rem;
    text-decoration: none;
    transition: var(--transition-fast);
}

.agent-name:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

.agent-name-link {
    font-weight: 600;
    color: var(--text-primary);
    text-decoration: none;
    transition: var(--transition-fast);
}

.agent-name-link:hover {
    color: var(--primary-color);
    text-decoration: underline;
}

.agent-code {
    font-size: var(--font-size-xs);
    color: var(--text-muted);
    font-family: 'Monaco', 'Menlo', 'Consolas', monospace;
    background: rgba(37, 99, 235, 0.1);
    padding: 0.125rem 0.375rem;
    border-radius: 0.25rem;
    display: inline-block;
}

/* ==================== 按钮样式 - 专业UI设计 ==================== */
.btn {
    border-radius: 0.5rem;
    font-weight: 500;
    transition: var(--transition-normal);
    font-size: var(--font-size-sm);
    border-width: 1px;
    position: relative;
    overflow: hidden;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn:active {
    transform: translateY(0);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    border: none;
    color: white;
    box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-dark) 0%, #7c3aed 100%);
    box-shadow: var(--shadow-md);
}

/* 浅色主按钮 */
.btn-light-primary {
    background: rgba(37, 99, 235, 0.1);
    border: 1px solid rgba(37, 99, 235, 0.2);
    color: var(--primary-color);
    font-weight: 500;
}

.btn-light-primary:hover {
    background: rgba(37, 99, 235, 0.15);
    border-color: rgba(37, 99, 235, 0.3);
    color: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
    background: transparent;
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.btn-success {
    background: var(--success-color);
    border-color: var(--success-color);
    color: white;
}

.btn-success:hover {
    background: #059669;
    border-color: #059669;
}

.btn-warning {
    background: var(--warning-color);
    border-color: var(--warning-color);
    color: white;
}

.btn-warning:hover {
    background: #d97706;
    border-color: #d97706;
}

.btn-outline-secondary {
    color: var(--text-secondary);
    border-color: var(--border-medium);
    background: transparent;
}

.btn-outline-secondary:hover {
    background: var(--text-secondary);
    border-color: var(--text-secondary);
    color: white;
}

.btn-light {
    background: rgba(255, 255, 255, 0.9);
    border-color: var(--border-light);
    color: var(--text-secondary);
}

.btn-light:hover {
    background: white;
    border-color: var(--border-medium);
    color: var(--text-primary);
}

/* 按钮组 */
.btn-group .btn {
    border-radius: 0;
}

.btn-group .btn:first-child {
    border-radius: 0.5rem 0 0 0.5rem;
}

.btn-group .btn:last-child {
    border-radius: 0 0.5rem 0.5rem 0;
}

.btn-group .btn.active {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
    box-shadow: var(--shadow-sm);
}

/* 危险按钮样式 */
.btn-outline-danger {
    color: var(--danger-color);
    border-color: var(--danger-color);
    background: transparent;
}

.btn-outline-danger:hover {
    background-color: var(--danger-color);
    border-color: var(--danger-color);
    color: white;
}

/* ==================== 状态徽章 ==================== */
.badge {
    font-weight: 500;
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
    border-radius: 0.375rem;
}

.bg-success {
    background-color: var(--success-color) !important;
}

.bg-warning {
    background-color: var(--warning-color) !important;
}

.bg-danger {
    background-color: var(--danger-color) !important;
}

.bg-secondary {
    background-color: var(--secondary-color) !important;
}

/* ==================== 下拉菜单 ==================== */
.dropdown-menu {
    border: 1px solid var(--border-color);
    box-shadow: var(--card-shadow-lg);
    border-radius: 0.5rem;
    padding: 0.5rem 0;
}

.dropdown-item {
    padding: 0.5rem 1rem;
    transition: var(--transition);
    font-size: 0.875rem;
}

.dropdown-item:hover {
    background-color: rgba(37, 99, 235, 0.1);
    color: var(--primary-color);
}

.dropdown-divider {
    margin: 0.5rem 0;
    border-color: var(--border-color);
}

/* ==================== 表单控件 ==================== */
.form-control, .form-select {
    border: 1px solid var(--border-color);
    border-radius: 0.375rem;
    transition: var(--transition);
    font-size: 0.875rem;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
}

.input-group-text {
    background-color: white;
    border: 1px solid var(--border-color);
    color: var(--secondary-color);
}

/* ==================== 分页 ==================== */
.pagination {
    margin-bottom: 0;
}

.page-link {
    color: var(--primary-color);
    border-color: var(--border-color);
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
}

.page-link:hover {
    background-color: rgba(37, 99, 235, 0.1);
    border-color: var(--primary-color);
}

.page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* ==================== 响应式设计 ==================== */
@media (max-width: 768px) {
    .agents-container {
        padding: 0.5rem;
    }

    .page-title {
        font-size: 1.5rem;
    }

    .stats-card {
        margin-bottom: 0.75rem;
    }

    .agent-card {
        margin-bottom: 0.75rem;
    }

    .toolbar-card .row {
        flex-direction: column;
    }

    .toolbar-card .col-lg-4,
    .toolbar-card .col-lg-5,
    .toolbar-card .col-lg-3 {
        margin-bottom: 0.75rem;
    }

    .agent-meta {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 576px) {
    .page-header .d-flex {
        flex-direction: column;
        align-items: flex-start !important;
    }

    .page-header .d-flex > div:last-child {
        margin-top: 0.75rem;
        width: 100%;
    }

    .btn {
        font-size: 0.875rem;
        padding: 0.375rem 0.75rem;
    }

    .stats-card h3 {
        font-size: 1.5rem;
    }

    .agent-card-header {
        padding: 0.5rem;
    }

    .agent-card-body {
        padding: 0.5rem;
    }

    .agent-card-footer {
        padding: 0.5rem;
    }
}

/* ==================== 动画效果 ==================== */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.3s ease-out;
}

/* ==================== 工具类 ==================== */
.text-primary {
    color: var(--primary-color) !important;
}

.text-success {
    color: var(--success-color) !important;
}

.text-warning {
    color: var(--warning-color) !important;
}

.text-danger {
    color: var(--danger-color) !important;
}

.border-primary {
    border-color: var(--primary-color) !important;
}

.bg-light {
    background-color: var(--light-color) !important;
}

/* ==================== 状态徽章 ==================== */
.badge {
    font-weight: 500;
    border-radius: 0.375rem;
    padding: 0.375rem 0.75rem;
}

.bg-gradient-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--info-color) 100%);
}

/* ==================== 输入框组件 ==================== */
.form-control {
    border: 1px solid var(--border-color);
    border-radius: 0.375rem;
    transition: var(--theme-transition);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 158, 247, 0.25);
}

.input-group-text {
    border: 1px solid var(--border-color);
    background-color: var(--background-color);
}

/* ==================== 下拉菜单 ==================== */
.dropdown-menu {
    border: none;
    box-shadow: var(--normal-shadow);
    border-radius: 0.5rem;
    padding: 0.5rem 0;
}

.dropdown-item {
    padding: 0.5rem 1rem;
    transition: var(--theme-transition);
}

.dropdown-item:hover {
    background-color: rgba(0, 158, 247, 0.1);
    color: var(--primary-color);
}

/* ==================== 模态框组件 ==================== */
.modal-content {
    border: none;
    border-radius: 0.75rem;
    box-shadow: var(--deep-shadow);
}

.modal-header {
    border-bottom: 1px solid var(--border-color);
    border-radius: 0.75rem 0.75rem 0 0;
}

.modal-footer {
    border-top: 1px solid var(--border-color);
    border-radius: 0 0 0.75rem 0.75rem;
}

/* ==================== 进度条组件 ==================== */
.progress {
    background-color: var(--border-color);
    border-radius: 0.25rem;
}

.progress-bar {
    border-radius: 0.25rem;
    transition: var(--theme-transition);
}

/* ==================== 工具提示 ==================== */
.tooltip {
    font-size: 0.875rem;
}

.tooltip-inner {
    background-color: var(--dark-text);
    border-radius: 0.375rem;
    padding: 0.5rem 0.75rem;
}

/* ==================== 加载状态 ==================== */
.spinner-border {
    border-width: 0.2em;
}

/* ==================== 空状态 ==================== */
.empty-state-icon {
    opacity: 0.5;
    font-size: 4rem;
    color: var(--light-text);
}

/* ==================== 响应式设计 ==================== */
@media (max-width: 768px) {
    .agent-card {
        margin-bottom: 1rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .btn-group .btn {
        padding: 0.375rem 0.5rem;
    }
    
    .modal-dialog {
        margin: 0.5rem;
    }
}

@media (max-width: 576px) {
    .breadcrumb {
        font-size: 0.875rem;
    }
    
    .card-header h1 {
        font-size: 1.5rem;
    }
    
    .btn {
        font-size: 0.875rem;
        padding: 0.375rem 0.75rem;
    }
}

/* ==================== 动画效果 ==================== */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

/* ==================== 主题适配 ==================== */
.theme-dark {
    --dark-text: #FFFFFF;
    --medium-text: #A1A5B7;
    --light-text: #5E6278;
    --border-color: #2B2B40;
    --background-color: #1B1B29;
    --card-background: #1E1E2D;
    
    --light-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.3);
    --normal-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.4);
    --deep-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.5);
    --hover-shadow: 0 0.5rem 2rem rgba(0, 0, 0, 0.3);
}

/* ==================== 自定义滚动条 ==================== */
.custom-scrollbar::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    background: var(--background-color);
    border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    background: var(--light-text);
    border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: var(--medium-text);
}

/* ==================== 紧凑布局优化 ==================== */
.card {
    margin-bottom: 0.75rem;
}

.card-body {
    padding: 0.75rem;
}

.card-header {
    padding: 0.75rem;
}

.card-footer {
    padding: 0.5rem 0.75rem;
}

/* 表单控件紧凑化 */
.form-select, .form-control {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
}

/* 按钮紧凑化 */
.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.8125rem;
}

/* 间距优化 */
.mb-3 {
    margin-bottom: 0.75rem !important;
}

.mb-2 {
    margin-bottom: 0.5rem !important;
}

.py-3 {
    padding-top: 0.75rem !important;
    padding-bottom: 0.75rem !important;
}

.py-2 {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important;
}

/* 网格间距优化 */
.g-3 {
    --bs-gutter-x: 0.75rem;
    --bs-gutter-y: 0.75rem;
}

.g-2 {
    --bs-gutter-x: 0.5rem;
    --bs-gutter-y: 0.5rem;
}

/* 下拉菜单优化 */
.dropdown-item {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
}

/* 徽章优化 */
.badge {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}
