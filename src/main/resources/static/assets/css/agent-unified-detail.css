/**
 * Agent统一详情页面样式
 * 支持查看、审批、补充资料三种模式
 */

/* CSS变量定义 */
:root {
    /* 主色调 */
    --primary-color: #009EF7;
    --success-color: #50CD89;
    --warning-color: #FFC700;
    --danger-color: #F1416C;
    --info-color: #7239EA;

    /* 中性色调 */
    --dark-text: #181C32;
    --medium-text: #5E6278;
    --light-text: #A1A5B7;
    --border-color: #E4E6EA;
    --background-color: #F9F9F9;
    --card-background: #FFFFFF;

    /* 间距系统 */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 3rem;

    /* 圆角系统 */
    --small-radius: 0.375rem;
    --medium-radius: 0.5rem;
    --large-radius: 0.75rem;

    /* 阴影系统 */
    --light-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --normal-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --deep-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
    --hover-shadow: 0 0.5rem 2rem rgba(0, 0, 0, 0.1);

    /* 主题过渡 */
    --theme-transition: all 0.3s ease;
}

/* 深色主题 */
.theme-dark {
    --dark-text: #FFFFFF;
    --medium-text: #A1A5B7;
    --light-text: #5E6278;
    --border-color: #2B2B40;
    --background-color: #1B1B29;
    --card-background: #1E1E2D;

    --light-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.3);
    --normal-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.4);
    --deep-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.5);
    --hover-shadow: 0 0.5rem 2rem rgba(0, 0, 0, 0.3);
}

/* 基础布局 - 全宽无留白 */
.agent-unified-container {
    min-height: 100vh;
    background-color: var(--background-color);
    padding: var(--spacing-md) 0;
    transition: var(--theme-transition);
}

.unified-wrapper {
    width: 100%;
    margin: 0;
    padding: 0 var(--spacing-sm);
}

/* 主卡片 */
.main-card {
    background-color: var(--card-background);
    border-radius: var(--medium-radius);
    box-shadow: var(--normal-shadow);
    overflow: hidden;
    transition: var(--theme-transition);
}

/* 头部操作区域 */
.header-actions {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    background-color: var(--card-background);
    transition: var(--theme-transition);
}

.header-actions-left {
    flex: 1;
}

.header-actions-title {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--dark-text);
    margin-bottom: var(--spacing-xs);
    transition: var(--theme-transition);
}

.header-actions-title i {
    color: var(--primary-color);
    font-size: 1.5rem;
}

.header-actions-desc {
    color: var(--medium-text);
    font-size: 0.875rem;
    line-height: 1.5;
    transition: var(--theme-transition);
}

.header-actions-right {
    display: flex;
    gap: var(--spacing-sm);
    align-items: flex-start;
}

/* 按钮样式 */
.btn-custom {
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--small-radius);
    font-size: 0.875rem;
    font-weight: 500;
    transition: var(--theme-transition);
    border: 1px solid transparent;
}

.btn-custom:hover {
    transform: translateY(-1px);
    box-shadow: var(--hover-shadow);
}

.btn-custom i {
    margin-right: var(--spacing-xs);
}

/* Tab导航样式 */
.nav-tabs {
    border-bottom: 1px solid var(--border-color);
    background-color: var(--card-background);
    padding: 0 var(--spacing-lg);
    transition: var(--theme-transition);
}

.nav-tabs .nav-link {
    border: none;
    border-bottom: 3px solid transparent;
    color: var(--medium-text);
    font-weight: 500;
    padding: var(--spacing-md) var(--spacing-lg);
    transition: var(--theme-transition);
}

.nav-tabs .nav-link:hover {
    border-color: transparent;
    color: var(--primary-color);
    background-color: transparent;
}

.nav-tabs .nav-link.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
    background-color: transparent;
}

.nav-tabs .nav-link i {
    margin-right: var(--spacing-xs);
    font-size: 1rem;
}

/* Tab内容区域 */
.tab-content {
    padding: var(--spacing-lg);
    background-color: var(--card-background);
    transition: var(--theme-transition);
}

/* 信息卡片 */
.info-card {
    background-color: var(--card-background);
    border-radius: var(--medium-radius);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    border: 1px solid var(--border-color);
    box-shadow: var(--light-shadow);
    transition: var(--theme-transition);
}

.info-card:hover {
    box-shadow: var(--normal-shadow);
}

.info-card-title {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--dark-text);
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--border-color);
    transition: var(--theme-transition);
}

.info-card-title i {
    color: var(--primary-color);
    font-size: 1.25rem;
}

/* 信息网格 */
.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-md);
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.info-label {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--medium-text);
    transition: var(--theme-transition);
}

.info-label i {
    color: var(--primary-color);
    font-size: 1rem;
}

.info-value {
    font-size: 1rem;
    color: var(--dark-text);
    font-weight: 400;
    line-height: 1.5;
    transition: var(--theme-transition);
}

/* 状态徽章 */
.status-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--small-radius);
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
}

.status-badge.status-active {
    background-color: var(--success-color);
    color: white;
}

.status-badge.status-pending {
    background-color: var(--warning-color);
    color: var(--dark-text);
}

.status-badge.status-inactive {
    background-color: var(--light-text);
    color: white;
}

.status-badge.status-rejected {
    background-color: var(--danger-color);
    color: white;
}

/* 富文本内容 */
.rich-text-content {
    color: var(--dark-text);
    line-height: 1.6;
    transition: var(--theme-transition);
}

.rich-text-content img {
    max-width: 100%;
    height: auto;
    border-radius: var(--small-radius);
    margin: var(--spacing-sm) 0;
}

.rich-text-content p {
    margin-bottom: var(--spacing-sm);
}

.rich-text-content h1,
.rich-text-content h2,
.rich-text-content h3,
.rich-text-content h4,
.rich-text-content h5,
.rich-text-content h6 {
    color: var(--dark-text);
    margin-top: var(--spacing-md);
    margin-bottom: var(--spacing-sm);
    transition: var(--theme-transition);
}

/* 表单样式 */
.form-section {
    margin-bottom: var(--spacing-lg);
}

.section-title {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--dark-text);
    margin-bottom: var(--spacing-md);
    transition: var(--theme-transition);
}

.section-title i {
    color: var(--primary-color);
    font-size: 1.25rem;
}

.form-control {
    border: 1px solid var(--border-color);
    border-radius: var(--small-radius);
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 0.875rem;
    color: var(--dark-text);
    background-color: var(--card-background);
    transition: var(--theme-transition);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 158, 247, 0.25);
    background-color: var(--card-background);
    color: var(--dark-text);
}

.form-control:disabled {
    background-color: var(--background-color);
    color: var(--medium-text);
    cursor: not-allowed;
}

.form-label {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--medium-text);
    margin-bottom: var(--spacing-xs);
    transition: var(--theme-transition);
}

/* 只读信息显示 */
.info-display-section {
    background-color: var(--background-color);
    border-radius: var(--medium-radius);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    border: 1px solid var(--border-color);
    transition: var(--theme-transition);
}

.info-display-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-md);
}

.info-display-item {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.info-display-item.info-display-full {
    grid-column: 1 / -1;
}

.info-display-label {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--medium-text);
    transition: var(--theme-transition);
}

.info-display-label i {
    color: var(--primary-color);
    font-size: 1rem;
}

.info-display-value {
    font-size: 1rem;
    color: var(--dark-text);
    font-weight: 400;
    padding: var(--spacing-sm);
    background-color: var(--card-background);
    border-radius: var(--small-radius);
    border: 1px solid var(--border-color);
    transition: var(--theme-transition);
}

.info-badge {
    display: inline-flex;
    align-items: center;
    padding: var(--spacing-xs) var(--spacing-sm);
    background-color: var(--info-color);
    color: white;
    border-radius: var(--small-radius);
    font-size: 0.75rem;
    font-weight: 500;
    margin-left: auto;
}

/* 加载状态 */
.loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-xl);
    color: var(--medium-text);
    transition: var(--theme-transition);
}

.loading-spinner .spinner-border {
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: var(--spacing-xl);
    color: var(--medium-text);
    transition: var(--theme-transition);
}

.empty-state i {
    font-size: 3rem;
    color: var(--light-text);
    margin-bottom: var(--spacing-md);
}

.empty-state h5 {
    color: var(--medium-text);
    margin-bottom: var(--spacing-sm);
}

.empty-state p {
    color: var(--light-text);
    font-size: 0.875rem;
}

/* 浮动操作区域 - 隐藏 */
.floating-actions {
    display: none !important;
}

.floating-actions-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--dark-text);
    margin-bottom: var(--spacing-xs);
    transition: var(--theme-transition);
}

.floating-actions-desc {
    font-size: 0.75rem;
    color: var(--medium-text);
    margin-bottom: var(--spacing-md);
    line-height: 1.4;
    transition: var(--theme-transition);
}

.floating-actions .btn-custom {
    width: 100%;
    margin-bottom: var(--spacing-sm);
    justify-content: flex-start;
    text-align: left;
}

.floating-actions .btn-custom:last-child {
    margin-bottom: 0;
}

.floating-actions .btn-custom span {
    margin-left: var(--spacing-xs);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .agent-unified-container {
        padding: var(--spacing-md);
    }

    .header-actions {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .header-actions-right {
        width: 100%;
        justify-content: flex-start;
        flex-wrap: wrap;
    }

    .info-grid {
        grid-template-columns: 1fr;
    }

    .info-display-grid {
        grid-template-columns: 1fr;
    }

    .nav-tabs {
        padding: 0 var(--spacing-md);
    }

    .nav-tabs .nav-link {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: 0.875rem;
    }

    .tab-content {
        padding: var(--spacing-md);
    }

    .floating-actions {
        position: static;
        transform: none;
        margin-top: var(--spacing-lg);
        right: auto;
        width: 100%;
    }
}

/* 轮播图样式 */
.carousel-inner {
    border-radius: var(--medium-radius);
    overflow: hidden;
    box-shadow: var(--normal-shadow);
}

.carousel-item {
    background-color: var(--background-color);
}

.carousel-item img {
    width: 100%;
    height: 400px;
    object-fit: contain;
    background-color: var(--background-color);
    cursor: pointer;
}

.carousel-control-prev,
.carousel-control-next {
    width: 50px;
    height: 50px;
    background-color: var(--primary-color);
    border-radius: 50%;
    top: 50%;
    transform: translateY(-50%);
    opacity: 0.8;
    transition: var(--theme-transition);
}

.carousel-control-prev {
    left: 20px;
}

.carousel-control-next {
    right: 20px;
}

.carousel-control-prev:hover,
.carousel-control-next:hover {
    opacity: 1;
    transform: translateY(-50%) scale(1.1);
}

.carousel-control-prev-icon,
.carousel-control-next-icon {
    width: 20px;
    height: 20px;
}

.carousel-indicators button {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin: 0 var(--spacing-xs);
    background-color: rgba(255, 255, 255, 0.5);
    border: 2px solid rgba(255, 255, 255, 0.8);
}

.carousel-indicators button.active {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* 无截图消息样式 */
#noScreenshotsMessage {
    display: none;
}

#screenshotsSection {
    display: none;
}

/* 截图上传样式 */
.screenshot-upload {
    border: 2px dashed var(--border-color);
    border-radius: var(--medium-radius);
    padding: var(--spacing-xl);
    text-align: center;
    background-color: var(--background-color);
    transition: var(--theme-transition);
    cursor: pointer;
}

.screenshot-upload:hover {
    border-color: var(--primary-color);
    background-color: rgba(0, 158, 247, 0.05);
}

.screenshot-upload.dragover {
    border-color: var(--primary-color);
    background-color: rgba(0, 158, 247, 0.1);
}

.screenshot-preview {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: var(--spacing-md);
    margin-top: var(--spacing-md);
}

.screenshot-item {
    position: relative;
    border-radius: var(--small-radius);
    overflow: hidden;
    box-shadow: var(--light-shadow);
    transition: var(--theme-transition);
}

.screenshot-item:hover {
    box-shadow: var(--normal-shadow);
    transform: translateY(-2px);
}

.screenshot-item img {
    width: 100%;
    height: 120px;
    object-fit: cover;
    cursor: pointer;
    transition: var(--theme-transition);
}

.screenshot-remove {
    position: absolute;
    top: var(--spacing-xs);
    right: var(--spacing-xs);
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background-color: var(--danger-color);
    color: white;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    cursor: pointer;
    opacity: 0;
    transition: var(--theme-transition);
}

.screenshot-item:hover .screenshot-remove {
    opacity: 1;
}

.screenshot-remove:hover {
    background-color: #dc3545;
    transform: scale(1.1);
}

/* 预览内容样式 */
.preview-section {
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
}

.preview-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.preview-section h6 {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    color: var(--dark-text);
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    transition: var(--theme-transition);
}

.preview-section h6 i {
    color: var(--primary-color);
}

.preview-info p {
    margin-bottom: var(--spacing-xs);
    color: var(--dark-text);
    transition: var(--theme-transition);
}

.preview-content-text {
    background-color: var(--background-color);
    border-radius: var(--small-radius);
    padding: var(--spacing-md);
    border: 1px solid var(--border-color);
    transition: var(--theme-transition);
}

.preview-screenshots {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: var(--spacing-sm);
}

.preview-screenshot {
    width: 100%;
    height: 100px;
    object-fit: cover;
    border-radius: var(--small-radius);
    border: 1px solid var(--border-color);
}

/* 扁平化时间线样式 */
.timeline {
    position: relative;
    padding-left: 1.5rem;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 0.75rem;
    top: 0;
    bottom: 0;
    width: 2px;
    background-color: var(--border-color);
    transition: var(--theme-transition);
}

.timeline-item {
    position: relative;
    margin-bottom: var(--spacing-sm);
    padding-left: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
}

.timeline-item:last-child {
    margin-bottom: 0;
}

.timeline-marker {
    position: absolute;
    left: -0.375rem;
    top: 0.25rem;
    width: 0.75rem;
    height: 0.75rem;
    border-radius: 50%;
    background-color: var(--primary-color);
    border: 2px solid var(--card-background);
    transition: var(--theme-transition);
    z-index: 1;
}

.timeline-item-current .timeline-marker {
    background-color: var(--success-color);
    transform: scale(1.2);
}

.timeline-content {
    background-color: transparent;
    border-radius: var(--small-radius);
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border-color);
    transition: var(--theme-transition);
    cursor: pointer;
}

.timeline-content:hover {
    background-color: var(--background-color);
    border-color: var(--primary-color);
    transform: translateX(2px);
}

/* 扁平化版本历史样式 */
.version-compact {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-xs) 0;
}

.version-number {
    font-weight: 600;
    color: var(--dark-text);
    font-size: 1rem;
    min-width: 4rem;
    transition: var(--theme-transition);
}

.version-status {
    display: inline-flex;
    align-items: center;
    padding: 2px var(--spacing-xs);
    border-radius: var(--small-radius);
    font-size: 0.7rem;
    font-weight: 500;
    min-width: 4rem;
    justify-content: center;
}

.version-status.status-draft {
    background-color: var(--light-text);
    color: white;
}

.version-status.status-pending {
    background-color: var(--warning-color);
    color: var(--dark-text);
}

.version-status.status-approved {
    background-color: var(--success-color);
    color: white;
}

.version-status.status-rejected {
    background-color: var(--danger-color);
    color: white;
}

.version-meta {
    color: var(--medium-text);
    font-size: 0.75rem;
    flex: 1;
    text-align: right;
    transition: var(--theme-transition);
}

.version-creator {
    color: var(--medium-text);
    font-size: 0.75rem;
    margin-left: var(--spacing-sm);
}

/* 扁平化审批历史样式 */
.approval-compact {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-xs) 0;
}

.approval-status {
    display: inline-flex;
    align-items: center;
    padding: 2px var(--spacing-xs);
    border-radius: var(--small-radius);
    font-size: 0.7rem;
    font-weight: 500;
    min-width: 5rem;
    justify-content: center;
}

.approval-status.status-approved {
    background-color: var(--success-color);
    color: white;
}

.approval-status.status-rejected {
    background-color: var(--danger-color);
    color: white;
}

.approval-status.status-pending {
    background-color: var(--warning-color);
    color: var(--dark-text);
}

.approval-version {
    font-weight: 500;
    color: var(--dark-text);
    font-size: 0.875rem;
    min-width: 4rem;
    transition: var(--theme-transition);
}

.approval-user {
    font-weight: 500;
    color: var(--dark-text);
    font-size: 0.875rem;
    min-width: 5rem;
    transition: var(--theme-transition);
}

.approval-date {
    color: var(--medium-text);
    font-size: 0.75rem;
    flex: 1;
    text-align: right;
    transition: var(--theme-transition);
}

/* 悬停提示样式 */
.timeline-tooltip {
    position: relative;
    cursor: pointer;
}

.timeline-tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: var(--dark-text);
    color: var(--card-background);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--small-radius);
    font-size: 0.75rem;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: var(--theme-transition);
    z-index: 1000;
    margin-bottom: var(--spacing-xs);
    max-width: 200px;
    white-space: normal;
    word-wrap: break-word;
    text-align: center;
    line-height: 1.3;
}

.timeline-tooltip::before {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 4px solid transparent;
    border-top-color: var(--dark-text);
    opacity: 0;
    visibility: hidden;
    transition: var(--theme-transition);
    z-index: 1000;
}

.timeline-tooltip:hover::after,
.timeline-tooltip:hover::before {
    opacity: 1;
    visibility: visible;
}

/* 深色主题下的提示框 */
.theme-dark .timeline-tooltip::after {
    background-color: var(--card-background);
    color: var(--dark-text);
    border: 1px solid var(--border-color);
}

.theme-dark .timeline-tooltip::before {
    border-top-color: var(--card-background);
}

/* Summernote富文本编辑器样式修复 */
.note-modal {
    z-index: 1060 !important;
}

.note-modal .modal-backdrop {
    z-index: 1055 !important;
}

.note-popover {
    z-index: 1070 !important;
}

.note-editor {
    border: 1px solid var(--border-color);
    border-radius: var(--small-radius);
    transition: var(--theme-transition);
}

.note-editor:focus-within {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 158, 247, 0.25);
}

.note-toolbar {
    background-color: var(--background-color);
    border-bottom: 1px solid var(--border-color);
    transition: var(--theme-transition);
}

.note-editable {
    background-color: var(--card-background);
    color: var(--dark-text);
    transition: var(--theme-transition);
}

.note-editable:focus {
    background-color: var(--card-background);
    color: var(--dark-text);
}

/* 深色主题下的富文本编辑器 */
.theme-dark .note-toolbar {
    background-color: var(--background-color);
    border-bottom-color: var(--border-color);
}

.theme-dark .note-editable {
    background-color: var(--card-background);
    color: var(--dark-text);
}

.theme-dark .note-btn {
    color: var(--dark-text);
}

.theme-dark .note-btn:hover {
    background-color: var(--border-color);
}

/* 打印样式 */
@media print {
    .header-actions-right,
    .floating-actions {
        display: none !important;
    }

    .main-card {
        box-shadow: none;
        border: 1px solid #ddd;
    }

    .info-card {
        box-shadow: none;
        border: 1px solid #ddd;
        break-inside: avoid;
    }
}
