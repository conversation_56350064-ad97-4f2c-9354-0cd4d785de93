/* SinoairAgent 企业级管理系统主题 */

/* ==================== 设计变量系统 ==================== */

:root {
    /* 统一使用Keen主题变量 */
    --primary: var(--kt-primary);
    --primary-hover: var(--kt-primary-hover);
    --primary-light: var(--kt-primary-light);

    --success: var(--kt-success);
    --success-light: var(--kt-success-light);

    --warning: var(--kt-warning);
    --warning-light: var(--kt-warning-light);

    --error: var(--kt-danger);
    --error-light: var(--kt-danger-light);

    --text-primary: var(--kt-text-dark);
    --text-secondary: var(--kt-text-muted);
    --text-tertiary: var(--kt-text-gray-400);
    --text-disabled: var(--kt-gray-400);

    --bg-primary: var(--kt-card-bg);
    --bg-secondary: var(--kt-body-bg);
    --bg-tertiary: var(--kt-gray-100);

    --border-primary: var(--kt-border-color);
    --border-secondary: var(--kt-gray-200);
}

/* 深色主题 */
.theme-dark {
    /* 深色主题下的Keen变量覆盖 */
    --kt-text-dark: #ffffff;
    --kt-text-muted: #a6a6a6;
    --kt-text-gray-400: #737373;
    --kt-text-gray-500: #595959;

    --kt-card-bg: #1f1f1f;
    --kt-body-bg: #141414;
    --kt-gray-100: #2a2a2a;
    --kt-gray-200: #404040;

    --kt-border-color: #404040;

    --kt-primary-light: #111b26;
    --kt-success-light: #162312;
    --kt-warning-light: #2b2111;
    --kt-danger-light: #2a1215;
    --kt-info-light: #1a1625;
}

/* 全局主题应用 */
body.theme-dark {
    background-color: var(--bg-secondary) !important;
    color: var(--text-primary) !important;
}

body.theme-dark .sidebar {
    background-color: var(--bg-primary) !important;
    border-color: var(--border-secondary) !important;
}

body.theme-dark .top-navbar,
body.theme-dark header.top-navbar {
    background-color: var(--bg-primary) !important;
    border-color: var(--border-secondary) !important;
}

body.theme-dark .main-content {
    background-color: var(--bg-secondary) !important;
}

body.theme-dark #content-container,
body.theme-dark .content-container {
    background-color: var(--bg-secondary) !important;
}

body.theme-dark .card,
body.theme-dark .modal-content {
    background-color: var(--bg-primary) !important;
    border-color: var(--border-secondary) !important;
}

body.theme-dark .card-header,
body.theme-dark .modal-header,
body.theme-dark .modal-footer {
    background-color: var(--bg-tertiary) !important;
    border-color: var(--border-secondary) !important;
}

body.theme-dark .card-body {
    background-color: var(--bg-primary) !important;
}

body.theme-dark .card-footer {
    background-color: var(--bg-tertiary) !important;
}

body.theme-dark .btn-primary {
    background-color: var(--primary) !important;
    border-color: var(--primary) !important;
    color: white !important;
}

body.theme-dark .btn-primary:hover {
    background-color: var(--primary-hover) !important;
    border-color: var(--primary-hover) !important;
}

body.theme-dark .btn-outline-primary {
    border-color: var(--primary) !important;
    color: var(--primary) !important;
    background-color: transparent !important;
}

body.theme-dark .btn-outline-primary:hover {
    background-color: var(--primary) !important;
    color: white !important;
}

body.theme-dark .btn-outline-secondary {
    border-color: var(--border-primary) !important;
    color: var(--text-secondary) !important;
    background-color: transparent !important;
}

body.theme-dark .btn-outline-secondary:hover {
    background-color: var(--bg-tertiary) !important;
    color: var(--text-primary) !important;
}

body.theme-dark .btn-link {
    color: var(--text-secondary) !important;
    background-color: transparent !important;
}

body.theme-dark .btn-link:hover {
    color: var(--primary) !important;
}

body.theme-dark .dropdown-menu {
    background-color: var(--bg-primary);
    border-color: var(--border-secondary);
}

body.theme-dark .dropdown-item {
    color: var(--text-secondary);
}

body.theme-dark .dropdown-item:hover {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
}

body.theme-dark .dropdown-divider {
    border-color: var(--border-secondary);
}

body.theme-dark .form-control,
body.theme-dark .form-select {
    background-color: var(--bg-tertiary);
    border-color: var(--border-primary);
    color: var(--text-primary);
}

body.theme-dark .table {
    color: var(--text-secondary);
}

body.theme-dark .table th {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
}

body.theme-dark .table td {
    border-color: var(--border-secondary);
}

body.theme-dark .table-light {
    background-color: var(--bg-tertiary) !important;
}

body.theme-dark .text-muted {
    color: var(--text-tertiary) !important;
}

/* 强制应用主题 */
.theme-dark * {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}
