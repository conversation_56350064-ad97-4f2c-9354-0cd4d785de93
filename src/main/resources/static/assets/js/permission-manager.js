/**
 * 权限管理工具类
 * 负责权限检查、按钮控制、菜单显示等
 */

class PermissionManager {
    constructor() {
        this.userPermissions = [];
        this.permissionCache = new Map();
        this.init();
    }

    /**
     * 初始化权限管理器
     */
    async init() {
        // 检查是否已有菜单权限控制器，避免冲突
        if (window.menuPermissionController) {
            console.log('检测到菜单权限控制器，跳过菜单权限控制');
            await this.loadUserPermissions();
            this.applyNonMenuPermissions();
        } else {
            await this.loadUserPermissions();
            this.applyPermissions();
        }
    }

    /**
     * 加载当前用户权限
     */
    async loadUserPermissions() {
        try {
            // 先设置默认权限，确保菜单可见
            this.userPermissions = this.getDefaultAdminPermissions();

            // 所有用户（包括admin）都通过API获取实际权限

            // 尝试从API加载权限，但不阻塞UI
            const response = await Promise.race([
                fetch('/api/v1/user/permissions', {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer ' + getToken(),
                        'Content-Type': 'application/json'
                    }
                }),
                new Promise((_, reject) =>
                    setTimeout(() => reject(new Error('权限API超时')), 3000)
                )
            ]);

            if (response.ok) {
                const result = await response.json();
                if (result.code === 200 && result.data && result.data.length > 0) {
                    this.userPermissions = result.data;
                    console.log('用户权限加载成功:', this.userPermissions);
                    // 重新应用权限
                    this.applyPermissions();
                } else {
                    console.warn('获取用户权限失败或权限为空，使用默认权限');
                }
            } else {
                console.warn('权限API调用失败，使用默认权限');
            }
        } catch (error) {
            console.error('加载用户权限失败，使用默认权限:', error);
            // 确保有默认权限
            if (!this.userPermissions || this.userPermissions.length === 0) {
                this.userPermissions = this.getDefaultAdminPermissions();
            }
        }
    }

    /**
     * 获取默认管理员权限（用于测试和API失败时的备用）
     */
    getDefaultAdminPermissions() {
        return [
            // 仪表板
            'DASHBOARD_VIEW',

            // Agent管理
            'AGENT_VIEW', 'AGENT_CREATE', 'AGENT_EDIT', 'AGENT_DELETE', 'AGENT_DEPLOY', 'AGENT_TEST',

            // 智能调试台
            'PLAYGROUND_VIEW', 'PLAYGROUND_TEST', 'PLAYGROUND_SAVE',

            // 业务模板
            'TEMPLATE_VIEW', 'TEMPLATE_CREATE', 'TEMPLATE_EDIT', 'TEMPLATE_DELETE', 'TEMPLATE_IMPORT', 'TEMPLATE_EXPORT',

            // 智能表单配置
            'business:smart-form', 'business:smart-form:view', 'business:smart-form:create', 'business:smart-form:edit', 'business:smart-form:delete',

            // 页面绑定
            'BINDING_VIEW', 'BINDING_CREATE', 'BINDING_EDIT', 'BINDING_DELETE', 'BINDING_TEST',

            // 文档识别
            'RECOGNITION_VIEW', 'RECOGNITION_UPLOAD', 'RECOGNITION_PROCESS', 'RECOGNITION_DOWNLOAD',

            // Agent审核
            'APPROVAL','AGENT_APPROVAL_VIEW','AGENT_APPROVAL_MANAGE', 'AGENT_APPROVAL_APPROVE', 'AGENT_APPROVAL_REJECT',

            // 用户管理
            'USER_VIEW', 'USER_CREATE', 'USER_EDIT', 'USER_DELETE', 'USER_RESET_PASSWORD', 'USER_EXPORT',

            // 角色管理
            'ROLE_VIEW', 'ROLE_CREATE', 'ROLE_EDIT', 'ROLE_DELETE', 'ROLE_ASSIGN_PERMISSIONS',

            // 权限管理
            'PERMISSION_VIEW', 'PERMISSION_CREATE', 'PERMISSION_EDIT', 'PERMISSION_DELETE',

            // 日志管理
            'LOG_VIEW', 'system:log:view', 'system:log:detail', 'system:log:export', 'system:log:statistics', 'system:log:cleanup'
        ];
    }

    /**
     * 检查用户是否有指定权限
     */
    hasPermission(permissionCode) {
        if (!permissionCode) return true;
        
        // 从缓存中获取
        if (this.permissionCache.has(permissionCode)) {
            return this.permissionCache.get(permissionCode);
        }
        
        const hasPermission = this.userPermissions.includes(permissionCode);
        this.permissionCache.set(permissionCode, hasPermission);
        return hasPermission;
    }

    /**
     * 检查用户是否有任意一个权限
     */
    hasAnyPermission(permissionCodes) {
        if (!permissionCodes || permissionCodes.length === 0) return true;
        return permissionCodes.some(code => this.hasPermission(code));
    }

    /**
     * 检查用户是否有所有权限
     */
    hasAllPermissions(permissionCodes) {
        if (!permissionCodes || permissionCodes.length === 0) return true;
        return permissionCodes.every(code => this.hasPermission(code));
    }

    /**
     * 应用权限控制到页面元素
     */
    applyPermissions() {
        console.log('开始应用权限控制...');

        // 先确保所有菜单项可见（移除加载状态）
        this.initMenuVisibility();

        // 控制按钮显示/隐藏
        this.controlButtons();

        // 控制菜单项显示/隐藏
        this.controlMenuItems();

        // 控制表格操作列
        this.controlTableActions();

        console.log('权限控制应用完成');
    }

    /**
     * 应用非菜单权限控制（避免与菜单权限控制器冲突）
     */
    applyNonMenuPermissions() {
        console.log('开始应用非菜单权限控制...');

        // 控制按钮显示/隐藏
        this.controlButtons();

        // 控制表格操作列
        this.controlTableActions();

        console.log('非菜单权限控制应用完成');
    }

    /**
     * 初始化菜单可见性
     */
    initMenuVisibility() {
        // 如果有专门的菜单权限控制器，不要干预菜单显示
        if (window.menuPermissionController) {
            console.log('跳过菜单可见性初始化（由专门的控制器处理）');
            return;
        }

        document.querySelectorAll('.sidebar .nav-item').forEach(item => {
            item.classList.remove('permission-loading', 'permission-hidden');
        });
    }

    /**
     * 控制按钮权限
     */
    controlButtons() {
        document.querySelectorAll('[data-permission]').forEach(element => {
            const permission = element.getAttribute('data-permission');
            if (!this.hasPermission(permission)) {
                element.style.display = 'none';
            }
        });

        document.querySelectorAll('[data-permissions]').forEach(element => {
            const permissions = element.getAttribute('data-permissions').split(',');
            if (!this.hasAnyPermission(permissions)) {
                element.style.display = 'none';
            }
        });
    }

    /**
     * 控制菜单项权限
     */
    controlMenuItems() {
        // 如果有专门的菜单权限控制器，跳过菜单控制
        if (window.menuPermissionController) {
            console.log('跳过菜单项权限控制（由专门的控制器处理）');
            return;
        }

        console.log('开始控制菜单项权限...');

        document.querySelectorAll('.nav-link[data-permission]').forEach(element => {
            const permission = element.getAttribute('data-permission');
            const navItem = element.closest('.nav-item');

            if (!this.hasPermission(permission)) {
                // 使用CSS类控制显示/隐藏，避免直接修改style
                navItem.classList.add('permission-hidden');
                navItem.classList.remove('permission-loading');
                console.log(`隐藏菜单项: ${permission}`);
            } else {
                // 确保有权限的菜单项显示
                navItem.classList.remove('permission-hidden', 'permission-loading');
                console.log(`显示菜单项: ${permission}`);
            }
        });

        console.log('菜单项权限控制完成');
    }

    /**
     * 控制表格操作列权限
     */
    controlTableActions() {
        document.querySelectorAll('.btn[data-permission]').forEach(button => {
            const permission = button.getAttribute('data-permission');
            if (!this.hasPermission(permission)) {
                button.style.display = 'none';
            }
        });
    }

    /**
     * 动态创建带权限控制的按钮
     */
    createButton(text, className, onclick, permission, icon = '') {
        if (permission && !this.hasPermission(permission)) {
            return '';
        }
        
        const iconHtml = icon ? `<i class="bi bi-${icon}"></i> ` : '';
        return `<button class="btn ${className}" onclick="${onclick}" ${permission ? `data-permission="${permission}"` : ''}>
            ${iconHtml}${text}
        </button>`;
    }

    /**
     * 刷新权限（重新加载用户权限并应用）
     */
    async refresh() {
        this.permissionCache.clear();
        await this.loadUserPermissions();
        this.applyPermissions();
    }

    /**
     * 获取用户所有权限
     */
    getUserPermissions() {
        return [...this.userPermissions];
    }
}

// 创建全局权限管理器实例
window.permissionManager = new PermissionManager();

// 页面加载完成后初始化权限
document.addEventListener('DOMContentLoaded', function() {
    console.log('权限管理器开始初始化...');

    // 延迟初始化，让菜单权限控制器先执行
    setTimeout(() => {
        // 检查菜单权限控制器是否已经初始化
        if (window.menuPermissionController && window.menuPermissionController.permissionsLoaded) {
            console.log('菜单权限控制器已完成，跳过权限管理器的菜单控制');
        }

        if (window.permissionManager) {
            window.permissionManager.init().then(() => {
                console.log('权限管理器初始化完成');
            }).catch(error => {
                console.error('权限管理器初始化失败:', error);
                // 即使初始化失败，也要应用默认权限
                window.permissionManager.applyPermissions();
            });
        }
    }, 500); // 延迟500ms，确保菜单权限控制器先执行
});

/**
 * 全局权限检查函数（简化调用）
 */
function hasPermission(permissionCode) {
    return window.permissionManager ? window.permissionManager.hasPermission(permissionCode) : true;
}

function hasAnyPermission(permissionCodes) {
    return window.permissionManager ? window.permissionManager.hasAnyPermission(permissionCodes) : true;
}

function hasAllPermissions(permissionCodes) {
    return window.permissionManager ? window.permissionManager.hasAllPermissions(permissionCodes) : true;
}
