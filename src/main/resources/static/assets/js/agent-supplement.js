/**
 * Agent补充资料页面JavaScript
 */

// 页面状态管理
const SupplementPage = {
    currentAgentId: null,
    uploadedFiles: [],
    isDirty: false
};

/**
 * 返回来源页面
 */
function goBackToSourcePage() {
    // 根据来源页面决定返回目标
    const source = sessionStorage.getItem('supplementSource') || 'agent-approval-list';

    if (typeof loadPage === 'function') {
        loadPage(source);
    } else {
        console.error('loadPage函数未定义');
        showToast('页面跳转失败', 'error');
    }
}

/**
 * 显示提交确认模态框
 */
function showSubmitConfirmModal() {
    console.log('showSubmitConfirmModal函数被调用');
    // 创建模态框HTML
    const modalHtml = `
        <div class="modal fade" id="submitConfirmModal" tabindex="-1" aria-labelledby="submitConfirmModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="submitConfirmModalLabel">
                            <i class="bi bi-send me-2 text-primary"></i>提交审核确认
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle me-2"></i>
                            <strong>提交说明：</strong>
                        </div>
                        <ul class="mb-3">
                            <li>提交后将发送给管理员审核</li>
                            <li>审核通过后Agent将正式发布</li>
                            <li>提交前请确认所有信息填写完整</li>
                        </ul>
                        <p class="text-muted mb-0">确定要提交补充资料进行审核吗？</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" id="cancelSubmitBtn">
                            <i class="bi bi-x-circle me-1"></i>取消
                        </button>
                        <button type="button" class="btn btn-primary" id="confirmSubmitBtn">
                            <i class="bi bi-send me-1"></i>确认提交
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 移除已存在的模态框
    const existingModal = document.getElementById('submitConfirmModal');
    if (existingModal) {
        existingModal.remove();
    }

    // 添加模态框到页面
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // 显示模态框
    const modalElement = document.getElementById('submitConfirmModal');
    const modal = new bootstrap.Modal(modalElement, {
        backdrop: 'static', // 防止点击背景关闭
        keyboard: true,     // 允许ESC键关闭
        focus: true         // 自动聚焦
    });

    // 设置更高的z-index，确保在Summernote模态框之上
    modalElement.style.zIndex = '1070';
    modalElement.style.position = 'fixed';

    // 监听模态框显示事件
    modalElement.addEventListener('shown.bs.modal', function() {
        console.log('提交确认模态框已显示');

        // 确保背景遮罩也有正确的z-index
        setTimeout(() => {
            const backdrops = document.querySelectorAll('.modal-backdrop');
            backdrops.forEach(backdrop => {
                backdrop.style.zIndex = '1069';
            });
        }, 10);

        // 确保模态框内容可以接收点击事件
        const modalContent = modalElement.querySelector('.modal-content');
        if (modalContent) {
            modalContent.style.pointerEvents = 'auto';
            modalContent.style.position = 'relative';
            modalContent.style.zIndex = '1072';
        }

        // 绑定确认按钮事件
        const confirmBtn = modalElement.querySelector('#confirmSubmitBtn');
        if (confirmBtn) {
            confirmBtn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                console.log('确认提交按钮被点击');
                confirmSubmitSupplement();
            });
            confirmBtn.focus();
        }

        // 绑定取消按钮事件（额外保险）
        const cancelBtn = modalElement.querySelector('#cancelSubmitBtn');
        if (cancelBtn) {
            cancelBtn.addEventListener('click', function(e) {
                console.log('取消按钮被点击');
                modal.hide();
            });
        }
    });

    // 监听模态框隐藏事件
    modalElement.addEventListener('hidden.bs.modal', function() {
        console.log('提交确认模态框已隐藏');
        // 清理模态框元素
        setTimeout(() => {
            if (modalElement && modalElement.parentNode) {
                modalElement.remove();
            }
        }, 300);
    });

    modal.show();

    // 调试：检查模态框状态
    setTimeout(() => {
        console.log('模态框状态检查:');
        console.log('- 模态框元素:', modalElement);
        console.log('- 模态框z-index:', window.getComputedStyle(modalElement).zIndex);
        console.log('- 模态框display:', window.getComputedStyle(modalElement).display);

        const backdrop = document.querySelector('.modal-backdrop');
        if (backdrop) {
            console.log('- 背景遮罩z-index:', window.getComputedStyle(backdrop).zIndex);
        }

        const confirmBtn = modalElement.querySelector('#confirmSubmitBtn');
        if (confirmBtn) {
            console.log('- 确认按钮可见:', confirmBtn.offsetParent !== null);
            console.log('- 确认按钮z-index:', window.getComputedStyle(confirmBtn).zIndex);
        }
    }, 500);
}

/**
 * 确认提交补充资料
 */
function confirmSubmitSupplement() {
    // 关闭模态框
    const modal = bootstrap.Modal.getInstance(document.getElementById('submitConfirmModal'));
    if (modal) {
        modal.hide();
    }

    // 执行提交
    saveSupplement(2); // 2-已提交
}

/**
 * 本地API调用函数（备用）
 */
async function localApiCall(url, options = {}) {
    const defaultOptions = {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
        }
    };

    // 合并选项
    const finalOptions = { ...defaultOptions, ...options };

    // 添加认证token
    const token = localStorage.getItem('token') || localStorage.getItem('authToken') || sessionStorage.getItem('token');
    if (token) {
        finalOptions.headers['Authorization'] = `Bearer ${token}`;
    }

    try {
        const response = await fetch(url, finalOptions);

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();

        if (result.code === 401) {
            localStorage.removeItem('token');
            localStorage.removeItem('authToken');
            sessionStorage.removeItem('token');
            window.location.href = '/login.html';
            return;
        }

        return result;
    } catch (error) {
        console.error('API调用失败:', error);
        throw error;
    }
}

// 页面初始化函数
function initAgentSupplementPage() {
    console.log('初始化Agent补充资料页面');

    // 获取当前Agent ID
    SupplementPage.currentAgentId = getCurrentAgentId();

    console.log('获取到的Agent ID:', SupplementPage.currentAgentId);
    console.log('window.currentAgentId:', window.currentAgentId);
    console.log('localStorage.currentAgentId:', localStorage.getItem('currentAgentId'));

    if (!SupplementPage.currentAgentId) {
        console.error('未找到Agent信息');
        showToast('未找到Agent信息', 'error');
        loadPage('agent-approval-list');
        return;
    }

    // 延迟初始化，确保DOM元素已经渲染
    setTimeout(() => {
        try {
            console.log('开始初始化页面组件...');

            // 初始化页面
            initPage();
            console.log('页面初始化完成');

            // 绑定事件
            bindEvents();
            console.log('事件绑定完成');

            // 加载数据
            loadSupplementData();
            console.log('数据加载开始');

            // 加载版本历史
            loadVersionHistory();
            console.log('版本历史加载开始');

            console.log('Agent补充资料页面初始化完成');
        } catch (error) {
            console.error('页面初始化过程中发生错误:', error);
        }
    }, 100);
}

/**
 * 初始化页面
 */
function initPage() {
    // 添加全局CSS修复
    addGlobalSummernoteCSS();

    // 初始化Tab切换事件
    initTabEvents();

    // 初始化富文本编辑器
    initSummernoteEditors();

    // 初始化文件上传
    initFileUpload();

    // 监听内容变化
    monitorContentChanges();

    // 加载初始数据
    loadInitialData();
}

/**
 * 加载初始数据
 */
function loadInitialData() {
    if (SupplementPage.currentAgentId) {
        console.log('开始加载初始数据，Agent ID:', SupplementPage.currentAgentId);

        // 加载审批历史数据
        loadApprovalHistory(SupplementPage.currentAgentId);

        // 加载版本历史数据
        loadVersionHistory();
    } else {
        console.warn('Agent ID不存在，无法加载初始数据');
    }
}

/**
 * 添加全局Summernote CSS修复
 */
function addGlobalSummernoteCSS() {
    const style = document.createElement('style');
    style.textContent = `
        /* 全局修复Summernote模态框问题 */
        .note-modal {
            z-index: 1060 !important;
        }

        .note-modal .modal-dialog {
            position: fixed !important;
            top: 50% !important;
            left: 50% !important;
            transform: translate(-50%, -50%) !important;
            margin: 0 !important;
            width: auto !important;
            max-width: 90vw !important;
        }

        .note-modal.fade .modal-dialog {
            transition: transform 0.3s ease-out !important;
        }

        .note-modal.show .modal-dialog {
            transform: translate(-50%, -50%) !important;
        }

        /* 防止模态框抖动 */
        .note-modal .modal-content {
            position: relative !important;
            display: flex !important;
            flex-direction: column !important;
            width: 100% !important;
            pointer-events: auto !important;
            background-clip: padding-box !important;
            border: none !important;
            border-radius: 0.5rem !important;
            box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important;
        }

        /* 确保背景遮罩层级正确 */
        .modal-backdrop {
            z-index: 1059 !important;
        }

        /* 修复链接和图片插入对话框 */
        .note-link-dialog .modal-dialog,
        .note-image-dialog .modal-dialog {
            max-width: 500px !important;
        }

        .note-link-dialog .form-control,
        .note-image-dialog .form-control {
            margin-bottom: 0.5rem !important;
        }
    `;

    document.head.appendChild(style);
    console.log('全局Summernote CSS修复已添加');
}

/**
 * 初始化Tab切换事件
 */
function initTabEvents() {
    // 监听Tab切换事件
    const tabButtons = document.querySelectorAll('#supplementTabs button[data-bs-toggle="tab"]');
    tabButtons.forEach(button => {
        button.addEventListener('shown.bs.tab', function (event) {
            const targetTab = event.target.getAttribute('data-bs-target');
            console.log('切换到Tab:', targetTab);

            // 根据不同Tab加载相应数据
            switch (targetTab) {
                case '#approval-history':
                    if (SupplementPage.currentAgentId) {
                        loadApprovalHistory(SupplementPage.currentAgentId);
                    }
                    break;
                case '#version-history':
                    if (SupplementPage.currentAgentId) {
                        loadVersionHistory();
                    }
                    break;
                case '#screenshots':
                    if (SupplementPage.currentAgentId) {
                        loadScreenshots(SupplementPage.currentAgentId);
                    }
                    break;
                case '#preview':
                    refreshPreview();
                    break;
            }
        });
    });
}

/**
 * 初始化Summernote富文本编辑器
 */
function initSummernoteEditors() {
    // 检查jQuery和Summernote是否已加载
    if (typeof $ === 'undefined') {
        console.error('jQuery未加载，无法初始化Summernote');
        return;
    }

    if (typeof $.fn.summernote === 'undefined') {
        console.error('Summernote未加载');
        return;
    }

    // 检查Bootstrap是否已加载
    if (typeof bootstrap === 'undefined') {
        console.warn('Bootstrap未加载，下拉菜单可能无法正常工作');
    }

    // Summernote配置
    const summernoteConfig = {
        height: 200,
        lang: 'zh-CN',
        placeholder: '请输入内容...',
        toolbar: [
            ['style', ['style']],
            ['font', ['bold', 'italic', 'underline', 'clear']],
            ['fontname', ['fontname']],
            ['fontsize', ['fontsize']],
            ['color', ['color']],
            ['para', ['ul', 'ol', 'paragraph']],
            ['table', ['table']],
            ['insert', ['link', 'picture', 'video']],
            ['view', ['fullscreen', 'codeview', 'help']]
        ],
        fontNames: ['Arial', 'Arial Black', 'Comic Sans MS', 'Courier New', 'Helvetica', 'Impact', 'Tahoma', 'Times New Roman', 'Verdana', '微软雅黑', '宋体', '黑体', '楷体'],
        fontSizes: ['8', '9', '10', '11', '12', '14', '16', '18', '20', '24', '36', '48'],
        // 修复模态框抖动问题的配置
        dialogsInBody: true,
        dialogsFade: true,
        disableDragAndDrop: false,
        callbacks: {
            onChange: function(contents, $editable) {
                SupplementPage.isDirty = true;
            },
            onInit: function() {
                console.log('Summernote初始化完成');
                // 修复Bootstrap 5下拉菜单兼容性问题
                setTimeout(function() {
                    fixSummernoteDropdowns();
                    fixSummernoteModals();
                }, 100);
            }
        }
    };

    // 初始化各个富文本编辑器
    $('#agentIntroduction').summernote(summernoteConfig);
    $('#usageScenarios').summernote(summernoteConfig);

    // 添加调试事件监听
    $('.note-btn[data-original-title*="链接"], .note-btn[data-bs-original-title*="链接"]').on('click', function() {
        console.log('链接按钮被点击');
    });

    $('.note-btn[data-original-title*="图片"], .note-btn[data-bs-original-title*="图片"]').on('click', function() {
        console.log('图片按钮被点击');
    });

    console.log('Summernote富文本编辑器初始化完成');
}

/**
 * 修复Summernote在Bootstrap 5下的下拉菜单问题
 */
function fixSummernoteDropdowns() {
    // 修复下拉菜单点击事件
    $('.note-toolbar .note-btn-group .dropdown-toggle').off('click.summernote-dropdown').on('click.summernote-dropdown', function(e) {
        e.preventDefault();
        e.stopPropagation();

        const $this = $(this);
        const $dropdown = $this.next('.dropdown-menu, .note-dropdown-menu');

        // 关闭其他下拉菜单
        $('.note-toolbar .dropdown-menu, .note-toolbar .note-dropdown-menu').not($dropdown).removeClass('show');

        // 切换当前下拉菜单
        $dropdown.toggleClass('show');

        // 点击外部关闭下拉菜单
        if ($dropdown.hasClass('show')) {
            $(document).off('click.summernote-dropdown-outside').on('click.summernote-dropdown-outside', function(event) {
                if (!$this.is(event.target) && !$dropdown.is(event.target) && $dropdown.has(event.target).length === 0) {
                    $dropdown.removeClass('show');
                    $(document).off('click.summernote-dropdown-outside');
                }
            });
        }
    });

    // 修复下拉菜单项点击事件
    $('.note-toolbar .dropdown-menu a, .note-toolbar .note-dropdown-menu a').off('click.summernote-dropdown-item').on('click.summernote-dropdown-item', function(e) {
        const $this = $(this);
        const $dropdown = $this.closest('.dropdown-menu, .note-dropdown-menu');

        // 延迟关闭下拉菜单，确保Summernote事件先执行
        setTimeout(function() {
            $dropdown.removeClass('show');
        }, 50);
    });

    console.log('Summernote下拉菜单修复完成');
}

/**
 * 修复Summernote模态框抖动问题
 */
function fixSummernoteModals() {
    // 监听所有Summernote模态框的显示事件
    $(document).on('show.bs.modal', '.note-modal', function(e) {
        const $modal = $(this);
        const zIndex = 1060;

        console.log('Summernote模态框即将显示:', $modal.attr('class'));

        // 设置z-index
        $modal.css('z-index', zIndex);

        // 修复模态框位置
        const $dialog = $modal.find('.modal-dialog');
        $dialog.css({
            'position': 'fixed',
            'top': '50%',
            'left': '50%',
            'transform': 'translate(-50%, -50%)',
            'margin': '0',
            'max-width': '90vw',
            'max-height': '90vh'
        });

        // 设置背景遮罩z-index
        setTimeout(function() {
            $('.modal-backdrop').last().css('z-index', zIndex - 1);
        }, 10);
    });

    // 模态框显示完成后的处理
    $(document).on('shown.bs.modal', '.note-modal', function() {
        const $modal = $(this);

        console.log('Summernote模态框已显示');

        // 确保模态框居中且不抖动
        const $dialog = $modal.find('.modal-dialog');
        $dialog.css({
            'position': 'fixed',
            'top': '50%',
            'left': '50%',
            'transform': 'translate(-50%, -50%)',
            'margin': '0'
        });

        // 聚焦到第一个输入框
        setTimeout(function() {
            const $firstInput = $modal.find('input[type="text"], input[type="url"], textarea').first();
            if ($firstInput.length) {
                $firstInput.focus().select();
            }
        }, 150);
    });

    // 防止模态框背景点击时的问题
    $(document).on('click', '.note-modal', function(e) {
        if (e.target === this) {
            e.stopPropagation();
            e.preventDefault();
        }
    });

    // 监听模态框隐藏事件
    $(document).on('hidden.bs.modal', '.note-modal', function() {
        console.log('Summernote模态框已隐藏');
        // 清理可能残留的背景遮罩
        $('.modal-backdrop').remove();
    });

    // 特别处理链接和图片插入对话框
    $(document).on('show.bs.modal', '.note-link-dialog, .note-image-dialog', function() {
        const $modal = $(this);
        console.log('链接/图片对话框即将显示');

        // 确保对话框大小合适
        $modal.find('.modal-dialog').css({
            'max-width': '500px',
            'width': '90%'
        });
    });

    console.log('Summernote模态框修复完成');
}

/**
 * 绑定事件
 */
function bindEvents() {
    console.log('开始绑定事件...');

    // 调试：列出所有按钮元素
    const allButtons = document.querySelectorAll('button');
    console.log('页面中所有按钮元素:', allButtons.length);
    allButtons.forEach((btn, index) => {
        console.log(`按钮 ${index}: id="${btn.id}", class="${btn.className}"`);
    });

    // 保存按钮 - 头部区域
    const saveBtnHeader = document.getElementById('saveBtnHeader');
    console.log('头部保存按钮元素:', saveBtnHeader);
    if (saveBtnHeader) {
        saveBtnHeader.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('头部保存按钮被点击');
            saveSupplement(1); // 1-草稿
        });
        console.log('头部保存按钮事件已绑定');
    } else {
        console.warn('未找到头部保存按钮元素 (saveBtnHeader)');
    }

    // 保存按钮 - 浮动区域
    const saveBtn = document.getElementById('saveBtn');
    if (saveBtn) {
        saveBtn.addEventListener('click', function() {
            saveSupplement(1); // 1-草稿
        });
    }

    // 保存按钮 - 底部区域
    const saveBtnBottom = document.getElementById('saveBtnBottom');
    if (saveBtnBottom) {
        saveBtnBottom.addEventListener('click', function() {
            saveSupplement(1); // 1-草稿
        });
    }

    // 提交按钮 - 头部区域
    const submitBtnHeader = document.getElementById('submitBtnHeader');
    console.log('头部提交按钮元素:', submitBtnHeader);
    if (submitBtnHeader) {
        submitBtnHeader.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('头部提交按钮被点击');
            showSubmitConfirmModal();
        });
        console.log('头部提交按钮事件已绑定');
    } else {
        console.warn('未找到头部提交按钮元素 (submitBtnHeader)');
    }

    // 提交按钮 - 浮动区域
    const submitBtn = document.getElementById('submitBtn');
    if (submitBtn) {
        submitBtn.addEventListener('click', function() {
            showSubmitConfirmModal();
        });
    }

    // 提交按钮 - 底部区域
    const submitBtnBottom = document.getElementById('submitBtnBottom');
    if (submitBtnBottom) {
        submitBtnBottom.addEventListener('click', function() {
            showSubmitConfirmModal();
        });
    }

    // 预览按钮 - 头部区域 (已通过onclick绑定，但也可以通过addEventListener绑定)
    const previewBtnHeader = document.getElementById('previewBtnHeader');
    if (previewBtnHeader) {
        // 移除onclick属性，使用addEventListener
        previewBtnHeader.removeAttribute('onclick');
        previewBtnHeader.addEventListener('click', function() {
            console.log('头部预览按钮被点击');
            refreshPreview();
        });
    }

    // 预览按钮 - 浮动区域
    const previewBtn = document.getElementById('previewBtn');
    if (previewBtn) {
        previewBtn.addEventListener('click', function() {
            refreshPreview();
        });
    }

    // 预览按钮 - 底部区域
    const previewBtnBottom = document.getElementById('previewBtnBottom');
    if (previewBtnBottom) {
        previewBtnBottom.addEventListener('click', function() {
            refreshPreview();
        });
    }

    // 监听页面离开事件
    window.addEventListener('beforeunload', function(e) {
        if (SupplementPage.isDirty) {
            e.preventDefault();
            e.returnValue = '您有未保存的更改，确定要离开吗？';
        }
    });
}

/**
 * 初始化文件上传
 */
function initFileUpload() {
    const uploadArea = document.getElementById('screenshotUpload');
    const fileInput = document.getElementById('screenshotInput');

    if (!uploadArea || !fileInput) {
        console.warn('文件上传元素未找到');
        return;
    }

    // 点击上传
    uploadArea.addEventListener('click', function() {
        fileInput.click();
    });

    // 文件选择
    fileInput.addEventListener('change', async function(e) {
        await handleFiles(e.target.files);
    });

    // 拖拽上传
    uploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        uploadArea.classList.add('dragover');
    });

    uploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
    });

    uploadArea.addEventListener('drop', async function(e) {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
        await handleFiles(e.dataTransfer.files);
    });
}

/**
 * 处理上传的文件
 */
async function handleFiles(files) {
    for (const file of files) {
        if (file.type.startsWith('image/')) {
            if (file.size > 5 * 1024 * 1024) {
                showToast(`文件 ${file.name} 超过5MB限制`, 'error');
                continue;
            }

            // 创建本地预览
            const reader = new FileReader();
            reader.onload = async function(e) {
                // 先显示本地预览
                addScreenshotPreview(e.target.result, file.name);
                SupplementPage.isDirty = true;

                try {
                    // 上传到服务器并获取公开URL
                    const publicUrl = await uploadScreenshotFile(file);

                    // 更新预览图片的src为服务器URL
                    if (publicUrl) {
                        const previewImages = document.querySelectorAll('#screenshotPreview .screenshot-item img');
                        const lastImage = previewImages[previewImages.length - 1];
                        if (lastImage) {
                            lastImage.src = publicUrl;
                            console.log('预览图片已更新为服务器URL:', publicUrl);
                        }
                    }
                } catch (error) {
                    console.error('上传失败，保持本地预览:', error);
                }
            };
            reader.readAsDataURL(file);
        } else {
            showToast(`文件 ${file.name} 不是有效的图片格式`, 'error');
        }
    }
}

/**
 * 上传文件到服务器
 */
async function uploadScreenshotFile(file) {
    try {
        console.log('开始上传文件:', file.name);

        // 检查认证状态
        const token = getToken();
        console.log('当前认证Token:', token ? token.substring(0, 10) + '...' : '无Token');

        if (!token) {
            throw new Error('未找到认证Token，请重新登录');
        }

        const formData = new FormData();
        formData.append('file', file);
        formData.append('businessType', 'agent-screenshot');

        // 调用admin-common.js中的文件上传函数
        const result = await window.uploadFile('/api/v1/files/upload', formData);

        // 检查返回结果是否有效
        if (!result) {
            throw new Error('上传请求失败，请检查网络连接或重新登录');
        }

        if (result.code === 200 && result.data) {
            // 使用公开预览URL，将 /api/v1/files/{id}/preview 转换为 /api/v1/files/public/{id}/preview
            const fileId = result.data.fileId;
            const publicPreviewUrl = `/api/v1/files/public/${fileId}/preview`;
            SupplementPage.uploadedFiles.push(publicPreviewUrl);
            console.log('文件上传成功:', file.name, '文件ID:', fileId, '公开预览URL:', publicPreviewUrl);
            return publicPreviewUrl;
        } else {
            throw new Error(result.message || result.msg || '上传失败');
        }
    } catch (error) {
        console.error('文件上传失败:', error);
        showToast('文件上传失败: ' + error.message, 'error');
    }
}

/**
 * 添加截图预览
 */
function addScreenshotPreview(src, fileName) {
    const previewContainer = document.getElementById('screenshotPreview');
    if (!previewContainer) {
        console.warn('截图预览容器未找到');
        return;
    }
    
    const previewItem = document.createElement('div');
    previewItem.className = 'screenshot-item';
    previewItem.innerHTML = `
        <img src="${src}" alt="${fileName}" title="${fileName}">
        <button type="button" class="screenshot-remove" onclick="removeScreenshot(this)">
            <i class="bi bi-x"></i>
        </button>
    `;
    
    previewContainer.appendChild(previewItem);
}

/**
 * 移除截图
 */
function removeScreenshot(button) {
    const item = button.closest('.screenshot-item');
    const img = item.querySelector('img');
    const src = img.src;

    console.log('删除图片，原始src:', src);
    console.log('当前uploadedFiles:', SupplementPage.uploadedFiles);

    // 将绝对URL转换为相对URL进行匹配
    let relativeUrl = src;
    if (src.startsWith('http')) {
        try {
            const url = new URL(src);
            relativeUrl = url.pathname;
        } catch (e) {
            console.warn('URL解析失败:', src);
        }
    }

    console.log('转换后的相对URL:', relativeUrl);

    // 从上传文件列表中移除（支持绝对URL和相对URL匹配）
    let index = SupplementPage.uploadedFiles.indexOf(src);
    if (index === -1) {
        index = SupplementPage.uploadedFiles.indexOf(relativeUrl);
    }

    if (index > -1) {
        SupplementPage.uploadedFiles.splice(index, 1);
        console.log('成功从列表中删除，剩余文件:', SupplementPage.uploadedFiles);
    } else {
        console.warn('未在uploadedFiles中找到要删除的URL:', src, relativeUrl);
    }

    // 移除DOM元素
    item.remove();

    SupplementPage.isDirty = true;
}

/**
 * 监听内容变化
 */
function monitorContentChanges() {
    // Summernote的内容变化监听已在初始化时通过callbacks.onChange设置
    // 这里可以添加其他需要监听的元素
    console.log('内容变化监听已设置');
}

/**
 * 加载补充资料数据
 */
async function loadSupplementData() {
    try {
        console.log('开始加载补充资料数据，Agent ID:', SupplementPage.currentAgentId);

        // 加载基本信息和统计数据
        const [supplementResponse, statisticsResponse] = await Promise.all([
            localApiCall(`/api/v1/agent-supplement/${SupplementPage.currentAgentId}`),
            localApiCall(`/api/v1/agent-supplement/${SupplementPage.currentAgentId}/statistics`)
        ]);

        console.log('补充资料API响应:', supplementResponse);
        console.log('统计信息API响应:', statisticsResponse);

        if (supplementResponse.code === 200) {
            console.log('填充补充资料数据:', supplementResponse.data);
            fillSupplementData(supplementResponse.data);
        } else {
            console.error('获取补充资料失败:', supplementResponse);
            showToast('获取补充资料失败: ' + (supplementResponse.message || '未知错误'), 'error');
        }

        if (statisticsResponse.code === 200) {
            console.log('填充统计数据:', statisticsResponse.data);
            fillStatisticsData(statisticsResponse.data);
            // 移除版本信息填充，因为基本信息Tab中已不再显示版本信息
            // fillVersionInfo(statisticsResponse.data);
        } else {
            console.error('获取统计信息失败:', statisticsResponse);
            showToast('获取统计信息失败: ' + (statisticsResponse.message || '未知错误'), 'error');
        }

    } catch (error) {
        console.error('加载数据失败:', error);
        showToast('加载数据失败: ' + error.message, 'error');
    }
}

/**
 * 填充补充资料数据
 */
function fillSupplementData(data) {
    console.log('开始填充补充资料数据:', data);

    if (!data) {
        console.warn('补充资料数据为空');
        return;
    }

    // 基本信息 - 更新为新的信息展示结构
    const agentNameDisplayEl = document.getElementById('agentNameDisplay');
    const agentCodeDisplayEl = document.getElementById('agentCodeDisplay');
    const agentDescriptionDisplayEl = document.getElementById('agentDescriptionDisplay');

    console.log('找到的DOM元素:', {
        agentNameDisplayEl,
        agentCodeDisplayEl,
        agentDescriptionDisplayEl
    });

    // 填充信息展示区域的内容
    if (agentNameDisplayEl) {
        agentNameDisplayEl.textContent = data.agentName || '-';
        console.log('设置Agent名称:', data.agentName);
    }
    if (agentCodeDisplayEl) {
        agentCodeDisplayEl.textContent = data.agentCode || '-';
        console.log('设置Agent编码:', data.agentCode);
    }
    if (agentDescriptionDisplayEl) {
        agentDescriptionDisplayEl.textContent = data.agentDescription || '-';
        console.log('设置Agent描述:', data.agentDescription);
    }
    
    // 富文本内容 - 使用Summernote的code方法设置内容
    if (typeof $ !== 'undefined' && $.fn.summernote) {
        $('#agentIntroduction').summernote('code', data.agentIntroduction || '');
        $('#usageScenarios').summernote('code', data.usageScenarios || '');
    } else {
        // 降级处理：直接设置textarea的值
        const agentIntroEl = document.getElementById('agentIntroduction');
        const usageScenariosEl = document.getElementById('usageScenarios');

        if (agentIntroEl) agentIntroEl.value = data.agentIntroduction || '';
        if (usageScenariosEl) usageScenariosEl.value = data.usageScenarios || '';
    }
    
    // 截图
    console.log('加载截图数据:', data.screenshotUrls);
    if (data.screenshotUrls && data.screenshotUrls.length > 0) {
        const publicUrls = data.screenshotUrls.map(url => {
            // 如果是私有预览URL，转换为公开预览URL
            if (url.includes('/api/v1/files/') && url.includes('/preview') && !url.includes('/public/')) {
                const fileIdMatch = url.match(/\/api\/v1\/files\/(\d+)\/preview/);
                if (fileIdMatch) {
                    return `/api/v1/files/public/${fileIdMatch[1]}/preview`;
                }
            }
            return url;
        });

        console.log('转换后的公开URL:', publicUrls);
        publicUrls.forEach(url => {
            addScreenshotPreview(url, '截图');
        });
        SupplementPage.uploadedFiles = [...publicUrls];
        console.log('设置uploadedFiles:', SupplementPage.uploadedFiles);
    } else {
        console.log('没有截图数据，清空uploadedFiles');
        SupplementPage.uploadedFiles = [];
    }
    
    // 重置脏标记
    SupplementPage.isDirty = false;
}

/**
 * 填充统计数据
 */
function fillStatisticsData(data) {
    if (!data) return;

    // 版本信息已从基本信息Tab中移除，不再需要渲染
    // if (data.versionInfo) {
    //     renderVersionInfo(data.versionInfo);
    // }

    // 注意：使用统计和调用历史部分已从页面中移除
    // 如果需要这些数据，可以在其他地方使用
}

/**
 * 渲染版本信息
 * @deprecated 基本信息Tab中的版本信息已移除，此函数已弃用
 */
function renderVersionInfo(versionInfo) {
    const container = document.getElementById('versionInfo');
    if (!container) {
        console.warn('版本信息容器未找到 - 此功能已从基本信息Tab中移除');
        return;
    }
    console.warn('renderVersionInfo函数已弃用，版本信息已从基本信息Tab中移除');
    // 保留原有逻辑以防其他地方还在使用
    container.innerHTML = `
        <div class="stat-card">
            <div class="stat-value">${versionInfo.currentVersion || '1.0.0'}</div>
            <div class="stat-label">当前版本</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">${versionInfo.totalVersions || 0}</div>
            <div class="stat-label">历史版本数</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">${versionInfo.lastUpdateTime ? formatDateTime(versionInfo.lastUpdateTime) : '-'}</div>
            <div class="stat-label">最后更新时间</div>
        </div>
    `;
}

/**
 * 渲染使用统计 - 已移除相关页面元素，保留函数以防其他地方需要使用
 */
function renderUsageStatistics(usageStats) {
    // 页面中的使用统计部分已被移除
    console.log('使用统计数据（页面元素已移除）:', usageStats);
    return;
}

/**
 * 渲染调用历史 - 已移除相关页面元素，保留函数以防其他地方需要使用
 */
function renderCallHistory(callHistory) {
    // 页面中的调用历史部分已被移除
    console.log('调用历史数据（页面元素已移除）:', callHistory);
    return;
}

/**
 * 保存补充资料
 */
async function saveSupplement(status) {
    console.log('saveSupplement函数被调用，状态:', status);
    try {
        // 获取富文本编辑器内容
        let agentIntroduction, usageScenarios;

        if (typeof $ !== 'undefined' && $.fn.summernote) {
            // 使用Summernote的code方法获取内容
            agentIntroduction = $('#agentIntroduction').summernote('code');
            usageScenarios = $('#usageScenarios').summernote('code');
        } else {
            // 降级处理：直接获取textarea的值
            const agentIntroEl = document.getElementById('agentIntroduction');
            const usageScenariosEl = document.getElementById('usageScenarios');

            agentIntroduction = agentIntroEl ? agentIntroEl.value : '';
            usageScenarios = usageScenariosEl ? usageScenariosEl.value : '';
        }

        const supplementData = {
            agentId: parseInt(SupplementPage.currentAgentId), // 确保agentId是数字类型
            agentIntroduction: String(agentIntroduction || ''), // 确保是字符串
            usageScenarios: String(usageScenarios || ''), // 确保是字符串
            painPointsSolved: '', // 移除痛点解决方案字段
            screenshotUrls: Array.isArray(SupplementPage.uploadedFiles) ? SupplementPage.uploadedFiles : [], // 确保是数组
            status: parseInt(status) // 确保是数字
        };

        console.log('保存补充资料数据:', supplementData);
        console.log('各字段类型检查:');
        console.log('- agentId:', typeof supplementData.agentId, supplementData.agentId);
        console.log('- agentIntroduction:', typeof supplementData.agentIntroduction, supplementData.agentIntroduction?.length || 0, '字符');
        console.log('- usageScenarios:', typeof supplementData.usageScenarios, supplementData.usageScenarios?.length || 0, '字符');
        console.log('- painPointsSolved:', typeof supplementData.painPointsSolved, supplementData.painPointsSolved?.length || 0, '字符');
        console.log('- screenshotUrls:', typeof supplementData.screenshotUrls, Array.isArray(supplementData.screenshotUrls), supplementData.screenshotUrls);
        console.log('- status:', typeof supplementData.status, supplementData.status);

        // 检查认证token
        const token = localStorage.getItem('token') || localStorage.getItem('authToken') || sessionStorage.getItem('token');
        console.log('当前认证token:', token ? `${token.substring(0, 10)}...` : '未找到token');

        // 打印请求数据，用于调试
        console.log('发送的数据:', JSON.stringify(supplementData, null, 2));

        // 使用本地API调用函数，直接发送数据对象
        const response = await fetch('/api/v1/agent-supplement/save', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('token') || localStorage.getItem('authToken') || sessionStorage.getItem('token')}`
            },
            body: JSON.stringify(supplementData)
        }).then(res => res.json());
        
        if (response.code === 200) {
            SupplementPage.isDirty = false;

            if (status === 1) {
                showToast('保存成功', 'success');
            } else {
                // 提交成功后，调用申请发布API
                await submitApplyPublishRequest();
            }
        } else {
            showToast('保存失败: ' + response.message, 'error');
        }
    } catch (error) {
        console.error('保存失败:', error);
        showToast('保存失败: ' + error.message, 'error');
    }
}

/**
 * 提交申请发布请求
 */
async function submitApplyPublishRequest() {
    try {
        const agentId = SupplementPage.currentAgentId;
        if (!agentId) {
            throw new Error('Agent ID不能为空');
        }

        // 获取版本信息（如果有选中的版本）
        const versionId = sessionStorage.getItem('selectedVersionId');
        const versionNumber = sessionStorage.getItem('selectedVersionNumber');

        // 如果没有选中版本，获取当前版本
        let finalVersionId = versionId;
        let finalVersionNumber = versionNumber;

        if (!finalVersionId) {
            // 获取当前版本信息
            const versionResponse = await fetch(`/api/v1/agents/${agentId}/versions`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('token') || localStorage.getItem('authToken') || sessionStorage.getItem('token')}`
                }
            }).then(res => res.json());

            if (versionResponse.code === 200) {
                const versions = versionResponse.data || [];
                const currentVersion = versions.find(v => v.isCurrent === 1);
                if (currentVersion) {
                    finalVersionId = currentVersion.id;
                    finalVersionNumber = currentVersion.versionNumber;
                }
            }
        }

        if (!finalVersionId) {
            throw new Error('未找到可发布的版本');
        }

        // 调用申请发布API
        const publishResponse = await fetch(`/api/v1/agent-approval/submit/${agentId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('token') || localStorage.getItem('authToken') || sessionStorage.getItem('token')}`
            },
            body: JSON.stringify({
                changeLog: '通过补充资料页面提交申请发布',
                versionId: finalVersionId,
                versionNumber: finalVersionNumber
            })
        }).then(res => res.json());

        if (publishResponse.code === 200) {
            showToast('提交成功，已发送给管理员审核', 'success');

            // 清除版本相关的sessionStorage
            sessionStorage.removeItem('selectedVersionId');
            sessionStorage.removeItem('selectedVersionNumber');

            // 提交成功后跳转到审批列表页面（不带时间参数）
            setTimeout(() => {
                // 设置标记，表示是从补充资料提交过来的
                sessionStorage.setItem('fromSupplementSubmit', 'true');

                // 清除可能的查询状态，避免时间回显问题
                if (typeof ApprovalListState !== 'undefined' && ApprovalListState.clearSearchState) {
                    ApprovalListState.clearSearchState();
                }
                loadPage('agent-approval-list');
            }, 1500);
        } else {
            throw new Error(publishResponse.message || '申请发布失败');
        }
    } catch (error) {
        console.error('申请发布失败:', error);
        showToast('申请发布失败: ' + error.message, 'error');
    }
}

/**
 * 刷新预览内容
 */
function refreshPreview() {
    const previewContent = generatePreviewContent();
    const previewContentEl = document.getElementById('previewContent');

    if (previewContentEl) {
        previewContentEl.innerHTML = previewContent;
    }

    // 切换到预览Tab
    const previewTab = document.getElementById('preview-tab');
    if (previewTab) {
        const tab = new bootstrap.Tab(previewTab);
        tab.show();
    }
}

/**
 * 显示预览模态框（保留原功能）
 */
function showPreview() {
    const previewContent = generatePreviewContent();
    const previewContentEl = document.getElementById('previewContent');
    const previewModalEl = document.getElementById('previewModal');

    if (previewContentEl) {
        previewContentEl.innerHTML = previewContent;
    }

    if (previewModalEl) {
        new bootstrap.Modal(previewModalEl).show();
    }
}

/**
 * 生成预览内容
 */
function generatePreviewContent() {
    // 获取富文本编辑器内容
    let agentIntroduction, usageScenarios;

    if (typeof $ !== 'undefined' && $.fn.summernote) {
        // 使用Summernote的code方法获取内容
        agentIntroduction = $('#agentIntroduction').summernote('code');
        usageScenarios = $('#usageScenarios').summernote('code');
    } else {
        // 降级处理：直接获取textarea的值
        const agentIntroEl = document.getElementById('agentIntroduction');
        const usageScenariosEl = document.getElementById('usageScenarios');

        agentIntroduction = agentIntroEl ? agentIntroEl.value : '';
        usageScenarios = usageScenariosEl ? usageScenariosEl.value : '';
    }

    return `
        <div class="preview-section">
            <h5><i class="bi bi-file-text"></i> Agent简介</h5>
            <div class="preview-content">${agentIntroduction || '<p class="text-muted">暂无内容</p>'}</div>
        </div>

        <div class="preview-section mt-4">
            <h5><i class="bi bi-lightbulb"></i> 使用场景描述</h5>
            <div class="preview-content">${usageScenarios || '<p class="text-muted">暂无内容</p>'}</div>
        </div>

        <div class="preview-section mt-4">
            <h5><i class="bi bi-images"></i> Chrome插件使用效果截图</h5>
            <div class="preview-content">
                ${SupplementPage.uploadedFiles.length > 0 ?
                    SupplementPage.uploadedFiles.map(url => `<img src="${url}" class="img-thumbnail me-2 mb-2" style="max-width: 200px;">`).join('') :
                    '<p class="text-muted">暂无截图</p>'
                }
            </div>
        </div>
    `;
}

/**
 * 加载审批历史
 */
async function loadApprovalHistory(agentId) {
    try {
        console.log('开始加载审批历史，Agent ID:', agentId);

        const response = await localApiCall(`/api/v1/agent-approval/history/${agentId}`);

        if (response.code === 200) {
            console.log('审批历史数据:', response.data);
            renderApprovalHistory(response.data);
        } else {
            console.error('获取审批历史失败:', response);
            console.log('使用模拟审批历史数据');
            renderApprovalHistory(getMockApprovalHistory());
        }
    } catch (error) {
        console.error('加载审批历史失败:', error);
        console.log('使用模拟审批历史数据');
        renderApprovalHistory(getMockApprovalHistory());
    }
}

/**
 * 渲染审批历史 - 与Agent详情页面保持一致的现代化时间线样式
 */
function renderApprovalHistory(data) {
    const container = document.getElementById('approvalHistory');
    if (!container) {
        console.warn('审批历史容器未找到');
        return;
    }

    if (!data || data.length === 0) {
        container.innerHTML = `
            <div class="text-center py-4">
                <i class="bi bi-clock-history" style="font-size: 3rem; color: #dee2e6;"></i>
                <p class="text-muted mt-2">暂无审批历史</p>
            </div>
        `;
        return;
    }

    let html = '<div class="modern-timeline">';

    data.forEach((item, index) => {
        const statusClass = item.approvalStatus === 2 ? 'success' :
                           item.approvalStatus === 3 ? 'danger' : 'warning';
        const statusText = item.approvalStatus === 2 ? '审批通过' :
                          item.approvalStatus === 3 ? '审批不通过' : '审批中';
        const iconClass = item.approvalStatus === 2 ? 'bi-check-circle' :
                         item.approvalStatus === 3 ? 'bi-x-circle' : 'bi-clock';

        html += `
            <div class="timeline-item">
                <div class="timeline-marker timeline-marker-${statusClass}">
                    <i class="bi ${iconClass}"></i>
                </div>
                <div class="timeline-content">
                    <div class="timeline-header">
                        <h6 class="timeline-title">${statusText}</h6>
                        <span class="timeline-badge timeline-badge-${statusClass}">${statusText}</span>
                    </div>
                    <div class="timeline-body">
                        <p class="timeline-opinion">${item.approvalOpinion || '无审批意见'}</p>
                        <div class="timeline-meta">
                            ${item.versionNumber ? `
                                <span class="timeline-version">
                                    <i class="bi bi-tag"></i> 版本: ${item.versionNumber}
                                </span>
                            ` : ''}
                            <span class="timeline-user">
                                <i class="bi bi-person"></i> ${item.approverName || '系统'}
                            </span>
                            <span class="timeline-time">
                                <i class="bi bi-clock"></i> ${formatDateTime(item.approvalTime || item.createdTime)}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });

    html += '</div>';

    // 添加现代化时间线样式
    html = `
        <style>
            .modern-timeline {
                position: relative;
                padding-left: 40px;
            }
            .modern-timeline::before {
                content: '';
                position: absolute;
                left: 20px;
                top: 0;
                bottom: 0;
                width: 3px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                border-radius: 2px;
            }
            .timeline-item {
                position: relative;
                margin-bottom: 30px;
            }
            .timeline-marker {
                position: absolute;
                left: -28px;
                top: 8px;
                width: 36px;
                height: 36px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
                font-size: 14px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                z-index: 2;
            }
            .timeline-marker-success {
                background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            }
            .timeline-marker-danger {
                background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
            }
            .timeline-marker-warning {
                background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
            }
            .timeline-content {
                background: white;
                border-radius: 15px;
                padding: 20px;
                box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
                border: 1px solid #f0f0f0;
                transition: all 0.3s ease;
            }
            .timeline-content:hover {
                transform: translateY(-2px);
                box-shadow: 0 12px 35px rgba(0, 0, 0, 0.12);
            }
            .timeline-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 15px;
                padding-bottom: 10px;
                border-bottom: 1px solid #f0f0f0;
            }
            .timeline-title {
                margin: 0;
                color: #2c3e50;
                font-weight: 600;
                flex: 1;
            }
            .timeline-badge {
                padding: 4px 12px;
                border-radius: 20px;
                font-size: 0.75rem;
                font-weight: 600;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }
            .timeline-badge-success {
                background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
                color: #155724;
            }
            .timeline-badge-danger {
                background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
                color: #721c24;
            }
            .timeline-badge-warning {
                background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
                color: #856404;
            }
            .timeline-body {
                color: #6c757d;
            }
            .timeline-opinion {
                margin-bottom: 15px;
                line-height: 1.6;
                color: #495057;
            }
            .timeline-meta {
                display: flex;
                gap: 20px;
                font-size: 0.85rem;
                color: #6c757d;
                flex-wrap: wrap;
            }
            .timeline-version {
                background: #e3f2fd;
                color: #1976d2;
                padding: 2px 8px;
                border-radius: 12px;
                font-size: 0.75rem;
                font-weight: 500;
                display: flex;
                align-items: center;
                gap: 5px;
            }
            .timeline-user, .timeline-time {
                display: flex;
                align-items: center;
                gap: 5px;
            }
            .timeline-user i, .timeline-time i {
                color: #667eea;
            }
        </style>
    ` + html;

    container.innerHTML = html;
}

/**
 * 显示审批历史错误信息
 */
function showApprovalHistoryError(message) {
    const container = document.getElementById('approvalHistory');
    if (container) {
        container.innerHTML = `
            <div class="text-center py-4">
                <i class="bi bi-exclamation-triangle" style="font-size: 3rem; color: var(--danger-color);"></i>
                <div class="mt-2 text-danger">${message}</div>
            </div>
        `;
    }
}

// getStatusClass和getStatusText函数已移除，新的实现直接在renderApprovalHistory中处理状态

/**
 * 获取当前Agent ID
 */
function getCurrentAgentId() {
    // 优先从sessionStorage获取，然后是全局变量，最后是localStorage
    const agentId = sessionStorage.getItem('currentAgentId') ||
                   window.currentAgentId ||
                   localStorage.getItem('currentAgentId');
    return agentId ? parseInt(agentId) : null;
}

/**
 * 加载版本历史
 */
async function loadVersionHistory() {
    try {
        console.log('开始加载版本历史，Agent ID:', SupplementPage.currentAgentId);

        const response = await localApiCall(`/api/v1/agents/${SupplementPage.currentAgentId}/versions`);

        if (response.code === 200) {
            console.log('版本历史数据:', response.data);
            renderVersionHistory(response.data);
        } else {
            console.error('获取版本历史失败:', response);
            console.log('使用模拟版本历史数据');
            renderVersionHistory(getMockVersionHistory());
        }
    } catch (error) {
        console.error('加载版本历史失败:', error);
        console.log('使用模拟版本历史数据');
        renderVersionHistory(getMockVersionHistory());
    }
}

/**
 * 渲染版本历史 - 与Agent详情页面保持一致的简化卡片布局
 */
function renderVersionHistory(versionData) {
    const container = document.getElementById('versionHistoryInfo');
    if (!container) {
        console.warn('版本历史容器未找到');
        return;
    }

    if (!versionData || versionData.length === 0) {
        container.innerHTML = `
            <div class="text-center py-4">
                <i class="bi bi-clock-history" style="font-size: 3rem; color: #dee2e6;"></i>
                <p class="text-muted mt-2">暂无版本历史</p>
            </div>
        `;
        return;
    }

    let html = '';
    versionData.forEach((version, index) => {
        const statusClass = version.approvalStatus === 2 ? 'approved' :
                           version.approvalStatus === 1 ? 'pending' : 'pending';

        html += `
            <div class="version-item ${statusClass}">
                <div class="version-header">
                    <div class="version-number">${version.versionNumber || 'v1.0.0'}</div>
                    <div class="version-status ${statusClass}">${version.approvalStatusName || '未知'}</div>
                </div>
                <div class="version-meta">
                    <div class="meta-item">
                        <i class="bi bi-calendar"></i>
                        <span>创建时间: ${formatDateTime(version.createdTime)}</span>
                    </div>
                    ${version.publishedTime ? `
                        <div class="meta-item">
                            <i class="bi bi-calendar-check"></i>
                            <span>发布时间: ${formatDateTime(version.publishedTime)}</span>
                        </div>
                    ` : ''}
                    <div class="meta-item">
                        <i class="bi bi-person"></i>
                        <span>创建者: ${version.creatorName || '未知'}</span>
                    </div>
                    ${version.approverName ? `
                        <div class="meta-item">
                            <i class="bi bi-person-check"></i>
                            <span>审批人: ${version.approverName}</span>
                        </div>
                    ` : ''}
                    ${version.approvalTime ? `
                        <div class="meta-item">
                            <i class="bi bi-clock"></i>
                            <span>审批时间: ${formatDateTime(version.approvalTime)}</span>
                        </div>
                    ` : ''}
                </div>
                ${version.changeLog ? `
                    <div class="version-changelog">
                        <div class="version-changelog-title">版本说明</div>
                        <div class="version-changelog-content">${version.changeLog}</div>
                    </div>
                ` : ''}
            </div>
        `;
    });

    container.innerHTML = html;
}

/**
 * 填充版本信息统计
 */
function fillVersionInfo(statisticsData) {
    const container = document.getElementById('versionInfo');
    if (!container) {
        console.warn('版本信息容器未找到');
        return;
    }

    // 默认统计数据
    const defaultStats = {
        totalVersions: 0,
        publishedVersions: 0,
        pendingVersions: 0,
        currentVersion: 'v1.0.0'
    };

    const stats = statisticsData || defaultStats;

    const versionInfoHtml = `
        <div class="stat-card">
            <div class="stat-value">${stats.currentVersion || 'v1.0.0'}</div>
            <div class="stat-label">当前版本</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">${stats.totalVersions || 0}</div>
            <div class="stat-label">总版本数</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">${stats.publishedVersions || 0}</div>
            <div class="stat-label">已发布版本</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">${stats.pendingVersions || 0}</div>
            <div class="stat-label">待审批版本</div>
        </div>
    `;

    container.innerHTML = versionInfoHtml;
}

/**
 * 显示版本历史错误信息
 */
function showVersionHistoryError(message) {
    const container = document.getElementById('versionHistoryInfo');
    if (container) {
        container.innerHTML = `
            <div class="text-center py-4">
                <i class="bi bi-exclamation-triangle" style="font-size: 3rem; color: var(--danger-color);"></i>
                <div class="mt-2 text-danger">${message}</div>
            </div>
        `;
    }
}

/**
 * 加载截图数据
 * @param {number} agentId Agent ID
 */
async function loadScreenshots(agentId) {
    try {
        console.log('加载截图数据:', agentId);

        // 如果已经有数据，直接使用
        if (window.currentAgentData && window.currentAgentData.usageScenarios) {
            renderScreenshotsOnly(window.currentAgentData.usageScenarios.screenshots || []);
            return;
        }

        // 否则重新加载
        const response = await localApiCall(`/api/v1/agent-approval/detail/${agentId}`);
        if (response.code === 200 && response.data.usageScenarios) {
            renderScreenshotsOnly(response.data.usageScenarios.screenshots || []);
        } else {
            renderScreenshotsOnly([]);
        }
    } catch (error) {
        console.error('加载截图失败:', error);
        renderScreenshotsOnly([]);
    }
}

/**
 * 渲染截图（仅截图Tab使用）
 * @param {Array} screenshots 截图URL数组
 */
function renderScreenshotsOnly(screenshots) {
    // agent-supplement.html的screenshots Tab是用于上传的，不需要渲染已有截图
    // 这个函数在补充资料页面中不执行任何操作
    console.log('补充资料页面的截图Tab用于上传，不渲染已有截图');
}

/**
 * 获取模拟版本历史数据
 */
function getMockVersionHistory() {
    return [
        {
            versionNumber: 'v1.2.0',
            approvalStatus: 2,
            approvalStatusName: '已发布',
            createdTime: '2024-01-20 14:30:00',
            publishedTime: '2024-01-21 09:15:00',
            creatorName: '开发者',
            approverName: '管理员',
            approvalTime: '2024-01-21 09:00:00',
            changeLog: '优化了文档识别算法，提升了识别准确率；修复了已知的bug；增加了新的表单模板支持。'
        },
        {
            versionNumber: 'v1.1.0',
            approvalStatus: 2,
            approvalStatusName: '已发布',
            createdTime: '2024-01-15 10:30:00',
            publishedTime: '2024-01-16 16:20:00',
            creatorName: '开发者',
            approverName: '管理员',
            approvalTime: '2024-01-16 16:00:00',
            changeLog: '增加了Chrome插件支持；优化了用户界面；提升了处理速度。'
        },
        {
            versionNumber: 'v1.0.0',
            approvalStatus: 2,
            approvalStatusName: '已发布',
            createdTime: '2024-01-10 09:00:00',
            publishedTime: '2024-01-12 11:30:00',
            creatorName: '开发者',
            approverName: '管理员',
            approvalTime: '2024-01-12 11:00:00',
            changeLog: '初始版本发布，包含基础的文档识别功能。'
        }
    ];
}

/**
 * 获取模拟审批历史数据
 */
function getMockApprovalHistory() {
    return [
        {
            approvalStatus: 2,
            approvalOpinion: 'Agent功能完善，文档识别准确率高，同意发布到生产环境。建议后续继续优化用户体验。',
            approverName: '系统管理员',
            approvalTime: '2024-01-21 09:15:00',
            versionNumber: 'v1.2.0',
            createdTime: '2024-01-21 09:15:00'
        },
        {
            approvalStatus: 1,
            approvalOpinion: '正在审核中，请耐心等待审批结果。',
            approverName: '审核专员',
            approvalTime: '2024-01-20 16:30:00',
            versionNumber: 'v1.2.0',
            createdTime: '2024-01-20 16:30:00'
        },
        {
            approvalStatus: 2,
            approvalOpinion: 'Chrome插件功能测试通过，界面友好，性能良好，批准发布。',
            approverName: '技术主管',
            approvalTime: '2024-01-16 16:20:00',
            versionNumber: 'v1.1.0',
            createdTime: '2024-01-16 16:20:00'
        },
        {
            approvalStatus: 3,
            approvalOpinion: '初始版本存在一些小问题，需要修复后重新提交审批。主要问题：1. 识别准确率需要提升；2. 错误处理机制需要完善。',
            approverName: '质量保证',
            approvalTime: '2024-01-11 14:20:00',
            versionNumber: 'v1.0.0',
            createdTime: '2024-01-11 14:20:00'
        },
        {
            approvalStatus: 2,
            approvalOpinion: '经过修复后，初始版本功能基本满足要求，同意发布。',
            approverName: '项目经理',
            approvalTime: '2024-01-12 11:30:00',
            versionNumber: 'v1.0.0',
            createdTime: '2024-01-12 11:30:00'
        }
    ];
}

// formatDateTime函数在admin-common.js中已定义，无需重复定义

/**
 * 测试版本历史功能（调试用）
 */
function testVersionHistory() {
    console.log('测试版本历史功能');
    const mockData = getMockVersionHistory();
    renderVersionHistory(mockData);
}

/**
 * 测试审批历史功能（调试用）
 */
function testApprovalHistory() {
    console.log('测试审批历史功能');
    const mockData = getMockApprovalHistory();
    renderApprovalHistory(mockData);
}

/**
 * 强制加载审批历史（调试用）
 */
function forceLoadApprovalHistory() {
    console.log('强制加载审批历史');
    if (SupplementPage.currentAgentId) {
        loadApprovalHistory(SupplementPage.currentAgentId);
    } else {
        console.log('Agent ID不存在，使用模拟数据');
        testApprovalHistory();
    }
}

// 在控制台中可以调用以下函数来测试功能：
// testVersionHistory() - 测试版本历史
// testApprovalHistory() - 测试审批历史
// forceLoadApprovalHistory() - 强制加载审批历史
