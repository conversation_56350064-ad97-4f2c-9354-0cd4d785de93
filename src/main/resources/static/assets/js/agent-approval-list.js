/**
 * Agent审核列表页面JS (页面片段版本)
 */

// Agent审批列表页面命名空间 - 避免函数冲突
const AgentApprovalListPage = {
    // 页面状态管理
    state: {
        pageId: null,
        generatePageId: function() {
            this.pageId = 'agent-approval-list-' + Date.now();
            return this.pageId;
        },
        isCurrentPage: function() {
            return document.getElementById('agentName') !== null &&
                   document.getElementById('tableBody') !== null;
        }
    },

    // 防止函数冲突的标识
    namespace: 'AgentApprovalList',

    // 清理函数 - 页面切换时调用
    cleanup: function() {
        console.log('清理Agent审批列表页面资源');
        this.state.pageId = null;
    }
};

// 全局错误处理 - 防止页面切换时的DOM操作错误
window.addEventListener('error', function(event) {
    if (event.error && event.error.message &&
        event.error.message.includes('Cannot set properties of null')) {
        console.warn('检测到DOM操作错误，可能是页面切换导致的:', event.error.message);
        event.preventDefault(); // 阻止错误冒泡
        return false;
    }
});

// 页面状态管理 - 使用命名空间中的state
const ApprovalListState = AgentApprovalListPage.state;

// 扩展状态管理功能
Object.assign(ApprovalListState, {

    // 保存查询条件到localStorage
    saveSearchState: function() {
        // 安全获取表单元素值
        const getElementValue = (id) => {
            const element = document.getElementById(id);
            return element ? element.value || '' : '';
        };

        const searchState = {
            agentName: getElementValue('agentName'),
            agentCode: getElementValue('agentCode'),
            status: getElementValue('status'),
            categoryId: getElementValue('categoryId'),
            startTime: getElementValue('startTime'),
            endTime: getElementValue('endTime'),
            pageSize: getElementValue('pageSize') || 10
        };
        localStorage.setItem('agentApprovalListState', JSON.stringify(searchState));
        console.log('保存查询状态:', searchState);
    },

    // 从localStorage恢复查询条件
    restoreSearchState: function() {
        const savedState = localStorage.getItem('agentApprovalListState');
        if (savedState) {
            try {
                const searchState = JSON.parse(savedState);
                console.log('恢复查询状态:', searchState);

                // 安全设置表单值
                const setElementValue = (id, value) => {
                    const element = document.getElementById(id);
                    if (element) element.value = value || '';
                };

                setElementValue('agentName', searchState.agentName);
                setElementValue('agentCode', searchState.agentCode);
                setElementValue('status', searchState.status);
                setElementValue('categoryId', searchState.categoryId);
                setElementValue('startTime', searchState.startTime);
                setElementValue('endTime', searchState.endTime);
                setElementValue('pageSize', searchState.pageSize || 10);

                return true; // 表示有保存的状态
            } catch (e) {
                console.warn('恢复查询状态失败:', e);
            }
        }
        return false; // 表示没有保存的状态
    },

    // 清除保存的状态
    clearSearchState: function() {
        localStorage.removeItem('agentApprovalListState');
    }
});

// 页面初始化函数
function initAgentApprovalListPage() {
    console.log(`[${AgentApprovalListPage.namespace}] 初始化Agent审核列表页面`);

    // 清理之前的状态
    AgentApprovalListPage.cleanup();

    // 生成新的页面ID
    ApprovalListState.generatePageId();
    console.log(`[${AgentApprovalListPage.namespace}] 页面ID:`, ApprovalListState.pageId);

    // 确保DOM完全加载后再初始化
    setTimeout(() => {
        // 检查页面是否仍然是当前页面
        if (!ApprovalListState.isCurrentPage()) {
            console.log(`[${AgentApprovalListPage.namespace}] 页面已切换，取消初始化`);
            return;
        }

        // 初始化页面
        initApprovalListPage();

        // 绑定事件
        bindApprovalListEvents();

        // 加载数据
        loadApprovalListData();
    }, 100); // 延迟100ms确保DOM完全渲染
}

/**
 * 初始化页面 - Agent审批列表专用
 */
function initApprovalListPage() {
    // 检查关键DOM元素是否存在
    const categorySelect = document.getElementById('categoryId');
    if (!categorySelect) {
        console.warn('分类选择框元素未找到，延迟重试...');
        setTimeout(initPage, 50);
        return;
    }

    // 加载Agent分类
    loadApprovalListCategories();

    // 尝试恢复保存的查询状态
    const hasRestoredState = ApprovalListState.restoreSearchState();

    // 检查是否是从补充资料页面跳转过来的
    const fromSupplement = sessionStorage.getItem('fromSupplementSubmit');
    if (fromSupplement) {
        // 清除标记
        sessionStorage.removeItem('fromSupplementSubmit');
        // 不设置默认时间，保持空白
    } else if (!hasRestoredState) {
        // 如果没有保存的状态且不是从补充资料页面过来，设置默认日期范围（最近一个月）
        const today = new Date();
        const oneMonthAgo = new Date();
        oneMonthAgo.setMonth(today.getMonth() - 1);

        const startTimeElement = document.getElementById('startTime');
        const endTimeElement = document.getElementById('endTime');
        if (startTimeElement) startTimeElement.value = formatDateForInput(oneMonthAgo);
        if (endTimeElement) endTimeElement.value = formatDateForInput(today);
    }

    // 加载数据
    loadApprovalListData(1);

    // 监听权限系统加载完成事件，重新渲染表格以应用权限控制
    if (window.permissionManager) {
        // 如果权限管理器已经加载，延迟一点时间确保权限已经应用
        setTimeout(() => {
            if (ApprovalListState.isCurrentPage()) {
                console.log('权限系统已加载，重新渲染表格以应用权限控制');
                // 不重新加载数据，只重新渲染当前数据
                const tableBody = document.getElementById('tableBody');
                if (tableBody && window.lastApprovalListData) {
                    renderApprovalListTable(window.lastApprovalListData);
                }
            }
        }, 1000);
    }

    // 添加权限系统加载监听器
    const checkPermissionSystemInterval = setInterval(() => {
        if (window.permissionManager && window.permissionManager.permissions && window.permissionManager.permissions.length > 0) {
            console.log('权限系统加载完成，重新渲染表格');
            clearInterval(checkPermissionSystemInterval);

            // 重新渲染表格以应用权限控制
            if (ApprovalListState.isCurrentPage() && window.lastApprovalListData) {
                renderApprovalListTable(window.lastApprovalListData);
            }
        }
    }, 500); // 每500ms检查一次

    // 10秒后停止检查
    setTimeout(() => {
        clearInterval(checkPermissionSystemInterval);
    }, 10000);
}

/**
 * 绑定事件 - Agent审批列表专用
 */
function bindApprovalListEvents() {
    // 检查页面是否仍然是当前页面
    if (!ApprovalListState.isCurrentPage()) {
        console.log('页面已切换，取消事件绑定');
        return;
    }

    // 安全绑定事件
    const bindEvent = (id, event, handler) => {
        const element = document.getElementById(id);
        if (element) {
            element.addEventListener(event, handler);
            console.log(`成功绑定事件: ${id} -> ${event}`);
        } else {
            console.warn(`找不到元素，跳过事件绑定: ${id}`);
        }
    };

    // 查询按钮点击事件
    bindEvent('searchBtn', 'click', function() {
        // 保存查询状态
        ApprovalListState.saveSearchState();
        loadApprovalListData(1);
    });

    // 重置按钮点击事件
    bindEvent('resetBtn', 'click', function() {
        const searchForm = document.getElementById('searchForm');
        if (searchForm) searchForm.reset();

        // 重置日期范围（最近一个月）
        const today = new Date();
        const oneMonthAgo = new Date();
        oneMonthAgo.setMonth(today.getMonth() - 1);

        const startTimeElement = document.getElementById('startTime');
        const endTimeElement = document.getElementById('endTime');
        if (startTimeElement) startTimeElement.value = formatDateForInput(oneMonthAgo);
        if (endTimeElement) endTimeElement.value = formatDateForInput(today);

        // 清除保存的状态
        ApprovalListState.clearSearchState();

        loadApprovalListData(1);
    });

    // 导出按钮点击事件（仅在按钮存在时绑定）
    const exportBtn = document.getElementById('exportBtn');
    if (exportBtn) {
        bindEvent('exportBtn', 'click', function() {
            exportToExcel();
        });
    }

    // 每页显示条数变化事件
    bindEvent('pageSize', 'change', function() {
        // 保存查询状态
        ApprovalListState.saveSearchState();
        loadApprovalListData(1);
    });
}

/**
 * 加载Agent分类（带重试机制）- Agent审批列表专用
 */
async function loadApprovalListCategories(retryCount = 3) {
    console.log(`[${AgentApprovalListPage.namespace}] 开始加载分类`);

    // 检查页面是否仍然是当前页面
    if (!ApprovalListState.isCurrentPage()) {
        console.log(`[${AgentApprovalListPage.namespace}] 页面已切换，取消加载分类`);
        return;
    }

    const categorySelect = document.getElementById('categoryId');

    if (!categorySelect) {
        console.warn('找不到分类选择框元素');
        return;
    }

    // 显示加载状态（再次检查页面状态）
    if (ApprovalListState.isCurrentPage()) {
        categorySelect.innerHTML = '<option value="">加载中...</option>';
    } else {
        console.log('页面已切换，取消分类加载状态显示');
        return;
    }

    for (let attempt = 1; attempt <= retryCount; attempt++) {
        try {
            // 在每次重试前检查页面状态
            if (!ApprovalListState.isCurrentPage()) {
                console.log('页面已切换，取消分类加载重试');
                return;
            }

            console.log(`尝试加载Agent分类 (第${attempt}次)`);
            const response = await apiCall('/api/v1/agent-categories/active');

            // 再次检查页面状态（异步操作完成后）
            if (!ApprovalListState.isCurrentPage()) {
                console.log('页面已切换，忽略分类加载结果');
                return;
            }

            // 确保DOM元素仍然存在
            const currentCategorySelect = document.getElementById('categoryId');
            if (!currentCategorySelect) {
                console.log('分类选择框元素已不存在，停止操作');
                return;
            }

            if (response.code === 200 && response.data) {
                let options = '<option value="">全部分类</option>';

                response.data.forEach(category => {
                    options += `<option value="${category.id}">${category.categoryName}</option>`;
                });

                currentCategorySelect.innerHTML = options;
                console.log(`Agent分类加载成功，共${response.data.length}个分类`);
                return; // 成功加载，退出重试循环
            } else {
                console.warn(`加载分类失败 (第${attempt}次):`, response.message);
                if (attempt === retryCount && currentCategorySelect) {
                    // 最后一次尝试失败，设置默认选项
                    currentCategorySelect.innerHTML = '<option value="">全部分类</option>';
                }
            }
        } catch (error) {
            console.error(`加载分类失败 (第${attempt}次):`, error);

            // 检查页面状态
            if (!ApprovalListState.isCurrentPage()) {
                console.log('页面已切换，停止分类加载重试');
                return;
            }

            const currentCategorySelect = document.getElementById('categoryId');
            if (attempt === retryCount && currentCategorySelect) {
                // 最后一次尝试失败，设置默认选项
                currentCategorySelect.innerHTML = '<option value="">全部分类</option>';
                // 显示用户友好的错误提示
                showToast('分类加载失败，请刷新页面重试', 'warning');
            } else if (attempt < retryCount) {
                // 等待一段时间后重试
                await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
            }
        }
    }
}

/**
 * 加载数据 - Agent审批列表专用
 * @param {number} page 页码，默认为1
 */
async function loadApprovalListData(page = 1) {
    console.log(`[${AgentApprovalListPage.namespace}] 开始加载数据，页码: ${page}`);

    // 检查页面是否仍然是当前页面
    if (!ApprovalListState.isCurrentPage()) {
        console.log(`[${AgentApprovalListPage.namespace}] 页面已切换，取消数据加载`);
        return;
    }

    // 检查必要的DOM元素是否存在
    const tableBody = document.getElementById('tableBody');
    if (!tableBody) {
        console.warn('找不到表格主体元素，可能页面还未加载完成');
        return;
    }

    // 显示加载状态（再次检查页面状态）
    if (ApprovalListState.isCurrentPage()) {
        tableBody.innerHTML = '<tr><td colspan="9" class="text-center py-4"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">加载中...</span></div></td></tr>';
    } else {
        console.log('页面已切换，取消数据加载状态显示');
        return;
    }

    // 安全获取表单元素值
    const getElementValue = (id) => {
        const element = document.getElementById(id);
        return element ? element.value || '' : '';
    };

    // 构建查询参数
    const params = new URLSearchParams({
        agentName: getElementValue('agentName'),
        agentCode: getElementValue('agentCode'),
        approvalStatus: getElementValue('status'),
        categoryId: getElementValue('categoryId'),
        startTime: getElementValue('startTime'),
        endTime: getElementValue('endTime'),
        current: page,
        size: getElementValue('pageSize') || 10
    });

    try {
        const response = await apiCall(`/api/v1/agent-approval/list?${params}`);

        // 检查页面状态（异步操作完成后）
        if (!ApprovalListState.isCurrentPage()) {
            console.log('页面已切换，忽略数据加载结果');
            return;
        }

        // 再次检查DOM元素是否存在
        const currentTableBody = document.getElementById('tableBody');
        if (!currentTableBody) {
            console.log('表格主体元素已不存在，停止操作');
            return;
        }

        if (response.code === 200) {
            // 保存数据用于权限系统加载后重新渲染
            window.lastApprovalListData = response.data;
            renderApprovalListTable(response.data);
            renderApprovalListPagination(response.data);
        } else {
            currentTableBody.innerHTML = '<tr><td colspan="9" class="text-center text-danger">加载失败: ' + response.message + '</td></tr>';
        }
    } catch (error) {
        console.error('加载数据失败:', error);

        // 检查页面状态和DOM元素
        if (ApprovalListState.isCurrentPage()) {
            const currentTableBody = document.getElementById('tableBody');
            if (currentTableBody) {
                currentTableBody.innerHTML = '<tr><td colspan="9" class="text-center text-danger">加载失败: ' + error.message + '</td></tr>';
            }
        }
    }
}

/**
 * 渲染表格 - Agent审批列表专用
 * @param {object} data 分页数据
 */
function renderApprovalListTable(data) {
    // 检查页面是否仍然是当前页面
    if (!ApprovalListState.isCurrentPage()) {
        console.log('页面已切换，取消表格渲染');
        return;
    }

    const tableBody = document.getElementById('tableBody');
    const totalCount = document.getElementById('totalCount');

    if (!tableBody) {
        console.warn('找不到表格主体元素');
        return;
    }

    if (!data.records || data.records.length === 0) {
        tableBody.innerHTML = '<tr><td colspan="9" class="text-center">暂无数据</td></tr>';
        if (totalCount) totalCount.textContent = '0';
        return;
    }
    
    let html = '';
    
    data.records.forEach(item => {
        // 状态样式
        let statusClass = '';
        let statusText = '';
        
        switch (item.approvalStatus) {
            case 1:
                statusClass = 'status-pending';
                statusText = '审批中';
                break;
            case 2:
                statusClass = 'status-approved';
                statusText = '已通过';
                break;
            case 3:
                statusClass = 'status-rejected';
                statusText = '未通过';
                break;
            default:
                statusText = '未知';
        }
        
        // 操作按钮 - 移除详情按钮
        let actionButtons = '';

        // 添加补充资料按钮（客户端功能）
        actionButtons += `
            <button type="button" class="btn btn-sm btn-outline-info action-btn" onclick="editSupplement(${item.id})" title="补充资料">
                <i class="bi bi-file-earmark-plus"></i> 补充资料
            </button>
        `;

        // 只有审批中的Agent且用户有审批权限才显示审批按钮（管理员功能）
        const hasPermission = hasApprovalPermission();
        if (item.approvalStatus === 1 && hasPermission) {
            actionButtons += `
                <button type="button" class="btn btn-sm btn-outline-success action-btn" onclick="approveAgent(${item.id})">
                    <i class="bi bi-check-circle"></i> 审批
                </button>
            `;
        }

        // 调试日志
        if (item.approvalStatus === 1) {
            console.log(`Agent ${item.agentName} 审批按钮权限检查: hasPermission=${hasPermission}`);
        }
        
        // 处理描述文本，超过30字符显示省略号和气泡提示
        const description = item.description || '-';
        const descriptionDisplay = description.length > 30
            ? `<span class="description-tooltip" title="${description.replace(/"/g, '&quot;')}" data-bs-toggle="tooltip" data-bs-placement="top">${description.substring(0, 30)}...</span>`
            : description;

        // 处理使用场景文本，超过25字符显示省略号和气泡提示
        const purpose = item.purpose || '-';
        const purposeDisplay = purpose.length > 25
            ? `<span class="description-tooltip" title="${purpose.replace(/"/g, '&quot;')}" data-bs-toggle="tooltip" data-bs-placement="top">${purpose.substring(0, 25)}...</span>`
            : purpose;

        html += `
            <tr>
                <td>
                    <div class="agent-info">
                        <div>
                            <a href="javascript:void(0);" class="agent-name-link" onclick="viewDetail(${item.id})" title="点击查看详情">
                                ${item.agentName || '-'}
                            </a>
                        </div>
                        <div class="agent-code">${item.agentCode || '-'}</div>
                    </div>
                </td>
                <td>${descriptionDisplay}</td>
                <td><span class="status-badge ${statusClass}">${statusText}</span></td>
                <td>${item.categoryName || '-'}</td>
                <td>
                    <span class="version-badge">
                        ${item.version || 'v1.0.0'}
                    </span>
                </td>
                <td>${purposeDisplay}</td>
                <td>${item.subscriptionCount || 0}</td>
                <td>${item.submitTime || '-'}</td>
                <td>${actionButtons}</td>
            </tr>
        `;
    });
    
    // 最后检查一次页面状态再更新DOM
    if (ApprovalListState.isCurrentPage() && document.getElementById('tableBody')) {
        tableBody.innerHTML = html;
        if (totalCount) totalCount.textContent = data.total;

        // 初始化Bootstrap Tooltip
        setTimeout(() => {
            // 再次检查页面状态
            if (ApprovalListState.isCurrentPage()) {
                const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
                tooltipTriggerList.map(function (tooltipTriggerEl) {
                    return new bootstrap.Tooltip(tooltipTriggerEl);
                });
            }
        }, 100);
    }
}

/**
 * 渲染分页 - Agent审批列表专用
 * @param {object} data 分页数据
 */
function renderApprovalListPagination(data) {
    // 检查页面是否仍然是当前页面
    if (!ApprovalListState.isCurrentPage()) {
        console.log('页面已切换，取消分页渲染');
        return;
    }

    const paginationElement = document.querySelector('.pagination');
    if (!paginationElement) {
        console.warn('找不到分页元素');
        return;
    }

    if (!data || data.total === 0) {
        paginationElement.innerHTML = '';
        return;
    }
    
    const current = data.current;
    const pages = data.pages;
    
    let html = '';
    
    // 上一页
    html += `
        <li class="page-item ${current === 1 ? 'disabled' : ''}">
            <a class="page-link" href="javascript:void(0);" onclick="loadApprovalListData(${current - 1})" aria-label="上一页">
                <span aria-hidden="true">&laquo;</span>
            </a>
        </li>
    `;

    // 页码
    if (pages <= 7) {
        // 页数较少，全部显示
        for (let i = 1; i <= pages; i++) {
            html += `
                <li class="page-item ${i === current ? 'active' : ''}">
                    <a class="page-link" href="javascript:void(0);" onclick="loadApprovalListData(${i})">${i}</a>
                </li>
            `;
        }
    } else {
        // 页数较多，显示部分页码
        html += `
            <li class="page-item ${current === 1 ? 'active' : ''}">
                <a class="page-link" href="javascript:void(0);" onclick="loadApprovalListData(1)">1</a>
            </li>
        `;
        
        if (current > 4) {
            html += '<li class="page-item disabled"><span class="page-link">...</span></li>';
        }
        
        let start = Math.max(2, current - 1);
        let end = Math.min(pages - 1, current + 1);
        
        for (let i = start; i <= end; i++) {
            html += `
                <li class="page-item ${i === current ? 'active' : ''}">
                    <a class="page-link" href="javascript:void(0);" onclick="loadApprovalListData(${i})">${i}</a>
                </li>
            `;
        }

        if (current < pages - 3) {
            html += '<li class="page-item disabled"><span class="page-link">...</span></li>';
        }

        if (pages > 1) {
            html += `
                <li class="page-item ${current === pages ? 'active' : ''}">
                    <a class="page-link" href="javascript:void(0);" onclick="loadApprovalListData(${pages})">${pages}</a>
                </li>
            `;
        }
    }

    // 下一页
    html += `
        <li class="page-item ${current === pages ? 'disabled' : ''}">
            <a class="page-link" href="javascript:void(0);" onclick="loadApprovalListData(${current + 1})" aria-label="下一页">
                <span aria-hidden="true">&raquo;</span>
            </a>
        </li>
    `;
    
    // 最后检查一次页面状态再更新DOM
    if (ApprovalListState.isCurrentPage() && document.querySelector('.pagination')) {
        paginationElement.innerHTML = html;
    }
}

/**
 * 查看Agent详情
 * @param {number} id Agent ID
 */
function viewDetail(id) {
    // 使用全局的showAgentDetail函数
    if (typeof showAgentDetail === 'function') {
        showAgentDetail(id, 'agent-approval-list');
    } else {
        // 降级方案
        setCurrentAgentId(id);
        sessionStorage.setItem('agentDetailSource', 'agent-approval-list');
        loadPage('agent-detail');
    }
}

/**
 * 审批Agent
 * @param {number} id Agent ID
 */
function approveAgent(id) {
    // 使用全局的showAgentApprovalDetail函数
    if (typeof showAgentApprovalDetail === 'function') {
        showAgentApprovalDetail(id, 'agent-approval-list');
    } else {
        // 降级方案
        setCurrentAgentId(id);
        loadPage('agent-approval-detail');
    }
}

/**
 * 设置当前Agent ID
 * @param {number} agentId Agent ID
 */
function setCurrentAgentId(agentId) {
    window.currentAgentId = agentId;
    localStorage.setItem('currentAgentId', agentId);
}

/**
 * 导出Excel
 */
function exportToExcel() {
    // 构建查询参数
    const params = {
        agentName: document.getElementById('agentName').value,
        agentCode: document.getElementById('agentCode').value,
        status: document.getElementById('status').value,
        categoryId: document.getElementById('categoryId').value,
        startTime: document.getElementById('startTime').value,
        endTime: document.getElementById('endTime').value,
        exportExcel: true
    };
    
    // 构建URL参数
    const queryString = Object.keys(params)
        .filter(key => params[key] !== '' && params[key] !== null && params[key] !== undefined)
        .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
        .join('&');
    
    // 下载Excel
    window.location.href = `/api/v1/agent-approval/export?${queryString}`;
}

/**
 * 编辑Agent补充资料
 * @param {number} agentId Agent ID
 */
function editSupplement(agentId) {
    // 使用全局的showAgentSupplement函数
    if (typeof showAgentSupplement === 'function') {
        showAgentSupplement(agentId, 'agent-approval-list');
    } else {
        // 降级方案
        setCurrentAgentId(agentId);
        sessionStorage.setItem('supplementSource', 'agent-approval-list');
        loadPage('agent-supplement');
    }
}

/**
 * 检查用户是否有审批权限
 * @returns {boolean} 是否有审批权限
 */
function hasApprovalPermission() {
    console.log('=== 开始检查审批权限 ===');

    // 首先检查用户基本信息
    try {
        const userInfo = localStorage.getItem('userInfo');
        console.log('localStorage中的userInfo:', userInfo);
        if (userInfo) {
            const user = JSON.parse(userInfo);
            console.log('解析后的用户信息:', user);
            console.log('用户角色代码:', user.roleCode);
            console.log('用户ID:', user.id);
            console.log('用户名:', user.username);
        }
    } catch (error) {
        console.error('获取用户基本信息失败:', error);
    }

    // 检查权限管理器是否存在
    if (window.permissionManager) {
        console.log('权限管理器存在，检查AGENT_APPROVAL_MANAGE权限');
        console.log('权限管理器对象:', window.permissionManager);
        console.log('当前用户权限列表:', window.permissionManager.permissions);

        // 检查是否包含审批权限
        const hasPermission = window.permissionManager.hasPermission('AGENT_APPROVAL_MANAGE');
        console.log('权限管理器检查结果:', hasPermission);

        // 如果有权限，直接返回
        if (hasPermission) {
            console.log('=== 通过权限管理器验证通过 ===');
            return true;
        }
    } else {
        console.log('权限管理器不存在');
    }

    // 检查菜单权限控制器
    if (window.menuPermissionController) {
        console.log('菜单权限控制器存在，检查AGENT_APPROVAL_MANAGE权限');
        console.log('菜单权限控制器对象:', window.menuPermissionController);

        const hasPermission = window.menuPermissionController.hasPermission('AGENT_APPROVAL_MANAGE');
        console.log('菜单权限控制器检查结果:', hasPermission);

        // 如果有权限，直接返回
        if (hasPermission) {
            console.log('=== 通过菜单权限控制器验证通过 ===');
            return true;
        }
    } else {
        console.log('菜单权限控制器不存在');
    }

    // 如果权限系统未加载，检查全局权限函数
    if (typeof hasPermission === 'function') {
        console.log('全局权限函数存在，检查AGENT_APPROVAL_APPROVE权限');
        const hasPermissionResult = hasPermission('AGENT_APPROVAL_APPROVE');
        console.log('全局权限函数检查结果:', hasPermissionResult);

        // 如果有权限，直接返回
        if (hasPermissionResult) {
            console.log('=== 通过全局权限函数验证通过 ===');
            return true;
        }
    } else {
        console.log('全局权限函数不存在');
    }

    // 最后的备用方案：检查用户角色（从localStorage获取）
    try {
        console.log('使用备用方案：检查用户角色');
        const userInfo = localStorage.getItem('userInfo');
        if (userInfo) {
            const user = JSON.parse(userInfo);
            // 管理员角色有审批权限
            const isAdmin = user.roleCode === 'ADMIN';
            console.log('是否为管理员角色:', isAdmin, '(roleCode:', user.roleCode, ')');

            if (isAdmin) {
                console.log('=== 通过角色验证通过（管理员） ===');
                return true;
            }
        }
    } catch (error) {
        console.error('获取用户信息失败:', error);
    }

    // 默认返回false，确保安全
    console.warn('无法确定用户审批权限，默认拒绝');
    console.log('=== 审批权限检查结束：权限被拒绝 ===');
    return false;
}

/**
 * 调试用户权限信息（调试用）
 */
function debugUserPermissions() {
    console.log('=== 用户权限调试信息 ===');

    // 检查localStorage中的用户信息
    const userInfo = localStorage.getItem('userInfo');
    console.log('localStorage userInfo:', userInfo);

    // 检查权限管理器
    if (window.permissionManager) {
        console.log('权限管理器存在');
        console.log('权限管理器权限列表:', window.permissionManager.permissions);
        console.log('是否有AGENT_APPROVAL_APPROVE权限:', window.permissionManager.hasPermission('AGENT_APPROVAL_APPROVE'));
    } else {
        console.log('权限管理器不存在');
    }

    // 检查菜单权限控制器
    if (window.menuPermissionController) {
        console.log('菜单权限控制器存在');
        console.log('菜单权限控制器权限列表:', window.menuPermissionController.permissions);
        console.log('是否有AGENT_APPROVAL_APPROVE权限:', window.menuPermissionController.hasPermission('AGENT_APPROVAL_APPROVE'));
    } else {
        console.log('菜单权限控制器不存在');
    }

    // 检查全局权限函数
    if (typeof hasPermission === 'function') {
        console.log('全局权限函数存在');
        console.log('是否有AGENT_APPROVAL_APPROVE权限:', hasPermission('AGENT_APPROVAL_APPROVE'));
    } else {
        console.log('全局权限函数不存在');
    }

    console.log('=== 权限调试信息结束 ===');
}

/**
 * 强制刷新权限并重新渲染表格（调试用）
 */
function forceRefreshPermissions() {
    console.log('=== 强制刷新权限 ===');

    // 重新加载权限
    if (window.permissionManager && typeof window.permissionManager.loadPermissions === 'function') {
        console.log('重新加载权限管理器权限');
        window.permissionManager.loadPermissions().then(() => {
            console.log('权限管理器权限重新加载完成');
            if (window.lastApprovalListData) {
                renderApprovalListTable(window.lastApprovalListData);
            }
        });
    }

    // 重新渲染表格
    if (window.lastApprovalListData) {
        console.log('重新渲染表格');
        renderApprovalListTable(window.lastApprovalListData);
    }

    // 显示当前权限检查结果
    const hasPermission = hasApprovalPermission();
    console.log('当前审批权限检查结果:', hasPermission);

    console.log('=== 强制刷新权限完成 ===');
}

// 在控制台中可以调用以下函数进行调试：
// debugUserPermissions() - 查看权限信息
// forceRefreshPermissions() - 强制刷新权限

/**
 * 格式化日期为YYYY-MM-DD格式（用于HTML date input）
 * @param {Date} date 日期对象
 * @returns {string} 格式化后的日期字符串
 */
function formatDateForInput(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
}
