/**
 * 通用剪贴板工具库
 * 解决不同环境下的剪贴板兼容性问题
 * 
 * 支持的环境：
 * - HTTPS环境（现代浏览器）
 * - HTTP环境（降级方案）
 * - 服务器环境（多种降级方案）
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

class ClipboardUtils {
    constructor() {
        this.isSecureContext = window.isSecureContext;
        this.hasClipboardAPI = !!(navigator.clipboard && navigator.clipboard.writeText);
        this.hasExecCommand = document.queryCommandSupported && document.queryCommandSupported('copy');
        
        console.log('📋 剪贴板工具初始化:', {
            isSecureContext: this.isSecureContext,
            hasClipboardAPI: this.hasClipboardAPI,
            hasExecCommand: this.hasExecCommand
        });
    }

    /**
     * 复制文本到剪贴板（主要方法）
     * @param {string} text 要复制的文本
     * @param {string} successMessage 成功提示消息
     * @param {Function} onSuccess 成功回调
     * @param {Function} onError 失败回调
     * @returns {Promise<boolean>} 是否成功
     */
    async copyText(text, successMessage = '内容已复制到剪贴板', onSuccess = null, onError = null) {
        if (!text || typeof text !== 'string') {
            const error = '复制内容不能为空';
            this._handleError(error, onError);
            return false;
        }

        console.log('📋 开始复制文本，长度:', text.length);

        // 方法1: 现代剪贴板API
        if (this.hasClipboardAPI && this.isSecureContext) {
            try {
                await navigator.clipboard.writeText(text);
                console.log('✅ 现代剪贴板API复制成功');
                this._handleSuccess(successMessage, onSuccess);
                return true;
            } catch (error) {
                console.warn('⚠️ 现代剪贴板API失败，尝试降级方案:', error.message);
            }
        }

        // 方法2: execCommand降级方案
        if (this.hasExecCommand) {
            try {
                const success = await this._copyWithExecCommand(text);
                if (success) {
                    console.log('✅ execCommand复制成功');
                    this._handleSuccess(successMessage, onSuccess);
                    return true;
                } else {
                    console.warn('⚠️ execCommand复制失败');
                }
            } catch (error) {
                console.warn('⚠️ execCommand方法异常:', error.message);
            }
        }

        // 方法3: 文本选择降级方案
        try {
            this._selectText(text);
            console.log('📝 已选择文本，请用户手动复制');
            this._handleError('自动复制失败，已为您选中内容，请使用 Ctrl+C 复制', onError);
            return false;
        } catch (error) {
            console.error('❌ 所有复制方案都失败了:', error.message);
            this._handleError('复制失败，请手动选择并复制内容', onError);
            return false;
        }
    }

    /**
     * 使用execCommand复制文本
     * @param {string} text 要复制的文本
     * @returns {Promise<boolean>} 是否成功
     */
    async _copyWithExecCommand(text) {
        return new Promise((resolve) => {
            const textArea = document.createElement('textarea');
            textArea.value = text;
            
            // 设置样式使其不可见但可操作
            textArea.style.position = 'fixed';
            textArea.style.top = '0';
            textArea.style.left = '0';
            textArea.style.width = '2em';
            textArea.style.height = '2em';
            textArea.style.padding = '0';
            textArea.style.border = 'none';
            textArea.style.outline = 'none';
            textArea.style.boxShadow = 'none';
            textArea.style.background = 'transparent';
            textArea.style.opacity = '0';
            textArea.style.pointerEvents = 'none';
            textArea.setAttribute('readonly', '');
            
            try {
                document.body.appendChild(textArea);
                textArea.focus();
                textArea.select();
                textArea.setSelectionRange(0, text.length);
                
                const successful = document.execCommand('copy');
                resolve(successful);
            } catch (error) {
                console.error('execCommand执行失败:', error);
                resolve(false);
            } finally {
                if (textArea.parentNode) {
                    document.body.removeChild(textArea);
                }
            }
        });
    }

    /**
     * 选择文本（最后的降级方案）
     * @param {string} text 要选择的文本
     */
    _selectText(text) {
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.top = '50%';
        textArea.style.left = '50%';
        textArea.style.transform = 'translate(-50%, -50%)';
        textArea.style.width = '300px';
        textArea.style.height = '100px';
        textArea.style.padding = '10px';
        textArea.style.border = '2px solid #007bff';
        textArea.style.borderRadius = '4px';
        textArea.style.backgroundColor = '#f8f9fa';
        textArea.style.zIndex = '9999';
        textArea.setAttribute('readonly', '');
        
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        
        // 5秒后自动移除
        setTimeout(() => {
            if (textArea.parentNode) {
                document.body.removeChild(textArea);
            }
        }, 5000);
    }

    /**
     * 处理成功情况
     * @param {string} message 成功消息
     * @param {Function} callback 成功回调
     */
    _handleSuccess(message, callback) {
        if (typeof showToast === 'function') {
            showToast(message, 'success');
        } else {
            console.log('✅', message);
        }
        
        if (typeof callback === 'function') {
            callback(true, message);
        }
    }

    /**
     * 处理错误情况
     * @param {string} message 错误消息
     * @param {Function} callback 错误回调
     */
    _handleError(message, callback) {
        if (typeof showToast === 'function') {
            showToast(message, 'warning');
        } else {
            console.warn('⚠️', message);
        }
        
        if (typeof callback === 'function') {
            callback(false, message);
        }
    }

    /**
     * 复制JSON对象（格式化后复制）
     * @param {Object} obj 要复制的对象
     * @param {number} indent 缩进空格数
     * @param {string} successMessage 成功消息
     * @returns {Promise<boolean>} 是否成功
     */
    async copyJSON(obj, indent = 2, successMessage = 'JSON已复制到剪贴板') {
        try {
            const jsonString = JSON.stringify(obj, null, indent);
            return await this.copyText(jsonString, successMessage);
        } catch (error) {
            console.error('JSON序列化失败:', error);
            this._handleError('JSON格式化失败', null);
            return false;
        }
    }

    /**
     * 复制HTML元素的文本内容
     * @param {string|HTMLElement} elementOrId 元素ID或元素对象
     * @param {string} successMessage 成功消息
     * @returns {Promise<boolean>} 是否成功
     */
    async copyElementText(elementOrId, successMessage = '内容已复制到剪贴板') {
        let element;
        
        if (typeof elementOrId === 'string') {
            element = document.getElementById(elementOrId);
        } else if (elementOrId instanceof HTMLElement) {
            element = elementOrId;
        }
        
        if (!element) {
            this._handleError('没有找到要复制的元素', null);
            return false;
        }
        
        const text = element.textContent || element.innerText || '';
        if (!text.trim()) {
            this._handleError('元素内容为空', null);
            return false;
        }
        
        return await this.copyText(text, successMessage);
    }

    /**
     * 检查剪贴板功能可用性
     * @returns {Object} 功能可用性信息
     */
    checkAvailability() {
        return {
            isSecureContext: this.isSecureContext,
            hasClipboardAPI: this.hasClipboardAPI,
            hasExecCommand: this.hasExecCommand,
            recommended: this.hasClipboardAPI && this.isSecureContext ? 'modern' : 
                        this.hasExecCommand ? 'legacy' : 'manual'
        };
    }
}

// 创建全局实例
const clipboardUtils = new ClipboardUtils();

// 兼容性函数（保持向后兼容）
function copyTextToClipboard(text, successMessage = '内容已复制到剪贴板') {
    return clipboardUtils.copyText(text, successMessage);
}

function copyJsonResult() {
    return clipboardUtils.copyElementText('jsonOutput', '结果已复制到剪贴板');
}

function copyFinalPrompt() {
    return clipboardUtils.copyElementText('finalPromptPreview', '提示词已复制到剪贴板');
}

// 导出（如果支持模块化）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ClipboardUtils;
}

if (typeof window !== 'undefined') {
    window.ClipboardUtils = ClipboardUtils;
    window.clipboardUtils = clipboardUtils;
}
