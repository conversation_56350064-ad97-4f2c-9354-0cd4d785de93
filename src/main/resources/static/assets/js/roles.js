/**
 * 角色管理页面JavaScript
 */

let roleCurrentPage = 1;
let rolePageSize = 10;
let currentRoleId = null;
let permissionTree = [];

// 角色管理页面初始化函数
function initRolesPage() {
    console.log('角色管理页面初始化');
    loadRoleList();
    loadPermissionTree();

    // 绑定搜索框回车事件
    const searchInput = document.getElementById('searchKeyword');
    if (searchInput) {
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchRoles();
            }
        });
    }
}

/**
 * 加载角色列表
 */
function loadRoleList(page = 1) {
    roleCurrentPage = page;
    const container = document.getElementById('roleTable');
    if (!container) return;

    try {
        // 显示加载状态
        container.innerHTML = `
            <div class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <div class="mt-2 text-muted">正在加载角色列表...</div>
            </div>
        `;

        const keyword = document.getElementById('searchKeyword')?.value || '';
        const status = document.getElementById('statusFilter')?.value || '';

        const params = new URLSearchParams({
            page: roleCurrentPage,
            size: rolePageSize
        });

        if (keyword) params.append('keyword', keyword);
        if (status) params.append('status', status);

        fetch(`/api/v1/roles?${params}`, {
            method: 'GET',
            headers: {
                'Authorization': 'Bearer ' + getToken(),
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(result => {
            console.log('角色列表API响应:', result); // 添加调试信息
            if (result.code === 200) {
                displayRoleList(result.data);
            } else {
                container.innerHTML = '<div class="alert alert-danger">加载角色列表失败: ' + result.message + '</div>';
            }
        })
        .catch(error => {
            console.error('加载角色列表失败:', error);

            // 如果API调用失败，显示测试数据
            console.log('API调用失败，使用测试数据');
            const testData = [
                {
                    id: 1,
                    roleName: '系统管理员',
                    roleCode: 'ADMIN',
                    description: '系统管理员，拥有所有权限',
                    status: 1,
                    userCount: 2,
                    createdTime: '2024-01-01 10:00:00'
                },
                {
                    id: 2,
                    roleName: '普通用户',
                    roleCode: 'USER',
                    description: '普通用户，基础权限',
                    status: 1,
                    userCount: 10,
                    createdTime: '2024-01-02 10:00:00'
                }
            ];
            displayRoleList(testData);
        });
    } catch (error) {
        console.error('加载角色列表失败:', error);
        if (container) {
            container.innerHTML = '<div class="alert alert-danger">加载角色列表失败</div>';
        }
    }
}

/**
 * 显示角色列表
 */
function displayRoleList(pageData) {
    const container = document.getElementById('roleTable');
    if (!container) return;

    console.log('displayRoleList接收到的数据:', pageData); // 添加调试信息

    // 处理分页数据结构
    let roles = [];
    if (pageData) {
        if (Array.isArray(pageData)) {
            // 如果直接是数组
            roles = pageData;
            console.log('数据是直接数组，长度:', roles.length);
        } else if (pageData.records && Array.isArray(pageData.records)) {
            // 如果是分页对象，包含records数组
            roles = pageData.records;
            console.log('数据包含records数组，长度:', roles.length);
        } else if (pageData.data && Array.isArray(pageData.data)) {
            // 如果是包含data数组的对象
            roles = pageData.data;
            console.log('数据包含data数组，长度:', roles.length);
        } else {
            console.log('未识别的数据结构:', typeof pageData, pageData);
        }
    }

    if (!roles || roles.length === 0) {
        container.innerHTML = '<div class="text-center py-5 text-muted">暂无角色数据</div>';
        return;
    }

    // 渲染角色表格
    container.innerHTML = `
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead>
                    <tr>
                        <th>角色信息</th>
                        <th>状态</th>
                        <th>用户数量</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    ${roles.map(role => `
                        <tr>
                            <td>
                                <div>
                                    <div class="fw-medium">${role.roleName}</div>
                                    <small class="text-muted">编码: ${role.roleCode}</small>
                                    ${role.description ? `<small class="text-info d-block">${role.description}</small>` : ''}
                                </div>
                            </td>
                            <td>
                                <span class="badge ${role.status === 1 ? 'bg-success' : 'bg-secondary'}">
                                    ${role.status === 1 ? '启用' : '禁用'}
                                </span>
                            </td>
                            <td>${role.userCount || 0}</td>
                            <td>${formatDateTime(role.createdTime)}</td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-primary" onclick="editRole(${role.id})" title="编辑" data-permission="ROLE_EDIT">
                                        <i class="bi bi-pencil"></i>
                                    </button>
                                    <button class="btn btn-outline-success" onclick="assignPermissions(${role.id})" title="分配权限" data-permission="ROLE_ASSIGN_PERMISSIONS">
                                        <i class="bi bi-shield-check"></i>
                                    </button>
                                    <button class="btn btn-outline-warning" onclick="toggleRoleStatus(${role.id}, ${role.status})" title="${role.status === 1 ? '禁用' : '启用'}" data-permission="ROLE_EDIT">
                                        <i class="bi bi-${role.status === 1 ? 'pause' : 'play'}"></i>
                                    </button>
                                    <button class="btn btn-outline-danger" onclick="deleteRole(${role.id})" title="删除" ${role.userCount > 0 ? 'disabled' : ''} data-permission="ROLE_DELETE">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>
    `;
}

/**
 * 更新分页
 */
function updatePagination(pageData) {
    const pagination = document.getElementById('rolePagination');
    pagination.innerHTML = '';
    
    const totalPages = pageData.pages;
    const current = pageData.current;
    
    // 上一页
    const prevLi = document.createElement('li');
    prevLi.className = `page-item ${current <= 1 ? 'disabled' : ''}`;
    prevLi.innerHTML = `<a class="page-link" href="#" onclick="loadRoleList(${current - 1})">上一页</a>`;
    pagination.appendChild(prevLi);
    
    // 页码
    const startPage = Math.max(1, current - 2);
    const endPage = Math.min(totalPages, current + 2);
    
    for (let i = startPage; i <= endPage; i++) {
        const li = document.createElement('li');
        li.className = `page-item ${i === current ? 'active' : ''}`;
        li.innerHTML = `<a class="page-link" href="#" onclick="loadRoleList(${i})">${i}</a>`;
        pagination.appendChild(li);
    }
    
    // 下一页
    const nextLi = document.createElement('li');
    nextLi.className = `page-item ${current >= totalPages ? 'disabled' : ''}`;
    nextLi.innerHTML = `<a class="page-link" href="#" onclick="loadRoleList(${current + 1})">下一页</a>`;
    pagination.appendChild(nextLi);
}

/**
 * 搜索角色
 */
function searchRoles() {
    loadRoleList(1);
}

/**
 * 刷新角色列表
 */
function refreshRoleList() {
    loadRoleList(roleCurrentPage);
}

/**
 * 显示创建角色模态框
 */
function showCreateRoleModal() {
    currentRoleId = null;

    // 创建模态框HTML
    const modalHtml = `
        <div class="modal fade" id="roleModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="roleModalTitle">新增角色</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="roleForm">
                            <input type="hidden" id="roleId">
                            <div class="mb-3">
                                <label for="roleName" class="form-label">角色名称 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="roleName" required>
                            </div>
                            <div class="mb-3">
                                <label for="roleCode" class="form-label">角色编码 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="roleCode" required>
                                <div class="form-text">角色编码用于系统内部识别，如：ADMIN、USER等</div>
                            </div>
                            <div class="mb-3">
                                <label for="roleDescription" class="form-label">角色描述</label>
                                <textarea class="form-control" id="roleDescription" rows="3"></textarea>
                            </div>
                            <div class="mb-3">
                                <label for="roleStatus" class="form-label">状态</label>
                                <select class="form-select" id="roleStatus">
                                    <option value="1">启用</option>
                                    <option value="0">禁用</option>
                                </select>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" onclick="saveRole()">保存</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 移除已存在的模态框
    const existingModal = document.getElementById('roleModal');
    if (existingModal) {
        existingModal.remove();
    }

    // 添加新模态框到页面
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('roleModal'));
    modal.show();
}

/**
 * 编辑角色
 */
function editRole(roleId) {
    fetch(`/api/v1/roles/${roleId}`, {
        method: 'GET',
        headers: {
            'Authorization': 'Bearer ' + getToken(),
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(result => {
        if (result.code === 200) {
            const role = result.data;
            currentRoleId = roleId;
            
            // 创建编辑模态框HTML
            const modalHtml = `
                <div class="modal fade" id="roleModal" tabindex="-1">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="roleModalTitle">编辑角色</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <form id="roleForm">
                                    <input type="hidden" id="roleId" value="${role.id}">
                                    <div class="mb-3">
                                        <label for="roleName" class="form-label">角色名称 <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="roleName" value="${role.roleName}" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="roleCode" class="form-label">角色编码 <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="roleCode" value="${role.roleCode}" required>
                                        <div class="form-text">角色编码用于系统内部识别，如：ADMIN、USER等</div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="roleDescription" class="form-label">角色描述</label>
                                        <textarea class="form-control" id="roleDescription" rows="3">${role.description || ''}</textarea>
                                    </div>
                                    <div class="mb-3">
                                        <label for="roleStatus" class="form-label">状态</label>
                                        <select class="form-select" id="roleStatus">
                                            <option value="1" ${role.status === 1 ? 'selected' : ''}>启用</option>
                                            <option value="0" ${role.status === 0 ? 'selected' : ''}>禁用</option>
                                        </select>
                                    </div>
                                </form>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                <button type="button" class="btn btn-primary" onclick="saveRole()">保存</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // 移除已存在的模态框
            const existingModal = document.getElementById('roleModal');
            if (existingModal) {
                existingModal.remove();
            }

            // 添加新模态框到页面
            document.body.insertAdjacentHTML('beforeend', modalHtml);
            
            const modal = new bootstrap.Modal(document.getElementById('roleModal'));
            modal.show();
        } else {
            showToast('获取角色信息失败: ' + result.message, 'error');
        }
    })
    .catch(error => {
        console.error('获取角色信息失败:', error);
        showToast('获取角色信息失败', 'error');
    });
}

/**
 * 保存角色
 */
function saveRole() {
    const roleData = {
        roleName: document.getElementById('roleName').value,
        roleCode: document.getElementById('roleCode').value,
        description: document.getElementById('roleDescription').value,
        status: parseInt(document.getElementById('roleStatus').value)
    };
    
    // 验证必填字段
    if (!roleData.roleName || !roleData.roleCode) {
        showToast('请填写角色名称和角色编码', 'warning');
        return;
    }
    
    const url = currentRoleId ? `/api/v1/roles/${currentRoleId}` : '/api/v1/roles';
    const method = currentRoleId ? 'PUT' : 'POST';
    
    fetch(url, {
        method: method,
        headers: {
            'Authorization': 'Bearer ' + getToken(),
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(roleData)
    })
    .then(response => response.json())
    .then(result => {
        if (result.code === 200) {
            showToast(currentRoleId ? '角色更新成功' : '角色创建成功', 'success');
            bootstrap.Modal.getInstance(document.getElementById('roleModal')).hide();
            loadRoleList(roleCurrentPage);
        } else {
            showToast('保存角色失败: ' + result.message, 'error');
        }
    })
    .catch(error => {
        console.error('保存角色失败:', error);
        showToast('保存角色失败', 'error');
    });
}

/**
 * 切换角色状态
 */
function toggleRoleStatus(roleId, currentStatus) {
    const newStatus = currentStatus === 1 ? 0 : 1;
    const action = newStatus === 1 ? '启用' : '禁用';

    showConfirmDialog(`确定要${action}这个角色吗？`, () => {
        performToggleRoleStatus(roleId, newStatus, action);
    });
}

/**
 * 执行角色状态切换
 */
function performToggleRoleStatus(roleId, newStatus, action) {
    fetch(`/api/v1/roles/${roleId}/status`, {
        method: 'PUT',
        headers: {
            'Authorization': 'Bearer ' + getToken(),
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ status: newStatus })
    })
    .then(response => response.json())
    .then(result => {
        if (result.code === 200) {
            showToast(`角色${action}成功`, 'success');
            loadRoleList(roleCurrentPage);
        } else {
            showToast(`角色${action}失败: ` + result.message, 'error');
        }
    })
    .catch(error => {
        console.error(`角色${action}失败:`, error);
        showToast(`角色${action}失败`, 'error');
    });
}

/**
 * 删除角色
 */
function deleteRole(roleId) {
    showConfirmDialog('确定要删除这个角色吗？删除后不可恢复！', () => {
        performDeleteRole(roleId);
    });
}

/**
 * 执行删除角色
 */
function performDeleteRole(roleId) {
    fetch(`/api/v1/roles/${roleId}`, {
        method: 'DELETE',
        headers: {
            'Authorization': 'Bearer ' + getToken(),
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(result => {
        if (result.code === 200) {
            showToast('角色删除成功', 'success');
            loadRoleList(roleCurrentPage);
        } else {
            showToast('角色删除失败: ' + result.message, 'error');
        }
    })
    .catch(error => {
        console.error('角色删除失败:', error);
        showToast('角色删除失败', 'error');
    });
}

/**
 * 加载权限树
 */
function loadPermissionTree() {
    fetch('/api/v1/permissions/tree', {
        method: 'GET',
        headers: {
            'Authorization': 'Bearer ' + getToken(),
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(result => {
        if (result.code === 200) {
            permissionTree = result.data;
        } else {
            console.error('加载权限树失败:', result.message);
        }
    })
    .catch(error => {
        console.error('加载权限树失败:', error);
    });
}

/**
 * 分配权限
 */
function assignPermissions(roleId) {
    console.log('分配权限，角色ID:', roleId);
    currentRoleId = roleId;

    // 先清理现有权限模态框
    const existingPermissionModal = document.getElementById('permissionModal');
    if (existingPermissionModal) {
        const modalInstance = bootstrap.Modal.getInstance(existingPermissionModal);
        if (modalInstance) {
            modalInstance.hide();
        }
        existingPermissionModal.remove();
    }

    // 获取角色信息
    fetch(`/api/v1/roles/${roleId}`, {
        method: 'GET',
        headers: {
            'Authorization': 'Bearer ' + getToken(),
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(result => {
        if (result.code === 200) {
            const role = result.data;

            // 创建权限分配模态框HTML
            const permissionModalHtml = `
                <div class="modal fade" id="permissionModal" tabindex="-1">
                    <div class="modal-dialog modal-xl">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">权限分配</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="row">
                                    <div class="col-md-4">
                                        <h6>角色信息</h6>
                                        <div class="card">
                                            <div class="card-body">
                                                <p><strong>角色名称：</strong><span id="permissionRoleName">${role.roleName}</span></p>
                                                <p><strong>角色编码：</strong><span id="permissionRoleCode">${role.roleCode}</span></p>
                                                <p><strong>描述：</strong><span id="permissionRoleDescription">${role.description || '-'}</span></p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-8">
                                        <h6>权限配置</h6>
                                        <div class="permission-tree" id="permissionTree">
                                            <!-- 权限树将通过JavaScript动态生成 -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                <button type="button" class="btn btn-primary" onclick="saveRolePermissions()">保存权限</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // 移除已存在的权限模态框
            const existingPermissionModal = document.getElementById('permissionModal');
            if (existingPermissionModal) {
                existingPermissionModal.remove();
            }

            // 添加新权限模态框到页面
            document.body.insertAdjacentHTML('beforeend', permissionModalHtml);

            // 获取角色的权限
            return fetch(`/api/v1/roles/${roleId}/permissions`, {
                method: 'GET',
                headers: {
                    'Authorization': 'Bearer ' + getToken(),
                    'Content-Type': 'application/json'
                }
            });
        } else {
            throw new Error(result.message);
        }
    })
    .then(response => response.json())
    .then(result => {
        if (result.code === 200) {
            const rolePermissions = result.data.permissions || [];
            const permissionIds = rolePermissions.map(p => p.id);

            // 渲染权限树
            renderPermissionTree(permissionTree, permissionIds);

            const modal = new bootstrap.Modal(document.getElementById('permissionModal'));
            modal.show();
        } else {
            throw new Error(result.message);
        }
    })
    .catch(error => {
        console.error('获取角色权限失败:', error);
        showToast('获取角色权限失败: ' + error.message, 'error');
    });
}

/**
 * 渲染权限树
 */
function renderPermissionTree(permissions, selectedIds = []) {
    const container = document.getElementById('permissionTree');
    container.innerHTML = '';

    if (!permissions || permissions.length === 0) {
        container.innerHTML = '<p class="text-muted">暂无权限数据</p>';
        return;
    }

    const tree = document.createElement('div');
    tree.className = 'permission-tree-container';

    permissions.forEach(permission => {
        const node = createPermissionNode(permission, selectedIds, 0);
        tree.appendChild(node);
    });

    container.appendChild(tree);
}

/**
 * 创建权限节点
 */
function createPermissionNode(permission, selectedIds, level = 0) {
    const node = document.createElement('div');
    node.className = `permission-node level-${level} mb-2`;
    node.setAttribute('data-permission-id', permission.id);

    const isChecked = selectedIds.includes(permission.id);
    const hasChildren = permission.children && permission.children.length > 0;

    let nodeHtml = `
        <div class="permission-node-header">
            <div class="permission-node-content">
                <button class="permission-toggle ${hasChildren ? '' : 'no-children'}"
                        onclick="toggleRolePermissionNode(this)"
                        ${hasChildren ? '' : 'style="visibility: hidden;"'}>
                    <i class="bi bi-chevron-down"></i>
                </button>
                <input type="checkbox" class="form-check-input permission-checkbox"
                       id="perm_${permission.id}"
                       value="${permission.id}"
                       ${isChecked ? 'checked' : ''}
                       onchange="handlePermissionCheck(${permission.id})">
                <div class="permission-info">
                    <label for="perm_${permission.id}" class="form-check-label">
                        <i class="bi bi-${getPermissionIcon(permission.permissionType)} me-1"></i>
                        ${permission.permissionName}
                        <small class="text-muted">(${permission.permissionCode})</small>
                    </label>
                </div>
            </div>
        </div>
    `;

    if (hasChildren) {
        nodeHtml += '<div class="permission-children">';
        permission.children.forEach(child => {
            const childNode = createPermissionNode(child, selectedIds, level + 1);
            nodeHtml += childNode.outerHTML;
        });
        nodeHtml += '</div>';
    }

    node.innerHTML = nodeHtml;
    return node;
}

/**
 * 获取权限图标
 */
function getPermissionIcon(type) {
    switch (type) {
        case 1: return 'folder'; // 菜单
        case 2: return 'cursor'; // 按钮
        case 3: return 'gear';   // 接口
        default: return 'question-circle';
    }
}

/**
 * 处理权限选择
 */
function handlePermissionCheck(permissionId) {
    const checkbox = document.getElementById(`perm_${permissionId}`);
    const isChecked = checkbox.checked;

    // 如果选中，自动选中所有子权限
    if (isChecked) {
        selectChildPermissions(permissionId, true);
    } else {
        selectChildPermissions(permissionId, false);
    }

    // 检查父权限状态
    updateParentPermissionStatus(permissionId);
}

/**
 * 选择子权限
 */
function selectChildPermissions(permissionId, checked) {
    const permission = findPermissionById(permissionTree, permissionId);
    if (permission && permission.children) {
        permission.children.forEach(child => {
            const childCheckbox = document.getElementById(`perm_${child.id}`);
            if (childCheckbox) {
                childCheckbox.checked = checked;
                selectChildPermissions(child.id, checked);
            }
        });
    }
}

/**
 * 更新父权限状态
 */
function updateParentPermissionStatus(permissionId) {
    const parent = findParentPermission(permissionTree, permissionId);
    if (parent) {
        const parentCheckbox = document.getElementById(`perm_${parent.id}`);
        if (parentCheckbox) {
            const childCheckboxes = parent.children.map(child =>
                document.getElementById(`perm_${child.id}`)
            ).filter(cb => cb !== null);

            const checkedChildren = childCheckboxes.filter(cb => cb.checked);

            if (checkedChildren.length === childCheckboxes.length) {
                parentCheckbox.checked = true;
                parentCheckbox.indeterminate = false;
            } else if (checkedChildren.length > 0) {
                parentCheckbox.checked = false;
                parentCheckbox.indeterminate = true;
            } else {
                parentCheckbox.checked = false;
                parentCheckbox.indeterminate = false;
            }

            updateParentPermissionStatus(parent.id);
        }
    }
}

/**
 * 查找权限
 */
function findPermissionById(permissions, id) {
    for (const permission of permissions) {
        if (permission.id === id) {
            return permission;
        }
        if (permission.children) {
            const found = findPermissionById(permission.children, id);
            if (found) return found;
        }
    }
    return null;
}

/**
 * 查找父权限
 */
function findParentPermission(permissions, childId, parent = null) {
    for (const permission of permissions) {
        if (permission.children) {
            for (const child of permission.children) {
                if (child.id === childId) {
                    return permission;
                }
            }
            const found = findParentPermission(permission.children, childId, permission);
            if (found) return found;
        }
    }
    return null;
}

/**
 * 切换角色权限节点的展开/折叠状态
 */
function toggleRolePermissionNode(toggleButton) {
    const node = toggleButton.closest('.permission-node');
    const childrenContainer = node.querySelector('.permission-children');

    if (!childrenContainer) return;

    const isCollapsed = childrenContainer.classList.contains('collapsed');

    if (isCollapsed) {
        // 展开
        childrenContainer.classList.remove('collapsed');
        toggleButton.classList.remove('collapsed');
        toggleButton.querySelector('i').className = 'bi bi-chevron-down';
    } else {
        // 折叠
        childrenContainer.classList.add('collapsed');
        toggleButton.classList.add('collapsed');
        toggleButton.querySelector('i').className = 'bi bi-chevron-right';
    }
}

/**
 * 展开所有角色权限节点
 */
function expandAllRolePermissions() {
    document.querySelectorAll('#permissionTree .permission-children').forEach(container => {
        container.classList.remove('collapsed');
    });
    document.querySelectorAll('#permissionTree .permission-toggle').forEach(toggle => {
        toggle.classList.remove('collapsed');
        const icon = toggle.querySelector('i');
        if (icon) {
            icon.className = 'bi bi-chevron-down';
        }
    });
}

/**
 * 折叠所有角色权限节点
 */
function collapseAllRolePermissions() {
    document.querySelectorAll('#permissionTree .permission-children').forEach(container => {
        container.classList.add('collapsed');
    });
    document.querySelectorAll('#permissionTree .permission-toggle').forEach(toggle => {
        if (!toggle.classList.contains('no-children')) {
            toggle.classList.add('collapsed');
            const icon = toggle.querySelector('i');
            if (icon) {
                icon.className = 'bi bi-chevron-right';
            }
        }
    });
}

/**
 * 全选权限
 */
function selectAllPermissions() {
    document.querySelectorAll('#permissionTree input[type="checkbox"]').forEach(checkbox => {
        checkbox.checked = true;
        checkbox.indeterminate = false;
    });
}

/**
 * 清空所有权限选择
 */
function clearAllPermissions() {
    document.querySelectorAll('#permissionTree input[type="checkbox"]').forEach(checkbox => {
        checkbox.checked = false;
        checkbox.indeterminate = false;
    });
}

/**
 * 保存角色权限
 */
function saveRolePermissions() {
    const checkboxes = document.querySelectorAll('#permissionTree input[type="checkbox"]:checked');
    const permissionIds = Array.from(checkboxes).map(cb => Number(cb.value));

    console.log('保存角色权限:', {
        roleId: currentRoleId,
        permissionIds: permissionIds,
        permissionCount: permissionIds.length
    });

    // 验证权限ID
    if (permissionIds.some(id => isNaN(id) || id <= 0)) {
        showToast('权限ID无效，请刷新页面重试', 'error');
        return;
    }

    fetch(`/api/v1/roles/${currentRoleId}/permissions`, {
        method: 'POST',
        headers: {
            'Authorization': 'Bearer ' + getToken(),
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ permissionIds: permissionIds })
    })
    .then(response => response.json())
    .then(result => {
        console.log('权限分配响应:', result);
        if (result.code === 200) {
            showToast('权限分配成功', 'success');
            bootstrap.Modal.getInstance(document.getElementById('permissionModal')).hide();
        } else {
            showToast('权限分配失败: ' + result.message, 'error');
        }
    })
    .catch(error => {
        console.error('权限分配失败:', error);
        showToast('权限分配失败', 'error');
    });
}

// 显示Toast消息
function showToast(message, type = 'info') {
    // 创建toast容器（如果不存在）
    let toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
        toastContainer.style.zIndex = '9999';
        document.body.appendChild(toastContainer);
    }

    // 创建toast元素
    const toastId = 'toast-' + Date.now();
    const toastHtml = `
        <div id="${toastId}" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header">
                <i class="bi bi-${getToastIcon(type)} text-${type} me-2"></i>
                <strong class="me-auto">系统消息</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body">
                ${message}
            </div>
        </div>
    `;

    toastContainer.insertAdjacentHTML('beforeend', toastHtml);

    // 显示toast
    const toastElement = document.getElementById(toastId);
    const toast = new bootstrap.Toast(toastElement, {
        autohide: true,
        delay: 3000
    });
    toast.show();

    // 自动清理
    toastElement.addEventListener('hidden.bs.toast', () => {
        toastElement.remove();
    });
}

// 获取Toast图标
function getToastIcon(type) {
    const icons = {
        'success': 'check-circle',
        'error': 'exclamation-triangle',
        'warning': 'exclamation-triangle',
        'info': 'info-circle'
    };
    return icons[type] || 'info-circle';
}

// 显示确认对话框
function showConfirmDialog(message, onConfirm, onCancel = null) {
    // 移除现有的确认对话框
    const existingModal = document.getElementById('confirmModal');
    if (existingModal) {
        existingModal.remove();
    }

    // 创建确认对话框
    const modalHtml = `
        <div class="modal fade" id="confirmModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="bi bi-question-circle text-warning me-2"></i>确认操作
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <p class="mb-0">${message}</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" id="confirmBtn">确定</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);

    const modal = new bootstrap.Modal(document.getElementById('confirmModal'));
    const confirmBtn = document.getElementById('confirmBtn');

    // 绑定确认按钮事件
    confirmBtn.addEventListener('click', () => {
        modal.hide();
        if (onConfirm) {
            onConfirm();
        }
    });

    // 绑定取消事件
    document.getElementById('confirmModal').addEventListener('hidden.bs.modal', () => {
        document.getElementById('confirmModal').remove();
        if (onCancel) {
            onCancel();
        }
    });

    modal.show();
}
