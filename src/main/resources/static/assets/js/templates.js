// 业务模板管理页面JavaScript

// 页面初始化函数
function initTemplatesPage() {
    console.log('业务模板页面初始化');
    loadTemplates();

    // 添加搜索框回车键支持
    const searchInput = document.getElementById('templateSearchInput');
    if (searchInput) {
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchTemplates();
            }
        });
    }
}

// 加载模板列表
async function loadTemplates() {
    const container = document.getElementById('templatesTable');
    if (!container) return;

    try {
        // 显示加载状态
        container.innerHTML = `
            <div class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <div class="mt-2 text-muted">正在加载模板列表...</div>
            </div>
        `;

        // 调用真实API
        const response = await apiCall('/api/v1/business-templates');
        let templates = [];

        if (response.code === 200) {
            templates = response.data.map(template => ({
                id: template.id,
                name: template.templateName,
                code: template.templateCode,
                category: template.category,
                description: template.description || '暂无描述',
                status: template.status === 1 ? 'active' : 'inactive',
                createdAt: template.createdTime ? template.createdTime.split(' ')[0] : '未知',
                lastModified: template.updatedTime ? template.updatedTime.split(' ')[0] : '未知',
                jsonTemplate: template.jsonTemplate
            }));
        } else {
            // 如果API失败，使用模拟数据
            templates = [
                {
                    id: 1,
                    name: '财务报表模板',
                    code: 'FINANCE_REPORT',
                    category: 'finance',
                    description: '用于提取财务报表中的关键财务指标',
                    status: 'active',
                    createdAt: '2024-01-15',
                    lastModified: '2024-01-20',
                    jsonTemplate: '{"revenue": "营收", "profit": "利润"}'
                },
                {
                    id: 2,
                    name: '身份证信息模板',
                    code: 'ID_CARD',
                    category: 'identity',
                    description: '提取身份证上的个人信息',
                    status: 'active',
                    createdAt: '2024-01-10',
                    lastModified: '2024-01-18',
                    jsonTemplate: '{"name": "姓名", "id_number": "身份证号"}'
                },
                {
                    id: 3,
                    name: '发票信息模板',
                    code: 'INVOICE',
                    category: 'finance',
                    description: '提取发票的基本信息和金额',
                    status: 'active',
                    createdAt: '2024-01-12',
                    lastModified: '2024-01-19',
                    jsonTemplate: '{"invoice_number": "发票号码", "amount": "金额"}'
                }
            ];
        }
        
        // 渲染模板表格
        renderTemplateTable(templates);
        
    } catch (error) {
        console.error('加载模板列表失败:', error);
        container.innerHTML = '<div class="alert alert-danger">加载模板列表失败</div>';
    }
}

// 更新统计信息
function updateTemplateStats(templates) {
    const totalTemplates = templates.length;
    const activeTemplates = templates.filter(t => t.status === 'active').length;
    const categories = [...new Set(templates.map(t => t.category))].length;

    document.getElementById('totalTemplates').textContent = totalTemplates;
    document.getElementById('activeTemplates').textContent = activeTemplates;
    document.getElementById('templateCategories').textContent = categories;
    document.getElementById('templatesInUse').textContent = activeTemplates; // 简化为启用数量
}

// 显示创建模板模态框
function showCreateTemplateModal() {
    // 重置表单
    document.getElementById('createTemplateForm').reset();

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('createTemplateModal'));
    modal.show();
}

// 根据模板名称自动生成编码
function generateTemplateCode() {
    const nameInput = document.querySelector('input[name="templateName"]');
    const codeInput = document.querySelector('input[name="templateCode"]');

    if (nameInput && codeInput && nameInput.value) {
        // 将中文和特殊字符转换为英文编码
        let code = nameInput.value
            .replace(/发票/g, 'INVOICE')
            .replace(/身份证/g, 'ID_CARD')
            .replace(/合同/g, 'CONTRACT')
            .replace(/运单/g, 'WAYBILL')
            .replace(/财务/g, 'FINANCE')
            .replace(/报表/g, 'REPORT')
            .replace(/识别/g, 'RECOGNITION')
            .replace(/模板/g, 'TEMPLATE')
            .replace(/[^A-Za-z0-9]/g, '_')
            .toUpperCase()
            .replace(/_+/g, '_')
            .replace(/^_|_$/g, '');

        codeInput.value = code;
    }
}

// 创建模板
async function createTemplate() {
    const form = document.getElementById('createTemplateForm');
    const formData = new FormData(form);

    // 验证必填字段
    const templateName = formData.get('templateName').trim();
    const templateCode = formData.get('templateCode').trim();
    const jsonTemplate = formData.get('jsonTemplate').trim();

    if (!templateName || !templateCode || !jsonTemplate) {
        showToast('请填写所有必填字段', 'warning');
        return;
    }

    // 验证JSON格式
    try {
        JSON.parse(jsonTemplate);
    } catch (e) {
        showToast('JSON模板格式不正确，请检查语法', 'error');
        return;
    }

    // 验证模板编码格式
    if (!/^[A-Z0-9_]+$/.test(templateCode)) {
        showToast('模板编码只能包含大写字母、数字和下划线', 'warning');
        return;
    }

    const templateData = {
        templateName: templateName,
        templateCode: templateCode,
        description: formData.get('description') || '',
        jsonTemplate: jsonTemplate,
        category: formData.get('category'),
        status: parseInt(formData.get('status'))
    };

    try {
        const response = await apiCall('/api/v1/business-templates', 'POST', templateData);

        if (response.code === 200) {
            showToast('模板创建成功！', 'success');
            // 关闭模态框
            bootstrap.Modal.getInstance(document.getElementById('createTemplateModal')).hide();
            // 重新加载列表
            loadTemplates();
        } else {
            showToast('创建失败：' + response.message, 'error');
        }
    } catch (error) {
        console.error('创建模板失败:', error);
        showToast('创建失败：' + error.message, 'error');
    }
}

// 预览模板
async function previewTemplate(id) {
    try {
        const response = await apiCall(`/api/v1/business-templates/${id}`);

        if (response.code === 200) {
            const template = response.data;

            // 创建预览模态框
            const modalHtml = `
                <div class="modal fade" id="previewTemplateModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">
                                    <i class="bi bi-eye me-2"></i>预览模板: ${template.templateName}
                                </h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <label class="form-label fw-bold">模板名称</label>
                                        <p class="form-control-plaintext">${template.templateName}</p>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label fw-bold">模板编码</label>
                                        <p class="form-control-plaintext"><code>${template.templateCode}</code></p>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label fw-bold">分类</label>
                                        <p class="form-control-plaintext">${getCategoryText(template.category)}</p>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label fw-bold">状态</label>
                                        <p class="form-control-plaintext">
                                            <span class="badge bg-${getTemplateStatusColor(template.status === 1 ? 'active' : 'inactive')}">
                                                ${getTemplateStatusText(template.status === 1 ? 'active' : 'inactive')}
                                            </span>
                                        </p>
                                    </div>
                                    <div class="col-12">
                                        <label class="form-label fw-bold">描述</label>
                                        <p class="form-control-plaintext">${template.description || '暂无描述'}</p>
                                    </div>
                                    <div class="col-12">
                                        <label class="form-label fw-bold">JSON模板</label>
                                        <pre class="bg-light p-3 rounded" style="max-height: 300px; overflow-y: auto;">${JSON.stringify(JSON.parse(template.jsonTemplate), null, 2)}</pre>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                                <button type="button" class="btn btn-primary" onclick="copyTemplateJson('${template.jsonTemplate.replace(/'/g, "\\'")}')">
                                    <i class="bi bi-clipboard"></i> 复制JSON
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // 移除已存在的模态框
            const existingModal = document.getElementById('previewTemplateModal');
            if (existingModal) {
                existingModal.remove();
            }

            // 添加新模态框
            document.body.insertAdjacentHTML('beforeend', modalHtml);

            // 显示模态框
            const modal = new bootstrap.Modal(document.getElementById('previewTemplateModal'));
            modal.show();

        } else {
            showToast('获取模板详情失败：' + response.message, 'error');
        }
    } catch (error) {
        console.error('预览模板失败:', error);
        showToast('预览模板失败：' + error.message, 'error');
    }
}

// 复制模板JSON
function copyTemplateJson(jsonStr) {
    try {
        const formatted = JSON.stringify(JSON.parse(jsonStr), null, 2);
        navigator.clipboard.writeText(formatted).then(() => {
            showToast('JSON已复制到剪贴板', 'success');
        }).catch(() => {
            showToast('复制失败', 'error');
        });
    } catch (e) {
        showToast('JSON格式错误', 'error');
    }
}

// 编辑模板
async function editTemplate(id) {
    try {
        // 获取模板详情
        const response = await apiCall(`/api/v1/business-templates/${id}`);

        if (response.code === 200) {
            const template = response.data;

            // 填充编辑表单
            const form = document.getElementById('editTemplateForm');
            form.querySelector('input[name="templateId"]').value = template.id;
            form.querySelector('input[name="templateName"]').value = template.templateName;
            form.querySelector('input[name="templateCode"]').value = template.templateCode;
            form.querySelector('select[name="category"]').value = template.category;
            form.querySelector('select[name="status"]').value = template.status;
            form.querySelector('textarea[name="description"]').value = template.description || '';

            // 格式化JSON显示
            try {
                const formattedJson = JSON.stringify(JSON.parse(template.jsonTemplate), null, 2);
                form.querySelector('textarea[name="jsonTemplate"]').value = formattedJson;
            } catch (e) {
                // 如果JSON格式有问题，直接显示原始内容
                form.querySelector('textarea[name="jsonTemplate"]').value = template.jsonTemplate;
            }

            // 显示编辑模态框
            const modal = new bootstrap.Modal(document.getElementById('editTemplateModal'));
            modal.show();

        } else {
            alert('获取模板详情失败：' + response.message);
        }
    } catch (error) {
        console.error('编辑模板失败:', error);
        alert('编辑模板失败：' + error.message);
    }
}

// 更新模板
async function updateTemplate() {
    const form = document.getElementById('editTemplateForm');
    const formData = new FormData(form);

    // 验证必填字段
    const templateName = formData.get('templateName').trim();
    const jsonTemplate = formData.get('jsonTemplate').trim();
    const templateId = formData.get('templateId');

    if (!templateName || !jsonTemplate) {
        alert('请填写所有必填字段');
        return;
    }

    // 验证JSON格式
    try {
        JSON.parse(jsonTemplate);
    } catch (e) {
        alert('JSON模板格式不正确，请检查语法');
        return;
    }

    const templateData = {
        templateName: templateName,
        templateCode: formData.get('templateCode'), // 编码不可修改，但需要传递
        description: formData.get('description') || '',
        jsonTemplate: jsonTemplate,
        category: formData.get('category'),
        status: parseInt(formData.get('status'))
    };

    try {
        const response = await apiCall(`/api/v1/business-templates/${templateId}`, 'PUT', templateData);

        if (response.code === 200) {
            showToast('模板更新成功！', 'success');
            // 关闭模态框
            bootstrap.Modal.getInstance(document.getElementById('editTemplateModal')).hide();
            // 重新加载列表
            loadTemplates();
        } else {
            showToast('更新失败：' + response.message, 'error');
        }
    } catch (error) {
        console.error('更新模板失败:', error);
        showToast('更新失败：' + error.message, 'error');
    }
}

// 复制模板
async function copyTemplate(id) {
    try {
        // 获取原模板详情
        const response = await apiCall(`/api/v1/business-templates/${id}`);

        if (response.code === 200) {
            const originalTemplate = response.data;

            // 生成新的模板名称和编码
            const newTemplateName = originalTemplate.templateName + ' - 副本';
            const timestamp = new Date().getTime().toString().slice(-4);
            const newTemplateCode = originalTemplate.templateCode + '_COPY_' + timestamp;

            // 填充复制表单
            const form = document.getElementById('copyTemplateForm');
            form.querySelector('input[name="originalId"]').value = originalTemplate.id;
            form.querySelector('input[name="templateName"]').value = newTemplateName;
            form.querySelector('input[name="templateCode"]').value = newTemplateCode;
            form.querySelector('select[name="category"]').value = originalTemplate.category;
            form.querySelector('select[name="status"]').value = originalTemplate.status;
            form.querySelector('textarea[name="description"]').value = (originalTemplate.description || '') + '\n\n(复制自: ' + originalTemplate.templateName + ')';
            form.querySelector('textarea[name="jsonTemplate"]').value = JSON.stringify(JSON.parse(originalTemplate.jsonTemplate), null, 2);

            // 显示复制模态框
            const modal = new bootstrap.Modal(document.getElementById('copyTemplateModal'));
            modal.show();

        } else {
            alert('获取模板详情失败：' + response.message);
        }
    } catch (error) {
        console.error('复制模板失败:', error);
        alert('复制模板失败：' + error.message);
    }
}

// 确认复制模板
async function confirmCopyTemplate() {
    const form = document.getElementById('copyTemplateForm');
    const formData = new FormData(form);

    // 验证必填字段
    const templateName = formData.get('templateName').trim();
    const templateCode = formData.get('templateCode').trim();
    const jsonTemplate = formData.get('jsonTemplate').trim();

    if (!templateName || !templateCode || !jsonTemplate) {
        alert('请填写所有必填字段');
        return;
    }

    // 验证模板编码格式
    if (!/^[A-Z0-9_]+$/.test(templateCode)) {
        alert('模板编码只能包含大写字母、数字和下划线');
        return;
    }

    const templateData = {
        templateName: templateName,
        templateCode: templateCode,
        description: formData.get('description') || '',
        jsonTemplate: jsonTemplate,
        category: formData.get('category'),
        status: parseInt(formData.get('status'))
    };

    try {
        const response = await apiCall('/api/v1/business-templates', 'POST', templateData);

        if (response.code === 200) {
            alert('模板复制成功！');
            // 关闭模态框
            bootstrap.Modal.getInstance(document.getElementById('copyTemplateModal')).hide();
            // 重新加载列表
            loadTemplates();
        } else {
            alert('复制失败：' + response.message);
        }
    } catch (error) {
        console.error('复制模板失败:', error);
        alert('复制失败：' + error.message);
    }
}

// 删除模板
async function deleteTemplate(id) {
    if (!await showConfirm('确定要删除这个模板吗？此操作不可恢复！', '删除确认')) {
        return;
    }

    try {
        const response = await apiCall(`/api/v1/business-templates/${id}`, 'DELETE');

        if (response.code === 200) {
            showToast('模板删除成功！', 'success');
            // 重新加载列表
            loadTemplates();
        } else {
            showToast('删除失败：' + response.message, 'error');
        }
    } catch (error) {
        console.error('删除模板失败:', error);
        showToast('删除失败：' + error.message, 'error');
    }
}

// 导入模板
function importTemplate() {
    showToast('导入模板功能开发中...', 'info');
}

// 搜索模板
async function searchTemplates() {
    const query = document.getElementById('templateSearchInput').value.trim();
    const category = document.getElementById('templateCategoryFilter').value;

    const container = document.getElementById('templatesTable');

    try {
        // 显示加载状态
        container.innerHTML = `
            <div class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">搜索中...</span>
                </div>
                <div class="mt-2 text-muted">正在搜索模板...</div>
            </div>
        `;

        // 构建查询参数
        const params = new URLSearchParams();
        if (category) params.append('category', category);
        if (query) params.append('keyword', query);

        const url = `/api/v1/business-templates${params.toString() ? '?' + params.toString() : ''}`;
        const response = await apiCall(url);

        if (response.code === 200) {
            const templates = response.data.map(template => ({
                id: template.id,
                name: template.templateName,
                code: template.templateCode,
                category: template.category,
                description: template.description || '暂无描述',
                status: template.status === 1 ? 'active' : 'inactive',
                createdAt: template.createdTime ? template.createdTime.split(' ')[0] : '未知',
                lastModified: template.updatedTime ? template.updatedTime.split(' ')[0] : '未知',
                jsonTemplate: template.jsonTemplate
            }));

            // 渲染结果
            renderTemplateTable(templates);
        } else {
            container.innerHTML = '<div class="alert alert-warning">搜索失败</div>';
        }
    } catch (error) {
        console.error('搜索模板失败:', error);
        container.innerHTML = '<div class="alert alert-danger">搜索失败</div>';
    }
}

// 筛选模板
function filterTemplates() {
    searchTemplates(); // 直接调用搜索函数，因为搜索函数已经处理了分类筛选
}

// 渲染模板表格（提取为独立函数）
function renderTemplateTable(templates) {
    const container = document.getElementById('templatesTable');

    if (templates.length === 0) {
        container.innerHTML = `
            <div class="text-center py-5">
                <i class="bi bi-inbox fs-1 text-muted"></i>
                <p class="mt-3 text-muted">没有找到匹配的模板</p>
            </div>
        `;
        return;
    }

    container.innerHTML = `
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead>
                    <tr>
                        <th width="40">
                            <input type="checkbox" class="form-check-input" id="selectAll" onchange="toggleSelectAll()">
                        </th>
                        <th style="width: 300px;">模板名称</th>
                        <th>模板编码</th>
                        <th>分类</th>
                        <th>状态</th>
                        <th>创建时间</th>
                        <th>最后修改</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    ${templates.map(template => `
                        <tr>
                            <td>
                                <input type="checkbox" class="form-check-input template-checkbox" value="${template.id}" onchange="updateBatchOperations()">
                            </td>
                            <td>
                                <div>
                                    <div class="fw-medium">${template.name}</div>
                                    <small class="text-muted">${template.description}</small>
                                </div>
                            </td>
                            <td><code class="small">${template.code}</code></td>
                            <td><span class="badge bg-secondary">${getCategoryText(template.category)}</span></td>
                            <td><span class="badge bg-${getTemplateStatusColor(template.status)}">${getTemplateStatusText(template.status)}</span></td>
                            <td>${template.createdAt}</td>
                            <td>${template.lastModified}</td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-info" onclick="previewTemplate(${template.id})" title="预览JSON">
                                        <i class="bi bi-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-primary" onclick="editTemplate(${template.id})" title="编辑">
                                        <i class="bi bi-pencil"></i>
                                    </button>
                                    <button class="btn btn-outline-success" onclick="copyTemplate(${template.id})" title="复制">
                                        <i class="bi bi-files"></i>
                                    </button>
                                    <button class="btn btn-outline-info" onclick="showTemplateUsage(${template.id})" title="查看使用情况">
                                        <i class="bi bi-graph-up"></i>
                                    </button>
                                    <button class="btn btn-outline-danger" onclick="deleteTemplate(${template.id})" title="删除">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>
    `;

    // 更新统计信息
    updateTemplateStats(templates);
}

// 辅助函数
function getCategoryText(category) {
    const categories = {
        'finance': '财务',
        'logistics': '物流',
        'legal': '法务',
        'identity': '身份',
        'general': '通用'
    };
    return categories[category] || category;
}

function getTemplateStatusColor(status) {
    const colors = {
        'active': 'success',
        'inactive': 'secondary'
    };
    return colors[status] || 'secondary';
}

function getTemplateStatusText(status) {
    const texts = {
        'active': '启用',
        'inactive': '禁用'
    };
    return texts[status] || status;
}

// 全选/取消全选
function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.template-checkbox');

    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });

    updateBatchOperations();
}

// 更新批量操作按钮显示状态
function updateBatchOperations() {
    const checkboxes = document.querySelectorAll('.template-checkbox:checked');
    const batchOperations = document.getElementById('batchOperations');
    const selectAll = document.getElementById('selectAll');

    if (checkboxes.length > 0) {
        batchOperations.style.display = 'inline-block';
    } else {
        batchOperations.style.display = 'none';
    }

    // 更新全选状态
    const allCheckboxes = document.querySelectorAll('.template-checkbox');
    if (allCheckboxes.length > 0) {
        selectAll.checked = checkboxes.length === allCheckboxes.length;
        selectAll.indeterminate = checkboxes.length > 0 && checkboxes.length < allCheckboxes.length;
    }
}

// 获取选中的模板ID
function getSelectedTemplateIds() {
    const checkboxes = document.querySelectorAll('.template-checkbox:checked');
    return Array.from(checkboxes).map(cb => parseInt(cb.value));
}

// 批量启用
async function batchEnable() {
    const selectedIds = getSelectedTemplateIds();
    if (selectedIds.length === 0) {
        alert('请先选择要启用的模板');
        return;
    }

    if (!confirm(`确定要启用选中的 ${selectedIds.length} 个模板吗？`)) {
        return;
    }

    try {
        let successCount = 0;
        for (const id of selectedIds) {
            // 先获取模板详情
            const getResponse = await apiCall(`/api/v1/business-templates/${id}`);
            if (getResponse.code === 200) {
                const template = getResponse.data;
                template.status = 1; // 设置为启用

                // 更新模板
                const updateResponse = await apiCall(`/api/v1/business-templates/${id}`, 'PUT', template);
                if (updateResponse.code === 200) {
                    successCount++;
                }
            }
        }

        alert(`成功启用 ${successCount} 个模板`);
        loadTemplates();
    } catch (error) {
        console.error('批量启用失败:', error);
        alert('批量启用失败：' + error.message);
    }
}

// 批量禁用
async function batchDisable() {
    const selectedIds = getSelectedTemplateIds();
    if (selectedIds.length === 0) {
        alert('请先选择要禁用的模板');
        return;
    }

    if (!confirm(`确定要禁用选中的 ${selectedIds.length} 个模板吗？`)) {
        return;
    }

    try {
        let successCount = 0;
        for (const id of selectedIds) {
            // 先获取模板详情
            const getResponse = await apiCall(`/api/v1/business-templates/${id}`);
            if (getResponse.code === 200) {
                const template = getResponse.data;
                template.status = 0; // 设置为禁用

                // 更新模板
                const updateResponse = await apiCall(`/api/v1/business-templates/${id}`, 'PUT', template);
                if (updateResponse.code === 200) {
                    successCount++;
                }
            }
        }

        alert(`成功禁用 ${successCount} 个模板`);
        loadTemplates();
    } catch (error) {
        console.error('批量禁用失败:', error);
        alert('批量禁用失败：' + error.message);
    }
}

// 批量删除
async function batchDelete() {
    const selectedIds = getSelectedTemplateIds();
    if (selectedIds.length === 0) {
        alert('请先选择要删除的模板');
        return;
    }

    if (!confirm(`确定要删除选中的 ${selectedIds.length} 个模板吗？此操作不可恢复！`)) {
        return;
    }

    try {
        let successCount = 0;
        for (const id of selectedIds) {
            const response = await apiCall(`/api/v1/business-templates/${id}`, 'DELETE');
            if (response.code === 200) {
                successCount++;
            }
        }

        alert(`成功删除 ${successCount} 个模板`);
        loadTemplates();
    } catch (error) {
        console.error('批量删除失败:', error);
        alert('批量删除失败：' + error.message);
    }
}

// 查看模板使用情况
async function showTemplateUsage(templateId) {
    try {
        // 获取模板详情
        const templateResponse = await apiCall(`/api/v1/business-templates/${templateId}`);
        if (templateResponse.code !== 200) {
            showToast('获取模板信息失败', 'error');
            return;
        }

        const template = templateResponse.data;

        // 获取使用该模板的Agent列表
        const agentsResponse = await apiCall('/api/v1/agents?size=100');
        let usingAgents = [];

        if (agentsResponse.code === 200) {
            usingAgents = agentsResponse.data.list.filter(agent =>
                agent.templateId === templateId ||
                (agent.jsonTemplate && agent.jsonTemplate === template.jsonTemplate)
            );
        }

        // 创建使用情况模态框
        const modalHtml = `
            <div class="modal fade" id="templateUsageModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="bi bi-graph-up me-2"></i>模板使用情况: ${template.templateName}
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row g-3 mb-4">
                                <div class="col-md-4">
                                    <div class="card text-center">
                                        <div class="card-body">
                                            <i class="bi bi-robot text-primary" style="font-size: 2rem;"></i>
                                            <h5 class="mt-2">${usingAgents.length}</h5>
                                            <small class="text-muted">使用的Agent</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card text-center">
                                        <div class="card-body">
                                            <i class="bi bi-file-code text-info" style="font-size: 2rem;"></i>
                                            <h5 class="mt-2">${template.templateCode}</h5>
                                            <small class="text-muted">模板编码</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card text-center">
                                        <div class="card-body">
                                            <i class="bi bi-tags text-success" style="font-size: 2rem;"></i>
                                            <h5 class="mt-2">${getCategoryText(template.category)}</h5>
                                            <small class="text-muted">分类</small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <h6 class="fw-bold mb-3">使用该模板的Agent列表</h6>
                            ${usingAgents.length > 0 ? `
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Agent名称</th>
                                                <th>Agent编码</th>
                                                <th>状态</th>
                                                <th>创建时间</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            ${usingAgents.map(agent => `
                                                <tr>
                                                    <td>${agent.agentName}</td>
                                                    <td><code class="small">${agent.agentCode}</code></td>
                                                    <td>
                                                        <span class="badge bg-${agent.status === 3 ? 'success' : agent.status === 2 ? 'warning' : 'secondary'}">
                                                            ${agent.status === 3 ? '已发布' : agent.status === 2 ? '测试中' : '草稿'}
                                                        </span>
                                                    </td>
                                                    <td>${agent.createdTime ? agent.createdTime.split(' ')[0] : '未知'}</td>
                                                </tr>
                                            `).join('')}
                                        </tbody>
                                    </table>
                                </div>
                            ` : `
                                <div class="text-center py-4">
                                    <i class="bi bi-inbox fs-1 text-muted"></i>
                                    <p class="mt-3 text-muted">暂无Agent使用此模板</p>
                                    <small class="text-muted">您可以在创建或编辑Agent时选择此模板</small>
                                </div>
                            `}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                            ${usingAgents.length === 0 ? `
                                <button type="button" class="btn btn-danger" onclick="deleteTemplate(${templateId}); bootstrap.Modal.getInstance(document.getElementById('templateUsageModal')).hide();">
                                    <i class="bi bi-trash"></i> 删除模板
                                </button>
                            ` : ''}
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 移除已存在的模态框
        const existingModal = document.getElementById('templateUsageModal');
        if (existingModal) {
            existingModal.remove();
        }

        // 添加新模态框
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('templateUsageModal'));
        modal.show();

    } catch (error) {
        console.error('查看模板使用情况失败:', error);
        alert('查看模板使用情况失败：' + error.message);
    }
}
