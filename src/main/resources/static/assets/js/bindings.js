// 页面绑定管理页面JavaScript

// 页面初始化函数
function initBindingsPage() {
    console.log('页面绑定页面初始化');
    loadBindings();

    // 添加搜索框回车键支持
    const searchInput = document.getElementById('bindingSearchInput');
    if (searchInput) {
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchBindings();
            }
        });
    }

    // 添加ESC键关闭字段选择器
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            hideFieldSelector();
        }
    });

    // 点击弹窗外部关闭字段选择器
    document.addEventListener('click', function(e) {
        const popup = document.getElementById('fieldSelectorPopup');
        if (e.target === popup) {
            hideFieldSelector();
        }
    });
}

// 全局变量存储字段数据
let allFields = [];
let boundFields = new Set();
let currentTargetElement = null;

// 加载绑定列表
async function loadBindings() {
    const container = document.getElementById('bindingsTable');
    if (!container) return;

    try {
        // 显示加载状态
        container.innerHTML = `
            <div class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <div class="mt-2 text-muted">正在加载绑定列表...</div>
            </div>
        `;

        // 调用真实API
        const response = await apiCall('/api/v1/page-bindings');
        let bindings = [];

        if (response.code === 200) {
            bindings = response.data.map(binding => ({
                id: binding.id,
                bindingName: binding.bindingName,
                targetUrl: binding.targetUrl,
                urlPattern: binding.urlPattern,
                templateId: binding.templateId,
                status: binding.status,
                createdTime: binding.createdTime,
                updatedTime: binding.updatedTime,
                bindingConfig: binding.bindingConfig,
                businessTemplate: binding.businessTemplate
            }));
        } else {
            // 如果API失败，使用模拟数据
            bindings = [
                {
                    id: 1,
                    bindingName: '财务系统绑定',
                    targetUrl: 'https://finance.example.com/form',
                    urlPattern: 'finance.example.com/form',
                    templateId: 1,
                    status: 1,
                    createdTime: '2024-01-15 10:30:00',
                    updatedTime: '2024-01-22 15:20:00'
                },
                {
                    id: 2,
                    bindingName: '人事系统绑定',
                    targetUrl: 'https://hr.example.com/employee',
                    urlPattern: 'hr.example.com/employee',
                    templateId: 2,
                    status: 1,
                    createdTime: '2024-01-18 14:15:00',
                    updatedTime: '2024-01-21 09:45:00'
                },
                {
                    id: 3,
                    bindingName: '采购系统绑定',
                    targetUrl: 'https://purchase.example.com/invoice',
                    urlPattern: 'purchase.example.com/invoice',
                    templateId: 3,
                    status: 0,
                    createdTime: '2024-01-20 16:00:00',
                    updatedTime: '2024-01-20 16:00:00'
                }
            ];
        }
        
        // 渲染绑定表格
        renderBindingsTable(bindings);

        // 更新统计信息
        updateBindingStats(bindings);
        
    } catch (error) {
        console.error('加载绑定列表失败:', error);
        container.innerHTML = '<div class="alert alert-danger">加载绑定列表失败</div>';
    }
}

// 渲染绑定表格
function renderBindingsTable(bindings) {
    const container = document.getElementById('bindingsTable');

    if (bindings.length === 0) {
        container.innerHTML = `
            <div class="text-center py-5">
                <i class="bi bi-inbox fs-1 text-muted"></i>
                <p class="mt-3 text-muted">暂无页面绑定</p>
                <button class="btn btn-primary" onclick="showCreateBindingModal()">
                    <i class="bi bi-plus-lg"></i> 创建第一个绑定
                </button>
            </div>
        `;
        return;
    }

    container.innerHTML = `
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead>
                    <tr>
                        <th>绑定名称</th>
                        <th>URL模式</th>
                        <th>状态</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    ${bindings.map(binding => {
                        const statusInfo = getBindingStatusInfo(binding.status);
                        const createdTime = binding.createdTime ? binding.createdTime.split(' ')[0] : '未知';

                        return `
                            <tr>
                                <td>
                                    <div>
                                        <div class="fw-medium">${binding.bindingName}</div>
                                        <small class="text-muted">
                                            <i class="bi bi-link-45deg me-1"></i>
                                            <a href="${binding.targetUrl}" target="_blank" class="text-decoration-none">
                                                ${binding.targetUrl.length > 50 ? binding.targetUrl.substring(0, 50) + '...' : binding.targetUrl}
                                            </a>
                                        </small>
                                    </div>
                                </td>
                                <td><code class="small">${binding.urlPattern || '-'}</code></td>
                                <td>
                                    <span class="badge ${statusInfo.class}">
                                        ${statusInfo.text}
                                    </span>
                                </td>
                                <td class="small text-muted">${createdTime}</td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary" onclick="viewBinding(${binding.id})" title="查看详情">
                                            <i class="bi bi-eye"></i>
                                        </button>
                                        <button class="btn btn-outline-warning" onclick="editBinding(${binding.id})" title="编辑">
                                            <i class="bi bi-pencil"></i>
                                        </button>
                                        <button class="btn btn-outline-success" onclick="testBinding(${binding.id})" title="测试">
                                            <i class="bi bi-play"></i>
                                        </button>
                                        <button class="btn btn-outline-danger" onclick="deleteBinding(${binding.id})" title="删除">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        `;
                    }).join('')}
                </tbody>
            </table>
        </div>
    `;
}

// 更新绑定统计信息
function updateBindingStats(bindings) {
    const totalBindings = bindings.length;
    const activeBindings = bindings.filter(b => b.status === 1).length;
    const uniqueDomains = [...new Set(bindings.map(b => {
        try {
            return new URL(b.targetUrl).hostname;
        } catch {
            return 'unknown';
        }
    }))].length;

    document.getElementById('totalBindings').textContent = totalBindings;
    document.getElementById('activeBindings').textContent = activeBindings;
    document.getElementById('uniqueDomains').textContent = uniqueDomains;
}

// 获取绑定状态信息
function getBindingStatusInfo(status) {
    const statusMap = {
        1: { class: 'bg-success', text: '启用' },
        0: { class: 'bg-secondary', text: '禁用' }
    };
    return statusMap[status] || { class: 'bg-secondary', text: '未知' };
}

// 显示创建绑定模态框
async function showCreateBindingModal() {
    // 加载业务模板列表
    try {
        const response = await apiCall('/api/v1/business-templates/active');
        if (response.code === 200) {
            const templates = response.data;
            const select = document.getElementById('bindingTemplateSelect');
            select.innerHTML = '<option value="">请选择业务模板</option>';
            templates.forEach(template => {
                select.innerHTML += `<option value="${template.id}">${template.templateName} (${template.templateCode})</option>`;
            });
        }
    } catch (error) {
        console.error('Failed to load templates:', error);
    }

    // 重置表单和状态
    document.getElementById('createBindingForm').reset();
    allFields = [];
    boundFields.clear();

    // 清空字段列表和预览
    document.getElementById('jsonFieldsList').innerHTML = `
        <div class="text-muted text-center py-4">
            <i class="bi bi-file-code fs-1"></i>
            <p class="mt-2">请先选择业务模板</p>
        </div>
    `;

    // 重置字段计数显示
    const fieldsCountElement = document.getElementById('fieldsCount');
    if (fieldsCountElement) {
        fieldsCountElement.textContent = '0 个字段';
    }

    document.getElementById('pagePreview').innerHTML = `
        <div class="text-muted text-center py-5">
            <i class="bi bi-globe fs-1"></i>
            <p class="mt-2">请输入目标URL并点击"加载页面"</p>
            <small>如果页面需要登录，会自动打开新窗口供您登录</small>

            <!-- 临时测试输入框 -->
            <div class="mt-4 p-3 border rounded bg-light">
                <h6>测试输入框（临时）</h6>
                <div class="mb-2">
                    <input type="text" class="form-control" placeholder="发票号码" id="testInput1">
                </div>
                <div class="mb-2">
                    <input type="text" class="form-control" placeholder="开票日期" id="testInput2">
                </div>
                <div class="mb-2">
                    <input type="text" class="form-control" placeholder="总金额" id="testInput3">
                </div>
                <button class="btn btn-sm btn-primary" onclick="setupInputClickBinding()">
                    设置点击绑定
                </button>
            </div>
        </div>
    `;

    // 重置统计
    updateFieldsCount();

    const modal = new bootstrap.Modal(document.getElementById('createBindingModal'));
    modal.show();

    // 模态框显示后，自动设置测试输入框的点击绑定
    modal._element.addEventListener('shown.bs.modal', function() {
        console.log('模态框已显示，设置点击绑定');
        setTimeout(() => {
            setupInputClickBinding();
        }, 500);
    });
}

// 解析嵌套JSON结构，支持对象和数组
function parseJsonFields(obj, prefix = '', result = []) {
    for (const [key, value] of Object.entries(obj)) {
        const currentPath = prefix ? `${prefix}.${key}` : key;

        if (Array.isArray(value)) {
            // 数组类型
            if (value.length > 0 && typeof value[0] === 'object') {
                // 对象数组
                result.push({
                    key: currentPath,
                    path: currentPath,
                    description: `${key} (对象数组)`,
                    type: 'array',
                    bound: false
                });

                // 递归解析数组中的对象
                parseJsonFields(value[0], `${currentPath}[0]`, result);
            } else {
                // 简单数组
                result.push({
                    key: currentPath,
                    path: currentPath,
                    description: `${key} (数组)`,
                    type: 'simple_array',
                    bound: false
                });
            }
        } else if (typeof value === 'object' && value !== null) {
            // 嵌套对象
            result.push({
                key: currentPath,
                path: currentPath,
                description: `${key} (对象)`,
                type: 'object',
                bound: false
            });

            // 递归解析嵌套对象
            parseJsonFields(value, currentPath, result);
        } else {
            // 简单字段
            result.push({
                key: currentPath,
                path: currentPath,
                description: typeof value === 'string' ? value : key,
                type: 'field',
                bound: false
            });
        }
    }

    return result;
}

// 加载选中模板的字段（支持嵌套结构）
async function loadTemplateFields() {
    const templateId = document.getElementById('bindingTemplateSelect').value;
    if (!templateId) {
        document.getElementById('jsonFieldsList').innerHTML = `
            <div class="text-muted text-center py-4">
                <i class="bi bi-file-code fs-1"></i>
                <p class="mt-2">请先选择业务模板</p>
            </div>
        `;
        allFields = [];
        boundFields.clear();
        updateFieldsCount();
        updateFieldSelectorList();
        return;
    }

    try {
        const response = await apiCall(`/api/v1/business-templates/${templateId}`);
        if (response.code === 200) {
            const template = response.data;
            const jsonObj = JSON.parse(template.jsonTemplate);

            // 解析嵌套JSON结构
            allFields = parseJsonFields(jsonObj);

            // 渲染字段列表
            renderFields();
            updateFieldsCount();
            updateFieldSelectorList();
        }
    } catch (error) {
        console.error('Failed to load template fields:', error);
        document.getElementById('jsonFieldsList').innerHTML = `
            <div class="alert alert-danger alert-sm">
                加载模板字段失败
            </div>
        `;
    }
}

// 渲染字段列表
function renderFields(filteredFields = null) {
    const fieldsToRender = filteredFields || allFields;
    const fieldsListDiv = document.getElementById('jsonFieldsList');

    if (fieldsToRender.length === 0) {
        fieldsListDiv.innerHTML = `
            <div class="text-muted text-center py-4">
                <i class="bi bi-search"></i>
                <p class="mt-2">没有找到匹配的字段</p>
            </div>
        `;
        return;
    }

    let fieldsHtml = '';
    fieldsToRender.forEach(field => {
        const isHidden = field.bound ? 'style="display: none;"' : '';
        const boundClass = field.bound ? 'bound-field' : '';
        const typeIcon = getFieldTypeIcon(field.type);

        fieldsHtml += `
            <div class="json-field-item p-2 mb-2 border rounded bg-light ${boundClass}"
                 ${isHidden}
                 data-field="${field.key}"
                 data-description="${field.description}">
                <div class="d-flex justify-content-between align-items-start">
                    <div class="flex-grow-1">
                        <div class="fw-bold text-primary">
                            ${typeIcon} ${field.key}
                        </div>
                        <small class="text-muted">${field.description}</small>
                        ${field.path !== field.key ? `<small class="text-success d-block">${field.path}</small>` : ''}
                    </div>
                    <div class="field-actions">
                        <button class="btn btn-sm btn-outline-secondary" onclick="copyFieldName('${field.key}')" title="复制字段名">
                            <i class="bi bi-clipboard"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;
    });

    fieldsListDiv.innerHTML = fieldsHtml;
}

// 获取字段类型图标
function getFieldTypeIcon(type) {
    switch (type) {
        case 'array': return '📋';
        case 'simple_array': return '📝';
        case 'object': return '📁';
        default: return '🏷️';
    }
}

// 更新字段计数
function updateFieldsCount() {
    const totalFields = allFields.length;
    const boundFieldsCount = boundFields.size;
    const unboundFields = totalFields - boundFieldsCount;

    // 更新各个计数显示
    const fieldsCountElement = document.getElementById('fieldsCount');
    if (fieldsCountElement) {
        fieldsCountElement.textContent = `${unboundFields}/${totalFields} 个字段`;
    }

    const jsonFieldCountElement = document.getElementById('jsonFieldCount');
    if (jsonFieldCountElement) {
        jsonFieldCountElement.textContent = totalFields;
    }

    const boundFieldCountElement = document.getElementById('boundFieldCount');
    if (boundFieldCountElement) {
        boundFieldCountElement.textContent = boundFieldsCount;
    }

    const unboundFieldCountElement = document.getElementById('unboundFieldCount');
    if (unboundFieldCountElement) {
        unboundFieldCountElement.textContent = unboundFields;
    }

    // 更新绑定状态显示
    const boundFieldsInfo = document.getElementById('boundFieldsInfo');
    const boundCount = document.getElementById('boundCount');
    const viewBindingsBtn = document.getElementById('viewBindingsBtn');
    const bindingCountInBtn = document.getElementById('bindingCountInBtn');

    if (boundFieldsCount > 0) {
        if (boundFieldsInfo) {
            boundFieldsInfo.style.display = 'block';
        }
        if (boundCount) {
            boundCount.textContent = boundFieldsCount;
        }
        if (viewBindingsBtn) {
            viewBindingsBtn.style.display = 'block';
        }
        if (bindingCountInBtn) {
            bindingCountInBtn.textContent = boundFieldsCount;
        }
    } else {
        if (boundFieldsInfo) {
            boundFieldsInfo.style.display = 'none';
        }
        if (viewBindingsBtn) {
            viewBindingsBtn.style.display = 'none';
        }
    }
}

// 测试绑定
function testBinding(id) {
    alert(`测试绑定 ${id} 功能开发中...`);
}

// 加载页面预览
async function loadPagePreview() {
    const targetUrl = document.querySelector('input[name="targetUrl"]').value.trim();
    const pageHtml = document.querySelector('textarea[name="pageHtml"]').value.trim();
    const preview = document.getElementById('pagePreview');

    if (!targetUrl && !pageHtml) {
        alert('请输入目标URL或粘贴HTML代码');
        return;
    }

    preview.innerHTML = `
        <div class="text-center py-3">
            <div class="spinner-border spinner-border-sm text-primary" role="status"></div>
            <span class="ms-2">正在加载页面...</span>
        </div>
    `;

    try {
        if (pageHtml) {
            // 使用提供的HTML
            renderPagePreview(pageHtml);
        } else {
            // 尝试加载URL（实际项目中需要后端代理）
            preview.innerHTML = `
                <div class="border rounded p-3">
                    <div class="alert alert-info alert-sm">
                        <i class="bi bi-info-circle me-2"></i>
                        由于跨域限制，无法直接加载外部页面。请复制页面HTML代码到"页面HTML"字段。
                    </div>
                    <div class="text-center">
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="openUrlInNewTab('${targetUrl}')">
                            <i class="bi bi-box-arrow-up-right"></i> 在新标签页中打开
                        </button>
                    </div>
                </div>
            `;
        }
    } catch (error) {
        console.error('加载页面预览失败:', error);
        preview.innerHTML = `
            <div class="alert alert-danger">
                加载页面预览失败：${error.message}
            </div>
        `;
    }
}

// 渲染页面预览
function renderPagePreview(html) {
    const preview = document.getElementById('pagePreview');

    // 创建iframe来安全地渲染HTML
    const iframe = document.createElement('iframe');
    iframe.style.width = '100%';
    iframe.style.height = '400px';
    iframe.style.border = '1px solid #dee2e6';
    iframe.style.borderRadius = '4px';

    preview.innerHTML = '';
    preview.appendChild(iframe);

    // 写入HTML内容
    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
    iframeDoc.open();
    iframeDoc.write(`
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <style>
                body { margin: 10px; font-family: Arial, sans-serif; }
                .page-element { position: relative; }
                .page-element:hover { outline: 2px dashed #007bff; outline-offset: 2px; }
                .page-element.bound { outline: 2px solid #28a745; outline-offset: 2px; background-color: rgba(40, 167, 69, 0.1); }
                .binding-indicator { position: absolute; top: -8px; right: -8px; background: #28a745; color: white; border-radius: 50%; width: 16px; height: 16px; font-size: 10px; display: flex; align-items: center; justify-content: center; z-index: 10; }
            </style>
        </head>
        <body>
            ${html}
        </body>
        </html>
    `);
    iframeDoc.close();

    // 等待iframe加载完成后添加事件监听
    iframe.onload = function() {
        addPageElementListeners(iframeDoc);
    };
}

// 添加页面元素事件监听
function addPageElementListeners(doc) {
    // 设置点击绑定，传递正确的文档
    setupInputClickBinding(doc);
}

// 从URL加载页面
async function loadPageFromUrl() {
    const urlInput = document.getElementById('targetUrlInput');
    const url = urlInput.value.trim();

    if (!url) {
        alert('请输入目标页面URL');
        return;
    }

    // 显示加载状态
    const statusDiv = document.getElementById('pageLoadStatus');
    const messageSpan = document.getElementById('pageLoadMessage');
    statusDiv.style.display = 'block';
    messageSpan.textContent = '正在加载页面...';

    try {
        // 尝试通过后端代理加载页面
        const response = await fetch('/api/v1/page-bindings/load-page', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('authToken')}`
            },
            body: JSON.stringify({ url: url })
        });

        if (response.ok) {
            const result = await response.json();
            if (result.code === 200) {
                const data = result.data;

                if (data.status === 'success') {
                    // 成功加载页面内容
                    displayPageContent(data.html);
                    let message = '页面加载成功！';
                    if (data.redirected) {
                        message += ` (重定向到: ${data.finalUrl})`;
                        if (data.redirectCount) {
                            message += ` [${data.redirectCount}次重定向]`;
                        }
                    }
                    messageSpan.textContent = message;
                    setTimeout(() => {
                        statusDiv.style.display = 'none';
                    }, 3000);
                } else if (data.status === 'empty') {
                    // 页面内容为空
                    messageSpan.textContent = data.message;
                    statusDiv.className = 'alert alert-warning alert-sm';
                    showFallbackOptions(data.url);
                } else if (data.status === 'redirect') {
                    // 重定向处理
                    messageSpan.textContent = data.message;
                    statusDiv.className = 'alert alert-info alert-sm';
                    showFallbackOptions(data.location || data.url);
                } else if (data.status === 'client_error') {
                    // 客户端错误（需要登录等）
                    messageSpan.textContent = data.message;
                    statusDiv.className = 'alert alert-warning alert-sm';
                    showFallbackOptions(data.url);
                } else if (data.status === 'server_error') {
                    // 服务器错误
                    messageSpan.textContent = data.message;
                    statusDiv.className = 'alert alert-danger alert-sm';
                    showFallbackOptions(data.url);
                } else if (data.status === 'fallback') {
                    // 需要手动处理
                    messageSpan.textContent = data.message;

                    // 添加打开新窗口按钮
                    const openButton = document.createElement('button');
                    openButton.className = 'btn btn-sm btn-warning ms-2';
                    openButton.innerHTML = '<i class="bi bi-box-arrow-up-right"></i> 打开页面';
                    openButton.onclick = () => {
                        const newWindow = window.open(url, '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes');
                        if (newWindow) {
                            messageSpan.textContent = '请在新窗口中完成登录，然后点击"手动获取页面"';

                            // 添加手动获取按钮
                            const manualButton = document.createElement('button');
                            manualButton.className = 'btn btn-sm btn-primary ms-2';
                            manualButton.innerHTML = '<i class="bi bi-download"></i> 手动获取页面';
                            manualButton.onclick = () => manualLoadPage(newWindow);

                            const alertDiv = statusDiv.querySelector('.alert');
                            alertDiv.appendChild(manualButton);
                        }
                    };

                    const alertDiv = statusDiv.querySelector('.alert');
                    alertDiv.appendChild(openButton);
                }
            } else {
                throw new Error(result.message || '加载失败');
            }
        } else {
            throw new Error('网络请求失败');
        }
    } catch (error) {
        console.error('页面加载失败:', error);
        messageSpan.textContent = '加载失败: ' + error.message;

        // 提供备选方案
        const retryButton = document.createElement('button');
        retryButton.className = 'btn btn-sm btn-outline-primary ms-2';
        retryButton.innerHTML = '<i class="bi bi-arrow-clockwise"></i> 重试';
        retryButton.onclick = () => {
            statusDiv.style.display = 'none';
            loadPageFromUrl();
        };

        const alertDiv = statusDiv.querySelector('.alert');
        alertDiv.appendChild(retryButton);
    }
}

// 手动从窗口获取页面内容
function manualLoadPage(targetWindow) {
    try {
        if (targetWindow && !targetWindow.closed) {
            // 尝试获取窗口内容
            const doc = targetWindow.document;
            const html = doc.documentElement.outerHTML;

            displayPageContent(html);
            targetWindow.close();

            const statusDiv = document.getElementById('pageLoadStatus');
            const messageSpan = document.getElementById('pageLoadMessage');
            messageSpan.textContent = '页面内容获取成功！';

            setTimeout(() => {
                statusDiv.style.display = 'none';
            }, 2000);
        } else {
            showToast('目标窗口已关闭或无法访问', 'error');
        }
    } catch (error) {
        console.error('获取页面内容失败:', error);
        showToast('获取页面内容失败，可能是跨域限制。请尝试使用Chrome插件进行绑定。', 'error');
    }
}

// 显示手动输入的HTML内容（同源，可以直接绑定）
function displayManualHtmlContent(html) {
    const previewDiv = document.getElementById('pagePreview');

    // 创建iframe来显示手动输入的HTML内容
    const iframe = document.createElement('iframe');
    iframe.style.width = '100%';
    iframe.style.height = '100%';
    iframe.style.border = 'none';

    // 手动输入的HTML是同源的，可以安全访问
    iframe.setAttribute('sandbox', 'allow-same-origin allow-scripts allow-forms');

    previewDiv.innerHTML = '';
    previewDiv.appendChild(iframe);

    // 写入HTML内容
    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
    iframeDoc.open();
    iframeDoc.write(html);
    iframeDoc.close();

    // 显示开始绑定按钮
    document.getElementById('startBindingBtn').style.display = 'inline-block';

    // 隐藏跨域帮助（如果存在）
    hideCrossOriginBindingHelp();

    console.log('手动HTML内容已加载，可以进行绑定操作');
}

// 显示页面内容
function displayPageContent(html) {
    const previewDiv = document.getElementById('pagePreview');

    // 创建一个安全的iframe来显示页面内容
    const iframe = document.createElement('iframe');
    iframe.style.width = '100%';
    iframe.style.height = '100%';
    iframe.style.border = 'none';

    // 设置sandbox属性以提高安全性，但允许脚本执行
    iframe.setAttribute('sandbox', 'allow-same-origin allow-scripts allow-forms');

    previewDiv.innerHTML = '';
    previewDiv.appendChild(iframe);

    try {
        // 写入HTML内容
        const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
        iframeDoc.open();
        iframeDoc.write(html);
        iframeDoc.close();

        // 显示开始绑定按钮
        document.getElementById('startBindingBtn').style.display = 'inline-block';

        // 隐藏跨域帮助（如果存在）
        hideCrossOriginBindingHelp();

    } catch (error) {
        console.error('写入iframe内容失败:', error);

        // 如果无法写入内容，显示错误信息
        previewDiv.innerHTML = `
            <div class="alert alert-warning">
                <h6><i class="bi bi-exclamation-triangle me-2"></i>内容加载失败</h6>
                <p>无法在iframe中显示页面内容，可能是由于安全限制。</p>
                <button class="btn btn-sm btn-primary" onclick="showManualHtmlModal()">
                    <i class="bi bi-code-square"></i> 使用手动输入HTML
                </button>
            </div>
        `;
    }
}

// 打开目标页面
function openTargetPage() {
    const urlInput = document.getElementById('targetUrlInput');
    const url = urlInput.value.trim();

    if (!url) {
        showToast('请先输入目标页面URL', 'warning');
        return;
    }

    window.open(url, '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes');
}

// 显示手动HTML输入模态框
function showManualHtmlModal() {
    const modal = new bootstrap.Modal(document.getElementById('manualHtmlModal'));

    // 清空之前的内容
    document.getElementById('manualUrlInput').value = '';
    document.getElementById('manualHtmlTextarea').value = '';
    updateHtmlCharCount();

    // 如果有URL，自动填入
    const urlInput = document.getElementById('targetUrlInput');
    if (urlInput.value.trim()) {
        document.getElementById('manualUrlInput').value = urlInput.value.trim();
    }

    modal.show();
}

// 更新HTML字符计数
function updateHtmlCharCount() {
    const textarea = document.getElementById('manualHtmlTextarea');
    const countSpan = document.getElementById('htmlCharCount');
    countSpan.textContent = textarea.value.length.toLocaleString();
}

// 加载手动输入的HTML
async function loadManualHtml() {
    const urlInput = document.getElementById('manualUrlInput');
    const htmlTextarea = document.getElementById('manualHtmlTextarea');

    const url = urlInput.value.trim();
    const html = htmlTextarea.value.trim();

    if (!html) {
        alert('请输入HTML内容');
        htmlTextarea.focus();
        return;
    }

    try {
        // 显示加载状态
        const statusDiv = document.getElementById('pageLoadStatus');
        const messageSpan = document.getElementById('pageLoadMessage');
        statusDiv.style.display = 'block';
        messageSpan.textContent = '正在处理HTML内容...';

        // 调用后端API处理HTML
        const response = await fetch('/api/v1/page-bindings/load-manual-html', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('authToken')}`
            },
            body: JSON.stringify({
                html: html,
                url: url || '手动输入'
            })
        });

        if (response.ok) {
            const result = await response.json();
            if (result.code === 200) {
                const data = result.data;

                // 显示HTML内容
                displayManualHtmlContent(data.html);
                messageSpan.textContent = 'HTML内容加载成功！';
                messageSpan.className = 'text-success';

                // 关闭模态框
                const modal = bootstrap.Modal.getInstance(document.getElementById('manualHtmlModal'));
                modal.hide();

                // 3秒后隐藏状态
                setTimeout(() => {
                    statusDiv.style.display = 'none';
                    messageSpan.className = '';
                }, 3000);

            } else {
                throw new Error(result.message || '处理HTML失败');
            }
        } else {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

    } catch (error) {
        console.error('加载手动HTML失败:', error);

        const statusDiv = document.getElementById('pageLoadStatus');
        const messageSpan = document.getElementById('pageLoadMessage');
        statusDiv.style.display = 'block';
        messageSpan.textContent = `加载失败: ${error.message}`;
        messageSpan.className = 'text-danger';

        // 5秒后隐藏错误信息
        setTimeout(() => {
            statusDiv.style.display = 'none';
            messageSpan.className = '';
        }, 5000);
    }
}

// 开始元素绑定模式
function startElementBinding() {
    const iframe = document.querySelector('#pagePreview iframe');
    if (iframe) {
        try {
            // 尝试访问iframe内容
            const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
            if (iframeDoc) {
                setupInputClickBinding(iframeDoc);

                // 切换按钮状态
                document.getElementById('startBindingBtn').style.display = 'none';
                document.getElementById('stopBindingBtn').style.display = 'inline-block';

                // 显示提示信息
                showBindingModeNotification(true);
            } else {
                throw new Error('无法访问iframe内容');
            }
        } catch (error) {
            console.error('跨域访问错误:', error);
            // 如果是跨域问题，提供替代方案
            showCrossOriginBindingHelp();
        }
    } else {
        showToast('请先加载页面内容', 'warning');
    }
}

// 停止元素绑定模式
function stopElementBinding() {
    const iframe = document.querySelector('#pagePreview iframe');
    if (iframe) {
        try {
            const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
            if (iframeDoc) {
                removeInputClickBinding(iframeDoc);
            }
        } catch (error) {
            console.log('停止绑定时的跨域访问错误（可忽略）:', error);
        }

        // 切换按钮状态
        document.getElementById('startBindingBtn').style.display = 'inline-block';
        document.getElementById('stopBindingBtn').style.display = 'none';

        // 隐藏提示信息
        showBindingModeNotification(false);
        hideCrossOriginBindingHelp();
    }
}

// 显示跨域绑定帮助
function showCrossOriginBindingHelp() {
    const previewDiv = document.getElementById('pagePreview');

    // 在iframe上方添加帮助信息
    let helpDiv = document.getElementById('crossOriginHelp');
    if (!helpDiv) {
        helpDiv = document.createElement('div');
        helpDiv.id = 'crossOriginHelp';
        helpDiv.className = 'alert alert-warning mb-2';
        helpDiv.innerHTML = `
            <div class="d-flex justify-content-between align-items-start">
                <div>
                    <h6><i class="bi bi-exclamation-triangle me-2"></i>跨域限制</h6>
                    <p class="mb-2">由于浏览器安全策略，无法直接在此页面中绑定输入框。</p>
                    <div class="d-flex gap-2">
                        <button class="btn btn-sm btn-primary" onclick="showManualHtmlModal()">
                            <i class="bi bi-code-square"></i> 手动输入HTML
                        </button>
                        <button class="btn btn-sm btn-outline-info" onclick="showCrossOriginInstructions()">
                            <i class="bi bi-question-circle"></i> 查看解决方案
                        </button>
                    </div>
                </div>
                <button type="button" class="btn-close" onclick="hideCrossOriginBindingHelp()"></button>
            </div>
        `;

        previewDiv.parentNode.insertBefore(helpDiv, previewDiv);
    }
    helpDiv.style.display = 'block';
}

// 隐藏跨域绑定帮助
function hideCrossOriginBindingHelp() {
    const helpDiv = document.getElementById('crossOriginHelp');
    if (helpDiv) {
        helpDiv.style.display = 'none';
    }
}

// 显示跨域解决方案说明
function showCrossOriginInstructions() {
    const modal = new bootstrap.Modal(document.getElementById('crossOriginModal') || createCrossOriginModal());
    modal.show();
}

// 创建跨域解决方案模态框
function createCrossOriginModal() {
    const modalHtml = `
        <div class="modal fade" id="crossOriginModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="bi bi-shield-exclamation me-2"></i>跨域限制解决方案
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle me-2"></i>
                            <strong>什么是跨域限制？</strong><br>
                            浏览器的同源策略阻止网页访问不同域名的iframe内容，这是为了保护用户安全。
                        </div>

                        <h6>解决方案：</h6>
                        <div class="row g-3">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-body">
                                        <h6 class="card-title">
                                            <i class="bi bi-code-square text-primary"></i> 方案一：手动输入HTML
                                        </h6>
                                        <p class="card-text small">复制页面HTML源代码，在本系统中进行绑定配置。</p>
                                        <button class="btn btn-sm btn-primary" onclick="showManualHtmlModal(); bootstrap.Modal.getInstance(document.getElementById('crossOriginModal')).hide();">
                                            开始使用
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-body">
                                        <h6 class="card-title">
                                            <i class="bi bi-browser-chrome text-success"></i> 方案二：浏览器插件
                                        </h6>
                                        <p class="card-text small">安装我们的浏览器插件，直接在目标页面中进行绑定。</p>
                                        <button class="btn btn-sm btn-outline-success" disabled>
                                            即将推出
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mt-3">
                            <h6>推荐流程：</h6>
                            <ol>
                                <li>在新窗口中打开目标页面</li>
                                <li>完成登录等必要操作</li>
                                <li>按F12打开开发者工具</li>
                                <li>复制页面HTML源代码</li>
                                <li>使用"手动输入HTML"功能进行绑定</li>
                            </ol>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                        <button type="button" class="btn btn-primary" onclick="showManualHtmlModal(); bootstrap.Modal.getInstance(document.getElementById('crossOriginModal')).hide();">
                            使用手动输入HTML
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);
    return document.getElementById('crossOriginModal');
}

// 显示/隐藏绑定模式通知
function showBindingModeNotification(show) {
    let notification = document.getElementById('bindingModeNotification');

    if (show) {
        if (!notification) {
            notification = document.createElement('div');
            notification.id = 'bindingModeNotification';
            notification.className = 'alert alert-info alert-sm mb-2';
            notification.innerHTML = `
                <i class="bi bi-cursor me-2"></i>
                <strong>绑定模式已启用</strong> - 点击页面中的输入框来建立字段绑定
            `;

            const previewContainer = document.getElementById('pagePreview').parentNode;
            previewContainer.insertBefore(notification, document.getElementById('pagePreview'));
        }
        notification.style.display = 'block';
    } else {
        if (notification) {
            notification.style.display = 'none';
        }
    }
}

// 刷新预览
function refreshPreview() {
    const iframe = document.querySelector('#pagePreview iframe');
    if (iframe) {
        // 如果当前是绑定模式，重新启动绑定
        const stopBtn = document.getElementById('stopBindingBtn');
        if (stopBtn.style.display !== 'none') {
            startElementBinding();
        }
    } else {
        // 如果没有iframe，显示默认内容
        const preview = document.getElementById('pagePreview');
        preview.innerHTML = `
            <div class="text-muted text-center py-5">
                <i class="bi bi-globe fs-1"></i>
                <p class="mt-2">请输入目标URL并点击"加载页面"</p>
                <small>如果页面需要登录，会自动打开新窗口供您登录</small>

                <!-- 临时测试输入框 -->
                <div class="mt-4 p-3 border rounded bg-light">
                    <h6>测试输入框（临时）</h6>
                    <div class="mb-2">
                        <input type="text" class="form-control" placeholder="发票号码" id="testInput1">
                    </div>
                    <div class="mb-2">
                        <input type="text" class="form-control" placeholder="开票日期" id="testInput2">
                    </div>
                    <div class="mb-2">
                        <input type="text" class="form-control" placeholder="总金额" id="testInput3">
                    </div>
                    <button class="btn btn-sm btn-primary" onclick="startElementBinding()">
                        开始绑定
                    </button>
                </div>
            </div>
        `;

        // 隐藏绑定按钮
        document.getElementById('startBindingBtn').style.display = 'none';
        document.getElementById('stopBindingBtn').style.display = 'none';
    }
}

// 清空字段搜索
function clearFieldSearch() {
    const searchInput = document.getElementById('fieldSearchInput');
    searchInput.value = '';
    filterFields();
}

// 高亮输入框
function highlightInputs() {
    const inputs = document.querySelectorAll('#pagePreview input, #pagePreview select, #pagePreview textarea');
    inputs.forEach(input => {
        input.style.outline = '2px solid #007bff';
        input.style.outlineOffset = '2px';
        setTimeout(() => {
            input.style.outline = '';
            input.style.outlineOffset = '';
        }, 2000);
    });
}

// 自动检测字段
function autoDetectFields() {
    alert('智能识别功能开发中...');
}

// 智能绑定
function autoBindFields() {
    alert('智能绑定功能开发中...');
}

// 保存草稿
function saveAsDraft() {
    alert('保存草稿功能开发中...');
}

// 选择页面元素
function selectPageElement(element) {
    // 显示字段选择器
    showFieldSelector(element);
}



// 显示字段选择器弹窗
function showFieldSelector(element) {
    console.log('显示字段选择器，目标元素:', element);

    currentTargetElement = element;
    const popup = document.getElementById('fieldSelectorPopup');

    if (!popup) {
        console.error('找不到字段选择器弹窗元素');
        return;
    }

    popup.style.display = 'flex';

    // 清空搜索框
    const searchInput = document.getElementById('fieldSelectorSearch');
    if (searchInput) {
        searchInput.value = '';
    }

    // 更新字段列表
    updateFieldSelectorList();

    // 聚焦搜索框
    setTimeout(() => {
        if (searchInput) {
            searchInput.focus();
        }
    }, 100);

    console.log('字段选择器已显示');
}

// 隐藏字段选择器
function hideFieldSelector() {
    const popup = document.getElementById('fieldSelectorPopup');
    if (popup) {
        popup.style.display = 'none';
    }
    currentTargetElement = null;
}

// 更新字段选择器列表
function updateFieldSelectorList(filteredFields = null) {
    const container = document.getElementById('fieldSelectorList');
    const fieldsToShow = filteredFields || allFields;

    if (!fieldsToShow || fieldsToShow.length === 0) {
        container.innerHTML = `
            <div class="text-muted text-center py-3">
                <i class="bi bi-file-code"></i>
                <p class="mt-1 mb-0 small">请先选择业务模板</p>
            </div>
        `;
        return;
    }

    let html = '';
    fieldsToShow.forEach(field => {
        const isBound = field.bound;
        const boundClass = isBound ? 'bound' : '';
        const typeIcon = getFieldTypeIcon(field.type);

        html += `
            <div class="field-selector-item ${boundClass}"
                 onclick="${isBound ? '' : `selectField('${field.key}')`}">
                <div class="field-name">
                    ${typeIcon} ${field.key}
                    ${isBound ? '<span class="badge bg-secondary ms-2">已绑定</span>' : ''}
                </div>
                <div class="field-description">${field.description}</div>
                ${field.path !== field.key ? `<div class="field-path">${field.path}</div>` : ''}
            </div>
        `;
    });

    container.innerHTML = html;
}

// 选择字段
function selectField(fieldKey) {
    if (!currentTargetElement) return;

    const field = allFields.find(f => f.key === fieldKey);
    if (!field || field.bound) return;

    // 创建绑定
    createFieldBinding(fieldKey, field.description, currentTargetElement);

    // 隐藏选择器
    hideFieldSelector();
}

// 创建字段绑定（优化版）
function createFieldBinding(fieldName, description, element) {
    // 检查字段是否已经绑定到其他元素
    if (boundFields.has(fieldName)) {
        // 找到已绑定的元素
        const existingElement = findBoundElement(fieldName);
        if (existingElement && existingElement !== element) {
            alert(`字段 "${fieldName}" 已经绑定到其他输入框，请先解除绑定或选择其他字段`);
            return;
        }
    }

    // 检查元素是否已经绑定其他字段
    if (element.dataset.boundField) {
        const oldFieldName = element.dataset.boundField;

        // 如果绑定的是同一个字段，直接返回
        if (oldFieldName === fieldName) {
            return;
        }

        // 解除旧绑定，恢复旧字段到可选列表
        unbindField(oldFieldName);
    }

    // 为元素添加标识
    element.dataset.boundField = fieldName;
    element.style.backgroundColor = '#e7f3ff';
    element.style.borderColor = '#007bff';
    element.title = `绑定字段: ${fieldName} (${description})`;

    // 更新输入框显示
    const originalPlaceholder = element.getAttribute('data-original-placeholder') || '';
    element.placeholder = `[${fieldName}] ${originalPlaceholder}`;

    // 更新字段状态
    boundFields.add(fieldName);
    const field = allFields.find(f => f.key === fieldName);
    if (field) {
        field.bound = true;
    }

    // 隐藏已绑定的字段（拖一个少一个效果）
    const fieldElement = document.querySelector(`[data-field="${fieldName}"]`);
    if (fieldElement) {
        fieldElement.style.display = 'none';
    }

    // 更新显示
    updateBindingRelations();
    updateFieldsCount();
    renderFields(); // 重新渲染字段列表
}

// 解除字段绑定
function unbindField(fieldName) {
    boundFields.delete(fieldName);

    const field = allFields.find(f => f.key === fieldName);
    if (field) {
        field.bound = false;
    }

    // 显示字段到可选列表
    const fieldElement = document.querySelector(`[data-field="${fieldName}"]`);
    if (fieldElement) {
        fieldElement.style.display = 'block';
    }

    // 更新显示
    updateBindingRelations();
    updateFieldsCount();
    renderFields();
}

// 查找绑定元素（支持iframe）
function findBoundElement(fieldName) {
    // 首先在主文档中查找
    let element = document.querySelector(`#pagePreview [data-bound-field="${fieldName}"]`);

    // 如果没找到，在iframe中查找
    if (!element) {
        const iframe = document.querySelector('#pagePreview iframe');
        if (iframe) {
            try {
                const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                if (iframeDoc) {
                    element = iframeDoc.querySelector(`[data-bound-field="${fieldName}"]`);
                }
            } catch (error) {
                console.warn('无法访问iframe内容:', error);
            }
        }
    }

    return element;
}

// 过滤字段选择器中的字段
function filterSelectorFields() {
    const searchInput = document.getElementById('fieldSelectorSearch');
    const query = searchInput.value.toLowerCase().trim();

    if (!query) {
        updateFieldSelectorList();
        return;
    }

    const filteredFields = allFields.filter(field =>
        field.key.toLowerCase().includes(query) ||
        field.description.toLowerCase().includes(query) ||
        field.path.toLowerCase().includes(query)
    );

    updateFieldSelectorList(filteredFields);
}

// 编辑绑定
async function editBinding(bindingId) {
    try {
        console.log('编辑绑定:', bindingId);

        // 调用API获取绑定详情
        const response = await apiCall(`/api/v1/page-bindings/${bindingId}`);

        if (response.code !== 200) {
            throw new Error(response.message || '获取绑定详情失败');
        }

        const binding = response.data;
        console.log('编辑绑定数据:', binding);

        // 填充编辑表单
        document.getElementById('editBindingId').value = binding.id;
        document.getElementById('editBindingName').value = binding.bindingName || '';
        document.getElementById('editBindingDescription').value = binding.description || '';
        document.getElementById('editTargetUrl').value = binding.targetUrl || '';
        document.getElementById('editUrlPattern').value = binding.urlPattern || '';
        document.getElementById('editBindingStatus').value = binding.status || 1;

        // 加载业务模板列表并选中当前模板
        await loadEditTemplateOptions(binding.templateId);

        // 显示编辑模态框
        const modal = new bootstrap.Modal(document.getElementById('editBindingModal'));
        modal.show();

    } catch (error) {
        console.error('编辑绑定失败:', error);
        alert('编辑绑定失败: ' + error.message);
    }
}

// 加载编辑模板选项
async function loadEditTemplateOptions(selectedTemplateId) {
    try {
        const response = await apiCall('/api/v1/business-templates/active');
        if (response.code === 200) {
            const templates = response.data;
            const select = document.getElementById('editBindingTemplateSelect');
            select.innerHTML = '<option value="">请选择业务模板</option>';

            templates.forEach(template => {
                const option = document.createElement('option');
                option.value = template.id;
                option.textContent = `${template.templateName} (${template.templateCode})`;
                if (template.id === selectedTemplateId) {
                    option.selected = true;
                }
                select.appendChild(option);
            });
        }
    } catch (error) {
        console.error('加载模板选项失败:', error);
    }
}

// 更新绑定
async function updateBinding() {
    try {
        const bindingId = document.getElementById('editBindingId').value;
        const bindingName = document.getElementById('editBindingName').value.trim();
        const templateId = document.getElementById('editBindingTemplateSelect').value;
        const targetUrl = document.getElementById('editTargetUrl').value.trim();
        const urlPattern = document.getElementById('editUrlPattern').value.trim();
        const status = parseInt(document.getElementById('editBindingStatus').value);
        const description = document.getElementById('editBindingDescription').value.trim();

        // 验证必填字段
        if (!bindingName || !templateId || !targetUrl || !urlPattern) {
            alert('请填写所有必填字段');
            return;
        }

        const bindingData = {
            bindingName: bindingName,
            templateId: parseInt(templateId),
            targetUrl: targetUrl,
            urlPattern: urlPattern,
            description: description,
            status: status
        };

        console.log('更新绑定数据:', bindingData);

        // 调用API更新绑定
        const response = await apiCall(`/api/v1/page-bindings/${bindingId}`, 'PUT', bindingData);

        if (response.code !== 200) {
            throw new Error(response.message || '更新绑定失败');
        }

        // 显示成功消息
        alert('绑定更新成功');

        // 关闭模态框
        bootstrap.Modal.getInstance(document.getElementById('editBindingModal')).hide();

        // 重新加载绑定列表
        loadBindings();

    } catch (error) {
        console.error('更新绑定失败:', error);
        alert('更新绑定失败: ' + error.message);
    }
}

// 创建绑定
async function createBinding() {
    const form = document.getElementById('createBindingForm');
    const formData = new FormData(form);

    // 验证必填字段
    const bindingName = formData.get('bindingName').trim();
    const templateId = formData.get('templateId');
    const targetUrl = formData.get('targetUrl').trim();
    const urlPattern = formData.get('urlPattern').trim();

    if (!bindingName || !templateId || !targetUrl || !urlPattern) {
        alert('请填写所有必填字段');
        return;
    }

    // 收集绑定关系
    const boundElements = getAllBoundElements();
    const bindingConfig = {};

    boundElements.forEach(element => {
        const fieldName = element.dataset.boundField;
        const selector = element.id ? `#${element.id}` :
                        element.name ? `[name="${element.name}"]` :
                        element.tagName.toLowerCase();

        bindingConfig[fieldName] = {
            selector: selector,
            type: element.tagName.toLowerCase(),
            id: element.id,
            name: element.name,
            placeholder: element.placeholder
        };
    });

    if (Object.keys(bindingConfig).length === 0) {
        alert('请至少绑定一个字段');
        return;
    }

    const bindingData = {
        bindingName: bindingName,
        templateId: parseInt(templateId),
        targetUrl: targetUrl,
        urlPattern: urlPattern,
        pageHtml: formData.get('pageHtml') || '',
        bindingConfig: JSON.stringify(bindingConfig),
        status: 1
    };

    try {
        console.log('创建页面绑定，数据:', bindingData);

        const response = await apiCall('/api/v1/page-bindings', 'POST', bindingData);

        if (response.code === 200) {
            alert('页面绑定创建成功！');
            bootstrap.Modal.getInstance(document.getElementById('createBindingModal')).hide();
            form.reset();

            // 清理绑定状态
            resetAllFields();

            // 重新加载绑定列表
            loadBindings();
        } else {
            alert('创建失败：' + (response.message || '未知错误'));
        }
    } catch (error) {
        console.error('创建绑定失败:', error);
        alert('创建失败：' + error.message);
    }
}

// 删除绑定
async function deleteBinding(bindingId) {
    if (!confirm('确定要删除这个绑定吗？删除后无法恢复。')) {
        return;
    }

    try {
        console.log('删除绑定:', bindingId);

        const response = await apiCall(`/api/v1/page-bindings/${bindingId}`, 'DELETE');

        if (response.code === 200) {
            alert('绑定删除成功！');
            loadBindings();
        } else {
            alert('删除失败：' + (response.message || '删除绑定失败'));
        }
    } catch (error) {
        console.error('删除绑定失败:', error);
        alert('删除失败：' + error.message);
    }
}

// 查看绑定详情
async function viewBinding(bindingId) {
    try {
        console.log('查看绑定详情:', bindingId);

        const response = await apiCall(`/api/v1/page-bindings/${bindingId}`);

        if (response.code !== 200) {
            throw new Error(response.message || '获取绑定详情失败');
        }

        const binding = response.data;
        console.log('绑定详情数据:', binding);

        // 获取业务模板信息
        let templateInfo = '未知模板';
        if (binding.templateId) {
            try {
                const templateResponse = await apiCall(`/api/v1/business-templates/${binding.templateId}`);
                if (templateResponse.code === 200) {
                    const template = templateResponse.data;
                    templateInfo = `${template.templateName} (${template.templateCode})`;
                }
            } catch (e) {
                console.warn('获取模板信息失败:', e);
            }
        }

        // 解析绑定配置
        let bindingConfig = {};
        try {
            bindingConfig = JSON.parse(binding.bindingConfig || '{}');
        } catch (e) {
            console.warn('解析绑定配置失败:', e);
        }

        const detailHtml = `
            <div class="row g-4">
                <div class="col-md-6">
                    <h6 class="fw-bold text-primary mb-3">基本信息</h6>
                    <table class="table table-sm">
                        <tr><td class="fw-medium">绑定名称:</td><td>${binding.bindingName}</td></tr>
                        <tr><td class="fw-medium">业务模板:</td><td>${templateInfo}</td></tr>
                        <tr><td class="fw-medium">目标URL:</td><td><a href="${binding.targetUrl}" target="_blank">${binding.targetUrl}</a></td></tr>
                        <tr><td class="fw-medium">URL模式:</td><td><code>${binding.urlPattern}</code></td></tr>
                        <tr><td class="fw-medium">状态:</td><td><span class="badge ${binding.status === 1 ? 'bg-success' : 'bg-secondary'}">${binding.status === 1 ? '启用' : '禁用'}</span></td></tr>
                        <tr><td class="fw-medium">创建时间:</td><td>${binding.createdTime}</td></tr>
                        <tr><td class="fw-medium">更新时间:</td><td>${binding.updatedTime}</td></tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <h6 class="fw-bold text-success mb-3">字段绑定配置</h6>
                    <div style="max-height: 300px; overflow-y: auto;">
                        ${Object.keys(bindingConfig).length > 0 ?
                            Object.entries(bindingConfig).map(([fieldPath, config]) => `
                                <div class="border rounded p-2 mb-2">
                                    <div class="fw-medium small text-primary">${fieldPath}</div>
                                    <div class="small text-muted">
                                        类型: ${config.type || 'unknown'}<br>
                                        ${config.selector ? `Selector: ${config.selector}` : ''}
                                        ${config.xpath ? `XPath: ${config.xpath}` : ''}
                                    </div>
                                </div>
                            `).join('') :
                            '<div class="text-muted text-center py-3">暂无绑定配置</div>'
                        }
                    </div>
                </div>
            </div>
        `;

        // 显示详情模态框
        showBindingDetailModal(binding, detailHtml);

    } catch (error) {
        console.error('查看绑定详情失败:', error);
        alert('查看绑定详情失败: ' + error.message);
    }
}

// 显示绑定详情模态框
function showBindingDetailModal(binding, detailHtml) {
    document.getElementById('bindingDetailContent').innerHTML = detailHtml;

    // 设置按钮事件
    document.getElementById('editBindingFromDetailBtn').onclick = function() {
        bootstrap.Modal.getInstance(document.getElementById('bindingDetailModal')).hide();
        editBinding(binding.id);
    };

    document.getElementById('testBindingFromDetailBtn').onclick = function() {
        testBinding(binding.id);
    };

    const modal = new bootstrap.Modal(document.getElementById('bindingDetailModal'));
    modal.show();
}

// 搜索绑定
function searchBindings() {
    const query = document.getElementById('bindingSearchInput').value.trim();
    if (!query) {
        loadBindings();
        return;
    }

    // 这里可以实现搜索逻辑
    alert(`搜索绑定: ${query} 功能开发中...`);
}

// 其他辅助功能
function fetchPageHtml() {
    const targetUrl = document.querySelector('input[name="targetUrl"]').value.trim();
    if (!targetUrl) {
        alert('请先输入目标URL');
        return;
    }

    alert('由于跨域限制，请手动复制页面HTML代码');
}

function refreshPreview() {
    loadPagePreview();
}

// 获取所有已绑定的元素
function getAllBoundElements() {
    const boundElements = [];

    // 在主文档中查找
    const mainElements = document.querySelectorAll('#pagePreview [data-bound-field]');
    boundElements.push(...mainElements);

    // 在iframe中查找
    const iframe = document.querySelector('#pagePreview iframe');
    if (iframe) {
        try {
            const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
            if (iframeDoc) {
                const iframeElements = iframeDoc.querySelectorAll('[data-bound-field]');
                boundElements.push(...iframeElements);
            }
        } catch (error) {
            console.warn('无法访问iframe内容:', error);
        }
    }

    return boundElements;
}

// 重置所有字段绑定
function resetAllFields() {
    // 清空绑定状态
    boundFields.clear();
    allFields.forEach(field => {
        field.bound = false;
    });

    // 清除页面预览中的绑定标记
    const boundElements = getAllBoundElements();
    boundElements.forEach(element => {
        element.classList.remove('bound');
        element.style.backgroundColor = '';
        element.style.borderColor = '';
        element.title = '';
        delete element.dataset.boundField;

        // 恢复原始placeholder
        const originalPlaceholder = element.getAttribute('data-original-placeholder');
        if (originalPlaceholder !== null) {
            element.placeholder = originalPlaceholder;
        }
    });

    // 更新显示
    renderFields();
    updateFieldsCount();
}

// 更新绑定关系显示（优化版）
function updateBindingRelations() {
    const boundElements = getAllBoundElements();
    const bindingCount = boundElements.length;

    // 更新中列的绑定信息显示
    const boundFieldsInfo = document.getElementById('boundFieldsInfo');
    const boundCount = document.getElementById('boundCount');
    const viewBindingsBtn = document.getElementById('viewBindingsBtn');
    const bindingCountInBtn = document.getElementById('bindingCountInBtn');

    if (bindingCount > 0) {
        // 显示已绑定字段提示
        if (boundFieldsInfo) {
            boundFieldsInfo.style.display = 'block';
            if (boundCount) boundCount.textContent = bindingCount;
        }

        // 显示查看绑定按钮
        if (viewBindingsBtn) {
            viewBindingsBtn.style.display = 'block';
            if (bindingCountInBtn) bindingCountInBtn.textContent = bindingCount;
        }
    } else {
        // 隐藏相关显示
        if (boundFieldsInfo) boundFieldsInfo.style.display = 'none';
        if (viewBindingsBtn) viewBindingsBtn.style.display = 'none';
    }

    // 更新底部状态信息
    const statusText = document.getElementById('bindingStatusText');
    if (statusText) {
        if (bindingCount === 0) {
            statusText.textContent = '请点击页面输入框，选择JSON字段建立绑定';
            statusText.className = 'text-muted small';
        } else {
            statusText.textContent = `已建立 ${bindingCount} 个字段绑定`;
            statusText.className = 'text-success small fw-bold';
        }
    }

    // 更新字段统计
    updateFieldsCount();

    // 更新详细绑定关系（用于模态框显示）
    updateBindingRelationsDetail();
}

// 更新绑定关系详情
function updateBindingRelationsDetail() {
    const boundElements = getAllBoundElements();
    const detailContainer = document.getElementById('bindingRelationsDetail');
    const totalCount = document.getElementById('totalBindingsCount');

    console.log('更新绑定关系详情，找到元素数量:', boundElements.length);

    if (!detailContainer || !totalCount) {
        console.error('找不到绑定详情容器元素');
        return;
    }

    if (boundElements.length === 0) {
        detailContainer.innerHTML = `
            <div class="text-muted text-center py-5">
                <i class="bi bi-link-45deg fs-1"></i>
                <p class="mt-2">暂无绑定关系</p>
                <small>点击页面输入框，选择JSON字段建立绑定</small>
            </div>
        `;
        totalCount.textContent = '0 个绑定';
        return;
    }

    totalCount.textContent = `${boundElements.length} 个绑定`;

    let relationsHtml = '<div class="list-group list-group-flush">';
    boundElements.forEach((element, index) => {
        const fieldName = element.dataset.boundField;
        const elementType = element.tagName.toLowerCase();
        const elementId = element.id || element.name || `element-${index}`;
        const elementText = element.placeholder || element.textContent || '未知元素';

        // 获取字段描述
        const field = allFields.find(f => f.key === fieldName);
        const fieldDescription = field ? field.description : '无描述';
        const fieldType = field ? field.type : 'simple';
        const typeIcon = getFieldTypeIcon(fieldType);

        relationsHtml += `
            <div class="list-group-item d-flex justify-content-between align-items-start">
                <div class="flex-grow-1">
                    <div class="d-flex align-items-center mb-1">
                        <span class="badge bg-primary me-2">${typeIcon} ${fieldName}</span>
                        <i class="bi bi-arrow-right text-muted me-2"></i>
                        <span class="badge bg-secondary">${elementType}</span>
                    </div>
                    <div class="text-muted small">
                        <div><strong>字段描述：</strong>${fieldDescription}</div>
                        <div><strong>目标元素：</strong>${elementId || '无ID'}</div>
                        <div><strong>元素提示：</strong>${elementText.substring(0, 50)}${elementText.length > 50 ? '...' : ''}</div>
                        ${field && field.path !== field.key ? `<div><strong>字段路径：</strong><code>${field.path}</code></div>` : ''}
                    </div>
                </div>
                <div class="btn-group-vertical btn-group-sm">
                    <button class="btn btn-outline-primary" onclick="highlightElement('${fieldName}')" title="高亮元素">
                        <i class="bi bi-cursor"></i>
                    </button>
                    <button class="btn btn-outline-danger" onclick="removeBinding('${fieldName}')" title="移除绑定">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            </div>
        `;
    });
    relationsHtml += '</div>';

    detailContainer.innerHTML = relationsHtml;
}

// 高亮元素
function highlightElement(fieldName) {
    const element = findBoundElement(fieldName);
    if (element) {
        // 高亮效果
        element.style.boxShadow = '0 0 15px rgba(255, 193, 7, 0.8)';
        element.style.backgroundColor = '#fff3cd';

        // 滚动到元素位置
        element.scrollIntoView({ behavior: 'smooth', block: 'center' });

        // 2秒后恢复
        setTimeout(() => {
            element.style.boxShadow = '';
            element.style.backgroundColor = '#e7f3ff';
        }, 2000);
    }
}

// 移除绑定（优化版）
function removeBinding(fieldName) {
    const element = findBoundElement(fieldName);
    if (element) {
        // 恢复输入框原始状态
        delete element.dataset.boundField;
        element.style.backgroundColor = '';
        element.style.borderColor = '';
        element.title = '';

        // 恢复原始placeholder
        const originalPlaceholder = element.getAttribute('data-original-placeholder') || '';
        element.placeholder = `[点击选择字段] ${originalPlaceholder}`;
    }

    // 解除字段绑定
    unbindField(fieldName);

    // 更新显示
    updateBindingRelations();
    updateFieldsCount();
    renderFields(); // 重新渲染字段列表，显示已恢复的字段
}

// 清理placeholder中的绑定标记
function cleanPlaceholder(placeholder) {
    if (!placeholder) return '';

    // 移除各种可能的绑定标记
    return placeholder
        .replace(/^\[.*?\]\s*/, '')  // 移除开头的 [字段名] 或 [点击选择字段] 等
        .trim();
}

// 复制字段名
function copyFieldName(fieldName) {
    navigator.clipboard.writeText(fieldName).then(() => {
        // 简单的提示
        const button = event.target.closest('button');
        const originalIcon = button.innerHTML;
        button.innerHTML = '<i class="bi bi-check"></i>';
        setTimeout(() => {
            button.innerHTML = originalIcon;
        }, 1000);
    });
}

// 显示绑定关系模态框
function showBindingRelationsModal() {
    updateBindingRelationsDetail();
    const modal = new bootstrap.Modal(document.getElementById('bindingRelationsModal'));
    modal.show();
}

// 清空所有绑定
function clearAllBindings() {
    if (confirm('确定要清空所有绑定关系吗？')) {
        resetAllFields();
        bootstrap.Modal.getInstance(document.getElementById('bindingRelationsModal')).hide();
    }
}

// 导出绑定配置
function exportBindingConfig() {
    const boundElements = getAllBoundElements();
    if (boundElements.length === 0) {
        alert('暂无绑定关系可导出');
        return;
    }

    const config = {};
    boundElements.forEach(element => {
        const fieldName = element.dataset.boundField;
        const selector = element.id ? `#${element.id}` :
                        element.name ? `[name="${element.name}"]` :
                        element.tagName.toLowerCase();

        config[fieldName] = {
            selector: selector,
            type: element.tagName.toLowerCase(),
            id: element.id || '',
            name: element.name || '',
            placeholder: element.placeholder || ''
        };
    });

    // 创建下载链接
    const dataStr = JSON.stringify(config, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});
    const url = URL.createObjectURL(dataBlob);

    const link = document.createElement('a');
    link.href = url;
    link.download = 'binding-config.json';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    alert('绑定配置已导出到 binding-config.json 文件');
}

// 字段搜索
function filterFields() {
    const searchInput = document.getElementById('fieldSearchInput');
    const query = searchInput.value.toLowerCase().trim();

    if (!query) {
        renderFields();
        return;
    }

    const filteredFields = allFields.filter(field =>
        field.key.toLowerCase().includes(query) ||
        field.description.toLowerCase().includes(query)
    );

    renderFields(filteredFields);
}

// 刷新字段
function refreshFields() {
    loadTemplateFields();
}

// 智能识别字段
function autoDetectFields() {
    alert('智能识别功能开发中...\n\n将基于输入框的name、id、placeholder等属性自动匹配JSON字段');
}

// 移除输入框点击事件绑定
function removeInputClickBinding(doc = document) {
    let inputs;
    if (doc === document) {
        inputs = doc.querySelectorAll('#pagePreview input, #pagePreview select, #pagePreview textarea');
    } else {
        inputs = doc.querySelectorAll('input, select, textarea');
    }

    inputs.forEach(input => {
        // 移除事件监听器
        input.removeEventListener('click', input._bindingClickHandler);
        // 移除样式
        input.style.outline = '';
        input.style.backgroundColor = '';
        input.style.cursor = '';
        // 移除标记
        input.classList.remove('binding-enabled');
    });

    console.log(`移除了 ${inputs.length} 个输入框的绑定事件`);
}

// 修改输入框点击事件处理
function setupInputClickBinding(doc = document) {
    // 根据传入的文档查找输入框
    let inputs;
    if (doc === document) {
        // 主文档中查找
        inputs = doc.querySelectorAll('#pagePreview input, #pagePreview select, #pagePreview textarea');
    } else {
        // iframe文档中查找
        inputs = doc.querySelectorAll('input, select, textarea');
    }

    let inputCount = 0;

    inputs.forEach(input => {
        // 跳过隐藏、只读、禁用的输入框
        if (input.type === 'hidden' || input.readOnly || input.disabled) {
            return;
        }

        // 移除可能存在的拖拽相关事件
        input.removeEventListener('dragover', handleDragOver);
        input.removeEventListener('drop', handleDrop);
        input.removeEventListener('dragenter', handleDragEnter);
        input.removeEventListener('dragleave', handleDragLeave);

        // 创建点击事件处理器
        const clickHandler = function(e) {
            e.preventDefault();
            e.stopPropagation();
            showFieldSelector(this);
        };

        // 保存事件处理器引用，以便后续移除
        input._bindingClickHandler = clickHandler;

        // 添加点击事件
        input.addEventListener('click', clickHandler);

        // 添加样式
        input.classList.add('binding-target');
        if (input.dataset.boundField) {
            input.classList.add('bound');
        }

        // 设置边框样式，表示可以绑定
        input.style.border = '2px dashed #007bff';
        input.style.backgroundColor = '#f8f9fa';
        input.style.cursor = 'pointer';
        input.style.transition = 'all 0.3s ease';

        // 更新placeholder - 修复重复拼接问题
        let originalPlaceholder = input.getAttribute('data-original-placeholder');

        // 如果没有保存过原始placeholder，则保存当前的
        if (!originalPlaceholder) {
            // 清理可能已经被修改过的placeholder
            originalPlaceholder = cleanPlaceholder(input.placeholder || '');
            input.setAttribute('data-original-placeholder', originalPlaceholder);
        }

        if (input.dataset.boundField) {
            input.placeholder = `[${input.dataset.boundField}] ${originalPlaceholder}`;
            input.style.backgroundColor = '#e7f3ff';
            input.style.borderColor = '#007bff';
            input.style.borderStyle = 'solid';
        } else {
            input.placeholder = `[点击选择字段] ${originalPlaceholder}`;
        }

        inputCount++;
    });

    console.log(`找到并设置了 ${inputCount} 个可绑定的输入框`);
}

// 拖拽相关的空函数（防止错误）
function handleDragOver(event) {
    // 空函数，防止removeEventListener报错
}

function handleDrop(event) {
    // 空函数，防止removeEventListener报错
}

function handleDragEnter(event) {
    // 空函数，防止removeEventListener报错
}

function handleDragLeave(event) {
    // 空函数，防止removeEventListener报错
}

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('页面绑定页面加载完成');

    // 不自动设置绑定，等待用户手动触发
    // setTimeout(() => {
    //     setupInputClickBinding();
    // }, 500);

    // 点击弹窗外部关闭字段选择器
    document.addEventListener('click', function(e) {
        const popup = document.getElementById('fieldSelectorPopup');
        if (e.target === popup) {
            hideFieldSelector();
        }
    });

    // ESC键关闭字段选择器
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            hideFieldSelector();
        }
    });

    // HTML文本框字符计数
    const htmlTextarea = document.getElementById('manualHtmlTextarea');
    if (htmlTextarea) {
        htmlTextarea.addEventListener('input', updateHtmlCharCount);
        htmlTextarea.addEventListener('paste', function() {
            // 粘贴后稍微延迟更新计数
            setTimeout(updateHtmlCharCount, 10);
        });
    }
});

function filterJsonFields() {
    const query = document.getElementById('fieldSearchInput').value.toLowerCase();
    const fieldItems = document.querySelectorAll('.json-field-item');

    fieldItems.forEach(item => {
        const fieldPath = item.dataset.fieldPath.toLowerCase();
        const description = item.querySelector('.text-muted').textContent.toLowerCase();

        if (fieldPath.includes(query) || description.includes(query)) {
            item.style.display = 'block';
        } else {
            item.style.display = 'none';
        }
    });
}

function selectJsonField(fieldPath) {
    // 高亮选中的字段
    document.querySelectorAll('.json-field-item').forEach(item => {
        item.classList.remove('highlight');
    });

    const selectedItem = document.querySelector(`[data-field-path="${fieldPath}"]`);
    if (selectedItem) {
        selectedItem.classList.add('highlight');
    }
}

function autoDetectFields() {
    alert('智能识别功能开发中...\n\n将基于输入框的name、id、placeholder等属性自动匹配JSON字段');
}

function openUrlInNewTab(url) {
    window.open(url, '_blank');
}

// 显示备选方案
function showFallbackOptions(url) {
    const previewDiv = document.getElementById('pagePreview');
    previewDiv.innerHTML = `
        <div class="border rounded p-3">
            <div class="alert alert-info alert-sm mb-3">
                <i class="bi bi-info-circle me-2"></i>
                无法直接加载页面内容，这通常是因为：
                <ul class="mb-0 mt-2">
                    <li>页面需要登录验证</li>
                    <li>页面有防爬虫保护</li>
                    <li>页面使用了复杂的JavaScript渲染</li>
                    <li>服务器返回了重定向</li>
                </ul>
            </div>

            <div class="d-flex gap-2 mb-3">
                <button type="button" class="btn btn-primary btn-sm" onclick="openUrlInNewTab('${url}')">
                    <i class="bi bi-box-arrow-up-right"></i> 在新窗口中打开
                </button>
                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="showManualHtmlModal()">
                    <i class="bi bi-code-square"></i> 手动输入HTML
                </button>
                <button type="button" class="btn btn-outline-info btn-sm" onclick="showManualInstructions()">
                    <i class="bi bi-question-circle"></i> 操作说明
                </button>
            </div>

            <div class="text-muted small">
                <strong>建议操作：</strong><br>
                1. 点击"在新窗口中打开"按钮<br>
                2. 在新窗口中完成登录（如需要）<br>
                3. 按F12打开开发者工具，复制HTML源代码<br>
                4. 点击"手动输入HTML"按钮，粘贴代码
            </div>
        </div>
    `;
}

// 显示手动操作说明
function showManualInstructions() {
    const modal = new bootstrap.Modal(document.getElementById('helpModal') || createHelpModal());
    modal.show();
}

// 创建帮助模态框
function createHelpModal() {
    const modalHtml = `
        <div class="modal fade" id="helpModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">页面加载帮助</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <h6>为什么无法直接加载页面？</h6>
                        <p>某些网站出于安全考虑，会阻止其他网站直接获取其内容。常见原因包括：</p>
                        <ul>
                            <li><strong>需要登录：</strong>页面需要用户身份验证</li>
                            <li><strong>CORS限制：</strong>跨域资源共享限制</li>
                            <li><strong>防爬虫保护：</strong>网站有反爬虫机制</li>
                            <li><strong>JavaScript渲染：</strong>页面内容由JavaScript动态生成</li>
                        </ul>

                        <h6>如何手动获取页面HTML？</h6>
                        <ol>
                            <li>在新窗口中打开目标页面</li>
                            <li>如果需要登录，请先完成登录</li>
                            <li>在页面上右键点击，选择"查看页面源代码"</li>
                            <li>复制整个HTML源代码</li>
                            <li>将代码粘贴到"页面HTML"字段中</li>
                            <li>点击"加载页面"按钮</li>
                        </ol>

                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            <strong>注意：</strong>请确保复制的是完整的HTML源代码，而不是经过浏览器渲染后的内容。
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);
    return document.getElementById('helpModal');
}



// 辅助函数
function getBindingStatusColor(status) {
    const colors = {
        'active': 'success',
        'inactive': 'secondary'
    };
    return colors[status] || 'secondary';
}

function getBindingStatusText(status) {
    const texts = {
        'active': '启用',
        'inactive': '禁用'
    };
    return texts[status] || status;
}
