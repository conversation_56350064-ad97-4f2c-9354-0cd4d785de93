// Agent管理页面JavaScript - 完全复制自admin.js

// 全局变量
let currentViewMode = 'table'; // 当前视图模式：'card' 或 'table'
let currentUserRole = null; // 当前用户角色

// 页面初始化函数
function initAgentsPage() {
    console.log('Agent管理页面初始化');

    // 清理可能存在的定时器和事件监听器
    cleanupAgentsPage();

    // 获取当前用户角色
    getCurrentUserRole();

    // 初始化页面增强功能
    initializeAgentsPageEnhancements();

    // 加载Agent列表
    loadAgents();

    // 初始化名称验证
    setupAgentNameValidation();
}

// 清理页面资源
function cleanupAgentsPage() {
    // 初始化全局定时器数组（如果不存在）
    if (!window.pageTimers) {
        window.pageTimers = [];
    }

    // 初始化全局事件监听器数组（如果不存在）
    if (!window.pageEventListeners) {
        window.pageEventListeners = [];
    }

    // 重置全局变量
    currentSearchParams = {
        page: 1,
        size: 10,
        keyword: '',
        categoryId: '',
        status: ''
    };

    console.log('Agent管理页面资源清理完成');
}

// 全局变量存储当前查询参数
let currentSearchParams = {
    page: 1,
    size: 10,
    keyword: '',
    categoryId: '',
    status: ''
};

/**
 * 获取当前用户角色
 */
async function getCurrentUserRole() {
    try {
        const response = await apiCall('/api/v1/users/current');
        if (response.code === 200 && response.data) {
            currentUserRole = response.data.roleCode;
            console.log('当前用户角色:', currentUserRole);
        }
    } catch (error) {
        console.error('获取用户角色失败:', error);
    }
}

/**
 * 检查是否为管理员
 */
function isAdmin() {
    return currentUserRole === 'ADMIN';
}

/**
 * 检查Agent是否可以删除
 * @param {number} status Agent状态
 * @returns {boolean} 是否可以删除
 */
function canDeleteAgent(status) {
    // 已发布的Agent(status=3)不能删除
    return status !== 3;
}

// 加载Agent列表
async function loadAgents(params = {}) {
    try {
        // 检查页面是否还是Agent管理页面
        if (!isAgentsPageActive()) {
            console.log('当前页面不是Agent管理页面，跳过加载');
            return;
        }

        // 显示加载状态
        showLoadingState();

        // 合并查询参数
        const searchParams = { ...currentSearchParams, ...params };

        // 构建查询字符串
        const queryParams = new URLSearchParams();
        Object.keys(searchParams).forEach(key => {
            if (searchParams[key] !== '' && searchParams[key] !== null && searchParams[key] !== undefined) {
                queryParams.append(key, searchParams[key]);
            }
        });

        const response = await apiCall(`/api/v1/agents?${queryParams.toString()}`);
        if (response.code === 200) {
            // 再次检查页面是否还是Agent管理页面
            if (!isAgentsPageActive()) {
                console.log('页面已切换，停止渲染Agent列表');
                return;
            }

            const agents = response.data.records || response.data.list || [];

            // 更新统计数据
            updateStats(agents);

            // 检查是否有数据
            if (agents.length === 0) {
                showEmptyState();
                return;
            }

            // 根据当前视图模式渲染
            if (currentViewMode === 'table') {
                renderAgentTable(agents);
            } else {
                renderAgentCards(agents);
            }

            // 渲染分页
            renderPagination(response.data);

            // 更新当前搜索参数
            currentSearchParams = searchParams;
        }
    } catch (error) {
        console.error('Failed to load agents:', error);
        if (isAgentsPageActive()) {
            showErrorState('加载Agent列表失败，请稍后重试');
        }
    }
}

// 检查当前页面是否是Agent管理页面
function isAgentsPageActive() {
    const agentsTable = document.getElementById('agentsTable');
    const loadingState = document.getElementById('loadingState');
    const emptyState = document.getElementById('emptyState');

    // 如果这些关键元素都不存在，说明不是Agent管理页面
    return agentsTable || loadingState || emptyState;
}

// 显示加载状态
function showLoadingState() {
    // 检查页面是否还是Agent管理页面
    if (!isAgentsPageActive()) {
        return;
    }

    const loadingState = document.getElementById('loadingState');
    const agentsTable = document.getElementById('agentsTable');
    const emptyState = document.getElementById('emptyState');

    if (loadingState) loadingState.style.display = 'block';
    if (agentsTable) agentsTable.style.display = 'none';
    if (emptyState) emptyState.style.display = 'none';
}

// 显示空状态
function showEmptyState() {
    // 检查页面是否还是Agent管理页面
    if (!isAgentsPageActive()) {
        return;
    }

    const loadingState = document.getElementById('loadingState');
    const agentsTable = document.getElementById('agentsTable');
    const emptyState = document.getElementById('emptyState');
    const agentCountBadge = document.getElementById('agentCountBadge');

    if (loadingState) loadingState.style.display = 'none';
    if (agentsTable) agentsTable.style.display = 'none';
    if (emptyState) emptyState.style.display = 'block';
    if (agentCountBadge) agentCountBadge.textContent = '0';
}

// 显示错误状态
function showErrorState(message) {
    // 检查页面是否还是Agent管理页面
    if (!isAgentsPageActive()) {
        return;
    }

    const loadingState = document.getElementById('loadingState');
    const emptyState = document.getElementById('emptyState');
    const agentsTable = document.getElementById('agentsTable');

    if (loadingState) loadingState.style.display = 'none';
    if (emptyState) emptyState.style.display = 'none';

    if (agentsTable) {
        agentsTable.style.display = 'block';
        agentsTable.innerHTML = `
            <div class="empty-state text-center py-5">
                <div class="d-flex flex-column align-items-center">
                    <div class="mb-4">
                        <i class="bi bi-exclamation-triangle text-danger" style="font-size: 4rem;"></i>
                    </div>
                    <h5 class="text-danger mb-2">加载失败</h5>
                    <p class="text-muted mb-4">${message}</p>
                    <button class="btn btn-primary" onclick="loadAgents()">
                        <i class="bi bi-arrow-clockwise me-2"></i>重新加载
                    </button>
                </div>
            </div>
        `;
    }
}

// 初始化工具提示
function initializeTooltips() {
    // 初始化Bootstrap工具提示
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

// 渲染Agent卡片
function renderAgentCards(agents) {
    // 显示内容区域，隐藏加载和空状态
    const agentsTable = document.getElementById('agentsTable');
    const loadingState = document.getElementById('loadingState');
    const emptyState = document.getElementById('emptyState');

    if (agentsTable) agentsTable.style.display = 'block';
    if (loadingState) loadingState.style.display = 'none';
    if (emptyState) emptyState.style.display = 'none';

    const cardsHtml = `
        <div class="p-3">
            <div class="row g-4">
                ${agents.map(agent => `
                    <div class="col-lg-6 col-xl-4">
                        <div class="agent-card">
                            <!-- 卡片头部 -->
                            <div class="agent-card-header">
                                <!-- 状态徽章 -->
                                <span class="agent-status-badge status-${getAgentStatusClass(agent.status)}">
                                    ${getAgentStatusText(agent.status)}
                                </span>
                                ${agent.approvalStatus === 1 ? '<span class="agent-status-badge status-warning" style="top: 1rem; right: 5rem;">审批中</span>' : ''}

                                <!-- 标题和编码 -->
                                <div class="agent-card-title">
                                    <a href="#" class="agent-name-link" onclick="showAgentDetail(${agent.id})">${agent.agentName}</a>
                                </div>
                                <div class="agent-card-code">${agent.agentCode}</div>
                            </div>

                            <!-- 卡片主体 -->
                            <div class="agent-card-body">
                                <!-- 描述 -->
                                <div class="agent-description">
                                    ${agent.description || '暂无描述'}
                                </div>

                                <!-- 版本信息 -->
                                <div class="agent-meta">
                                    <div class="agent-meta-item">
                                        <div class="agent-meta-label">最新版本</div>
                                        <div class="agent-meta-value text-primary">v${agent.latestVersion || agent.version || '1.0'}</div>
                                    </div>
                                    <div class="agent-meta-item">
                                        <div class="agent-meta-label">发布版本</div>
                                        <div class="agent-meta-value ${agent.publishedVersion ? 'text-success' : 'text-muted'}">
                                            ${agent.publishedVersion ? `v${agent.publishedVersion}` : '未发布'}
                                        </div>
                                    </div>
                                </div>

                                <!-- 详细信息 -->
                                <div class="row g-2 text-center small">
                                    <div class="col-4">
                                        <div class="text-muted">分类</div>
                                        <div class="fw-medium">${agent.categoryName || '-'}</div>
                                    </div>
                                    <div class="col-4">
                                        <div class="text-muted">创建者</div>
                                        <div class="fw-medium">${agent.creatorName || '-'}</div>
                                    </div>
                                    <div class="col-4">
                                        <div class="text-muted">创建时间</div>
                                        <div class="fw-medium">${formatDate(agent.createdTime)}</div>
                                    </div>
                                </div>
                            </div>

                            <!-- 卡片底部操作区 -->
                            <div class="agent-card-footer">
                                <div class="d-flex gap-1 flex-wrap">
                                    <!-- 主要操作按钮 -->
                                    ${agent.status === 2 ? `
                                        <button class="btn btn-success btn-sm" hidden="hidden" onclick="publishAgent(${agent.id})" title="申请发布">
                                            <i class="bi bi-rocket"></i>
                                        </button>
                                    ` : agent.status === 3 ? `
                                        <button class="btn btn-warning btn-sm" onclick="unpublishAgent(${agent.id})" title="下线">
                                            <i class="bi bi-pause"></i>
                                        </button>
                                    ` : `
                                        <button class="btn btn-primary btn-sm" onclick="goToDebugConsole('${agent.agentCode}')" title="调试台">
                                            <i class="bi bi-terminal"></i>
                                        </button>
                                    `}

                                    <!-- 其他操作按钮 -->
                                    <button class="btn btn-outline-secondary btn-sm" onclick="showAgentVersionHistory(${agent.id})" title="版本历史">
                                        <i class="bi bi-clock-history"></i>
                                    </button>
                                    <button class="btn btn-outline-secondary btn-sm" onclick="showDebugHistory(${agent.id})" title="调试历史">
                                        <i class="bi bi-bug"></i>
                                    </button>
                                    <button class="btn btn-outline-secondary btn-sm" onclick="cloneAgent(${agent.id})" title="克隆">
                                        <i class="bi bi-files"></i>
                                    </button>
                                    ${canDeleteAgent(agent.status) ? `
                                        <button class="btn btn-outline-danger btn-sm" onclick="deleteAgent(${agent.id})" title="删除">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    ` : ''}
                                </div>
                            </div>
                        </div>
                    </div>
                `).join('')}
            </div>
        </div>
    `;

    if (agentsTable) {
        agentsTable.innerHTML = cardsHtml;
    }
}

// 获取Agent状态对应的CSS类
function getAgentStatusClass(status) {
    switch(status) {
        case 3: return 'published';
        case 2: return 'testing';
        case 1: return 'draft';
        default: return 'draft';
    }
}

// 渲染Agent表格
function renderAgentTable(agents) {
    // 显示内容区域，隐藏加载和空状态
    const agentsTable = document.getElementById('agentsTable');
    const loadingState = document.getElementById('loadingState');
    const emptyState = document.getElementById('emptyState');

    if (agentsTable) agentsTable.style.display = 'block';
    if (loadingState) loadingState.style.display = 'none';
    if (emptyState) emptyState.style.display = 'none';

    const tableHtml = `
        <div class="agents-table">
            <table class="table table-hover mb-0">
                <thead>
                    <tr>
                        <th>Agent信息</th>
                        <th>状态</th>
                        <th>分类</th>
                        <th>版本</th>
                        <th>创建者</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    ${agents.map(agent => `
                        <tr>
                            <td>
                                <div class="agent-info">
                                    <div class="agent-avatar">
                                        ${agent.agentName.charAt(0).toUpperCase()}
                                    </div>
                                    <div class="flex-grow-1">
                                        <div>
                                            <a href="#" class="agent-name" onclick="showAgentDetail(${agent.id})">${agent.agentName}</a>
                                        </div>
                                        <div class="agent-code">${agent.agentCode}</div>
                                        <div class="small text-muted" style="max-width: 180px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;"
                                             title="${agent.description || '暂无描述'}">
                                            ${agent.description || '暂无描述'}
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div>
                                    <span class="badge bg-${getAgentStatusColor(agent.status)}">
                                        ${getAgentStatusText(agent.status)}
                                    </span>
                                    ${agent.approvalStatus === 1 ? '<div class="mt-1"><span class="badge bg-warning small">审批中</span></div>' : ''}
                                </div>
                            </td>
                            <td>
                                <span class="text-muted small">${agent.categoryName || '-'}</span>
                            </td>
                            <td>
                                <div>
                                    <div class="small text-muted">最新: v${agent.latestVersion || agent.version || '1.0'}</div>
                                    ${agent.publishedVersion ?
                                        `<div class="small text-success">发布: v${agent.publishedVersion}</div>` :
                                        '<div class="small text-muted">暂无发布版本</div>'
                                    }
                                </div>
                            </td>
                            <td>
                                <span class="text-muted small">${agent.creatorName || '-'}</span>
                            </td>
                            <td>
                                <span class="text-muted small">${formatDate(agent.createdTime)}</span>
                            </td>
                            <td>
                                <div class="d-flex gap-1 flex-wrap align-items-center">
                                    <!-- 主要操作按钮 -->
                                    ${agent.status === 2 ? `
                                        <button class="btn btn-success btn-sm" onclick="publishAgent(${agent.id})" hidden="hidden" title="申请发布">
                                            <i class="bi bi-rocket"></i>
                                            <span class="d-none d-md-inline ms-1">发布</span>
                                        </button>
                                    ` : agent.status === 3 ? `
                                        <button class="btn btn-warning btn-sm" onclick="unpublishAgent(${agent.id})" title="下线">
                                            <i class="bi bi-pause"></i>
                                            <span class="d-none d-md-inline ms-1">下线</span>
                                        </button>
                                    ` : `
                                        <button class="btn btn-primary btn-sm" onclick="goToDebugConsole('${agent.agentCode}')" title="调试台">
                                            <i class="bi bi-terminal"></i>
                                            <span class="d-none d-md-inline ms-1">调试</span>
                                        </button>
                                    `}

                                    <!-- 其他操作按钮 -->
                                    <button class="btn btn-outline-secondary btn-sm" onclick="showAgentVersionHistory(${agent.id})" title="版本历史">
                                        <i class="bi bi-clock-history"></i>
                                        <span class="d-none d-md-inline ms-1">版本</span>
                                    </button>
                                    <button class="btn btn-outline-secondary btn-sm" onclick="showDebugHistory(${agent.id})" title="调试历史">
                                        <i class="bi bi-bug"></i>
                                        <span class="d-none d-md-inline ms-1">调试</span>
                                    </button>
                                    <button class="btn btn-outline-secondary btn-sm" onclick="cloneAgent(${agent.id})" title="克隆">
                                        <i class="bi bi-files"></i>
                                        <span class="d-none d-md-inline ms-1">克隆</span>
                                    </button>
                                    ${canDeleteAgent(agent.status) ? `
                                        <button class="btn btn-outline-danger btn-sm" onclick="deleteAgent(${agent.id})" title="删除">
                                            <i class="bi bi-trash"></i>
                                            <span class="d-none d-md-inline ms-1">删除</span>
                                        </button>
                                    ` : ''}
                                </div>
                            </td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>
    `;

    if (agentsTable) {
        agentsTable.innerHTML = tableHtml;
    }
}


// 视图切换函数
function switchView(viewMode) {
    // 更新当前视图模式
    currentViewMode = viewMode;

    // 更新按钮状态
    const cardBtn = document.getElementById('cardViewBtn');
    const tableBtn = document.getElementById('tableViewBtn');

    if (cardBtn && tableBtn) {
        if (viewMode === 'card') {
            cardBtn.classList.add('active');
            tableBtn.classList.remove('active');
        } else {
            tableBtn.classList.add('active');
            cardBtn.classList.remove('active');
        }
    }

    // 重新渲染当前数据
    loadAgents();
}

// 显示Agent详情（使用全局函数）
// 注意：showAgentDetail函数已在admin.js中定义为全局函数

// 渲染分页控件
function renderPagination(data) {
    const paginationContainer = document.getElementById('paginationContainer');
    if (!paginationContainer) {
        console.warn('paginationContainer element not found');
        return;
    }

    const pagination = data.pagination || data;
    const totalPages = Math.ceil(pagination.total / pagination.size);
    const currentPage = pagination.current || currentSearchParams.page;

    if (totalPages <= 1) {
        paginationContainer.innerHTML = '';
        return;
    }

    let paginationHtml = `
        <nav aria-label="Agent列表分页">
            <ul class="pagination justify-content-center">
                <li class="page-item ${currentPage <= 1 ? 'disabled' : ''}">
                    <a class="page-link" href="#" onclick="loadAgents({page: ${currentPage - 1}})">上一页</a>
                </li>
    `;

    // 显示页码
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);

    if (startPage > 1) {
        paginationHtml += `<li class="page-item"><a class="page-link" href="#" onclick="loadAgents({page: 1})">1</a></li>`;
        if (startPage > 2) {
            paginationHtml += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
        }
    }

    for (let i = startPage; i <= endPage; i++) {
        paginationHtml += `
            <li class="page-item ${i === currentPage ? 'active' : ''}">
                <a class="page-link" href="#" onclick="loadAgents({page: ${i}})">${i}</a>
            </li>
        `;
    }

    if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
            paginationHtml += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
        }
        paginationHtml += `<li class="page-item"><a class="page-link" href="#" onclick="loadAgents({page: ${totalPages}})">${totalPages}</a></li>`;
    }

    paginationHtml += `
                <li class="page-item ${currentPage >= totalPages ? 'disabled' : ''}">
                    <a class="page-link" href="#" onclick="loadAgents({page: ${currentPage + 1}})">下一页</a>
                </li>
            </ul>
        </nav>
        <div class="text-center text-muted mt-2">
            共 ${pagination.total} 条记录，第 ${currentPage} / ${totalPages} 页
        </div>
    `;

    paginationContainer.innerHTML = paginationHtml;
}

// 显示创建Agent模态框
async function showCreateAgentModal() {
    // 加载业务模板列表
    await loadAgentTemplates();

    // 加载平台列表
    await loadPlatforms();

    // 加载分类列表
    await loadAgentCategories();

    // 设置名称验证
    setupAgentNameValidation();

    const modal = new bootstrap.Modal(document.getElementById('createAgentModal'));
    modal.show();
}

// 加载Agent模板
async function loadAgentTemplates() {
    // 这里可以加载模板列表，暂时跳过
    console.log('加载Agent模板...');
}

// 加载Agent分类列表
async function loadAgentCategories() {
    try {
        const response = await apiCall('/api/v1/agent-categories/active');
        if (response.code === 200 && response.data) {
            const categories = response.data;

            // 填充创建模态框的分类选择框
            const createCategorySelect = document.querySelector('#createAgentModal select[name="categoryId"]');
            if (createCategorySelect) {
                createCategorySelect.innerHTML = '<option value="">请选择分类...</option>';
                categories.forEach(category => {
                    const option = document.createElement('option');
                    option.value = category.id;
                    option.textContent = category.categoryName;
                    createCategorySelect.appendChild(option);
                });
            }

            // 填充编辑模态框的分类选择框（如果存在）
            const editCategorySelect = document.querySelector('#editAgentModal select[name="categoryId"]');
            if (editCategorySelect) {
                editCategorySelect.innerHTML = '<option value="">请选择分类...</option>';
                categories.forEach(category => {
                    const option = document.createElement('option');
                    option.value = category.id;
                    option.textContent = category.categoryName;
                    editCategorySelect.appendChild(option);
                });
            }

            console.log('Agent分类加载成功，共', categories.length, '个分类');
        } else {
            console.error('加载Agent分类失败:', response.message);
        }
    } catch (error) {
        console.error('加载Agent分类时发生错误:', error);
    }
}

// 根据平台确定Agent类型
function determineAgentType(platformKey) {
    // 根据选择的平台获取平台名称
    const platformSelect = document.getElementById('createPlatformSelect');
    if (!platformSelect) return 1; // 默认为内部LLM

    const selectedOption = platformSelect.options[platformSelect.selectedIndex];
    if (!selectedOption) return 1;

    const platformName = selectedOption.text;

    // 根据平台名称确定Agent类型
    if (platformName.includes('内部大模型')) {
        return 1; // Agent.TYPE_INTERNAL_LLM (包括LLM和VLM)
    } else if (platformName.includes('外部大模型')) {
        return 2; // Agent.TYPE_EXTERNAL_LLM
    } else {
        return 1; // 默认为内部LLM
    }
}

// 创建Agent
async function createAgent() {
    const form = document.getElementById('createAgentForm');
    const formData = new FormData(form);

    // 验证Agent名称
    const agentName = formData.get('agentName');
    if (!agentName || agentName.trim().length === 0) {
        showToast('请输入Agent名称', 'warning');
        return;
    }

    // 检查名称是否已存在
    const nameExists = await checkAgentNameExists(agentName.trim());
    if (nameExists) {
        showToast('Agent名称已存在，请使用其他名称', 'warning');
        return;
    }

    // 获取平台和模型信息
    const platform = formData.get('platform');
    const model = formData.get('model');
    const temperature = formData.get('temperature') || 0.7;
    const maxTokens = formData.get('maxTokens') || 2000;
    const topP = formData.get('topP') || 0.9;

    if (!platform || !model) {
        showToast('请选择平台和模型', 'warning');
        return;
    }

    // 根据平台确定Agent类型
    const agentType = determineAgentType(platform);

    const agentData = {
        agentName: formData.get('agentName'),
        // agentCode 由后台自动生成，不再从前端传递
        description: formData.get('description'),
        categoryId: formData.get('categoryId') ? parseInt(formData.get('categoryId')) : null,
        businessTypeId: 1, // 默认业务类型
        agentType: agentType, // 根据平台动态确定
        config: JSON.stringify({
            llm_config: {
                platform: platform,
                model: model,
                temperature: parseFloat(temperature),
                max_tokens: parseInt(maxTokens),
                top_p: parseFloat(topP)
            }
        }),
        promptTemplate: formData.get('promptTemplate'),
        jsonTemplate: formData.get('jsonTemplate')
    };

    try {
        const response = await apiCall('/api/v1/agents', 'POST', agentData);
        if (response.code === 200) {
            showToast('Agent创建成功！', 'success');
            bootstrap.Modal.getInstance(document.getElementById('createAgentModal')).hide();
            form.reset();
            loadAgents();
        } else {
            showToast('创建失败：' + response.message, 'danger');
        }
    } catch (error) {
        console.error('Failed to create agent:', error);
        showToast('创建失败：' + error.message, 'danger');
    }
}

// 编辑Agent - 复制自admin.js
async function editAgent(id) {
    try {
        // 获取Agent详情
        const response = await apiCall(`/api/v1/agents/${id}`, 'GET');
        if (response.code === 200) {
            const agent = response.data;

            // 加载业务模板列表
            await loadAgentTemplatesForEdit();

            // 加载分类列表
            await loadAgentCategories();

            // 加载平台列表
            await loadPlatformsForEdit();

            // 填充表单数据
            document.getElementById('editAgentId').value = agent.id;
            document.getElementById('editAgentName').value = agent.agentName;
            document.getElementById('editAgentCode').value = agent.agentCode;
            document.getElementById('editAgentDescription').value = agent.description || '';
            document.getElementById('editPromptTemplate').value = agent.promptTemplate || '';

            // 选择对应的业务模板
            const templateSelect = document.getElementById('editAgentTemplateSelect');
            if (agent.templateId) {
                templateSelect.value = agent.templateId;
                loadSelectedEditTemplate(); // 加载模板JSON显示
            }

            // 选择对应的分类
            const categorySelect = document.getElementById('editAgentCategorySelect');
            if (categorySelect && agent.categoryId) {
                categorySelect.value = agent.categoryId;
            }

            // 填充LLM配置
            if (agent.config) {
                try {
                    const config = JSON.parse(agent.config);
                    const llmConfig = config.llm_config || {};

                    // 设置平台
                    const platformSelect = document.getElementById('editPlatformSelect');
                    if (platformSelect && llmConfig.platform) {
                        platformSelect.value = llmConfig.platform;
                        // 触发平台变化事件来加载模型列表
                        await onPlatformChange('edit');

                        // 设置模型
                        const modelSelect = document.getElementById('editModelSelect');
                        if (modelSelect && llmConfig.model) {
                            modelSelect.value = llmConfig.model;
                        }
                    }

                    // 设置其他参数
                    if (llmConfig.temperature !== undefined) {
                        document.getElementById('editTemperature').value = llmConfig.temperature;
                    }
                    if (llmConfig.max_tokens !== undefined) {
                        document.getElementById('editMaxTokens').value = llmConfig.max_tokens;
                    }
                    if (llmConfig.top_p !== undefined) {
                        document.getElementById('editTopP').value = llmConfig.top_p;
                    }
                } catch (error) {
                    console.error('Failed to parse agent config:', error);
                }
            }

            // 设置名称验证
            setupAgentNameValidation();

            // 显示模态框
            const modal = new bootstrap.Modal(document.getElementById('editAgentModal'));
            modal.show();
        } else {
            showToast('获取Agent详情失败：' + response.message, 'danger');
        }
    } catch (error) {
        console.error('Failed to load agent details:', error);
        showToast('获取Agent详情失败：' + error.message, 'danger');
    }
}

// 加载业务模板到编辑表单 - 复制自admin.js
async function loadAgentTemplatesForEdit() {
    try {
        const response = await apiCall('/api/v1/business-templates/active');
        if (response.code === 200) {
            const templates = response.data;
            const select = document.getElementById('editAgentTemplateSelect');
            if (select) {
                select.innerHTML = '<option value="">请选择业务模板</option>';
                templates.forEach(template => {
                    select.innerHTML += `<option value="${template.id}" data-json='${template.jsonTemplate}'>${template.templateName} (${template.templateCode})</option>`;
                });
            }
        }
    } catch (error) {
        console.error('Failed to load templates for edit:', error);
    }
}

// 加载选中的编辑模板
function loadSelectedEditTemplate() {
    const select = document.getElementById('editAgentTemplateSelect');
    const selectedOption = select.options[select.selectedIndex];
    const jsonDisplay = document.getElementById('selectedEditTemplateJson');

    if (selectedOption && selectedOption.value) {
        const jsonTemplate = selectedOption.getAttribute('data-json');
        if (jsonTemplate) {
            try {
                const formatted = JSON.stringify(JSON.parse(jsonTemplate), null, 2);
                jsonDisplay.textContent = formatted;
                jsonDisplay.className = 'mb-0';
            } catch (error) {
                jsonDisplay.textContent = jsonTemplate;
                jsonDisplay.className = 'mb-0 text-danger';
            }
        }
    } else {
        jsonDisplay.textContent = '请先选择业务模板';
        jsonDisplay.className = 'mb-0 text-muted';
    }
}

// 更新Agent - 复制自admin.js
async function updateAgent() {
    const form = document.getElementById('editAgentForm');
    const formData = new FormData(form);
    const agentId = document.getElementById('editAgentId').value;

    // 验证Agent名称
    const agentName = formData.get('agentName');
    if (!agentName || agentName.trim().length === 0) {
        showToast('请输入Agent名称', 'warning');
        return;
    }

    // 检查名称是否已存在（排除当前Agent）
    const nameExists = await checkAgentNameExists(agentName.trim(), parseInt(agentId));
    if (nameExists) {
        showToast('Agent名称已存在，请使用其他名称', 'warning');
        return;
    }

    const templateId = formData.get('templateId');
    if (!templateId) {
        showToast('请选择业务模板', 'warning');
        return;
    }

    // 验证平台和模型选择
    const platform = formData.get('platform');
    const model = formData.get('model');
    if (!platform) {
        showToast('请选择平台', 'warning');
        return;
    }
    if (!model) {
        showToast('请选择模型', 'warning');
        return;
    }

    // 获取选中模板的JSON
    const select = document.getElementById('editAgentTemplateSelect');
    const selectedOption = select.options[select.selectedIndex];
    const jsonTemplate = selectedOption.getAttribute('data-json');

    // 根据平台确定Agent类型
    let agentType = 1; // 默认为内部LLM
    const platformSelect = document.getElementById('editPlatformSelect');
    if (platformSelect && platform) {
        for (let option of platformSelect.options) {
            if (option.value === platform) {
                const platformName = option.text;
                if (platformName.includes('外部大模型')) {
                    agentType = 2; // Agent.TYPE_EXTERNAL_LLM
                } else {
                    agentType = 1; // Agent.TYPE_INTERNAL_LLM
                }
                break;
            }
        }
    }

    const agentData = {
        agentName: formData.get('agentName'),
        agentCode: formData.get('agentCode'),
        description: formData.get('description'),
        categoryId: formData.get('categoryId') ? parseInt(formData.get('categoryId')) : 1,
        businessTypeId: 1, // 默认业务类型
        agentType: agentType,
        templateId: parseInt(templateId),
        config: JSON.stringify({
            llm_config: {
                platform: platform,
                provider: "qianwen", // 保持兼容性
                model: model,
                temperature: parseFloat(formData.get('temperature')) || 0.7,
                max_tokens: parseInt(formData.get('maxTokens')) || 2000,
                top_p: parseFloat(formData.get('topP')) || 0.9
            }
        }),
        promptTemplate: formData.get('promptTemplate'),
        jsonTemplate: jsonTemplate
    };

    try {
        const response = await apiCall(`/api/v1/agents/${agentId}`, 'PUT', agentData);
        if (response.code === 200) {
            showToast('Agent更新成功！', 'success');
            bootstrap.Modal.getInstance(document.getElementById('editAgentModal')).hide();
            loadAgents();
        } else {
            showToast('更新失败：' + response.message, 'danger');
        }
    } catch (error) {
        console.error('Failed to update agent:', error);
        showToast('更新失败：' + error.message, 'danger');
    }
}

// 预览编辑的Agent - 复制自admin.js
function previewEditAgent() {
    const agentName = document.getElementById('editAgentName').value;
    const agentCode = document.getElementById('editAgentCode').value;
    const description = document.getElementById('editAgentDescription').value;
    const promptTemplate = document.getElementById('editPromptTemplate').value;

    showToast(`Agent预览：
名称：${agentName}
编码：${agentCode}
描述：${description}
提示词：${promptTemplate.substring(0, 100)}...`, 'info');
}

// 测试Agent - 跳转到文档识别页面
function testAgent(agentCode) {
    // 存储agentCode到sessionStorage
    if (agentCode) {
        sessionStorage.setItem('selectedAgentCode', agentCode);
    }

    // 检查是否在主框架中
    if (window.parent && window.parent !== window) {
        // 在iframe中，通知父窗口切换到文档识别
        window.parent.postMessage({
            type: 'switchToRecognition',
            agentCode: agentCode
        }, '*');
    } else if (typeof loadPage === 'function') {
        // 在主页面中，直接切换到文档识别
        loadPage('recognition');
    } else {
        // 如果在独立页面中，跳转到主页面的文档识别
        window.location.href = 'admin.html#recognition';
    }
}

// 删除Agent - 复制自admin.js
async function deleteAgent(id) {
    try {
        // 先获取Agent信息检查状态
        const agentResponse = await apiCall(`/api/v1/agents/${id}`);
        if (agentResponse.code === 200) {
            const agent = agentResponse.data;
            if (!canDeleteAgent(agent.status)) {
                showToast('已发布的Agent不能删除！', 'warning');
                return;
            }
        }
    } catch (error) {
        console.error('获取Agent信息失败:', error);
        showToast('获取Agent信息失败', 'danger');
        return;
    }

    showConfirmModal(
        '确认删除Agent',
        '确定要删除这个Agent吗？此操作不可恢复！',
        'danger',
        async () => {
            try {
                const response = await apiCall(`/api/v1/agents/${id}`, 'DELETE');
                if (response.code === 200) {
                    showToast('Agent删除成功！', 'success');
                    loadAgents(); // 重新加载列表
                } else {
                    showToast('删除失败：' + response.message, 'danger');
                }
            } catch (error) {
                console.error('Failed to delete agent:', error);
                showToast('删除失败：' + error.message, 'danger');
            }
        }
    );
}

// 发布Agent - 复制自admin.js
async function publishAgent(agentId) {
    showConfirmModal(
        '确认发布Agent',
        '确定要发布这个Agent吗？发布后用户就可以使用了。',
        'success',
        async () => {
            try {
                const response = await apiCall(`/api/v1/agents/${agentId}/publish`, 'POST');
                if (response.code === 200) {
                    showToast('Agent发布成功！', 'success');
                    loadAgents(); // 刷新列表
                } else {
                    showToast('发布失败：' + response.message, 'danger');
                }
            } catch (error) {
                console.error('Failed to publish agent:', error);
                showToast('发布失败：' + error.message, 'danger');
            }
        }
    );
}

// 下线Agent - 复制自admin.js
async function unpublishAgent(agentId) {
    // 检查管理员权限
    if (!isAdmin()) {
        showToast('请联系管理员！', 'warning');
        return;
    }

    showConfirmModal(
        '确认下线Agent',
        '确定要下线这个Agent吗？下线后用户将无法使用。',
        'warning',
        async () => {
            try {
                const response = await apiCall(`/api/v1/agents/${agentId}/unpublish`, 'POST');
                if (response.code === 200) {
                    showToast('Agent已下线！', 'success');
                    loadAgents(); // 刷新列表
                } else {
                    showToast('下线失败：' + response.message, 'danger');
                }
            } catch (error) {
                console.error('Failed to unpublish agent:', error);
                showToast('下线失败：' + error.message, 'danger');
            }
        }
    );
}

// 克隆Agent - 复制自admin.js
async function cloneAgent(agentId) {
    showInputModal(
        '克隆Agent',
        '请输入新Agent的名称：',
        '请输入Agent名称',
        '',
        async (newName) => {
            try {
                const response = await apiCall(`/api/v1/agents/${agentId}/clone`, 'POST', {
                    newName: newName
                });
                if (response.code === 200) {
                    showToast('Agent克隆成功！', 'success');
                    loadAgents(); // 刷新列表
                } else {
                    showToast('克隆失败：' + response.message, 'danger');
                }
            } catch (error) {
                console.error('Failed to clone agent:', error);
                showToast('克隆失败：' + error.message, 'danger');
            }
        }
    );
}

// 显示Agent版本历史（新的弹窗版本）- 复制自admin.js
async function showAgentVersionHistory(agentId) {
    try {
        // 存储当前Agent ID，供版本详情查看使用
        window.currentAgentIdForVersionHistory = agentId;

        // 获取Agent信息
        const agentResponse = await apiCall(`/api/v1/agents/${agentId}`);
        if (agentResponse.code !== 200) {
            throw new Error('获取Agent信息失败');
        }

        const agent = agentResponse.data;
        document.getElementById('agentVersionModalTitle').textContent = `(${agent.agentName})`;

        // 获取版本历史数据
        const versionResponse = await apiCall(`/api/v1/agents/${agentId}/versions`);
        if (versionResponse.code !== 200) {
            throw new Error('获取版本历史失败');
        }



        const versions = versionResponse.data || [];

        const versionsHtml = versions.map( version => {
            // 获取审批状态显示
            const getApprovalStatusBadge = (approvalStatus) => {
                switch (approvalStatus) {
                    case 1:
                        return '<span class="badge bg-warning ms-2">审批中</span>';
                    case 2:
                        return '<span class="badge bg-success ms-2">审批通过</span>';
                    case 3:
                        return '<span class="badge bg-danger ms-2">审批不通过</span>';
                    default:
                        return '<span class="badge bg-secondary ms-2">未提交</span>';
                }
            };

            // 获取Agent审批状态
            const currentApprovalStatus =  (version.approvalStatus || 0);

            // 判断是否可以显示"使用当前版本"按钮（只有审批通过的版本才能使用）
            const canUseVersion = version.isCurrent !== 1 && currentApprovalStatus === 2;

            return `
                <div class="card mb-3 ${version.isCurrent === 1 ? 'border-primary' : ''}">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h6 class="card-title d-flex align-items-center">
                                    版本 ${version.versionNumber}
                                    ${version.isCurrent === 1 ? '<span class="badge bg-primary ms-2">当前版本</span>' : ''}
                                    ${version.status === 3 ? '<span class="badge bg-success ms-2">已发布</span>' : ''}
                                    ${version.status === 2 ? '<span class="badge bg-warning ms-2">测试中</span>' : ''}
                                    ${version.status === 1 ? '<span class="badge bg-secondary ms-2">草稿</span>' : ''}
                                    ${getApprovalStatusBadge(currentApprovalStatus)}
                                </h6>
                                <p class="card-text text-muted small mb-2">${version.changeLog || '无变更说明'}</p>
                                <small class="text-muted">创建时间: ${formatDate(version.createdTime)}</small>
                                ${version.creatorName ? `<br><small class="text-muted">创建者: ${version.creatorName}</small>` : ''}
                            </div>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-success" onclick="goToDebugConsole('${agent.agentCode}')" title="调试">
                                    <i class="bi bi-bug"></i> 调试
                                </button>
                                ${currentApprovalStatus !== 2 ? `
                                    <button class="btn btn-outline-warning" onclick="showApplyPublishModalForVersion(${agentId}, ${version.id}, '${version.versionNumber}')" title="申请发布">
                                        <i class="bi bi-send"></i> 申请发布
                                    </button>
                                ` : ''}
                                ${canUseVersion ? `
                                    <button class="btn btn-outline-primary" hidden="hidden" onclick="switchToVersion(${agentId}, '${version.versionNumber}', ${version.id})" title="切换到此版本">
                                        <i class="bi bi-arrow-clockwise"></i> 使用当前版本
                                    </button>
                                ` : ''}
                                <button class="btn btn-outline-info" onclick="viewVersionDetails(${version.id}, ${agentId})" title="查看详情">
                                    <i class="bi bi-eye"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }).join('');

        document.getElementById('agentVersionsList').innerHTML = versionsHtml;

        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('agentVersionHistoryModal'));
        modal.show();

    } catch (error) {
        console.error('Failed to load version history:', error);
        showToast('加载版本历史失败：' + error.message, 'danger');
    }
}

// 切换到指定版本 - 复制自admin.js
async function switchToVersion(agentId, version, versionId) {
    const message = `确定要将Agent切换到版本 ${version} 吗？<br><br>这将会：<br>• 更新Agent的当前版本<br>• 保存当前版本作为历史记录<br>• 应用选定版本的所有配置`;

    showConfirmModal(
        '确认切换版本',
        message,
        'warning',
        async () => {
            try {
                // 显示加载状态
                const button = event.target;
                const originalText = button.innerHTML;
                button.innerHTML = '<i class="bi bi-spinner-border spinner-border-sm"></i> 切换中...';
                button.disabled = true;

                // 调用版本回滚API
                const response = await apiCall(`/api/v1/agents/${agentId}/rollback`, 'POST', {
                    versionNumber: version
                });

                if (response.code === 200) {
                    showToast(`成功切换到版本 ${version}！`, 'success');
                    // 关闭模态框并刷新列表
                    bootstrap.Modal.getInstance(document.getElementById('agentVersionHistoryModal')).hide();
                    loadAgents();
                } else {
                    throw new Error(response.message || '版本切换失败');
                }

            } catch (error) {
                console.error('Failed to switch version:', error);
                showToast('版本切换失败：' + error.message, 'danger');

                // 恢复按钮状态
                if (button) {
                    button.innerHTML = originalText;
                    button.disabled = false;
                }
            }
        }
    );
}

// 查看版本详情
function viewVersionDetails(versionId, agentId) {
    // 关闭版本历史模态框
    const versionHistoryModal = bootstrap.Modal.getInstance(document.getElementById('agentVersionHistoryModal'));
    if (versionHistoryModal) {
        versionHistoryModal.hide();
    }

    // 使用传入的Agent ID，如果没有则尝试从全局变量获取
    const targetAgentId = agentId || window.currentAgentIdForVersionHistory;

    if (targetAgentId) {
        // 设置Agent ID到sessionStorage，供详情页面使用
        sessionStorage.setItem('currentAgentId', targetAgentId);

        // 记录来源页面，供详情页面的返回按钮使用
        sessionStorage.setItem('agentDetailSource', 'agents');

        // 跳转到Agent详情页面
        if (typeof loadPage === 'function') {
            loadPage('agent-detail');
        } else {
            console.error('loadPage函数未定义');
            showToast('页面跳转失败', 'error');
        }
    } else {
        showToast('无法获取Agent信息，请重新打开版本历史', 'warning');
    }
}

// 申请发布Agent - 跳转到补充资料页面
async function showApplyPublishModal(agentId) {
    try {
        // 关闭版本历史模态框
        const versionHistoryModal = bootstrap.Modal.getInstance(document.getElementById('agentVersionHistoryModal'));
        if (versionHistoryModal) {
            versionHistoryModal.hide();
        }

        // 使用全局的showAgentSupplement函数
        if (typeof showAgentSupplement === 'function') {
            showAgentSupplement(agentId, 'agents');
        } else {
            // 降级方案
            sessionStorage.setItem('currentAgentId', agentId);
            sessionStorage.setItem('supplementSource', 'agents');
            if (typeof loadPage === 'function') {
                loadPage('agent-supplement');
            } else {
                console.error('loadPage函数未定义');
                showToast('页面跳转失败', 'error');
            }
        }

    } catch (error) {
        console.error('申请发布跳转失败:', error);
        showToast('申请发布跳转失败：' + error.message, 'danger');
    }
}

// 申请发布Agent（带版本信息）- 跳转到补充资料页面
function showApplyPublishModalForVersion(agentId, versionId, versionNumber) {
    try {
        // 关闭版本历史模态框
        const versionHistoryModal = bootstrap.Modal.getInstance(document.getElementById('agentVersionHistoryModal'));
        if (versionHistoryModal) {
            versionHistoryModal.hide();
        }

        // 设置Agent ID和版本信息到sessionStorage
        sessionStorage.setItem('currentAgentId', agentId);
        sessionStorage.setItem('selectedVersionId', versionId);
        sessionStorage.setItem('selectedVersionNumber', versionNumber);

        // 记录来源页面，供补充资料页面的返回按钮使用
        sessionStorage.setItem('supplementSource', 'agents');

        // 跳转到补充资料页面
        if (typeof loadPage === 'function') {
            loadPage('agent-supplement');
        } else {
            console.error('loadPage函数未定义');
            showToast('页面跳转失败', 'error');
        }

    } catch (error) {
        console.error('申请发布跳转失败:', error);
        showToast('申请发布跳转失败：' + error.message, 'danger');
    }
}

// 提交申请发布
async function submitApplyPublish(agentId, versionId, versionNumber) {
    try {
        const changeLog = document.getElementById('changeLogInput').value.trim();

        if (!changeLog) {
            showToast('请填写版本更新说明', 'warning');
            return;
        }

        const response = await apiCall(`/api/v1/agent-approval/submit/${agentId}`, 'POST', {
            changeLog: changeLog,
            versionId: versionId,
            versionNumber: versionNumber
        });

        if (response.code === 200) {
            showToast('申请发布成功，请等待管理员审批', 'success');
            // 关闭模态框
            bootstrap.Modal.getInstance(document.getElementById('applyPublishModal')).hide();
            // 刷新版本历史
            showAgentVersionHistory(agentId);
        } else {
            throw new Error(response.message || '申请发布失败');
        }
    } catch (error) {
        console.error('申请发布失败:', error);
        showToast('申请发布失败：' + error.message, 'danger');
    }
}

// 显示调试历史
async function showDebugHistory(agentId) {
    try {
        // 获取Agent信息
        const agentResponse = await apiCall(`/api/v1/agents/${agentId}`);
        if (agentResponse.code !== 200) {
            throw new Error('获取Agent信息失败');
        }

        const agent = agentResponse.data;
        document.getElementById('debugAgentName').textContent = `(${agent.agentName})`;

        // 获取调试历史数据
        const debugResponse = await apiCall(`/api/v1/agents/${agentId}/debug-history`);
        if (debugResponse.code !== 200) {
            throw new Error('获取调试历史失败');
        }

        const debugHistory = debugResponse.data || [];

        // 获取调试统计信息
        const statsResponse = await apiCall(`/api/v1/agents/${agentId}/debug-statistics`);
        const stats = statsResponse.code === 200 ? statsResponse.data : {};

        // 渲染调试历史表格
        const tableHtml = debugHistory.map(record => `
            <tr>
                <td>${formatDate(record.createdTime)}</td>
                <td><span class="badge bg-info">${record.agentName || 'N/A'}</span></td>
                <td><span class="badge bg-secondary">${record.modelName || 'N/A'}</span></td>
                <td><span class="badge bg-${record.status === 1 ? 'success' : 'danger'}">${record.status === 1 ? '成功' : '失败'}</span></td>
                <td>${record.responseTime > 0 ? record.responseTime + 'ms' : '-'}</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="viewDebugDetails(${record.id})" title="查看详情">
                        <i class="bi bi-eye"></i>
                    </button>
                </td>
            </tr>
        `).join('');

        document.getElementById('debugHistoryTable').innerHTML = tableHtml;

        // 更新统计信息
        document.getElementById('totalCalls').textContent = stats.totalCount || 0;
        document.getElementById('successRate').textContent = (stats.successRate || 0) + '%';
        document.getElementById('avgTime').textContent = (stats.averageExecutionTime || 0) + 'ms';
        document.getElementById('minTime').textContent = (stats.averageExecutionTime || 0) + 'ms'; // 暂时用平均时间代替最小时间

        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('debugHistoryModal'));
        modal.show();

    } catch (error) {
        console.error('Failed to load debug history:', error);
        showToast('加载调试历史失败：' + error.message, 'danger');
    }
}

// 查看调试详情
async function viewDebugDetails(recordId) {
    try {
        const response = await apiCall(`/api/v1/agents/debug-history/${recordId}`);
        if (response.code !== 200) {
            throw new Error('获取调试详情失败');
        }

        const record = response.data;

        // 构建详情HTML
        const detailsHtml = `
            <div class="row">
                <div class="col-md-6">
                    <h6>基本信息</h6>
                    <table class="table table-sm">
                        <tr><td>调试时间:</td><td>${formatDate(record.createdTime)}</td></tr>
                        <tr><td>响应时间:</td><td>${record.responseTime}ms</td></tr>
                        <tr><td>执行状态:</td><td><span class="badge bg-${record.status === 1 ? 'success' : 'danger'}">${record.status === 1 ? '成功' : '失败'}</span></td></tr>
                        <tr><td>模型:</td><td>${record.modelName || 'N/A'}</td></tr>
                        <tr><td>用户:</td><td>${record.userName || 'N/A'}</td></tr>
                        ${record.tokensUsed ? `<tr><td>Token使用:</td><td>${record.tokensUsed}</td></tr>` : ''}
                        ${record.cost ? `<tr><td>调用成本:</td><td>¥${record.cost}</td></tr>` : ''}
                    </table>
                </div>
                <div class="col-md-6">
                    <h6>模型配置</h6>
                    <pre class="bg-light p-2 small">${record.modelConfig || 'N/A'}</pre>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-md-6">
                    <h6>输入数据</h6>
                    ${renderInputData(record.inputData)}
                </div>
                <div class="col-md-6">
                    <h6>输出结果</h6>
                    <pre class="bg-light p-2 small" style="max-height: 200px; overflow-y: auto;">${formatOutputData(record.outputData)}</pre>
                </div>
            </div>
            ${record.errorMessage ? `
                <div class="row mt-3">
                    <div class="col-12">
                        <h6>错误信息</h6>
                        <div class="alert alert-danger">${record.errorMessage}</div>
                    </div>
                </div>
            ` : ''}
        `;

        // 显示在模态框中
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.innerHTML = `
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">调试记录详情</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        ${detailsHtml}
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        const bootstrapModal = new bootstrap.Modal(modal);
        bootstrapModal.show();

        // 模态框显示后处理待加载的文件
        modal.addEventListener('shown.bs.modal', () => {
            console.log('模态框已显示，开始处理待加载文件');
            if (window.pendingFileLoads && window.pendingFileLoads.length > 0) {
                window.pendingFileLoads.forEach(task => {
                    console.log('处理文件加载任务:', task);
                    setTimeout(() => {
                        loadFileContent(task.fileId, task.containerId, task.displayType, task.fileName);
                    }, 100);
                });
                // 清空待处理队列
                window.pendingFileLoads = [];
            }
        });

        // 模态框关闭后移除DOM元素
        modal.addEventListener('hidden.bs.modal', () => {
            document.body.removeChild(modal);
        });

    } catch (error) {
        console.error('Failed to load debug details:', error);
        showToast('加载调试详情失败：' + error.message, 'danger');
    }
}

// 渲染输入数据
function renderInputData(inputData) {
    if (!inputData || inputData === 'N/A') {
        return '<div class="text-muted">无输入数据</div>';
    }

    try {
        // 尝试解析JSON格式的输入数据
        const data = JSON.parse(inputData);

        switch (data.type) {
            case 'text':
                return `
                    <div class="mb-2">
                        <small class="text-muted">类型：文本输入 (${data.length || 0} 字符)</small>
                    </div>
                    <textarea class="form-control" rows="8" readonly style="resize: none; font-family: monospace; font-size: 12px;">${data.content || ''}</textarea>
                `;

            case 'file':
                return renderFileInput(data);

            case 'empty':
                return '<div class="text-muted">空输入</div>';

            default:
                // 未知类型，显示原始数据
                return `<pre class="bg-light p-2 small" style="max-height: 200px; overflow-y: auto;">${inputData}</pre>`;
        }
    } catch (e) {
        // 不是JSON格式，按文本处理
        return `
            <div class="mb-2">
                <small class="text-muted">类型：文本输入</small>
            </div>
            <textarea class="form-control" rows="8" readonly style="resize: none; font-family: monospace; font-size: 12px;">${inputData}</textarea>
        `;
    }
}

// 渲染文件输入
function renderFileInput(data) {
    const fileInfo = `
        <div class="mb-2">
            <small class="text-muted">
                类型：文件输入 |
                文件名：${data.fileName || 'N/A'} |
                大小：${formatFileSize(data.fileSize || 0)} |
                格式：${data.fileType || 'N/A'}
            </small>
        </div>
    `;

    const displayType = data.displayType || 'document';
    const fileId = data.fileId;
    const containerId = 'file-container-' + fileId + '-' + Date.now();

    // 将文件加载任务添加到待处理队列
    if (!window.pendingFileLoads) {
        window.pendingFileLoads = [];
    }
    window.pendingFileLoads.push({
        fileId: fileId,
        containerId: containerId,
        displayType: displayType,
        fileName: data.fileName
    });

    switch (displayType) {
        case 'image':
            return fileInfo + `
                <div class="text-center" id="${containerId}">
                    <div class="d-flex justify-content-center align-items-center" style="height: 200px;">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <span class="ms-2">正在加载图片...</span>
                    </div>
                </div>
            `;

        case 'pdf':
            return fileInfo + `
                <div class="border rounded" id="${containerId}">
                    <div class="d-flex justify-content-center align-items-center" style="height: 300px;">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <span class="ms-2">正在加载PDF...</span>
                    </div>
                </div>
            `;

        case 'text':
            return fileInfo + `
                <div class="border rounded p-2 bg-light" id="${containerId}">
                    <div class="text-center text-muted">
                        <div class="spinner-border spinner-border-sm text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <span class="ms-2">正在加载文本内容...</span>
                    </div>
                </div>
            `;

        default:
            return fileInfo + `
                <div class="border rounded p-3 text-center bg-light">
                    <i class="bi bi-file-earmark"></i>
                    <div class="mt-2">
                        <small class="text-muted">文档文件</small>
                        <br><button class="btn btn-sm btn-outline-primary mt-2" onclick="downloadFileWithAuth('${fileId}', '${data.fileName}')">
                            <i class="bi bi-download"></i> 下载文件
                        </button>
                    </div>
                </div>
            `;
    }
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 加载文件内容（带认证）
async function loadFileContent(fileId, containerId, displayType, fileName) {
    console.log('loadFileContent 被调用:', { fileId, containerId, displayType, fileName });

    const container = document.getElementById(containerId);
    if (!container) {
        console.error('Container not found:', containerId);
        return;
    }

    console.log('容器找到，开始加载文件内容:', fileId);
    try {
        // 获取认证token
        const token = getToken ? getToken() : (authToken || localStorage.getItem('authToken') || '');

        // 使用fetch获取文件内容，自动处理认证
        const response = await fetch(`/api/v1/files/${fileId}/preview`, {
            method: 'GET',
            headers: {
                'Authorization': 'Bearer ' + token
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const blob = await response.blob();
        const blobUrl = URL.createObjectURL(blob);

        // 根据显示类型渲染内容
        switch (displayType) {
            case 'image':
                container.innerHTML = `
                    <img src="${blobUrl}"
                         alt="${fileName}"
                         class="img-fluid border rounded"
                         style="max-height: 300px; max-width: 100%;"
                         onload="URL.revokeObjectURL('${blobUrl}')"
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                    <div class="alert alert-warning" style="display: none;">
                        <i class="bi bi-exclamation-triangle"></i> 图片加载失败
                        <br><button class="btn btn-sm btn-outline-primary mt-2" onclick="downloadFileWithAuth('${fileId}', '${fileName}')">
                            <i class="bi bi-download"></i> 下载文件
                        </button>
                    </div>
                `;
                break;

            case 'pdf':
                container.innerHTML = `
                    <iframe src="${blobUrl}"
                            style="width: 100%; height: 300px; border: none;"
                            onload="setTimeout(() => URL.revokeObjectURL('${blobUrl}'), 1000)">
                    </iframe>
                    <div class="text-center mt-2">
                        <button class="btn btn-sm btn-outline-primary" onclick="downloadFileWithAuth('${fileId}', '${fileName}')">
                            <i class="bi bi-download"></i> 下载PDF
                        </button>
                    </div>
                `;
                break;

            case 'text':
                // 对于文本文件，读取内容并显示
                const text = await blob.text();
                container.innerHTML = `
                    <textarea class="form-control" rows="8" readonly style="resize: none; font-family: monospace; font-size: 12px;">${text}</textarea>
                    <div class="text-center mt-2">
                        <button class="btn btn-sm btn-outline-primary" onclick="downloadFileWithAuth('${fileId}', '${fileName}')">
                            <i class="bi bi-download"></i> 下载文件
                        </button>
                    </div>
                `;
                break;

            default:
                container.innerHTML = `
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle"></i> 无法预览此文件类型
                        <br><button class="btn btn-sm btn-outline-primary mt-2" onclick="downloadFileWithAuth('${fileId}', '${fileName}')">
                            <i class="bi bi-download"></i> 下载文件
                        </button>
                    </div>
                `;
        }

    } catch (error) {
        console.error('加载文件内容失败:', error);
        container.innerHTML = `
            <div class="alert alert-danger">
                <i class="bi bi-exclamation-triangle"></i> 加载失败: ${error.message}
                <br><button class="btn btn-sm btn-outline-primary mt-2" onclick="downloadFileWithAuth('${fileId}', '${fileName}')">
                    <i class="bi bi-download"></i> 下载文件
                </button>
            </div>
        `;
    }
}

// 带认证的文件下载
async function downloadFileWithAuth(fileId, fileName) {
    try {
        // 获取认证token
        const token = getToken ? getToken() : (authToken || localStorage.getItem('authToken') || '');

        const response = await fetch(`/api/v1/files/${fileId}/download`, {
            method: 'GET',
            headers: {
                'Authorization': 'Bearer ' + token
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const blob = await response.blob();
        const url = URL.createObjectURL(blob);

        // 创建下载链接
        const link = document.createElement('a');
        link.href = url;
        link.download = fileName || 'download';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // 清理URL
        URL.revokeObjectURL(url);

    } catch (error) {
        console.error('下载文件失败:', error);
        showToast('下载文件失败: ' + error.message, 'danger');
    }
}

// 筛选调试历史
function filterDebugHistory(filter) {
    showToast(`筛选调试历史: ${filter} 功能开发中...`, 'info');
}

// 跳转到智能调试台 - 复制自admin.js
function goToDebugConsole(agentCode) {
    // 关闭版本历史模态框
    const versionHistoryModal = bootstrap.Modal.getInstance(document.getElementById('agentVersionHistoryModal'));
    if (versionHistoryModal) {
        versionHistoryModal.hide();
    }

    // 将agentCode存储到sessionStorage，以便在智能调试台页面使用
    if (agentCode) {
        sessionStorage.setItem('selectedAgentCode', agentCode);
    }

    // 检查是否在主框架中
    if (window.parent && window.parent !== window) {
        // 在iframe中，通知父窗口切换到智能调试台
        window.parent.postMessage({
            type: 'switchToPlayground',
            agentCode: agentCode
        }, '*');
    } else if (typeof loadPage === 'function') {
        // 在主页面中，直接切换到智能调试台
        loadPage('playground');
    } else {
        // 如果在独立页面中，跳转到主页面的智能调试台
        window.location.href = 'admin.html#playground';
    }
}

// // 辅助函数
// function getCategoryText(category) {
//     const categories = {
//         'finance': '财务',
//         'logistics': '物流',
//         'legal': '法务',
//         'identity': '身份',
//         'general': '通用'
//     };
//     return categories[category] || category;
// }
//
// function getModelText(model) {
//     const models = {
//         'qwen-plus': '通义千问Plus',
//         'qwen-max': '通义千问Max',
//         'gpt-3.5-turbo': 'GPT-3.5',
//         'gpt-4': 'GPT-4'
//     };
//     return models[model] || model;
// }

// Agent状态函数已在admin-common.js中定义，这里不需要重复定义

// API调用函数已在admin-common.js中定义，这里不需要重复定义

// 导入Agent - 复制自admin.js
function importAgent() {
    showToast('导入Agent功能开发中...', 'info');
}

// 搜索功能
function searchAgents() {
    const keyword = document.getElementById('agentSearchInput').value.trim();
    loadAgents({ keyword: keyword, page: 1 });
}

// 注意：搜索框回车事件监听器已移动到initializeAgentsPageEnhancements函数中

// 筛选Agents
function filterAgents(filter) {
    let filterParams = { page: 1 };

    switch(filter) {
        case 'all':
            filterParams.status = '';
            break;
        case 'draft':
            filterParams.status = 0;
            break;
        case 'testing':
            filterParams.status = 1;
            break;
        case 'approved':
            filterParams.status = 2;
            break;
        case 'published':
            filterParams.status = 3;
            break;
        case 'rejected':
            filterParams.status = 4;
            break;
        default:
            filterParams.status = '';
    }

    loadAgents(filterParams);
}

// 按分类筛选
function filterByCategory(categoryId) {
    loadAgents({ categoryId: categoryId, page: 1 });
}

// 清除所有筛选条件
function clearFilters() {
    // 清空搜索框
    document.getElementById('agentSearchInput').value = '';

    // 重置状态过滤器
    const statusFilter = document.getElementById('statusFilter');
    if (statusFilter) {
        statusFilter.value = '';
    }

    // 重置分类过滤器
    const categoryFilter = document.getElementById('categoryFilter');
    if (categoryFilter) {
        categoryFilter.value = '';
    }

    // 重置搜索参数
    currentSearchParams = {
        page: 1,
        size: 10,
        keyword: '',
        categoryId: '',
        status: ''
    };

    // 重新加载数据
    loadAgents();
}



// 加载业务模板到Agent创建表单 - 复制自admin.js
async function loadAgentTemplates() {
    try {
        const response = await apiCall('/api/v1/business-templates/active');
        if (response.code === 200) {
            const templates = response.data;
            const select = document.getElementById('agentTemplateSelect');
            if (select) {
                select.innerHTML = '<option value="">请选择业务模板</option>';
                templates.forEach(template => {
                    select.innerHTML += `<option value="${template.id}" data-json='${template.jsonTemplate}'>${template.templateName} (${template.templateCode})</option>`;
                });
            }
        }
    } catch (error) {
        console.error('Failed to load templates:', error);
    }
}

// 加载选中的模板 - 复制自admin.js
function loadSelectedTemplate() {
    const select = document.getElementById('agentTemplateSelect');
    const selectedOption = select.options[select.selectedIndex];
    const jsonDisplay = document.getElementById('selectedTemplateJson');

    if (selectedOption && selectedOption.value) {
        const jsonTemplate = selectedOption.getAttribute('data-json');
        if (jsonTemplate) {
            try {
                const formatted = JSON.stringify(JSON.parse(jsonTemplate), null, 2);
                jsonDisplay.textContent = formatted;
                jsonDisplay.className = 'mb-0';
            } catch (error) {
                jsonDisplay.textContent = jsonTemplate;
                jsonDisplay.className = 'mb-0 text-danger';
            }
        }
    } else {
        jsonDisplay.textContent = '请先选择业务模板';
        jsonDisplay.className = 'mb-0 text-muted';
    }
}

// 预览Agent - 复制自admin.js
function previewAgent() {
    showToast('Agent预览功能开发中...', 'info');
}

// 修改创建Agent函数，使用模板 - 复制自admin.js
async function createAgentFromTemplate() {
    const form = document.getElementById('createAgentForm');
    const formData = new FormData(form);

    const templateId = formData.get('templateId');
    if (!templateId) {
        showToast('请选择业务模板', 'warning');
        return;
    }

    // 获取选中模板的JSON
    const select = document.getElementById('agentTemplateSelect');
    const selectedOption = select.options[select.selectedIndex];
    const jsonTemplate = selectedOption.getAttribute('data-json');

    // 获取平台信息（如果有的话）
    const platform = formData.get('platform') || '1'; // 默认为内部大模型LLM

    // 根据平台确定Agent类型 - 对于模板创建，使用简化的判断逻辑
    let agentType = 1; // 默认为内部LLM

    // 如果有平台选择，尝试获取平台名称
    const platformSelect = document.getElementById('createPlatformSelect');
    if (platformSelect && platform) {
        // 查找对应的平台选项
        for (let option of platformSelect.options) {
            if (option.value === platform) {
                const platformName = option.text;
                if (platformName.includes('外部大模型')) {
                    agentType = 2; // Agent.TYPE_EXTERNAL_LLM
                } else {
                    agentType = 1; // Agent.TYPE_INTERNAL_LLM
                }
                break;
            }
        }
    }

    const agentData = {
        agentName: formData.get('agentName'),
        agentCode: formData.get('agentCode'),
        description: formData.get('description'),
        categoryId: parseInt(formData.get('categoryId')) || 1,
        businessTypeId: 1, // 默认业务类型
        agentType: agentType, // 根据平台动态确定
        templateId: parseInt(templateId),
        config: JSON.stringify({
            llm_config: {
                platform: platform,
                provider: "qianwen", // 保持兼容性
                model: formData.get('model') || "qwen-plus",
                temperature: parseFloat(formData.get('temperature')) || 0.7,
                max_tokens: parseInt(formData.get('maxTokens')) || 2000,
                top_p: parseFloat(formData.get('topP')) || 0.9
            }
        }),
        promptTemplate: formData.get('promptTemplate'),
        jsonTemplate: jsonTemplate
    };

    try {
        const response = await apiCall('/api/v1/agents', 'POST', agentData);
        if (response.code === 200) {
            showToast('Agent创建成功！', 'success');
            bootstrap.Modal.getInstance(document.getElementById('createAgentModal')).hide();
            form.reset();
            document.getElementById('selectedTemplateJson').textContent = '请先选择业务模板';
            document.getElementById('selectedTemplateJson').className = 'mb-0 text-muted';
            loadAgents();
        } else {
            showToast('创建失败：' + response.message, 'danger');
        }
    } catch (error) {
        console.error('Failed to create agent:', error);
        showToast('创建失败：' + error.message, 'danger');
    }
}

// 加载平台列表
async function loadPlatforms() {
    try {
        const response = await apiCall('/api/v1/sys-config/dict/大模型平台配置');
        if (response.code === 200) {
            const platforms = response.data;
            const createSelect = document.getElementById('createPlatformSelect');
            if (createSelect) {
                createSelect.innerHTML = '<option value="">请选择平台...</option>';
                platforms.forEach(platform => {
                    createSelect.innerHTML += `<option value="${platform.configKey}">${platform.configValue}</option>`;
                });
            }
        }
    } catch (error) {
        console.error('Failed to load platforms:', error);
    }
}

// 加载平台列表到编辑表单
async function loadPlatformsForEdit() {
    try {
        const response = await apiCall('/api/v1/sys-config/dict/大模型平台配置');
        if (response.code === 200) {
            const platforms = response.data;
            const editSelect = document.getElementById('editPlatformSelect');
            if (editSelect) {
                editSelect.innerHTML = '<option value="">请选择平台...</option>';
                platforms.forEach(platform => {
                    editSelect.innerHTML += `<option value="${platform.configKey}">${platform.configValue}</option>`;
                });
            }
        }
    } catch (error) {
        console.error('Failed to load platforms for edit:', error);
    }
}

// 检查Agent名称是否已存在
async function checkAgentNameExists(agentName, excludeId = null) {
    try {
        const params = new URLSearchParams({ agentName });
        if (excludeId !== null) {
            params.append('excludeId', excludeId);
        }

        const response = await apiCall(`/api/v1/agents/check-name?${params.toString()}`);
        if (response.code === 200) {
            return response.data; // 直接返回布尔值
        }
        return false;
    } catch (error) {
        console.error('Failed to check agent name:', error);
        return false;
    }
}

// Agent名称输入验证
function setupAgentNameValidation() {
    const createNameInput = document.querySelector('#createAgentModal input[name="agentName"]');
    const editNameInput = document.querySelector('#editAgentModal input[name="agentName"]');

    if (createNameInput) {
        let createTimeout;
        createNameInput.addEventListener('input', function() {
            clearTimeout(createTimeout);
            const nameValue = this.value.trim();
            const feedbackElement = this.parentNode.querySelector('.name-feedback') ||
                                  this.parentNode.appendChild(document.createElement('div'));
            feedbackElement.className = 'name-feedback small mt-1';

            if (nameValue.length === 0) {
                feedbackElement.textContent = '';
                feedbackElement.className = 'name-feedback small mt-1';
                return;
            }

            feedbackElement.textContent = '检查中...';
            feedbackElement.className = 'name-feedback small mt-1 text-muted';

            createTimeout = setTimeout(async () => {
                const exists = await checkAgentNameExists(nameValue);
                if (exists) {
                    feedbackElement.textContent = '该名称已存在，请使用其他名称';
                    feedbackElement.className = 'name-feedback small mt-1 text-danger';
                    this.classList.add('is-invalid');
                } else {
                    feedbackElement.textContent = '名称可用';
                    feedbackElement.className = 'name-feedback small mt-1 text-success';
                    this.classList.remove('is-invalid');
                }
            }, 500);
        });
    }

    if (editNameInput) {
        let editTimeout;
        editNameInput.addEventListener('input', function() {
            clearTimeout(editTimeout);
            const nameValue = this.value.trim();
            const currentAgentId = document.getElementById('editAgentId')?.value;
            const feedbackElement = this.parentNode.querySelector('.name-feedback') ||
                                  this.parentNode.appendChild(document.createElement('div'));
            feedbackElement.className = 'name-feedback small mt-1';

            if (nameValue.length === 0) {
                feedbackElement.textContent = '';
                feedbackElement.className = 'name-feedback small mt-1';
                return;
            }

            feedbackElement.textContent = '检查中...';
            feedbackElement.className = 'name-feedback small mt-1 text-muted';

            editTimeout = setTimeout(async () => {
                const exists = await checkAgentNameExists(nameValue, parseInt(currentAgentId));
                if (exists) {
                    feedbackElement.textContent = '该名称已存在，请使用其他名称';
                    feedbackElement.className = 'name-feedback small mt-1 text-danger';
                    this.classList.add('is-invalid');
                } else {
                    feedbackElement.textContent = '名称可用';
                    feedbackElement.className = 'name-feedback small mt-1 text-success';
                    this.classList.remove('is-invalid');
                }
            }, 500);
        });
    }
}

// 平台选择变化事件
async function onPlatformChange(type) {
    const platformSelect = document.getElementById(type + 'PlatformSelect');
    const modelSelect = document.getElementById(type + 'ModelSelect');
    const selectedPlatform = platformSelect.value;

    // 清空模型选择
    modelSelect.innerHTML = '<option value="">请选择模型...</option>';

    if (selectedPlatform) {
        try {
            // 根据平台获取对应的模型列表
            const platformName = platformSelect.options[platformSelect.selectedIndex].text;
            const response = await apiCall(`/api/v1/sys-config/dict/${platformName}`);
            if (response.code === 200) {
                const models = response.data;
                models.forEach(model => {
                    modelSelect.innerHTML += `<option value="${model.configKey}">${model.configValue}</option>`;
                });
            }

            // 控制参数显示/隐藏
            const isInternal = platformName.includes('内部');
            const temperatureDiv = document.getElementById(type + 'TemperatureDiv');
            const maxTokensDiv = document.getElementById(type + 'MaxTokensDiv');
            const topPDiv = document.getElementById(type + 'TopPDiv');

            if (isInternal) {
                // 内部平台隐藏参数控制
                if (temperatureDiv) temperatureDiv.style.display = 'none';
                if (maxTokensDiv) maxTokensDiv.style.display = 'none';
                if (topPDiv) topPDiv.style.display = 'none';
            } else {
                // 外部平台显示参数控制
                if (temperatureDiv) temperatureDiv.style.display = 'block';
                if (maxTokensDiv) maxTokensDiv.style.display = 'block';
                if (topPDiv) topPDiv.style.display = 'block';
            }
        } catch (error) {
            console.error('Failed to load models:', error);
        }
    }
}

// 格式化输出数据，处理JSON格式
function formatOutputData(outputData) {
    if (!outputData || outputData.trim() === '') {
        return 'N/A';
    }

    try {
        // 尝试解析为JSON并格式化
        const parsed = JSON.parse(outputData);
        return JSON.stringify(parsed, null, 2);
    } catch (e) {
        // 如果不是JSON，直接返回原始内容
        return outputData;
    }
}

// 显示Bootstrap风格的确认对话框
function showConfirmModal(title, message, type = 'warning', onConfirm = null, onCancel = null) {
    // 根据类型设置图标和颜色
    const typeConfig = {
        'warning': {
            icon: 'bi-exclamation-triangle-fill',
            iconColor: 'text-warning',
            btnClass: 'btn-warning'
        },
        'danger': {
            icon: 'bi-exclamation-triangle-fill',
            iconColor: 'text-danger',
            btnClass: 'btn-danger'
        },
        'info': {
            icon: 'bi-info-circle-fill',
            iconColor: 'text-info',
            btnClass: 'btn-primary'
        },
        'success': {
            icon: 'bi-check-circle-fill',
            iconColor: 'text-success',
            btnClass: 'btn-success'
        }
    };

    const config = typeConfig[type] || typeConfig['warning'];

    // 创建模态框HTML
    const modalHtml = `
        <div class="modal fade" id="confirmModal" tabindex="-1" aria-labelledby="confirmModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header border-0 pb-0">
                        <h5 class="modal-title d-flex align-items-center" id="confirmModalLabel">
                            <i class="bi ${config.icon} ${config.iconColor} me-2"></i>
                            ${title}
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body pt-2">
                        <p class="mb-0">${message}</p>
                    </div>
                    <div class="modal-footer border-0 pt-0">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="bi bi-x-lg me-1"></i>取消
                        </button>
                        <button type="button" class="btn ${config.btnClass}" id="confirmModalBtn">
                            <i class="bi bi-check-lg me-1"></i>确定
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 移除已存在的确认模态框
    const existingModal = document.getElementById('confirmModal');
    if (existingModal) {
        existingModal.remove();
    }

    // 添加模态框到页面
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // 获取模态框元素
    const modalElement = document.getElementById('confirmModal');
    const modal = new bootstrap.Modal(modalElement);

    // 绑定确定按钮事件
    const confirmBtn = document.getElementById('confirmModalBtn');
    confirmBtn.addEventListener('click', () => {
        modal.hide();
        if (onConfirm && typeof onConfirm === 'function') {
            onConfirm();
        }
    });

    // 绑定模态框关闭事件
    modalElement.addEventListener('hidden.bs.modal', () => {
        modalElement.remove();
        if (onCancel && typeof onCancel === 'function') {
            onCancel();
        }
    });

    // 显示模态框
    modal.show();
}

// 显示Bootstrap风格的输入对话框
function showInputModal(title, message, placeholder = '', defaultValue = '', onConfirm = null, onCancel = null) {
    // 创建模态框HTML
    const modalHtml = `
        <div class="modal fade" id="inputModal" tabindex="-1" aria-labelledby="inputModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header border-0 pb-0">
                        <h5 class="modal-title d-flex align-items-center" id="inputModalLabel">
                            <i class="bi bi-pencil-square text-primary me-2"></i>
                            ${title}
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body pt-2">
                        <p class="mb-3">${message}</p>
                        <div class="form-floating">
                            <input type="text" class="form-control" id="inputModalValue" placeholder="${placeholder}" value="${defaultValue}">
                            <label for="inputModalValue">${placeholder}</label>
                        </div>
                    </div>
                    <div class="modal-footer border-0 pt-0">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="bi bi-x-lg me-1"></i>取消
                        </button>
                        <button type="button" class="btn btn-primary" id="inputModalBtn">
                            <i class="bi bi-check-lg me-1"></i>确定
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 移除已存在的输入模态框
    const existingModal = document.getElementById('inputModal');
    if (existingModal) {
        existingModal.remove();
    }

    // 添加模态框到页面
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // 获取模态框元素
    const modalElement = document.getElementById('inputModal');
    const modal = new bootstrap.Modal(modalElement);
    const inputElement = document.getElementById('inputModalValue');

    // 绑定确定按钮事件
    const confirmBtn = document.getElementById('inputModalBtn');
    const handleConfirm = () => {
        const value = inputElement.value.trim();
        if (value) {
            modal.hide();
            if (onConfirm && typeof onConfirm === 'function') {
                onConfirm(value);
            }
        } else {
            inputElement.focus();
            inputElement.classList.add('is-invalid');
        }
    };

    confirmBtn.addEventListener('click', handleConfirm);

    // 绑定回车键事件
    inputElement.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            handleConfirm();
        }
    });

    // 移除验证样式
    inputElement.addEventListener('input', () => {
        inputElement.classList.remove('is-invalid');
    });

    // 绑定模态框关闭事件
    modalElement.addEventListener('hidden.bs.modal', () => {
        modalElement.remove();
        if (onCancel && typeof onCancel === 'function') {
            onCancel();
        }
    });

    // 显示模态框并聚焦输入框
    modal.show();
    modalElement.addEventListener('shown.bs.modal', () => {
        inputElement.focus();
        inputElement.select();
    });
}

// ==================== Tab式提示词编辑器相关函数 ====================

// 当前编辑的目标元素ID
let currentPromptTargetId = '';

// 显示创建Agent的提示词模板模态框
function showCreatePromptTemplateModal() {
    currentPromptTargetId = 'createPromptTemplate';
    const currentTemplate = document.getElementById('createPromptTemplate').value;

    // 如果当前模板为空，加载默认模板
    if (!currentTemplate || currentTemplate.trim() === '') {
        loadDefaultPromptTemplate();
    } else {
        // 解析现有的提示词模板
        parseAgentExistingPrompt(currentTemplate);
    }

    // 更新最终提示词预览
    updateAgentFinalPrompt();

    const modal = new bootstrap.Modal(document.getElementById('agentPromptTemplateModal'));
    modal.show();
}

// 显示编辑Agent的提示词模板模态框
function showEditPromptTemplateModal() {
    currentPromptTargetId = 'editPromptTemplate';
    const currentTemplate = document.getElementById('editPromptTemplate').value;

    // 解析现有的提示词模板
    parseAgentExistingPrompt(currentTemplate);

    // 更新最终提示词预览
    updateAgentFinalPrompt();

    const modal = new bootstrap.Modal(document.getElementById('agentPromptTemplateModal'));
    modal.show();
}

// 解析现有的提示词模板到各个Tab
function parseAgentExistingPrompt(template) {
    if (!template || template.trim() === '') {
        // 如果没有现有模板，清空所有Tab
        document.getElementById('agentRoleContent').value = '';
        document.getElementById('agentSkillsContent').value = '';
        document.getElementById('agentActionContent').value = '';
        document.getElementById('agentConstraintsContent').value = '';
        return;
    }

    // 简单的解析逻辑，根据常见的标题分割
    const sections = {
        role: '',
        skills: '',
        action: '',
        constraints: ''
    };

    // 按行分割并分析
    const lines = template.split('\n');
    let currentSection = '';
    let currentContent = [];

    for (let line of lines) {
        const trimmedLine = line.trim();

        // 检测章节标题
        if (trimmedLine.startsWith('# 角色') || trimmedLine.includes('角色')) {
            if (currentSection && currentContent.length > 0) {
                sections[currentSection] = currentContent.join('\n').trim();
            }
            currentSection = 'role';
            currentContent = [line];
        } else if (trimmedLine.startsWith('## 技能') || trimmedLine.includes('技能')) {
            if (currentSection && currentContent.length > 0) {
                sections[currentSection] = currentContent.join('\n').trim();
            }
            currentSection = 'skills';
            currentContent = [line];
        } else if (trimmedLine.startsWith('## Action') || trimmedLine.includes('Action')) {
            if (currentSection && currentContent.length > 0) {
                sections[currentSection] = currentContent.join('\n').trim();
            }
            currentSection = 'action';
            currentContent = [line];
        } else if (trimmedLine.startsWith('## 限制') || trimmedLine.includes('限制')) {
            if (currentSection && currentContent.length > 0) {
                sections[currentSection] = currentContent.join('\n').trim();
            }
            currentSection = 'constraints';
            currentContent = [line];
        } else {
            currentContent.push(line);
        }
    }

    // 处理最后一个章节
    if (currentSection && currentContent.length > 0) {
        sections[currentSection] = currentContent.join('\n').trim();
    }

    // 如果没有检测到明确的章节，将整个内容放到角色部分
    if (!sections.role && !sections.skills && !sections.action && !sections.constraints) {
        sections.role = template;
    }

    // 填充到对应的Tab
    document.getElementById('agentRoleContent').value = sections.role;
    document.getElementById('agentSkillsContent').value = sections.skills;
    document.getElementById('agentActionContent').value = sections.action;
    document.getElementById('agentConstraintsContent').value = sections.constraints;
}

// 更新最终提示词预览
function updateAgentFinalPrompt() {
    const role = document.getElementById('agentRoleContent').value.trim();
    const skills = document.getElementById('agentSkillsContent').value.trim();
    const action = document.getElementById('agentActionContent').value.trim();
    const constraints = document.getElementById('agentConstraintsContent').value.trim();

    let finalPrompt = '';

    if (role) {
        finalPrompt += '# 角色\n' + role + '\n\n';
    }

    if (skills) {
        finalPrompt += '## 技能\n' + skills + '\n\n';
    }

    if (action) {
        finalPrompt += '## Action\n' + action + '\n\n';
    }

    if (constraints) {
        finalPrompt += '## 限制\n' + constraints;
    }

    document.getElementById('agentFinalPromptPreview').textContent = finalPrompt.trim();
}

// 复制最终提示词到剪贴板
function copyAgentFinalPrompt() {
    const finalPrompt = document.getElementById('agentFinalPromptPreview').textContent;

    if (!finalPrompt || finalPrompt.trim() === '') {
        showToast('没有可复制的内容', 'warning');
        return;
    }

    navigator.clipboard.writeText(finalPrompt).then(() => {
        showToast('提示词已复制到剪贴板', 'success');
    }).catch(err => {
        console.error('复制失败:', err);
        // 降级方案：使用传统的复制方法
        const textArea = document.createElement('textarea');
        textArea.value = finalPrompt;
        document.body.appendChild(textArea);
        textArea.select();
        try {
            document.execCommand('copy');
            showToast('提示词已复制到剪贴板', 'success');
        } catch (err) {
            showToast('复制失败，请手动复制', 'error');
        }
        document.body.removeChild(textArea);
    });
}

// 保存提示词模板
function saveAgentPromptTemplate() {
    // 获取最终提示词
    const finalPrompt = document.getElementById('agentFinalPromptPreview').textContent;

    if (!finalPrompt || finalPrompt.trim() === '') {
        showToast('请至少填写一个Tab的内容', 'warning');
        return;
    }

    // 更新目标元素的提示词模板
    if (currentPromptTargetId) {
        document.getElementById(currentPromptTargetId).value = finalPrompt;
    }

    // 关闭模态框
    bootstrap.Modal.getInstance(document.getElementById('agentPromptTemplateModal')).hide();

    showToast('提示词模板已保存', 'success');
}

// 加载默认提示词模板
function loadDefaultPromptTemplate() {
    // 基于您提供的航空货运专家提示词模板
    const defaultRole = `你是一位航空货运专家，精通航空主单、IATA-Cargo/ONE-Record标准`;

    const defaultSkills = `### 技能 1: 整理订单数据

识别主运单的信息，使用IATA-Cargo/ONE-Record字段的说明将其对应填入以下JSON数据中。KEY":"",VALUE格式如下":"",
{
    "waybillType": "",
    "waybillPrefix": "",
    "waybillNumber": "",
    "arrivalLocationCode": "",
    "airlineCode": "",
    "arrivalLocationName": "",
    "transportIdentifier": "",
    "departureLocationCode": "",
    "departureLocationName": "",
    // 航班日期转成yyyy-mm-dd格式
    "movementTimestamp": "",
    "insuredAmount": "",
    // 默认是CNY
    "insuredCurrencyUnit": "CNY",
    "originCurrency": "",
    // CHCC对应数据
    "carrierChargeCode": "",
    // 运费支付方式。根据WT/VAL对应的数据取前一位
    "weightValuationIndicator": "",
    // 其他费用支付方式。根据Other对应的数据取前一位
    "otherChargesIndicator": "",
    "declaredValueForCarriage": "",
    "declaredValueForCustoms": "",
    "shipper": {
        "country": "",
        "cityCode": "",
        "postalCode": "",
        "regionCode": "",
        "streetAddressLines": "",
        "textualValue": "",
        // 联系方式类型：多选（EMAIL_ADDRESS, FAX_NUMBER, PHONE_NUMBER, TELEX, WEBSITE）
        "contactDetailType": [],
        "partyRole": "SHP"
    },
    "consignee": {
        "country": "",
        "cityCode": "",
        "postalCode": "",
        "regionCode": "",
        "streetAddressLines": "",
        "textualValue": "",
        "contactDetailType": [],
        "partyRole": "CNE"
    },
    "notify": {
        "country": "",
        "cityCode": "",
        "postalCode": "",
        "regionCode": "",
        "streetAddressLines": "",
        "textualValue": "",
        "contactDetailType": [],
        "partyRole": "NTF"
    },
    "pieceCount": "",
    "grossWeight": "",
    // 默认KGM
    "grossWeightUnit": "KGM",
    "chargeableWeight": "",
    // 计费重单位（默认KGM）
    "chargeableWeightUnit": "KGM",
    // 费率率类（独立大写字母）
    "rateClassCode": "",
    "rateCharge": "",
    "Total": "",
    "commodityItemNumber": "",
    "goodsDescription": "",
    // 商品税号（多个时用数组）
    "hsCode": [],
    // 商品小件数合计
    "itemQuantity": "",
    "dimensions": {
        "length": "",
        "width": "",
        "height": "",
        "volume": "",
        // 体积单位（默认CMQ）
        "volumeUnit": "CMQ"
    },
    "otherChargeCode": "",
    "otherChargeAmount": "",
    // 默认是P
    "chargePaymentType": "P",
    "entitlement": "",
    "specialHandlingCodes": "",
    "textualHandlingInstructions": "",
    "accountingInformation": "",
    "carrierDeclarationDate": "",
    "carrierDeclarationPlace": "",
    "carrierDeclarationSignature": "",
    "consignorDeclarationSignature": ""
}

### 技能 2: 格式化地址信息
1. 将地址信息字符串全部转换为大写。
2. 使用正则表达式去除以下特殊字符：\_、－、'、"、!、@、:、：、/、#、&、＃、＆、，、。、(、)、（、）、+、%*、~、\`、;、·，将这些字符替换为空格。
3. 将中文逗号转换为英文逗号。
4. 将换行符转换为空格。
5. 不同格式用不同插件。
6 把多个连续空格合并为一个空格。
7. 按照以下规则解析地址信息各部分：
    - **公司名称**：精确查找字符串中是否包含"LTD"（截取其后3位）、"LIMITED"（截取其后7位）、"INC."（截取其后4位），若{#/InputSlot#}存在，按规则截取公司名称。若包含"BRANCH"且公司名称截取位置在"BRANCH"之前，再根据是否包含"COMPANY"进一步精准调整截取位置。
    - **电话**：利用先进的正则表达式技术查找包含"TEL"或"TE"的电话号码部分，准确提取并去除无关字符。
    - **传真**：运用高效的正则表达式查找包含"FAX"的传真号码部分，精准提取并去除无关字符。
    - **邮编**：采用精准的正则表达式查找4到6位数字的邮编部分，提取并去除空格。
    - **详细地址**：查找字符串中是否包含"STREET"（截取其后6位）、"DISTRICT"（截取其后8位）、"ROAD"（截取其后4位），按规则截取详细地址。
    - **城市**：利用智能的正则表达式查找4位及以上大写字母的城市名称部分。
    - **国家**：通过精确的正则表达式查找2位大写字母的国家代码部分。
8. Insurance如果查找内容为NVL，则转换为0，declaredValueForCustoms和declaredValueForCarriage如果内容带"."去除。
9. 查找SHIPPER对应SHP，CONSIGNEE对应CNE，Notify对应NFY，Agent对应AGT，从name字段到 contactDetailType按不同的partyRole列出来。循环里不包括otherIdentifierType，otherIdentifierValue，iataCargoAgentCode和iataCargoAgentLocationIdentifier。
10. 将解析后的各部分信息整理到数组中返回，数组顺序为：公司名称、详细地址、电话、邮编、传真、纳税人识别号。
11. contactDetailType联系方式，从EMAIL_ADDRESS, FAX_NUMBER, PHONE_NUMBER, TELEX, WEBSITE5个方式，如果同时存在多种都列出来，不需要循环其他信息，在一个数组中列出来。
12. 商品名称如果有换行的情况，请都列出来。
13. 在Other Charges区域内，编码加数字的内容都提取出来，如果有换行的情况，也都要提取出来，不要遗漏，otherChargeCode取编码前两位，根据数据库表othercharges表othercharges_code字段匹配，otherChargeAmount提取对应的数字，chargePaymentTyp默认P，entitlement取三字码最后一位，，根据不同的编码，从otherChargeCode到entitlement循环列出来。不要漏掉费用，金额合计与OC费用的金额是一致的。如果entitlement没有提取出来，默认是C。
14. 从Prepaid纵列向下取三个费用值，otherChargeCode分别对应"WT"，"OC"和"CT"，WT对应 Weight Charge下的金额，OC对应 Total Other Charges下面金额，CT对应 Total Prepaid下面金额，chargePaymentTyp默认P，"WT"与"CT"entitlement默认C，"OC"entitlement默认A。根据不同的杂费费用编码，从otherChargeCode到entitlement循环列出来。
15. arrivalLocationCode一共对应三个目的港，在文件中对应三个To，如果第三个To内容为空，第一个To是中转目的港，第二个To是最终目的港；如果第三个To内容不为空，第一个To为一程中转港，第二个To为二程中转港，第三个To为最终目的港；如果第二个To为空，则第一个To为最终目的港。第一个To在Airport of Departure的下面，第二个To的目的港在Requested上面，第三个目的港在 Flight/Date上面；airlineCode提取航司，By First Carrier对应第一个目的港的航司，后面两个By分别对应第二个目的港和第三个目的港的航司，请从arrivalLocationCode到airlineCode循环列出来。包括三个循环无论是否有数据请都列出来。
15.带乘号的为长宽高数据，如果有多组长宽高请从itemQuantity到height循环列出来，/后面为小件数数据，提取到itemQuantity。`;

    const defaultAction = `请使用 技能1 和技能2 对给定的航空主单内容进行格式化，并按制定的Json输出，不需要其他无关内容`;

    const defaultConstraints = `1. 严格按照JSON格式中规定的数据类型和长度要求进行整理。`;

    // 填充到对应的Tab
    document.getElementById('agentRoleContent').value = defaultRole;
    document.getElementById('agentSkillsContent').value = defaultSkills;
    document.getElementById('agentActionContent').value = defaultAction;
    document.getElementById('agentConstraintsContent').value = defaultConstraints;
}

// ==================== 新增功能函数 ====================

// 分类过滤功能
function filterByCategory(categoryId) {
    currentSearchParams.categoryId = categoryId;
    currentSearchParams.page = 1; // 重置到第一页
    loadAgents();
}

// 加载分类过滤下拉菜单
async function loadCategoryFilter() {
    try {
        const response = await apiCall('/api/v1/agent-categories');
        if (response.code === 200) {
            const categories = response.data || [];
            const dropdown = document.getElementById('categoryFilterDropdown');

            // 清空现有选项（保留"全部分类"）
            const allOption = dropdown.querySelector('li:first-child');
            dropdown.innerHTML = '';
            dropdown.appendChild(allOption);

            // 添加分类选项
            categories.forEach(category => {
                const li = document.createElement('li');
                li.innerHTML = `
                    <a class="dropdown-item" href="#" onclick="filterByCategory('${category.id}')">
                        <i class="bi bi-tag text-info me-2"></i>${category.categoryName}
                    </a>
                `;
                dropdown.appendChild(li);
            });
        }
    } catch (error) {
        console.error('Failed to load categories:', error);
    }
}

// 提示词质量检测
function checkPromptQuality(promptText) {
    if (!promptText || promptText.trim().length === 0) {
        return { score: 0, level: 'poor', text: '待评估' };
    }

    let score = 0;
    const text = promptText.trim();

    // 基础评分规则
    if (text.length > 50) score += 20;
    if (text.length > 200) score += 20;
    if (text.length > 500) score += 10;

    // 结构化评分
    if (text.includes('你是') || text.includes('角色')) score += 15;
    if (text.includes('技能') || text.includes('能力')) score += 15;
    if (text.includes('要求') || text.includes('限制')) score += 10;
    if (text.includes('JSON') || text.includes('格式')) score += 10;

    // 确定质量等级
    let level, levelText;
    if (score >= 80) {
        level = 'excellent';
        levelText = '优秀';
    } else if (score >= 60) {
        level = 'good';
        levelText = '良好';
    } else if (score >= 40) {
        level = 'fair';
        levelText = '一般';
    } else {
        level = 'poor';
        levelText = '需改进';
    }

    return { score, level, text: levelText };
}

// 更新提示词质量指示器
function updatePromptQuality(textareaId) {
    const textarea = document.getElementById(textareaId);
    const text = textarea.value;
    const quality = checkPromptQuality(text);

    // 更新字符计数
    const charCount = document.getElementById('promptCharCount');
    if (charCount) {
        charCount.textContent = `${text.length}/2000`;
        charCount.className = `badge ${text.length > 1800 ? 'bg-warning' : 'bg-secondary'}`;
    }

    // 更新质量指示器
    const qualityBar = document.getElementById('promptQualityBar');
    const qualityText = document.getElementById('promptQualityText');

    if (qualityBar && qualityText) {
        qualityBar.style.width = `${quality.score}%`;
        qualityText.textContent = quality.text;

        // 设置颜色
        qualityBar.className = 'progress-bar';
        switch (quality.level) {
            case 'excellent':
                qualityBar.classList.add('bg-success');
                break;
            case 'good':
                qualityBar.classList.add('bg-info');
                break;
            case 'fair':
                qualityBar.classList.add('bg-warning');
                break;
            default:
                qualityBar.classList.add('bg-danger');
        }
    }
}

// 批量操作功能
function bulkDelete() {
    // 获取选中的Agent
    const selectedAgents = getSelectedAgents();
    if (selectedAgents.length === 0) {
        showToast('请先选择要删除的Agent', 'warning');
        return;
    }

    showConfirmModal(
        '批量删除确认',
        `确定要删除选中的 ${selectedAgents.length} 个Agent吗？此操作不可撤销。`,
        'danger',
        () => {
            // 执行批量删除
            bulkDeleteAgents(selectedAgents);
        }
    );
}

function bulkExport() {
    const selectedAgents = getSelectedAgents();
    if (selectedAgents.length === 0) {
        showToast('请先选择要导出的Agent', 'warning');
        return;
    }

    // 执行批量导出
    exportAgents(selectedAgents);
}

function getSelectedAgents() {
    const checkboxes = document.querySelectorAll('.agent-checkbox:checked');
    return Array.from(checkboxes).map(cb => cb.value);
}

// 更新统计数据
function updateStats(agents) {
    const stats = {
        total: agents.length,
        published: agents.filter(a => a.status === 3).length,
        testing: agents.filter(a => a.status === 2).length,
        draft: agents.filter(a => a.status === 1).length
    };

    // 检查统计元素是否存在，如果不存在则跳过更新
    const totalElement = document.getElementById('totalAgents');
    const publishedElement = document.getElementById('publishedAgents');
    const testingElement = document.getElementById('testingAgents');
    const draftElement = document.getElementById('draftAgents');

    if (totalElement) totalElement.textContent = stats.total;
    if (publishedElement) publishedElement.textContent = stats.published;
    if (testingElement) testingElement.textContent = stats.testing;
    if (draftElement) draftElement.textContent = stats.draft;
}

// 加载分类过滤选项
async function loadCategoryFilterOptions() {
    try {
        const response = await apiCall('/api/v1/agent-categories');
        if (response.code === 200) {
            const categories = response.data || [];
            const select = document.getElementById('categoryFilter');

            // 检查元素是否存在
            if (!select) {
                console.warn('categoryFilter element not found');
                return;
            }

            // 清空现有选项（保留"全部分类"）
            select.innerHTML = '<option value="">全部分类</option>';

            // 添加分类选项
            categories.forEach(category => {
                const option = document.createElement('option');
                option.value = category.id;
                option.textContent = category.categoryName;
                select.appendChild(option);
            });
        }
    } catch (error) {
        console.error('Failed to load categories:', error);
    }
}

// 分类过滤
function filterByCategory() {
    const categorySelect = document.getElementById('categoryFilter');
    if (!categorySelect) {
        console.warn('categoryFilter element not found');
        return;
    }

    const categoryId = categorySelect.value;
    currentSearchParams.categoryId = categoryId;
    currentSearchParams.page = 1;
    loadAgents();
}

// 状态过滤
function filterAgents() {
    const statusSelect = document.getElementById('statusFilter');
    if (!statusSelect) {
        console.warn('statusFilter element not found');
        return;
    }

    const status = statusSelect.value;
    currentSearchParams.status = status;
    currentSearchParams.page = 1;
    loadAgents();
}

// 页面初始化时的额外设置
function initializeAgentsPageEnhancements() {
    // 加载分类过滤选项
    loadCategoryFilterOptions();

    // 为搜索框添加回车键支持
    const searchInput = document.getElementById('agentSearchInput');
    if (searchInput) {
        const searchHandler = (e) => {
            if (e.key === 'Enter') {
                searchAgents();
            }
        };

        searchInput.addEventListener('keypress', searchHandler);

        // 将事件监听器添加到全局管理
        if (!window.pageEventListeners) {
            window.pageEventListeners = [];
        }
        window.pageEventListeners.push({
            element: searchInput,
            event: 'keypress',
            handler: searchHandler
        });
    }
}
