// 智能表单配置管理器
class SmartFormConfigManager {
    constructor() {
        this.currentStep = 1;
        this.totalSteps = 4;
        this.selectedTemplate = null;
        this.configType = null; // 'single' or 'multi'
        this.businessTemplates = [];
        this.currentConfig = {
            name: '',
            description: '',
            templateId: null,
            type: 'single',
            steps: []
        };
        this.currentStepIndex = 0;
        this.bindingMode = false;
        this.actionMode = false;
        this.selectedField = null;
        this.currentViewMode = 'card'; // 'card' or 'table'
        this.currentSearchParams = {};
        this.init();
    }

    async init() {
        console.log('🚀 开始初始化SmartFormConfigManager...');
        try {
            // 先清理之前的状态
            this.cleanup();

            // 检查DOM是否准备好
            if (document.readyState === 'loading') {
                console.log('⏳ 等待DOM加载完成...');
                await new Promise(resolve => {
                    document.addEventListener('DOMContentLoaded', resolve);
                });
            }

            console.log('📋 开始加载业务模板...');
            await this.loadBusinessTemplates();

            console.log('📋 开始加载配置列表...');
            await this.loadConfigList();

            this.setupEventListeners();
            this.setupMessageListener();
        } catch (error) {
            console.error('❌ SmartFormConfigManager初始化失败:', error);
            // 即使初始化失败，也要设置基本的事件监听器
            try {
                this.setupEventListeners();
                this.setupMessageListener();
            } catch (e) {
                console.error('❌ 设置事件监听器也失败:', e);
            }
        }
    }

    // 清理方法 - 避免页面切换时的资源冲突
    cleanup() {
        console.log('🧹 SmartFormConfigManager清理开始...');

        // 清理定时器
        if (this.refreshTimer) {
            clearInterval(this.refreshTimer);
            this.refreshTimer = null;
        }

        // 清理可能存在的事件监听器
        try {
            const elements = [
                'configSearchInput',
                'statusFilter',
                'typeFilter'
            ];

            elements.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    // 移除可能的事件监听器
                    element.onchange = null;
                    element.oninput = null;
                    element.onclick = null;
                }
            });
        } catch (error) {
            console.warn('⚠️ 清理事件监听器时出错:', error);
        }

        // 重置状态
        this.currentStep = 1;
        this.selectedTemplate = null;
        this.configType = null;
        this.currentConfig = {
            name: '',
            description: '',
            templateId: null,
            type: 'single',
            steps: []
        };
        this.currentStepIndex = 0;
        this.bindingMode = false;
        this.actionMode = false;
        this.selectedField = null;


    }

    // 设置消息监听器，接收来自Chrome插件的HTML数据
    setupMessageListener() {
        window.addEventListener('message', (event) => {
            if (event.data.type === 'HTML_EXTRACTED') {
                this.handleExtractedHTML(event.data.data);
            }
        });
    }

    // 处理提取的HTML数据
    handleExtractedHTML(data) {
        console.log('收到提取的HTML数据:', data);

        // 显示提示
        this.showAlert('收到页面HTML数据，正在处理...', 'info');

        // 如果当前在配置向导中，自动填充HTML数据
        if (this.currentStep >= 3) {
            this.fillHTMLData(data);
        } else {
            // 存储数据，等待用户进入配置步骤
            this.extractedHTMLData = data;
            this.showAlert('HTML数据已保存，请继续配置流程', 'success');
        }
    }

    // 填充HTML数据到配置中
    fillHTMLData(data) {
        try {
            // 填充URL模式
            const urlPatternInput = document.getElementById('urlPattern');
            if (urlPatternInput && data.url) {
                // 提取域名和路径作为URL模式
                const url = new URL(data.url);
                const urlPattern = url.hostname + url.pathname;
                urlPatternInput.value = urlPattern;
            }

            // 填充页面HTML
            const pageHtmlTextarea = document.getElementById('pageHtml');
            if (pageHtmlTextarea && data.html) {
                pageHtmlTextarea.value = data.html;
            }

            // 更新页面标题显示
            const pageTitleSpan = document.getElementById('pageTitle');
            if (pageTitleSpan && data.title) {
                pageTitleSpan.textContent = data.title;
            }

            this.showAlert('HTML数据已自动填充到配置中', 'success');
        } catch (error) {
            console.error('填充HTML数据失败:', error);
            this.showAlert('填充HTML数据失败: ' + error.message, 'danger');
        }
    }

    // 加载业务模板列表
    async loadBusinessTemplates() {
        console.log('📋 开始加载业务模板...');
        console.log('📋 authToken:', typeof authToken !== 'undefined' ? 'exists' : 'undefined');
        console.log('📋 apiCall function:', typeof apiCall);

        try {
            console.log('📋 调用API: /api/v1/business-templates');
            const result = await apiCall('/api/v1/business-templates');
            console.log('📋 业务模板API结果:', result);

            if (result.code === 200) {
                this.businessTemplates = result.data || [];
                console.log('📋 业务模板数量:', this.businessTemplates.length);
            } else {
                console.warn('📋 业务模板API返回非200状态:', result);
            }
        } catch (error) {
            console.error('❌ 加载业务模板失败:', error);
            this.showAlert('加载业务模板失败', 'danger');
        }
    }

    // 加载配置列表
    async loadConfigList() {
        console.log('📋 开始加载配置列表...');
        console.log('📋 检查API函数:', typeof apiCall);
        console.log('📋 检查authToken:', typeof authToken !== 'undefined' ? 'exists' : 'undefined');

        try {
            // 只加载主绑定配置（避免重复显示子步骤）
            console.log('📋 调用API: /api/v1/page-bindings');
            const result = await apiCall('/api/v1/page-bindings');
            console.log('📋 API响应:', result);

            this.allConfigs = [];

            if (result.code === 200 && result.data) {
                // 过滤出主绑定（parent_binding_id为null或0的记录）
                const mainBindings = result.data.filter(config =>
                    !config.parentBindingId || config.parentBindingId === 0
                );

                console.log('📋 过滤后的主绑定数量:', mainBindings.length);

                mainBindings.forEach(config => {
                    // 计算步骤数量
                    let stepCount = 1; // 至少有主步骤
                    if (config.isMultiStep === 1 && config.subSteps && Array.isArray(config.subSteps)) {
                        stepCount = config.subSteps.length + 1; // 主步骤 + 子步骤
                    }

                    console.log(`📊 配置: ${config.bindingName}, 多步骤: ${config.isMultiStep}, 子步骤数: ${config.subSteps?.length || 0}, 总步骤: ${stepCount}`);

                    this.allConfigs.push({
                        ...config,
                        type: config.isMultiStep === 1 ? 'multi' : 'single',
                        stepCount: stepCount
                    });
                });
            }

            console.log('📋 总配置数量:', this.allConfigs.length);
            this.renderConfigList();
            this.updateStats();

        } catch (error) {
            console.error('❌ 加载配置列表失败:', error);
            this.showAlert('加载配置列表失败', 'danger');
        }
    }

    // 渲染配置列表
    renderConfigList(configs = null) {
        console.log('🎨 开始渲染配置列表...');

        const configsToRender = configs || this.allConfigs || [];
        console.log('📊 要渲染的配置数量:', configsToRender.length);

        // 更新统计信息
        this.updateStats(configsToRender);

        if (configsToRender.length === 0) {
            this.showEmptyState();
            return;
        }

        this.hideEmptyState();

        // 根据当前视图模式渲染
        if (this.currentViewMode === 'table') {
            this.renderConfigTable(configsToRender);
        } else {
            this.renderConfigCards(configsToRender);
        }
    }

    // 渲染配置卡片
    renderConfigCards(configs) {
        const container = document.getElementById('configCardsContainer');
        if (!container) {
            console.error('❌ 找不到configCardsContainer元素');
            return;
        }

        container.innerHTML = configs.map(config => `
            <div class="col-lg-6 col-xl-4">
                <div class="config-card" data-config-id="${config.id}" data-config-type="${config.type}">
                    <div class="config-card-header">
                        <div class="config-type-badge ${config.type === 'single' ? 'config-type-single' : 'config-type-multi'}">
                            ${config.type === 'single' ? '单页面' : '多步骤'}
                        </div>
                        <div class="d-flex align-items-center">
                            <span class="status-indicator ${config.status === 1 ? 'status-active' : 'status-inactive'}"></span>
                            <small class="text-muted">${config.status === 1 ? '已启用' : '已禁用'}</small>
                        </div>
                    </div>

                    <div class="config-card-body">
                        <h6 class="config-card-title">${config.bindingName || '未命名配置'}</h6>
                        <p class="config-description">${config.description || '暂无描述'}</p>

                        <div class="config-card-meta">
                            <span><i class="bi bi-file-earmark-text me-1"></i>${config.templateName || '未知模板'}</span>
                            <span><i class="bi bi-layers me-1"></i>${config.stepCount} 步骤</span>
                        </div>
                    </div>

                    <div class="config-card-footer">
                        <div class="d-flex justify-content-between align-items-center">
                            <small class="text-muted">
                                <i class="bi bi-clock me-1"></i>
                                ${config.createdTime ? new Date(config.createdTime).toLocaleDateString() : '未知时间'}
                            </small>
                            <div class="d-flex gap-1">
                                <button class="btn btn-sm btn-outline-secondary" onclick="viewConfig(${config.id}, '${config.type}')" title="查看">
                                    <i class="bi bi-eye"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-primary" onclick="editConfig(${config.id}, '${config.type}')" title="编辑">
                                    <i class="bi bi-pencil"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-danger" onclick="deleteConfig(${config.id}, '${config.type}')" title="删除">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');
    }

    // 渲染配置表格
    renderConfigTable(configs) {
        const tableBody = document.getElementById('configTableBody');
        if (!tableBody) {
            console.error('❌ 找不到configTableBody元素');
            return;
        }

        tableBody.innerHTML = configs.map(config => `
            <tr>
                <td>
                    <div class="config-info">
                        <div class="config-avatar">
                            ${(config.bindingName || '未命名').charAt(0).toUpperCase()}
                        </div>
                        <div class="flex-grow-1">
                            <div>
                                <a href="#" class="config-name" onclick="viewConfig(${config.id}, '${config.type}')">${config.bindingName || '未命名配置'}</a>
                            </div>
                            <div class="config-description" title="${config.description || '暂无描述'}">
                                ${config.description || '暂无描述'}
                            </div>
                        </div>
                    </div>
                </td>
                <td>
                    <span class="badge ${config.type === 'single' ? 'bg-primary' : 'config-type-multi-badge'}">
                        ${config.type === 'single' ? '单页面' : '多步骤'}
                    </span>
                </td>
                <td>
                    <span class="badge ${config.status === 1 ? 'bg-success' : 'bg-secondary'}">
                        ${config.status === 1 ? '已启用' : '已禁用'}
                    </span>
                </td>
                <td>${config.templateName || '未知模板'}</td>
                <td>${config.createdTime ? new Date(config.createdTime).toLocaleString() : '未知时间'}</td>
                <td>
                    <div class="d-flex gap-1">
                        <button class="btn btn-sm btn-outline-secondary" onclick="viewConfig(${config.id}, '${config.type}')" title="查看">
                            <i class="bi bi-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-primary" onclick="editConfig(${config.id}, '${config.type}')" title="编辑">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="deleteConfig(${config.id}, '${config.type}')" title="删除">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }

    // 更新统计信息
    updateStats(configs = null) {
        const configsToCount = configs || this.allConfigs || [];
        const total = configsToCount.length;

        const totalElement = document.getElementById('totalConfigs');
        if (totalElement) {
            totalElement.textContent = total;
        }
    }

    // 显示空状态
    showEmptyState() {
        const emptyState = document.getElementById('emptyState');
        const cardsView = document.getElementById('configCardsView');
        const tableView = document.getElementById('configTableView');

        if (emptyState) emptyState.style.display = 'block';
        if (cardsView) cardsView.style.display = 'none';
        if (tableView) tableView.style.display = 'none';
    }

    // 隐藏空状态
    hideEmptyState() {
        const emptyState = document.getElementById('emptyState');
        const cardsView = document.getElementById('configCardsView');
        const tableView = document.getElementById('configTableView');

        if (emptyState) emptyState.style.display = 'none';

        if (this.currentViewMode === 'table') {
            if (cardsView) cardsView.style.display = 'none';
            if (tableView) tableView.style.display = 'block';
        } else {
            if (cardsView) cardsView.style.display = 'block';
            if (tableView) tableView.style.display = 'none';
        }
    }

    // 设置事件监听器
    setupEventListeners() {
        // 搜索功能
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.filterConfigs();
            });
        }

        // 筛选按钮
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
                e.target.classList.add('active');
                this.filterConfigs();
            });
        });
    }

    // 筛选配置
    filterConfigs() {
        const searchTerm = document.getElementById('searchInput').value.toLowerCase();
        const activeFilter = document.querySelector('.filter-btn.active').dataset.filter;

        let filteredConfigs = this.allConfigs;

        // 应用搜索过滤
        if (searchTerm) {
            filteredConfigs = filteredConfigs.filter(config =>
                (config.bindingName || '').toLowerCase().includes(searchTerm) ||
                (config.templateName || '').toLowerCase().includes(searchTerm) ||
                (config.description || '').toLowerCase().includes(searchTerm)
            );
        }

        // 应用类型过滤
        if (activeFilter !== 'all') {
            if (activeFilter === 'active') {
                filteredConfigs = filteredConfigs.filter(config => config.status === 1);
            } else if (activeFilter === 'inactive') {
                filteredConfigs = filteredConfigs.filter(config => config.status === 0);
            } else {
                filteredConfigs = filteredConfigs.filter(config => config.type === activeFilter);
            }
        }

        this.renderConfigList(filteredConfigs);
    }

    // 渲染模板选择
    renderTemplateSelection() {
        console.log('📋 开始渲染模板选择...');
        console.log('📋 业务模板数量:', this.businessTemplates?.length || 0);

        const container = document.getElementById('templateSelection');
        console.log('📋 模板容器元素:', !!container);
        if (!container) {
            console.error('❌ 找不到templateSelection容器');
            return;
        }

        if (this.businessTemplates.length === 0) {
            console.log('📋 没有业务模板，显示提示信息');
        } else {
            console.log('📋 开始渲染', this.businessTemplates.length, '个模板');
        }

        if (this.businessTemplates.length === 0) {
            container.innerHTML = `
                <div class="col-12">
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>
                        暂无可用的业务模板，请先创建业务模板。
                        <a href="#" onclick="window.open('/admin.html#templates', '_blank')" class="alert-link ms-2">
                            <i class="bi bi-plus-circle"></i> 创建模板
                        </a>
                    </div>
                </div>
            `;

            // 如果没有模板，创建一个测试模板用于演示
            console.log('📋 创建测试模板用于演示...');
            this.businessTemplates = [
                {
                    id: 1,
                    templateName: '测试模板',
                    description: '用于测试的业务模板',
                    category: 'form',
                    templateCode: 'TEST_TEMPLATE'
                }
            ];
            // 重新渲染
            setTimeout(() => this.renderTemplateSelection(), 100);
            return;
        }

        // 按分类分组模板
        const templatesByCategory = this.businessTemplates.reduce((acc, template) => {
            const category = template.category || 'other';
            if (!acc[category]) acc[category] = [];
            acc[category].push(template);
            return acc;
        }, {});

        container.innerHTML = this.businessTemplates.map((template, index) => {
            const categoryColors = {
                'form': { bg: '#e3f2fd', icon: 'bi-file-earmark-text', color: '#1976d2' },
                'ecommerce': { bg: '#f3e5f5', icon: 'bi-cart', color: '#7b1fa2' },
                'registration': { bg: '#e8f5e8', icon: 'bi-person-plus', color: '#388e3c' },
                'survey': { bg: '#fff3e0', icon: 'bi-clipboard-data', color: '#f57c00' },
                'other': { bg: '#f5f5f5', icon: 'bi-file-earmark', color: '#616161' }
            };

            const category = template.category || 'other';
            const categoryStyle = categoryColors[category] || categoryColors['other'];

            return `
                <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6">
                    <div class="template-card"
                         onclick="selectTemplate(${template.id})"
                         data-template-id="${template.id}"
                         data-category="${category}">
                        <div class="template-icon" style="background: ${categoryStyle.bg}; color: ${categoryStyle.color};">
                            <i class="${categoryStyle.icon}"></i>
                        </div>
                        <div class="template-title">${template.templateName}</div>
                        <div class="template-desc">${template.description || '暂无描述'}</div>
                        <div class="template-meta">
                            <span class="badge bg-secondary">${template.templateCode}</span>
                        </div>
                    </div>
                </div>
            `;
        }).join('');
    }

    // 选择模板
    selectTemplate(templateId) {
        // 移除之前的选中状态
        document.querySelectorAll('.template-card').forEach(card => {
            card.classList.remove('selected');
        });

        // 添加选中状态
        const selectedCard = document.querySelector(`[data-template-id="${templateId}"]`);
        if (selectedCard) {
            selectedCard.classList.add('selected');
        }

        this.selectedTemplate = this.businessTemplates.find(t => t.id === templateId);
        this.currentConfig.templateId = templateId;

        // 显示已选择模板信息
        const selectedTemplateInfo = document.getElementById('selectedTemplateInfo');
        const selectedTemplateName = document.getElementById('selectedTemplateName');
        const selectedTemplateDesc = document.getElementById('selectedTemplateDesc');

        if (selectedTemplateInfo && selectedTemplateName && selectedTemplateDesc) {
            selectedTemplateName.textContent = this.selectedTemplate.templateName;
            selectedTemplateDesc.textContent = this.selectedTemplate.description || '暂无描述';
            selectedTemplateInfo.style.display = 'block';
        }

        // 启用下一步按钮
        const nextBtn = document.getElementById('step1NextBtn');
        if (nextBtn) {
            nextBtn.disabled = false;
        }
    }

    // 选择配置类型
    selectConfigType(type) {
        console.log('📋 selectConfigType被调用，type:', type);

        // 移除之前的选中状态
        document.querySelectorAll('.config-type-card').forEach(card => {
            card.classList.remove('selected');
        });

        // 添加选中状态
        const selectedCard = document.getElementById(type + 'TypeCard');
        if (selectedCard) {
            selectedCard.classList.add('selected');
        }

        this.configType = type;
        this.currentConfig.type = type;
        // 启用下一步按钮
        const nextBtn = document.getElementById('step2NextBtn');
        if (nextBtn) {
            nextBtn.disabled = false;
        }
    }

    // 下一步
    nextStep() {
        if (this.currentStep < this.totalSteps) {
            // 隐藏当前步骤
            document.getElementById(`step${this.currentStep}`).style.display = 'none';
            
            this.currentStep++;
            
            // 显示下一步骤
            document.getElementById(`step${this.currentStep}`).style.display = 'block';
            
            // 更新进度
            this.updateWizardProgress();
            
            // 如果进入步骤3，初始化可视化配置
            if (this.currentStep === 3) {
                this.initVisualConfig();
            }
            
            // 如果进入步骤4，生成配置摘要
            if (this.currentStep === 4) {
                this.generateConfigSummary();
            }
        }
    }

    // 上一步
    prevStep() {
        if (this.currentStep > 1) {
            // 隐藏当前步骤
            document.getElementById(`step${this.currentStep}`).style.display = 'none';
            
            this.currentStep--;
            
            // 显示上一步骤
            document.getElementById(`step${this.currentStep}`).style.display = 'block';
            
            // 更新进度
            this.updateWizardProgress();
        }
    }

    // 更新向导进度
    updateWizardProgress() {
        for (let i = 1; i <= this.totalSteps; i++) {
            const stepNumber = document.getElementById(`step${i}Number`);
            if (stepNumber) {
                stepNumber.classList.remove('active', 'completed');
                if (i < this.currentStep) {
                    stepNumber.classList.add('completed');
                    stepNumber.innerHTML = '<i class="bi bi-check"></i>';
                } else if (i === this.currentStep) {
                    stepNumber.classList.add('active');
                    stepNumber.textContent = i;
                } else {
                    stepNumber.textContent = i;
                }
            }
        }
    }

    // 初始化可视化配置
    initVisualConfig() {
        if (this.configType === 'single') {
            this.initSingleStepConfig();
        } else {
            this.initMultiStepConfig();
        }

        // 确保URL输入框有值
        const urlInput = document.getElementById('previewUrlInput');
        if (urlInput && !urlInput.value) {
            urlInput.placeholder = '请输入目标页面URL，例如：https://example.com/form';
        }
    }

    // 初始化单步骤配置
    initSingleStepConfig() {
        const configPanel = document.getElementById('configPanelContent');
        if (!configPanel) return;

        configPanel.innerHTML = `
            <div class="mb-4">
                <h6 class="mb-3">
                    <i class="bi bi-file-earmark me-2"></i>单页面配置
                </h6>
                <p class="text-muted small">配置单个页面的字段绑定</p>
            </div>

            <hr>

            <h6 class="mb-3">
                字段绑定状态
                <span class="badge bg-secondary" id="boundFieldsCount">0</span>
            </h6>

            <div class="field-list" id="fieldList">
                ${this.renderFieldList()}
            </div>

            <div class="mt-4">
                <div class="alert alert-success small">
                    <i class="bi bi-magic me-2"></i>
                    <strong>新的绑定方式：</strong><br>
                    1. 在右侧输入URL并加载页面<br>
                    2. 点击"开始绑定"按钮<br>
                    3. 在页面中点击输入框<br>
                    4. 在弹出的窗口中选择要绑定的字段
                </div>
            </div>
        `;

        // 更新进度
        const progressText = document.getElementById('progressText');
        const configProgress = document.getElementById('configProgress');
        if (progressText) progressText.textContent = '单页面配置';
        if (configProgress) configProgress.style.width = '100%';
    }

    // 初始化多步骤配置
    initMultiStepConfig() {
        // 初始化第一个步骤
        this.currentConfig.steps = [{
            stepName: '步骤1',
            urlPattern: '',
            fieldMappings: [],
            nextAction: null
        }];
        
        this.currentStepIndex = 0;
        this.renderMultiStepConfig();
    }

    // 渲染多步骤配置
    renderMultiStepConfig() {
        const configPanel = document.getElementById('configPanelContent');
        const currentStep = this.currentConfig.steps[this.currentStepIndex];

        configPanel.innerHTML = `
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h6 class="mb-0">步骤 ${this.currentStepIndex + 1}</h6>
                <div class="btn-group btn-group-sm">
                    <button type="button" class="btn btn-outline-secondary" onclick="smartFormManager.prevConfigStep()" ${this.currentStepIndex === 0 ? 'disabled' : ''}>
                        <i class="bi bi-arrow-left"></i>
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="smartFormManager.nextConfigStep()">
                        <i class="bi bi-arrow-right"></i>
                    </button>
                </div>
            </div>

            <div class="mb-3">
                <label class="form-label">步骤名称</label>
                <input type="text" class="form-control" value="${currentStep.stepName}" onchange="smartFormManager.updateStepName(this.value)">
            </div>

            <!-- 目标URL和加载页面按钮已移除，现在只使用HTML粘贴方式 -->

            <hr>

            <h6 class="mb-3">字段绑定 <span class="badge bg-secondary" id="stepBoundFieldsCount">${currentStep.fieldMappings.length}</span></h6>
            <div class="field-list" id="stepFieldList">
                ${this.renderFieldList()}
            </div>

            <!-- 绑定字段按钮已移除，现在直接点击输入框进行绑定 -->

            <hr>

            <h6 class="mb-3">下一步动作</h6>
            <div class="mb-3">
                <label class="form-label">动作类型</label>
                <select class="form-select" id="actionType" onchange="smartFormManager.updateActionType(this.value)">
                    <option value="">请选择</option>
                    <option value="click">点击按钮</option>
                    <option value="submit">提交表单</option>
                    <option value="navigate">跳转URL</option>
                </select>
            </div>

            <div class="mb-3">
                <button type="button" class="btn btn-outline-warning btn-sm w-100" onclick="smartFormManager.selectActionTarget()" id="actionTargetBtn">
                    <i class="bi bi-cursor"></i> 选择目标元素
                </button>
            </div>

            <div class="mt-3">
                <button type="button" class="btn btn-success btn-sm w-100" onclick="smartFormManager.addNewStep()">
                    <i class="bi bi-plus"></i> 添加下一步骤
                </button>
            </div>
        `;

        // 更新进度
        const progress = ((this.currentStepIndex + 1) / this.currentConfig.steps.length) * 100;
        const progressText = document.getElementById('progressText');
        const configProgress = document.getElementById('configProgress');
        if (progressText) progressText.textContent = `步骤 ${this.currentStepIndex + 1} / ${this.currentConfig.steps.length}`;
        if (configProgress) configProgress.style.width = `${progress}%`;
    }

    // 渲染字段列表
    renderFieldList() {
        if (!this.selectedTemplate || !this.selectedTemplate.jsonTemplate) {
            return '<div class="text-muted text-center py-3">请先选择业务模板</div>';
        }

        try {
            const jsonObj = JSON.parse(this.selectedTemplate.jsonTemplate);
            const fields = this.parseJsonFields(jsonObj);
            
            return fields.map(field => {
                // 检查字段是否已绑定
                const isBound = this.isFieldBound(field.key);

                return `
                    <div class="field-item ${isBound ? 'bound' : ''}" data-field="${field.key}">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <div class="fw-bold">${field.key}</div>
                                <small class="text-muted">${field.description}</small>
                            </div>
                            <div class="field-status">
                                ${isBound ? '<i class="bi bi-check-circle text-success"></i>' : ''}
                            </div>
                        </div>
                    </div>
                `;
            }).join('');
        } catch (error) {
            return '<div class="text-danger text-center py-3">模板格式错误</div>';
        }
    }

    // 解析JSON字段
    parseJsonFields(obj, prefix = '', result = []) {
        for (const [key, value] of Object.entries(obj)) {
            const currentPath = prefix ? `${prefix}.${key}` : key;
            
            if (Array.isArray(value)) {
                if (value.length > 0 && typeof value[0] === 'object') {
                    result.push({
                        key: currentPath,
                        description: `${key} (对象数组)`,
                        type: 'array'
                    });
                    this.parseJsonFields(value[0], `${currentPath}[0]`, result);
                } else {
                    result.push({
                        key: currentPath,
                        description: `${key} (数组)`,
                        type: 'simple_array'
                    });
                }
            } else if (typeof value === 'object' && value !== null) {
                result.push({
                    key: currentPath,
                    description: `${key} (对象)`,
                    type: 'object'
                });
                this.parseJsonFields(value, currentPath, result);
            } else {
                result.push({
                    key: currentPath,
                    description: typeof value === 'string' ? value : key,
                    type: 'field'
                });
            }
        }
        return result;
    }

    // 检查字段是否已绑定
    isFieldBound(fieldKey) {
        if (this.configType === 'single') {
            return this.currentConfig.fieldMappings &&
                   this.currentConfig.fieldMappings.some(mapping => mapping.fieldKey === fieldKey);
        } else {
            const currentStep = this.currentConfig.steps[this.currentStepIndex];
            return currentStep.fieldMappings.some(mapping => mapping.fieldKey === fieldKey);
        }
    }

    // 选择字段（保留兼容性，但不再使用）
    selectField(fieldKey) {
        // 移除之前的选中状态
        document.querySelectorAll('.field-item').forEach(item => {
            item.classList.remove('selected');
        });

        // 添加选中状态
        const selectedItem = document.querySelector(`[data-field="${fieldKey}"]`);
        if (selectedItem) {
            selectedItem.classList.add('selected');
        }

        this.selectedField = fieldKey;
    }

    // 切换绑定模式
    async toggleBindingMode() {
        if (this.bindingMode) {
            this.exitBindingMode();
        } else {
            await this.enterBindingMode();
        }
    }

    // 进入绑定模式
    async enterBindingMode() {
        console.log('🎯 进入绑定模式...');
        this.bindingMode = true;

        // 更新UI
        const bindingMode = document.getElementById('bindingMode');
        const bindingToggleBtn = document.getElementById('bindingToggleBtn');
        const previewContent = document.getElementById('previewContent');

        if (bindingMode) {
            bindingMode.classList.add('active');
            bindingMode.classList.remove('minimized');
        }

        // 添加预览区域的绑定样式
        if (previewContent) {
            previewContent.classList.add('binding-active');
        }

        if (bindingToggleBtn) {
            bindingToggleBtn.innerHTML = '<i class="bi bi-x"></i> 退出绑定';
            bindingToggleBtn.classList.remove('btn-outline-success');
            bindingToggleBtn.classList.add('btn-outline-danger');
        }

        // 检查是否有可用的iframe
        console.log('🔍 检查iframe可用性:');
        console.log('  - this.currentIframe:', !!this.currentIframe);
        console.log('  - this.currentIframeDoc:', !!this.currentIframeDoc);

        // 尝试从DOM中查找iframe
        const existingIframe = previewContent ? previewContent.querySelector('iframe.preview-iframe') : null;
        console.log('  - DOM中的iframe:', !!existingIframe);

        if (existingIframe && !this.currentIframe) {
            console.log('🔧 发现DOM中有iframe但未设置引用，尝试修复...');
            try {
                const iframeDoc = existingIframe.contentDocument || existingIframe.contentWindow.document;
                console.log('🔍 iframe文档检查:', {
                    contentDocument: !!existingIframe.contentDocument,
                    contentWindow: !!existingIframe.contentWindow,
                    document: !!(existingIframe.contentWindow && existingIframe.contentWindow.document),
                    readyState: iframeDoc ? iframeDoc.readyState : 'null',
                    body: !!(iframeDoc && iframeDoc.body),
                    src: existingIframe.src || 'data/blob URL'
                });

                if (iframeDoc && iframeDoc.body) {
                    this.currentIframe = existingIframe;
                    this.currentIframeDoc = iframeDoc;
                    console.log('✅ 成功修复iframe引用');
                } else if (iframeDoc && iframeDoc.readyState !== 'complete') {
                    console.log('⏳ iframe还在加载中，等待加载完成...');
                    // 等待iframe加载完成
                    await this.waitForIframeLoad(existingIframe);
                } else {
                    console.warn('⚠️ iframe文档不可访问或为空');
                }
            } catch (error) {
                console.warn('⚠️ 无法访问iframe文档:', error);
                console.warn('⚠️ 这可能是跨域问题或iframe内容损坏');
            }
        }

        // 最终检查iframe可用性
        if (!this.currentIframe || !this.currentIframeDoc) {
            console.warn('⚠️ 没有可用的iframe，无法启动绑定模式');

            // 提供更详细的错误信息
            let errorMessage = '请先加载页面内容后再开始绑定';
            if (existingIframe) {
                try {
                    const iframeDoc = existingIframe.contentDocument || existingIframe.contentWindow.document;
                    if (!iframeDoc) {
                        errorMessage = '无法访问页面内容，可能存在跨域限制';
                    } else if (!iframeDoc.body) {
                        errorMessage = '页面内容为空或还在加载中，请稍后再试';
                    } else if (iframeDoc.readyState !== 'complete') {
                        errorMessage = '页面还在加载中，请等待加载完成后再试';
                    }
                } catch (error) {
                    errorMessage = '页面内容访问失败：' + error.message;
                }
            }

            this.showAlert(errorMessage, 'warning');
            this.exitBindingMode();
            return;
        }

        // 显示加载遮罩
        this.showBindingLoadingMask();

        try {
            // 诊断当前iframe状态
            console.log('🔍 当前iframe状态:');
            console.log('  - currentIframe存在:', !!this.currentIframe);
            console.log('  - currentIframeDoc存在:', !!this.currentIframeDoc);

            if (this.currentIframe) {
                console.log('  - iframe src:', this.currentIframe.src || 'data/blob URL');
                console.log('  - iframe readyState:', this.currentIframe.readyState);
            }

            if (this.currentIframeDoc) {
                console.log('  - iframe document readyState:', this.currentIframeDoc.readyState);
                console.log('  - iframe body存在:', !!this.currentIframeDoc.body);

                // 检查可绑定元素数量
                const bindableElements = this.currentIframeDoc.querySelectorAll('input, select, textarea');
                console.log('  - 可绑定元素数量:', bindableElements.length);

                if (bindableElements.length > 0) {
                    console.log('  - 前3个元素:', Array.from(bindableElements).slice(0, 3).map(el => ({
                        tag: el.tagName,
                        type: el.type,
                        name: el.name,
                        id: el.id,
                        visible: el.offsetWidth > 0 && el.offsetHeight > 0
                    })));
                }
            }

            console.log('🚀 开始异步处理绑定功能...');
            // 异步处理绑定功能添加，避免阻塞UI
            await this.addBindingFunctionalityAsync();
            console.log('✅ 绑定功能添加完成');

            // 添加ESC键退出绑定模式
            this.addEscapeKeyListener();

            console.log('🎉 绑定模式启动成功');

            // 注意：不在这里隐藏遮罩和显示成功消息
            // 这些操作将在所有监听器真正添加完成后执行
        } catch (error) {
            console.error('❌ 绑定模式启动失败:', error);
            console.error('❌ 错误堆栈:', error.stack);
            this.hideBindingLoadingMask();
            this.showAlert('绑定模式启动失败：' + error.message, 'danger');
            this.exitBindingMode();
        }
    }

    // 显示绑定加载遮罩
    showBindingLoadingMask() {
        // 移除现有遮罩（如果存在）
        this.hideBindingLoadingMask();

        const previewContent = document.getElementById('previewContent');
        if (!previewContent) return;

        const mask = document.createElement('div');
        mask.id = 'bindingLoadingMask';
        mask.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(2px);
            z-index: 9999;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            border-radius: 8px;
        `;

        mask.innerHTML = `
            <div class="text-center">
                <div class="spinner-border text-primary mb-3" role="status" style="width: 3rem; height: 3rem;">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <h5 class="text-primary mb-2">正在识别页面元素...</h5>
                <p class="text-muted mb-0">
                    <span id="bindingProgress">正在扫描输入框</span>
                    <span class="loading-dots">...</span>
                </p>
                <div class="mt-3">
                    <small class="text-muted">
                        <i class="bi bi-info-circle me-1"></i>
                        复杂页面可能需要几秒钟时间
                    </small>
                </div>
            </div>
        `;

        previewContent.appendChild(mask);

        // 添加动态加载点效果
        this.startLoadingDotsAnimation();
    }

    // 隐藏绑定加载遮罩
    hideBindingLoadingMask() {
        const mask = document.getElementById('bindingLoadingMask');
        if (mask) {
            mask.remove();
        }
        this.stopLoadingDotsAnimation();
    }

    // 开始加载点动画
    startLoadingDotsAnimation() {
        if (this.loadingDotsInterval) {
            clearInterval(this.loadingDotsInterval);
        }

        let dotCount = 0;
        this.loadingDotsInterval = setInterval(() => {
            const dotsElement = document.querySelector('.loading-dots');
            if (dotsElement) {
                dotCount = (dotCount + 1) % 4;
                dotsElement.textContent = '.'.repeat(dotCount);
            }
        }, 500);
    }

    // 停止加载点动画
    stopLoadingDotsAnimation() {
        if (this.loadingDotsInterval) {
            clearInterval(this.loadingDotsInterval);
            this.loadingDotsInterval = null;
        }
    }

    // 异步添加绑定功能
    async addBindingFunctionalityAsync() {
        try {
            console.log('🔧 开始添加绑定功能...');

            // 更新进度
            this.updateBindingProgress('正在移除旧的监听器');

            // 先移除之前的监听器
            this.removeElementClickListeners(this.currentIframeDoc);
            console.log('🧹 旧监听器已移除');

            // 等待一小段时间让UI更新
            await new Promise(resolve => setTimeout(resolve, 50));

            this.updateBindingProgress('正在添加新的监听器');
            console.log('🔧 开始添加新的监听器...');

            // 添加新的监听器（优化版本）- 现在返回Promise
            await this.addElementClickListenersOptimized(this.currentIframeDoc);
            console.log('✅ 新监听器添加完成');

            this.updateBindingProgress('正在应用绑定样式');

            // 添加绑定模式样式
            if (this.currentIframeDoc && this.currentIframeDoc.body) {
                this.currentIframeDoc.body.classList.add('binding-mode-active');
                console.log('🎨 绑定样式已应用');
            }

            console.log('✅ 成功为iframe添加绑定功能');
        } catch (error) {
            console.error('❌ 添加绑定功能失败:', error);
            console.error('❌ 错误详情:', error.stack);
            throw error;
        }
    }

    // 更新绑定进度文本
    updateBindingProgress(text) {
        const progressElement = document.getElementById('bindingProgress');
        if (progressElement) {
            progressElement.textContent = text;
        }
    }

    // 等待iframe加载完成
    async waitForIframeLoad(iframe, maxWaitTime = 5000) {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();

            const checkIframe = () => {
                try {
                    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;

                    if (iframeDoc && iframeDoc.readyState === 'complete' && iframeDoc.body) {
                        console.log('✅ iframe加载完成');
                        this.currentIframe = iframe;
                        this.currentIframeDoc = iframeDoc;
                        resolve();
                        return;
                    }

                    // 检查是否超时
                    if (Date.now() - startTime > maxWaitTime) {
                        console.warn('⏰ iframe加载超时');
                        reject(new Error('iframe加载超时'));
                        return;
                    }

                    // 继续等待
                    setTimeout(checkIframe, 100);
                } catch (error) {
                    console.warn('⚠️ 检查iframe状态时出错:', error);
                    reject(error);
                }
            };

            // 开始检查
            checkIframe();
        });
    }

    // 退出绑定模式
    exitBindingMode() {
        console.log('🚪 退出绑定模式');
        this.bindingMode = false;

        // 清理加载遮罩
        this.hideBindingLoadingMask();
        this.stopLoadingDotsAnimation();

        // 更新UI
        const bindingMode = document.getElementById('bindingMode');
        const bindingToggleBtn = document.getElementById('bindingToggleBtn');
        const previewContent = document.getElementById('previewContent');

        if (bindingMode) {
            bindingMode.classList.remove('active', 'minimized');
        }

        // 移除预览区域的绑定样式
        if (previewContent) {
            previewContent.classList.remove('binding-active');
        }

        if (bindingToggleBtn) {
            bindingToggleBtn.innerHTML = '<i class="bi bi-magic"></i> 开始绑定';
            bindingToggleBtn.classList.remove('btn-outline-danger');
            bindingToggleBtn.classList.add('btn-outline-success');
        }

        // 移除iframe中的绑定模式样式和事件
        if (this.currentIframe && this.currentIframeDoc) {
            try {
                this.currentIframeDoc.body.classList.remove('binding-mode-active');
                this.removeElementClickListeners(this.currentIframeDoc);

                // 移除绑定模式的视觉效果，但保留已绑定元素的样式
                const bindableElements = this.currentIframeDoc.querySelectorAll('input, select, textarea, button, a');
                bindableElements.forEach(element => {
                    // 移除所有绑定相关的CSS类
                    element.classList.remove('smart-form-element', 'binding-mode', 'action-mode');

                    // 只移除未绑定元素的绑定模式样式
                    if (!element.dataset.boundField) {
                        element.style.border = '';
                        element.style.backgroundColor = '';
                        element.style.cursor = '';
                        element.style.transition = '';
                        element.style.outline = '';
                        element.style.outlineOffset = '';
                        element.style.boxShadow = '';
                    }
                    // 移除所有动作相关的样式
                    element.classList.remove('clickable-element');
                });

                // 移除动态添加的样式表
                const styleElement = this.currentIframeDoc.getElementById('smart-form-binding-styles');
                if (styleElement) {
                    styleElement.remove();
                }

                console.log('✅ iframe绑定样式已清理');
            } catch (error) {
                console.warn('⚠️ 清理iframe绑定样式时出错:', error);
            }
        }

        // 移除ESC键监听器
        this.removeEscapeKeyListener();

        // 清除选中的字段
        this.selectedField = null;
        document.querySelectorAll('.field-item').forEach(item => {
            item.classList.remove('selected');
        });

        console.log('✅ 绑定模式已退出');
        this.showAlert('已退出绑定模式', 'info');
    }

    // 添加ESC键监听器
    addEscapeKeyListener() {
        this.escapeKeyHandler = (event) => {
            if (event.key === 'Escape' && this.bindingMode) {
                event.preventDefault();
                this.exitBindingMode();
            }
        };
        document.addEventListener('keydown', this.escapeKeyHandler);
    }

    // 移除ESC键监听器
    removeEscapeKeyListener() {
        if (this.escapeKeyHandler) {
            document.removeEventListener('keydown', this.escapeKeyHandler);
            this.escapeKeyHandler = null;
        }
    }

    // 显示提示信息
    showAlert(message, type = 'info') {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1080;
            min-width: 300px;
            max-width: 500px;
        `;
        alertDiv.innerHTML = `
            <i class="bi bi-${this.getAlertIcon(type)} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(alertDiv);
        
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 3000);
    }

    // 获取提示图标
    getAlertIcon(type) {
        switch (type) {
            case 'success': return 'check-circle';
            case 'danger': return 'exclamation-triangle';
            case 'warning': return 'exclamation-triangle';
            case 'info': return 'info-circle';
            default: return 'info-circle';
        }
    }

    // 加载页面预览
    async loadPagePreview(sourceUrl = null) {
        let url = sourceUrl;

        if (!url) {
            const urlInput = document.getElementById('previewUrlInput') ||
                            document.getElementById('targetUrl') ||
                            document.getElementById('stepTargetUrl');
            url = urlInput ? urlInput.value.trim() : '';
        }

        if (!url) {
            this.showAlert('请输入目标URL', 'warning');
            return;
        }

        // 确保URL有协议
        if (!url.startsWith('http://') && !url.startsWith('https://')) {
            url = 'https://' + url;
        }

        // URL变化时重置绑定状态
        this.resetBindingsOnPageChange();

        // 同步URL到所有输入框
        this.syncUrlToInputs(url);

        const previewContent = document.getElementById('previewContent');
        if (!previewContent) return;

        // 显示加载状态
        previewContent.innerHTML = `
            <div class="preview-loading">
                <div class="spinner-border text-primary mb-3" role="status"></div>
                <h6>正在加载页面...</h6>
                <p class="text-muted">正在获取页面内容，支持重定向和登录页面</p>
                <div class="mt-3">
                    <small class="text-muted">目标URL: ${url}</small>
                </div>
            </div>
        `;

        try {
            // 尝试通过后端代理加载页面
            const result = await apiCall('/api/v1/page-bindings/load-page', 'POST', { url: url });

            if (result.code === 200) {
                const data = result.data;
                if (data.status === 'success') {
                    this.renderPageContent(data.html, data.finalUrl || url);

                    let message = '页面加载成功！';
                    if (data.redirected && data.finalUrl !== url) {
                        message += ` (已重定向到: ${data.finalUrl})`;
                        // 更新URL输入框为最终URL
                        this.syncUrlToInputs(data.finalUrl);
                    }

                    // 检查是否有iframe限制
                    if (data.hasFrameRestriction) {
                        message += '<br><strong>注意：</strong>' + data.warning;
                        this.showAlert(message, 'warning');

                        // 显示iframe限制的特殊提示
                        this.showFrameRestrictionWarning(data.finalUrl || url, data.frameRestrictionReason);
                    } else {
                        this.showAlert(message, 'success');
                    }
                } else if (data.status === 'fallback') {
                    this.showFallbackPreview(url, data.message);
                } else {
                    this.showFallbackPreview(url, data.message || '页面加载失败');
                }
            } else {
                this.showFallbackPreview(url, result.message || '页面加载失败');
            }
        } catch (error) {
            console.error('加载页面失败:', error);
            this.showFallbackPreview(url, '加载失败，请检查URL是否正确');
        }
    }

    // 同步URL到预览相关输入框（不包括配置保存的targetUrl）
    syncUrlToInputs(url) {
        const inputs = [
            document.getElementById('previewUrlInput'),
            document.getElementById('stepTargetUrl')
        ];

        inputs.forEach(input => {
            if (input) {
                input.value = url;
            }
        });

        // targetUrl是用户手动配置的，不自动同步
        // document.getElementById('targetUrl') 保持用户输入的值
    }

    // 渲染页面内容
    renderPageContent(html, finalUrl = null) {
        const previewContent = document.getElementById('previewContent');
        if (!previewContent) {
            this.showAlert('❌ 预览区域不存在，请检查页面结构', 'danger');
            return;
        }

        // 显示页面加载遮罩
        this.showPageLoadingMask();

        // 页面切换时重置绑定状态
        this.resetBindingsOnPageChange();

        // 异步处理HTML清理和渲染，避免阻塞UI
        setTimeout(() => {
            this.renderPageContentAsync(html, finalUrl);
        }, 100);
    }

    // 显示页面加载遮罩
    showPageLoadingMask() {
        const previewContent = document.getElementById('previewContent');
        if (!previewContent) return;

        previewContent.innerHTML = `
            <div class="page-loading-mask" style="
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(248, 249, 250, 0.95);
                backdrop-filter: blur(2px);
                z-index: 1000;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                border-radius: 8px;
            ">
                <div class="text-center">
                    <div class="spinner-border text-primary mb-3" role="status" style="width: 2.5rem; height: 2.5rem;">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <h6 class="text-primary mb-2">正在加载页面内容...</h6>
                    <p class="text-muted mb-0">
                        <span id="pageLoadingProgress">正在处理HTML内容</span>
                    </p>
                    <div class="mt-3">
                        <small class="text-muted">
                            <i class="bi bi-info-circle me-1"></i>
                            大型页面可能需要几秒钟时间
                        </small>
                    </div>
                </div>
            </div>
        `;
    }

    // 异步渲染页面内容
    async renderPageContentAsync(html, finalUrl = null) {
        try {
            // 更新进度
            this.updatePageLoadingProgress('正在清理HTML内容');

            // 清理HTML内容，修复localhost域名问题
            const cleanHtml = await this.cleanHtmlContentAsync(html);

            // 更新进度
            this.updatePageLoadingProgress('正在创建页面预览');

            // 创建iframe并渲染内容
            await this.createIframeAndRender(cleanHtml, finalUrl);

        } catch (error) {
            console.error('页面渲染失败:', error);
            this.showRenderError(error.message);
        }
    }

    // 更新页面加载进度
    updatePageLoadingProgress(text) {
        const progressElement = document.getElementById('pageLoadingProgress');
        if (progressElement) {
            progressElement.textContent = text;
        }
    }

    // 异步清理HTML内容
    async cleanHtmlContentAsync(html) {
        return new Promise((resolve) => {
            // 使用 setTimeout 让UI有机会更新
            setTimeout(() => {
                const cleaned = this.cleanHtmlContent(html);
                resolve(cleaned);
            }, 50);
        });
    }

    // 创建iframe并渲染内容
    async createIframeAndRender(cleanHtml, finalUrl = null) {
        const previewContent = document.getElementById('previewContent');
        if (!previewContent) return;

        // 更新进度
        this.updatePageLoadingProgress('正在创建iframe容器');

        // 创建iframe来安全地渲染HTML
        const iframe = document.createElement('iframe');
        iframe.className = 'preview-iframe';
        iframe.style.cssText = 'width: 100%; height: 100%; border: none; border-radius: 8px;';

        // 设置iframe的安全属性，允许更多权限以支持复杂页面
        iframe.setAttribute('sandbox', 'allow-same-origin allow-scripts allow-forms allow-popups allow-modals allow-downloads allow-top-navigation-by-user-activation');
        iframe.setAttribute('loading', 'eager'); // 立即加载

        // 更新进度
        this.updatePageLoadingProgress('正在准备页面容器');

        // 清空预览区域并添加iframe
        previewContent.innerHTML = '';
        previewContent.appendChild(iframe);

        // 更新进度
        this.updatePageLoadingProgress('正在写入页面内容');

        // 异步写入HTML内容，避免阻塞UI
        await this.writeIframeContentAsync(iframe, cleanHtml, finalUrl);
    }

    // 异步写入iframe内容
    async writeIframeContentAsync(iframe, cleanHtml, finalUrl) {
        return new Promise((resolve, reject) => {
            try {
                // 写入HTML内容
                const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                iframeDoc.open();

                // Chrome插件已经将相对路径转换为绝对路径，不需要设置base标签

                const iframeContent = `
                    <!DOCTYPE html>
                    <html>
                    <head>
                        <meta charset="utf-8">
                        <meta name="viewport" content="width=device-width, initial-scale=1">
                        <meta http-equiv="Content-Security-Policy" content="default-src 'self' 'unsafe-inline' 'unsafe-eval' *; img-src 'self' data: *; font-src 'self' data: *;">
                        <!-- 不设置base标签，因为Chrome插件已经将相对路径转换为绝对路径 -->
                        <!-- 调试标记: ${new Date().getTime()} -->
                <style>
                    /* 重置样式，确保在iframe中正常显示 */
                    * {
                        box-sizing: border-box;
                    }

                    body {
                        margin: 15px;
                        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                        line-height: 1.5;
                        zoom: 0.85;
                        background: white;
                        color: #333;
                        overflow-x: auto;
                    }

                    /* 确保图片和媒体元素正常显示 */
                    img, video, audio {
                        max-width: 100%;
                        height: auto;
                    }

                    /* 修复可能的布局问题 */
                    .container, .container-fluid {
                        max-width: 100%;
                        padding-left: 15px;
                        padding-right: 15px;
                    }

                    /* 修复可能的CSS加载问题 */
                    link[rel="stylesheet"] {
                        /* 确保CSS能正常加载 */
                    }

                    /* 防止页面过宽 */
                    html, body {
                        max-width: 100%;
                        overflow-x: auto;
                    }

                    /* 智能表单元素样式 */
                    .smart-form-element {
                        position: relative;
                        transition: all 0.2s ease;
                        cursor: pointer;
                    }

                    .smart-form-element:hover {
                        outline: 2px dashed #667eea;
                        outline-offset: 2px;
                        background-color: rgba(102, 126, 234, 0.05);
                    }

                    .smart-form-element.bound {
                        outline: 2px solid #48bb78;
                        outline-offset: 2px;
                        background-color: rgba(72, 187, 120, 0.1);
                    }

                    .binding-indicator {
                        position: absolute;
                        top: -10px;
                        right: -10px;
                        background: #48bb78;
                        color: white;
                        border-radius: 50%;
                        width: 20px;
                        height: 20px;
                        font-size: 12px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        z-index: 1000;
                        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
                        font-weight: bold;
                    }

                    .action-target {
                        outline: 2px solid #f6ad55 !important;
                        outline-offset: 2px;
                        background-color: rgba(246, 173, 85, 0.1);
                    }

                    .action-indicator {
                        position: absolute;
                        top: -10px;
                        right: -10px;
                        background: #f6ad55;
                        color: white;
                        border-radius: 50%;
                        width: 20px;
                        height: 20px;
                        font-size: 12px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        z-index: 1000;
                        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
                        font-weight: bold;
                    }

                    /* 绑定模式下的特殊样式 */
                    .binding-mode-active .smart-form-element {
                        animation: pulse 2s infinite;
                    }

                    @keyframes pulse {
                        0% { box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.4); }
                        70% { box-shadow: 0 0 0 10px rgba(102, 126, 234, 0); }
                        100% { box-shadow: 0 0 0 0 rgba(102, 126, 234, 0); }
                    }

                    /* 重定向提示样式 */
                    .redirect-notice {
                        position: fixed;
                        top: 10px;
                        right: 10px;
                        background: #667eea;
                        color: white;
                        padding: 8px 12px;
                        border-radius: 6px;
                        font-size: 12px;
                        z-index: 10000;
                        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
                    }
                </style>
            </head>
            <body>
                ${finalUrl && finalUrl !== document.getElementById('previewUrlInput')?.value ?
                    `<div class="redirect-notice">已重定向到: ${finalUrl}</div>` : ''}
                ${cleanHtml}
            </body>
            </html>
        `;

                console.log('📝 即将写入iframe的完整内容长度:', iframeContent.length);
                console.log('📝 iframe内容片段:', iframeContent.substring(0, 1000));

                // 测试：检查iframe内容中是否还有localhost
                if (iframeContent.includes('localhost')) {
                    console.log('❌ iframe内容中仍然包含localhost!');
                    const localhostMatches = iframeContent.match(/localhost[^"'\s]*/g);
                    console.log('🔍 localhost匹配:', localhostMatches);
                } else {
                    console.log('✅ iframe内容中没有localhost');
                }

                // 分批写入内容，避免阻塞UI
                const contentChunks = this.splitContentIntoChunks(iframeContent, 50000); // 每块50KB
                this.writeContentChunks(iframeDoc, contentChunks, 0, () => {
                    iframeDoc.close();

                    // 添加iframe错误处理
                    iframe.onerror = (error) => {
                        console.error('❌ iframe加载错误:', error);
                        this.showRenderError('页面预览加载失败，可能存在资源加载问题');
                        reject(error);
                    };

                    // 等待iframe加载完成后添加事件监听
                    iframe.onload = () => {
                        try {
                            // 更新进度
                            this.updatePageLoadingProgress('页面加载完成');

                            // 延迟一点时间让页面完全渲染
                            setTimeout(() => {
                                this.setupIframeInteraction(iframe, iframeDoc);
                                console.log('✅ iframe加载完成');
                                resolve();
                            }, 200);
                        } catch (error) {
                            console.error('❌ iframe交互设置失败:', error);
                            reject(error);
                        }
                    };

                    // 如果iframe已经加载完成，直接触发onload
                    if (iframe.readyState === 'complete') {
                        iframe.onload();
                    }
                });

            } catch (error) {
                console.error('❌ 写入iframe内容失败:', error);
                reject(error);
            }
        });
    }

    // 将内容分割成块
    splitContentIntoChunks(content, chunkSize) {
        const chunks = [];
        for (let i = 0; i < content.length; i += chunkSize) {
            chunks.push(content.substring(i, i + chunkSize));
        }
        return chunks;
    }

    // 分批写入内容块
    writeContentChunks(iframeDoc, chunks, index, callback) {
        if (index >= chunks.length) {
            callback();
            return;
        }

        // 写入当前块
        iframeDoc.write(chunks[index]);

        // 更新进度
        const progress = Math.round((index + 1) / chunks.length * 100);
        this.updatePageLoadingProgress(`正在写入页面内容 (${progress}%)`);

        // 使用 setTimeout 让浏览器有机会更新UI
        setTimeout(() => {
            this.writeContentChunks(iframeDoc, chunks, index + 1, callback);
        }, 10);
    }

    // 显示渲染错误
    showRenderError(message) {
        const previewContent = document.getElementById('previewContent');
        if (!previewContent) return;

        previewContent.innerHTML = `
            <div class="text-center py-5">
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    <strong>页面渲染失败</strong>
                </div>
                <p class="text-muted">${message}</p>
                <button class="btn btn-outline-primary" onclick="smartFormManager.showHtmlInput()">
                    <i class="bi bi-arrow-clockwise me-2"></i>重新尝试
                </button>
            </div>
        `;
    }

    // 清理HTML内容，保持绝对路径不变
    cleanHtmlContent(html) {
        if (!html) return '';

        // 查找所有的link和script标签，看看URL是什么样的
        const linkMatches = html.match(/<link[^>]*href=["'][^"']*["'][^>]*>/gi);
        const scriptMatches = html.match(/<script[^>]*src=["'][^"']*["'][^>]*>/gi);

        if (linkMatches) {
            console.log('🔗 发现的link标签:', linkMatches.slice(0, 3));
            // 检查是否有localhost的URL
            linkMatches.forEach(link => {
                if (link.includes('localhost')) {
                    console.log('⚠️ 发现localhost链接:', link);
                }
            });
        }
        if (scriptMatches) {
            console.log('📜 发现的script标签:', scriptMatches.slice(0, 3));
            // 检查是否有localhost的URL
            scriptMatches.forEach(script => {
                if (script.includes('localhost')) {
                    console.log('⚠️ 发现localhost脚本:', script);
                }
            });
        }

        const cleaned = html
            // 处理转义字符 - 这是关键步骤
            .replace(/\\"/g, '"')
            .replace(/\\'/g, "'")
            .replace(/\\\//g, '/')  // 处理转义的斜杠
            .replace(/\\n/g, '\n')  // 处理换行符转义
            .replace(/\\r/g, '\r')  // 处理回车符转义
            .replace(/\\t/g, '\t')  // 处理制表符转义
            .replace(/\\\\/g, '\\') // 处理反斜杠转义
            // 修复可能的相对路径问题
            .replace(/href=["']\.\.?\//g, (match) => {
                console.log('🔧 修复相对路径:', match);
                return match.replace(/["']\.\.?\//, `"${window.location.origin}/`);
            })
            .replace(/src=["']\.\.?\//g, (match) => {
                console.log('🔧 修复相对路径:', match);
                return match.replace(/["']\.\.?\//, `"${window.location.origin}/`);
            })
            // 只移除安全风险元素，不修改任何URL
            // Chrome插件已经将相对路径转换为绝对路径了
            .replace(/<script[^>]*>[\s\S]*?<\/script>/gi, '')
            .replace(/on\w+\s*=\s*["'][^"']*["']/gi, '')
            .replace(/javascript:/gi, '');

        // 再次检查清理后的URL
        const cleanedLinkMatches = cleaned.match(/<link[^>]*href=["'][^"']*["'][^>]*>/gi);
        if (cleanedLinkMatches) {
            console.log('🧹 清理后的link标签:', cleanedLinkMatches.slice(0, 3));
        }

        return cleaned;
    }



    // 设置iframe交互
    setupIframeInteraction(iframe, iframeDoc) {
        console.log('🔧 设置iframe交互，绑定模式:', this.bindingMode);
        console.log('🔧 iframe参数:', !!iframe);
        console.log('🔧 iframeDoc参数:', !!iframeDoc);

        // 存储当前iframe引用
        this.currentIframe = iframe;
        this.currentIframeDoc = iframeDoc;

        console.log('✅ iframe引用已设置');
        console.log('  - this.currentIframe:', !!this.currentIframe);
        console.log('  - this.currentIframeDoc:', !!this.currentIframeDoc);

        // 先移除之前的监听器，避免重复
        this.removeElementClickListeners(iframeDoc);

        // 在绑定模式或动作模式下添加元素监听器
        if (this.bindingMode || this.actionMode) {
            this.addElementClickListeners(iframeDoc);
        }

        // 处理表单提交 - 在非绑定模式下允许正常提交
        const forms = iframeDoc.querySelectorAll('form');
        forms.forEach(form => {
            // 移除之前的监听器
            if (form._submitHandler) {
                form.removeEventListener('submit', form._submitHandler);
            }

            // 添加新的监听器
            const submitHandler = (e) => {
                if (this.bindingMode || this.actionMode) {
                    // 绑定模式下阻止表单提交，用于绑定操作
                    e.preventDefault();
                    // 不调用handleElementClick，因为表单提交不是元素绑定
                } else {
                    // 正常模式下允许表单提交（登录等操作）
                    console.log('允许表单提交，用于登录等操作');
                    // 不阻止默认行为，让表单正常提交
                }
            };

            form.addEventListener('submit', submitHandler);
            form._submitHandler = submitHandler;
        });

        // 处理链接点击（这些事件已经在addElementClickListeners中处理了，这里不需要重复添加）
        // 链接点击事件已经通过addElementClickListeners统一处理

        // 处理输入框交互
        const inputs = iframeDoc.querySelectorAll('input, textarea, select');
        inputs.forEach(input => {
            if (!this.bindingMode) {
                // 非绑定模式下，移除绑定相关的样式和事件
                input.classList.remove('smart-form-element');
            }
        });

        // 监听iframe内的页面变化（如登录后跳转）
        this.setupIframeNavigationListener(iframe);
    }

    // 设置iframe导航监听
    setupIframeNavigationListener(iframe) {
        try {
            // 监听iframe的URL变化
            const checkNavigation = () => {
                try {
                    const currentUrl = iframe.contentWindow.location.href;
                    if (currentUrl && currentUrl !== 'about:blank') {
                        // 更新URL输入框
                        this.syncUrlToInputs(currentUrl);
                        console.log('iframe导航到:', currentUrl);
                    }
                } catch (error) {
                    // 跨域限制，忽略
                }
            };

            // 定期检查URL变化
            setInterval(checkNavigation, 1000);

        } catch (error) {
            console.log('无法监听iframe导航，可能存在跨域限制');
        }
    }

    // 显示备用预览
    showFallbackPreview(url, message) {
        const previewContent = document.getElementById('previewContent');
        if (!previewContent) return;

        previewContent.innerHTML = `
            <div class="text-center py-4">
                <div class="alert alert-warning mb-4">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    ${message}
                </div>

                <div class="alert alert-info mb-4">
                    <h6 class="alert-heading">
                        <i class="bi bi-info-circle me-2"></i>关于X-Frame-Options限制
                    </h6>
                    <p class="mb-2">目标网站设置了安全策略，禁止在iframe中加载。这是正常的安全措施。</p>
                    <p class="mb-0">请选择以下方式之一来继续配置：</p>
                </div>

                <div class="row g-3 mb-4">
                    <div class="col-md-4">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="bi bi-box-arrow-up-right display-6 text-primary mb-3"></i>
                                <h6>新窗口打开</h6>
                                <p class="small text-muted">在新标签页中打开目标页面，然后复制HTML代码</p>
                                <button type="button" class="btn btn-primary btn-sm" onclick="smartFormManager.openInNewWindow('${url}')">
                                    打开页面
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="bi bi-code-slash display-6 text-success mb-3"></i>
                                <h6>手动输入HTML</h6>
                                <p class="small text-muted">从浏览器开发者工具复制页面HTML代码</p>
                                <button type="button" class="btn btn-success btn-sm" onclick="smartFormManager.showHtmlInput()">
                                    输入HTML
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="bi bi-globe display-6 text-warning mb-3"></i>
                                <h6>尝试强制加载</h6>
                                <p class="small text-muted">尝试绕过限制（可能失败）</p>
                                <button type="button" class="btn btn-warning btn-sm" onclick="smartFormManager.loadDirectIframe('${url}')">
                                    强制加载
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="alert alert-light">
                    <h6><i class="bi bi-lightbulb me-2"></i>推荐流程：</h6>
                    <ol class="mb-0 text-start">
                        <li>点击"打开页面"在新窗口中访问目标网站</li>
                        <li>在新窗口中完成登录等操作，导航到目标页面</li>
                        <li>按F12打开开发者工具，在Elements标签中右键点击&lt;html&gt;标签</li>
                        <li>选择"Copy" → "Copy outerHTML"</li>
                        <li>回到这里点击"输入HTML"，粘贴代码并加载</li>
                    </ol>
                </div>
            </div>
        `;
    }

    // 在新窗口中打开URL
    openInNewWindow(url) {
        const newWindow = window.open(url, '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes');

        if (newWindow) {
            this.showAlert(`
                <strong>页面已在新窗口中打开</strong><br>
                请在新窗口中完成登录和导航，然后：<br>
                1. 按F12打开开发者工具<br>
                2. 右键点击&lt;html&gt;标签<br>
                3. 选择"Copy outerHTML"<br>
                4. 回到这里粘贴HTML代码
            `, 'info');
        } else {
            this.showAlert('无法打开新窗口，请检查浏览器弹窗设置', 'warning');
        }
    }

    // 显示HTML输入界面
    showHtmlInput() {
        const previewContent = document.getElementById('previewContent');
        if (!previewContent) return;

        previewContent.innerHTML = `
            <div class="p-4">
                <div class="text-center mb-4">
                    <i class="bi bi-code-slash display-4 text-success mb-3"></i>
                    <h5>手动输入HTML代码</h5>
                    <p class="text-muted">请粘贴从浏览器开发者工具复制的HTML代码</p>
                </div>

                <div class="mb-3">
                    <label class="form-label">HTML代码：</label>
                    <textarea class="form-control" rows="12" placeholder="请粘贴完整的HTML代码..." id="manualHtmlInput" style="font-family: monospace; font-size: 12px;"></textarea>
                </div>

                <div class="d-flex gap-2 justify-content-center">
                    <button type="button" class="btn btn-success" onclick="loadManualHtml()">
                        <i class="bi bi-upload me-2"></i>加载HTML
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="smartFormManager.loadPagePreview()">
                        <i class="bi bi-arrow-left me-2"></i>返回URL加载
                    </button>
                </div>

                <div class="alert alert-info mt-3">
                    <h6><i class="bi bi-info-circle me-2"></i>如何获取HTML代码：</h6>
                    <ol class="mb-0">
                        <li>在目标页面按F12打开开发者工具</li>
                        <li>在Elements标签中找到&lt;html&gt;标签</li>
                        <li>右键点击&lt;html&gt;标签</li>
                        <li>选择"Copy" → "Copy outerHTML"</li>
                        <li>粘贴到上方文本框中</li>
                    </ol>
                </div>
            </div>
        `;

        // 聚焦到文本框
        setTimeout(() => {
            const textarea = document.getElementById('manualHtmlInput');
            if (textarea) {
                textarea.focus();
            }
        }, 100);
    }

    // 直接在iframe中加载URL（改进版）
    loadDirectIframe(url) {
        const previewContent = document.getElementById('previewContent');
        if (!previewContent) return;

        // 显示提示信息
        previewContent.innerHTML = `
            <div class="p-4">
                <div class="alert alert-info mb-4">
                    <h6><i class="bi bi-info-circle me-2"></i>iframe加载模式</h6>
                    <p class="mb-2">正在尝试在iframe中加载页面，但可能遇到以下限制：</p>
                    <ul class="mb-0">
                        <li>X-Frame-Options安全策略限制</li>
                        <li>跨域访问限制</li>
                        <li>会话隔离问题</li>
                    </ul>
                </div>

                <div class="text-center mb-4">
                    <div class="spinner-border text-primary mb-3" role="status"></div>
                    <h6>正在加载页面...</h6>
                    <p class="text-muted">如果长时间无响应，建议使用"独立预览"</p>
                </div>

                <div class="d-flex gap-2 justify-content-center">
                    <button type="button" class="btn btn-primary" onclick="openIndependentPreview()">
                        <i class="bi bi-window-plus me-2"></i>改用独立预览
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="smartFormManager.showHtmlInput()">
                        <i class="bi bi-code-slash me-2"></i>手动输入HTML
                    </button>
                </div>
            </div>
        `;

        // 创建iframe
        const iframe = document.createElement('iframe');
        iframe.className = 'preview-iframe';
        iframe.style.cssText = 'width: 100%; height: 100%; border: none; border-radius: 8px; position: absolute; top: 0; left: 0; z-index: 1;';

        // 设置iframe属性以支持交互，但限制某些功能以避免问题
        iframe.setAttribute('sandbox', 'allow-same-origin allow-scripts allow-forms allow-popups allow-top-navigation-by-user-activation allow-downloads');
        iframe.src = url;

        // 设置超时
        const timeout = setTimeout(() => {
            this.showAlert('页面加载超时，建议使用"独立预览"获得更好的体验', 'warning');
            // 不移除iframe，让用户自己判断
        }, 10000);

        // iframe加载完成
        iframe.onload = () => {
            clearTimeout(timeout);

            // 移除加载提示，显示iframe
            previewContent.innerHTML = '';
            previewContent.appendChild(iframe);

            try {
                // 尝试访问iframe内容
                const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                if (iframeDoc && iframeDoc.body && iframeDoc.body.children.length > 0) {
                    // 成功访问，可以进行绑定操作
                    this.currentIframe = iframe;
                    this.currentIframeDoc = iframeDoc;
                    this.setupIframeInteraction(iframe, iframeDoc);
                    this.showAlert(`
                        <strong>页面加载成功！</strong><br>
                        • 您可以在页面中进行登录和操作<br>
                        • 如需更好的体验，建议使用"独立预览"<br>
                        • 绑定字段前请先点击"开始绑定"
                    `, 'success');
                } else {
                    // 无法访问iframe内容，但页面可能已加载
                    this.currentIframe = iframe;
                    this.showAlert(`
                        <strong>页面已加载，但存在访问限制</strong><br>
                        • 您可以在页面中进行基本操作<br>
                        • 如需绑定字段，请使用"独立预览"或"手动输入HTML"<br>
                        • 独立预览提供完全隔离的浏览环境
                    `, 'warning');
                }
            } catch (error) {
                console.log('跨域限制，但iframe可能正常工作:', error);
                this.currentIframe = iframe;
                this.showAlert(`
                    <strong>页面已加载（存在跨域限制）</strong><br>
                    • 页面可以正常显示和操作<br>
                    • 无法进行字段绑定<br>
                    • 建议使用"独立预览"获得完整功能
                `, 'info');
            }
        };

        iframe.onerror = () => {
            clearTimeout(timeout);
            this.showAlert('页面加载失败，请使用"独立预览"或检查URL是否正确', 'danger');
        };

        // 延迟添加iframe，让用户先看到提示
        setTimeout(() => {
            const container = previewContent.querySelector('.p-4');
            if (container) {
                container.appendChild(iframe);
            }
        }, 2000);
    }

    // 显示iframe限制警告
    showFrameRestrictionWarning(url, reason) {
        const previewContent = document.getElementById('previewContent');
        if (!previewContent) return;

        // 在页面内容上方添加警告横幅
        const warningBanner = document.createElement('div');
        warningBanner.className = 'alert alert-warning mb-0';
        warningBanner.style.cssText = 'border-radius: 0; border-left: none; border-right: none; border-top: none;';
        warningBanner.innerHTML = `
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    <strong>iframe限制检测：</strong>${reason}
                </div>
                <div class="btn-group btn-group-sm">
                    <button type="button" class="btn btn-outline-warning btn-sm" onclick="smartFormManager.showFrameRestrictionHelp('${url}')">
                        <i class="bi bi-question-circle me-1"></i>解决方案
                    </button>
                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="this.parentElement.parentElement.parentElement.remove()">
                        <i class="bi bi-x"></i>
                    </button>
                </div>
            </div>
        `;

        // 插入到预览内容的顶部
        previewContent.insertBefore(warningBanner, previewContent.firstChild);
    }

    // 显示iframe限制解决方案
    showFrameRestrictionHelp(url) {
        const previewContent = document.getElementById('previewContent');
        if (!previewContent) return;

        previewContent.innerHTML = `
            <div class="p-4">
                <div class="text-center mb-4">
                    <i class="bi bi-shield-exclamation display-4 text-warning mb-3"></i>
                    <h4>iframe限制解决方案</h4>
                    <p class="text-muted">目标网站设置了安全策略，禁止在iframe中加载</p>
                </div>

                <div class="alert alert-info mb-4">
                    <h6><i class="bi bi-info-circle me-2"></i>什么是X-Frame-Options？</h6>
                    <p class="mb-0">X-Frame-Options是一个HTTP响应头，用于防止网页被嵌入到iframe中，这是一种防止点击劫持攻击的安全措施。</p>
                </div>

                <div class="row g-3 mb-4">
                    <div class="col-md-6">
                        <div class="card border-primary h-100">
                            <div class="card-header bg-primary text-white">
                                <h6 class="mb-0"><i class="bi bi-1-circle me-2"></i>推荐方案：手动复制HTML</h6>
                            </div>
                            <div class="card-body">
                                <ol class="mb-3">
                                    <li>点击下方按钮在新窗口打开页面</li>
                                    <li>在新窗口中完成登录和导航</li>
                                    <li>按F12打开开发者工具</li>
                                    <li>右键点击&lt;html&gt;标签，选择"Copy outerHTML"</li>
                                    <li>回到这里粘贴HTML代码</li>
                                </ol>
                                <button type="button" class="btn btn-primary w-100" onclick="smartFormManager.openInNewWindow('${url}')">
                                    <i class="bi bi-box-arrow-up-right me-2"></i>在新窗口中打开
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card border-secondary h-100">
                            <div class="card-header bg-secondary text-white">
                                <h6 class="mb-0"><i class="bi bi-2-circle me-2"></i>备选方案：强制加载</h6>
                            </div>
                            <div class="card-body">
                                <p class="mb-3">尝试绕过限制直接加载（成功率较低）：</p>
                                <div class="d-grid gap-2">
                                    <button type="button" class="btn btn-warning" onclick="smartFormManager.loadDirectIframe('${url}')">
                                        <i class="bi bi-globe me-2"></i>强制在iframe中加载
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary" onclick="smartFormManager.showHtmlInput()">
                                        <i class="bi bi-code-slash me-2"></i>手动输入HTML
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="alert alert-light">
                    <h6><i class="bi bi-lightbulb me-2"></i>为什么会有这个限制？</h6>
                    <p class="mb-2">网站设置iframe限制主要是为了：</p>
                    <ul class="mb-0">
                        <li>防止点击劫持攻击（Clickjacking）</li>
                        <li>保护用户隐私和安全</li>
                        <li>防止网站被恶意嵌入</li>
                        <li>确保用户在正确的域名下操作</li>
                    </ul>
                </div>

                <div class="text-center mt-4">
                    <button type="button" class="btn btn-outline-primary" onclick="smartFormManager.loadPagePreview()">
                        <i class="bi bi-arrow-left me-2"></i>返回重新加载
                    </button>
                </div>
            </div>
        `;
    }

    // 添加元素点击监听器（优化版本）
    addElementClickListenersOptimized(doc = null) {
        return new Promise((resolve) => {
            console.time('⏱️ 添加元素监听器（优化版）');
            const targetDoc = doc || document;

            // 根据模式选择不同的元素
            let selector;
            if (this.bindingMode) {
                // 绑定模式：包含原生表单元素和第三方控件
                selector = this.getFormElementSelector();
            } else if (this.actionMode) {
                // 动作模式：选择所有可点击的元素
                selector = `
                    button,
                    a,
                    input[type="button"],
                    input[type="submit"],
                    input[type="reset"],
                    [onclick],
                    [role="button"],
                    [role="tab"],
                    [role="link"],
                    .btn,
                    .button,
                    .tab,
                    .nav-link,
                    .nav-item,
                    [data-toggle],
                    [data-bs-toggle],
                    span[onclick],
                    div[onclick],
                    li[onclick]
                `.replace(/\s+/g, ' ').trim();
            } else {
                resolve(); // 非绑定/动作模式，直接返回
                return;
            }

            const elements = targetDoc.querySelectorAll(selector);
            console.log('🎯 找到', elements.length, '个元素需要添加监听器');

            // 更新进度显示
            this.updateBindingProgress(`找到 ${elements.length} 个元素，正在添加监听器`);

            // 如果没有元素，直接返回
            if (elements.length === 0) {
                console.log('✅ 没有找到需要绑定的元素');
                setTimeout(() => {
                    this.hideBindingLoadingMask();
                    this.showAlert('页面中没有找到可绑定的输入框', 'warning');
                }, 100);
                resolve();
                return;
            }

            // 批量处理元素，避免长时间阻塞
            const batchSize = 50; // 每批处理50个元素
            let processedCount = 0;

            const processBatch = (startIndex) => {
                const endIndex = Math.min(startIndex + batchSize, elements.length);

                for (let i = startIndex; i < endIndex; i++) {
                    const element = elements[i];

                    // 跳过已经有监听器的元素
                    if (element._smartFormHandler) {
                        continue;
                    }

                    // 在绑定模式下，跳过不可编辑的元素
                    if (this.bindingMode) {
                        if (!this.isEditableFormElement(element)) {
                            continue;
                        }
                    }

                    // 在动作模式下，跳过禁用的元素
                    if (this.actionMode && element.disabled) {
                        continue;
                    }

                    element.classList.add('smart-form-element');

                    // 添加新的监听器 - 使用捕获阶段确保优先处理
                    const clickHandler = (event) => {
                        if (this.bindingMode || this.actionMode) {
                            // 立即阻止事件传播和默认行为
                            event.preventDefault();
                            event.stopPropagation();
                            event.stopImmediatePropagation();

                            // 处理绑定逻辑
                            this.handleElementClick(event, element);
                            return false;
                        }
                    };

                    // 使用捕获阶段监听，确保在其他事件处理器之前执行
                    element.addEventListener('click', clickHandler, true);

                    // 存储处理器引用以便后续移除
                    element._smartFormHandler = clickHandler;
                    processedCount++;
                }

                // 如果还有更多元素需要处理，继续下一批
                if (endIndex < elements.length) {
                    // 更新进度
                    const progress = Math.round((endIndex / elements.length) * 100);
                    this.updateBindingProgress(`正在处理元素 ${endIndex}/${elements.length} (${progress}%)`);

                    // 使用 setTimeout 让浏览器有机会更新UI
                    setTimeout(() => processBatch(endIndex), 10);
                } else {
                    // 所有元素处理完成，批量应用样式
                    this.applyBindingStylesBatch(targetDoc);
                    console.timeEnd('⏱️ 添加元素监听器（优化版）');
                    console.log('✅ 成功为', processedCount, '个元素添加了监听器');

                    // 在这里隐藏加载遮罩并显示成功消息
                    this.updateBindingProgress(`完成！已为 ${processedCount} 个元素添加监听器`);
                    setTimeout(() => {
                        this.hideBindingLoadingMask();
                        this.showAlert('绑定模式已开启！现在点击页面中的输入框来绑定字段', 'success');
                        console.log('🎉 绑定模式完全启动，用户现在可以点击元素了');
                    }, 500); // 稍微延迟一下，让用户看到完成状态

                    resolve(); // 完成时调用resolve
                }
            };

            // 开始批量处理
            processBatch(0);
        });
    }

    // 批量应用绑定样式（使用CSS类而不是内联样式）
    applyBindingStylesBatch(doc) {
        // 添加样式表到iframe文档
        if (!doc.getElementById('smart-form-binding-styles')) {
            const style = doc.createElement('style');
            style.id = 'smart-form-binding-styles';
            style.textContent = `
                .smart-form-element {
                    transition: all 0.2s ease !important;
                    cursor: pointer !important;
                }

                .smart-form-element:hover {
                    outline: 2px dashed #007bff !important;
                    outline-offset: 2px !important;
                    background-color: rgba(0, 123, 255, 0.05) !important;
                }

                .smart-form-element.binding-mode {
                    outline: 2px dashed #007bff !important;
                    outline-offset: 2px !important;
                }

                .smart-form-element.action-mode {
                    outline: 2px dashed #ff6b35 !important;
                    outline-offset: 2px !important;
                }

                .binding-mode-active .smart-form-element {
                    animation: pulse-binding 2s infinite;
                }

                @keyframes pulse-binding {
                    0% { box-shadow: 0 0 0 0 rgba(0, 123, 255, 0.4); }
                    70% { box-shadow: 0 0 0 10px rgba(0, 123, 255, 0); }
                    100% { box-shadow: 0 0 0 0 rgba(0, 123, 255, 0); }
                }
            `;
            doc.head.appendChild(style);
        }

        // 为元素添加相应的CSS类
        const elements = doc.querySelectorAll('.smart-form-element');
        elements.forEach(element => {
            if (this.bindingMode) {
                element.classList.add('binding-mode');
                element.title = '点击绑定字段';
            } else if (this.actionMode) {
                element.classList.add('action-mode');
                element.title = '点击设置为下一步动作';
            }
        });
    }

    // 添加元素点击监听器（原版本，保持兼容性）
    addElementClickListeners(doc = null) {
        // 调用优化版本
        return this.addElementClickListenersOptimized(doc);
    }

    // 移除元素点击监听器
    removeElementClickListeners(doc = null) {
        console.log('🧹 移除元素点击监听器...');
        const targetDoc = doc || document;

        // 查找所有可能的元素，不仅仅是有.smart-form-element类的
        const selectors = [
            '.smart-form-element',
            'input', 'select', 'textarea', 'button', 'a',
            '[onclick]', '[role="button"]', '[role="tab"]', '[role="link"]',
            '.btn', '.button', '.tab', '.nav-link', '.nav-item',
            '[data-toggle]', '[data-bs-toggle]'
        ];

        let cleanedCount = 0;

        selectors.forEach(selector => {
            const elements = targetDoc.querySelectorAll(selector);
            elements.forEach(element => {
                // 移除所有相关的CSS类
                element.classList.remove(
                    'smart-form-element',
                    'binding-mode',
                    'action-mode',
                    'binding-mode-active',
                    'clickable-element'
                );

                // 清理视觉样式（但保留已绑定元素的样式）
                if (!element.dataset.boundField) {
                    element.style.outline = '';
                    element.style.outlineOffset = '';
                    element.style.cursor = '';
                    element.style.backgroundColor = '';
                    element.style.border = '';
                    element.style.boxShadow = '';
                    element.style.transition = '';
                    element.title = element.title.replace('点击绑定字段', '').replace('点击设置为下一步动作', '').trim();
                }

                // 移除主要的点击监听器
                if (element._smartFormHandler) {
                    element.removeEventListener('click', element._smartFormHandler, true);
                    delete element._smartFormHandler;
                    cleanedCount++;
                }

                // 移除额外的事件阻止监听器
                if (element._preventHandlers) {
                    ['mousedown', 'mouseup', 'submit'].forEach(eventType => {
                        element.removeEventListener(eventType, element._preventHandlers, true);
                    });
                    delete element._preventHandlers;
                }
            });
        });

        // 移除动态添加的样式表
        const styleElement = targetDoc.getElementById('smart-form-binding-styles');
        if (styleElement) {
            styleElement.remove();
            console.log('✅ 已移除动态样式表');
        }

        console.log(`✅ 已清理 ${cleanedCount} 个元素的监听器`);
    }

    // 处理元素点击
    handleElementClick(event, element) {
        // 强制阻止所有事件传播和默认行为
        event.preventDefault();
        event.stopPropagation();
        event.stopImmediatePropagation();

        console.log('🎯 处理元素点击:', element.tagName, element.type || '', element.textContent?.trim().substring(0, 20));
        console.log('🎯 当前模式:', this.bindingMode ? '绑定模式' : this.actionMode ? '动作模式' : '未知模式');

        if (this.bindingMode) {
            // 绑定模式：显示字段选择弹窗
            console.log('🎯 执行字段绑定...');
            this.showFieldSelectionModal(element);
        } else if (this.actionMode) {
            // 动作模式：设置为下一步动作目标
            console.log('🎯 执行动作设置...');
            this.setActionTarget(element);
        } else {
            console.warn('🎯 未知模式，无法处理点击');
        }

        return false; // 确保阻止事件
    }

    // 显示字段选择弹窗
    showFieldSelectionModal(element) {
        // 获取可用字段
        const fields = this.getAvailableFields();

        if (fields.length === 0) {
            this.showAlert('没有可用的字段进行绑定', 'warning');
            return;
        }

        // 检查是否已存在模态框，如果存在则先移除
        const existingModal = document.getElementById('fieldSelectionModal');
        if (existingModal) {
            existingModal.remove();
        }

        // 创建模态框
        const modalHtml = `
            <div class="modal fade" id="fieldSelectionModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="bi bi-link me-2"></i>选择要绑定的字段
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="mb-3">
                                <label class="form-label">目标元素信息：</label>
                                <div class="alert alert-light">
                                    <strong>标签：</strong> ${element.tagName.toLowerCase()}<br>
                                    <strong>类型：</strong> ${element.type || '无'}<br>
                                    <strong>名称：</strong> ${element.name || '无'}<br>
                                    <strong>ID：</strong> ${element.id || '无'}
                                </div>
                            </div>

                            <!-- 搜索框 -->
                            <div class="mb-3">
                                <label class="form-label">搜索字段：</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="bi bi-search"></i>
                                    </span>
                                    <input type="text" class="form-control" id="fieldSearchInput"
                                           placeholder="输入字段名称或描述进行搜索..."
                                           autocomplete="off">
                                </div>
                                <small class="text-muted">共 ${fields.length} 个字段</small>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">选择字段：</label>
                                <div class="field-selection-list" id="fieldSelectionList" style="max-height: 300px; overflow-y: auto;">
                                    ${fields.map(field => `
                                        <div class="field-option" data-field-key="${field.key}"
                                             data-field-name="${field.name.toLowerCase()}"
                                             data-field-desc="${(field.description || '').toLowerCase()}">
                                            <div class="d-flex justify-content-between align-items-center p-3 border rounded mb-2 cursor-pointer field-option-item">
                                                <div>
                                                    <strong class="field-name">${field.name}</strong>
                                                    <br><small class="text-muted field-desc">${field.description || '无描述'}</small>
                                                    <br><small class="text-info">键值: ${field.key}</small>
                                                </div>
                                                <i class="bi bi-arrow-right text-primary"></i>
                                            </div>
                                        </div>
                                    `).join('')}
                                </div>
                                <div id="noFieldsFound" class="text-center text-muted py-4" style="display: none;">
                                    <i class="bi bi-search"></i>
                                    <p class="mb-0">未找到匹配的字段</p>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 添加新模态框
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('fieldSelectionModal'));
        modal.show();

        // 添加搜索功能
        const searchInput = document.getElementById('fieldSearchInput');
        const fieldList = document.getElementById('fieldSelectionList');
        const noFieldsFound = document.getElementById('noFieldsFound');

        searchInput.addEventListener('input', (e) => {
            const searchTerm = e.target.value.toLowerCase().trim();
            const fieldOptions = document.querySelectorAll('.field-option');
            let visibleCount = 0;

            fieldOptions.forEach(option => {
                const fieldName = option.dataset.fieldName || '';
                const fieldDesc = option.dataset.fieldDesc || '';
                const fieldKey = option.dataset.fieldKey || '';

                const isMatch = fieldName.includes(searchTerm) ||
                               fieldDesc.includes(searchTerm) ||
                               fieldKey.toLowerCase().includes(searchTerm);

                if (isMatch) {
                    option.style.display = '';
                    visibleCount++;

                    // 高亮搜索关键词
                    if (searchTerm) {
                        this.highlightSearchTerm(option, searchTerm);
                    } else {
                        this.removeHighlight(option);
                    }
                } else {
                    option.style.display = 'none';
                }
            });

            // 显示/隐藏无结果提示
            if (visibleCount === 0 && searchTerm) {
                fieldList.style.display = 'none';
                noFieldsFound.style.display = 'block';
            } else {
                fieldList.style.display = '';
                noFieldsFound.style.display = 'none';
            }
        });

        // 添加字段选择事件
        document.querySelectorAll('.field-option').forEach(option => {
            option.addEventListener('click', () => {
                const fieldKey = option.dataset.fieldKey;
                const field = fields.find(f => f.key === fieldKey);

                // 执行绑定
                this.bindFieldToElement(element, field);

                // 关闭模态框
                modal.hide();
            });
        });

        // 自动聚焦搜索框
        setTimeout(() => {
            searchInput.focus();
        }, 300);
    }

    // 高亮搜索关键词
    highlightSearchTerm(option, searchTerm) {
        const fieldName = option.querySelector('.field-name');
        const fieldDesc = option.querySelector('.field-desc');

        if (fieldName) {
            const originalText = fieldName.textContent;
            const highlightedText = this.addHighlight(originalText, searchTerm);
            fieldName.innerHTML = highlightedText;
        }

        if (fieldDesc) {
            const originalText = fieldDesc.textContent;
            const highlightedText = this.addHighlight(originalText, searchTerm);
            fieldDesc.innerHTML = highlightedText;
        }
    }

    // 移除高亮
    removeHighlight(option) {
        const fieldName = option.querySelector('.field-name');
        const fieldDesc = option.querySelector('.field-desc');

        if (fieldName) {
            fieldName.innerHTML = fieldName.textContent;
        }

        if (fieldDesc) {
            fieldDesc.innerHTML = fieldDesc.textContent;
        }
    }

    // 添加高亮标记
    addHighlight(text, searchTerm) {
        if (!searchTerm) return text;

        const regex = new RegExp(`(${searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
        return text.replace(regex, '<mark class="bg-warning">$1</mark>');
    }

    // 获取可用字段
    getAvailableFields() {
        if (!this.selectedTemplate || !this.selectedTemplate.jsonTemplate) {
            return [];
        }

        try {
            // 解析JSON模板获取字段
            const jsonObj = JSON.parse(this.selectedTemplate.jsonTemplate);
            const allFields = this.parseJsonFields(jsonObj);

            // 获取已绑定的字段
            const boundFields = new Set();
            if (this.configType === 'single') {
                if (this.currentConfig.fieldMappings) {
                    this.currentConfig.fieldMappings.forEach(mapping => {
                        boundFields.add(mapping.fieldKey);
                    });
                }
            } else {
                const currentStep = this.currentConfig.steps[this.currentStepIndex];
                currentStep.fieldMappings.forEach(mapping => {
                    boundFields.add(mapping.fieldKey);
                });
            }

            // 返回未绑定的字段，转换为正确的格式
            return allFields
                .filter(field => !boundFields.has(field.key))
                .map(field => ({
                    key: field.key,
                    name: field.key,
                    description: field.description,
                    type: field.type
                }));
        } catch (error) {
            console.error('解析模板字段失败:', error);
            return [];
        }
    }

    // 绑定字段到元素
    bindFieldToElement(element, field) {
        // 检查元素是否已经绑定了字段
        if (element.dataset.boundField) {
            const oldFieldKey = element.dataset.boundField;

            // 如果绑定的是同一个字段，直接返回
            if (oldFieldKey === field.key) {
                this.showAlert(`字段 "${field.name}" 已经绑定到此元素`, 'info');
                return;
            }

            // 如果绑定的是不同字段，询问是否替换
            if (!confirm(`此元素已绑定字段 "${oldFieldKey}"，是否替换为 "${field.name}"？`)) {
                return;
            }

            // 移除旧绑定
            this.removeBindingByFieldKey(oldFieldKey);
        }

        // 检查字段是否已经绑定到其他元素
        const existingBinding = this.findBindingByFieldKey(field.key);
        if (existingBinding) {
            if (!confirm(`字段 "${field.name}" 已绑定到其他元素，是否移除原绑定并绑定到当前元素？`)) {
                return;
            }

            // 移除原绑定
            this.removeBinding(existingBinding.selector);
        }

        // 在生成选择器之前，确保元素处于原始状态
        this.ensureElementInOriginalState(element);

        // 生成并验证选择器
        const selector = this.generateAndValidateSelector(element);

        // 恢复元素的绑定状态
        this.restoreElementBindingState(element);

        console.log('🎯 为元素生成选择器:', {
            element: element.tagName.toLowerCase(),
            id: element.id,
            classes: element.className,
            placeholder: element.getAttribute('placeholder'),
            text: element.textContent?.trim().substring(0, 30),
            selector: selector
        });

        // 收集元素的详细信息
        const elementInfo = this.collectElementInfo(element);

        // 检测控件类型
        const controlType = this.detectControlType(element);

        // 创建绑定记录
        const binding = {
            fieldKey: field.key,
            fieldName: field.name,
            elementId: element.id || '',
            elementName: element.name || '',
            selector: selector,
            elementType: element.tagName.toLowerCase(),
            controlType: controlType, // 新增：控件类型
            // 新增：详细的元素信息
            elementInfo: elementInfo
        };

        // 保存绑定
        if (this.configType === 'single') {
            if (!this.currentConfig.fieldMappings) {
                this.currentConfig.fieldMappings = [];
            }
            this.currentConfig.fieldMappings.push(binding);
        } else {
            const currentStep = this.currentConfig.steps[this.currentStepIndex];
            currentStep.fieldMappings.push(binding);
        }

        // 更新UI - 参考页面绑定功能的实现
        element.classList.add('bound');

        // 设置元素绑定标识
        element.dataset.boundField = field.key;

        // 设置样式
        element.style.backgroundColor = '#e7f3ff';
        element.style.borderColor = '#007bff';
        element.style.borderWidth = '2px';
        element.style.borderStyle = 'solid';

        // 保存原始title（如果还没保存的话）
        if (!element.getAttribute('data-original-title')) {
            element.setAttribute('data-original-title', element.title || '');
        }
        element.title = `绑定字段: ${field.name} (${field.description || field.key})`;

        // 更新placeholder显示绑定的字段名
        const originalPlaceholder = element.getAttribute('data-original-placeholder') || element.placeholder || '';
        if (!element.getAttribute('data-original-placeholder')) {
            // 保存原始placeholder
            element.setAttribute('data-original-placeholder', originalPlaceholder);
        }
        element.placeholder = `[${field.name}] ${originalPlaceholder}`;

        // 移除之前的绑定指示器
        const existingIndicator = element.querySelector('.binding-indicator');
        if (existingIndicator) {
            existingIndicator.remove();
        }

        // 添加绑定指示器（小徽章样式）
        const indicator = document.createElement('div');
        indicator.className = 'binding-indicator';
        indicator.innerHTML = `<i class="bi bi-check-circle-fill"></i>`;
        indicator.title = `已绑定字段: ${field.name} (${field.key})`;
        indicator.style.cssText = `
            position: absolute;
            top: -8px;
            right: -8px;
            background: #28a745;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            z-index: 1000;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            cursor: pointer;
        `;

        // 添加点击事件显示绑定详情
        indicator.addEventListener('click', (e) => {
            e.stopPropagation();
            this.showBindingDetails(element, field, binding);
        });

        // 确保元素有相对定位以便指示器正确定位
        if (getComputedStyle(element).position === 'static') {
            element.style.position = 'relative';
        }

        element.appendChild(indicator);

        // 更新字段列表状态
        const fieldItem = document.querySelector(`[data-field="${field.key}"]`);
        if (fieldItem) {
            fieldItem.classList.add('bound');
        }

        // 更新计数
        this.updateBoundFieldsCount();

        // 不自动退出绑定模式，允许继续绑定其他字段
        this.showAlert(`字段 "${field.name}" 已绑定成功！可以继续绑定其他字段`, 'success');

        // 更新绑定列表显示
        this.updateBindingsList();
    }

    // 显示绑定详情
    showBindingDetails(element, field, binding) {
        const modalHtml = `
            <div class="modal fade" id="bindingDetailsModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="bi bi-info-circle me-2"></i>绑定详情
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6><i class="bi bi-tag me-2"></i>字段信息</h6>
                                    <div class="card">
                                        <div class="card-body">
                                            <p><strong>字段名称：</strong>${field.name}</p>
                                            <p><strong>字段键值：</strong><code>${field.key}</code></p>
                                            <p><strong>字段描述：</strong>${field.description || '无'}</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <h6><i class="bi bi-code-square me-2"></i>元素信息</h6>
                                    <div class="card">
                                        <div class="card-body">
                                            <p><strong>元素类型：</strong>${element.tagName.toLowerCase()}</p>
                                            <p><strong>元素ID：</strong>${element.id || '无'}</p>
                                            <p><strong>元素名称：</strong>${element.name || '无'}</p>
                                            <p><strong>选择器：</strong><code>${binding.selector}</code></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-danger" onclick="smartFormManager.removeBinding('${binding.selector}')">
                                <i class="bi bi-trash"></i> 移除绑定
                            </button>
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 移除已存在的模态框
        const existingModal = document.getElementById('bindingDetailsModal');
        if (existingModal) {
            existingModal.remove();
        }

        // 添加新模态框
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('bindingDetailsModal'));
        modal.show();
    }

    // 更新绑定列表显示
    updateBindingsList() {
        // 这个方法会在页面UI中更新绑定列表
        // 具体实现取决于页面结构
        console.log('更新绑定列表显示');
    }

    // 移除绑定
    removeBinding(selector) {
        let bindings;
        if (this.configType === 'single') {
            bindings = this.currentConfig.fieldMappings || [];
        } else {
            const currentStep = this.currentConfig.steps[this.currentStepIndex];
            bindings = currentStep.fieldMappings || [];
        }

        // 找到并移除绑定
        const bindingIndex = bindings.findIndex(b => b.selector === selector);
        if (bindingIndex !== -1) {
            const binding = bindings[bindingIndex];
            bindings.splice(bindingIndex, 1);

            // 移除页面上的绑定指示器和样式
            if (this.currentIframeDoc) {
                const element = this.currentIframeDoc.querySelector(selector);
                if (element) {
                    // 恢复元素原始状态（参考页面绑定功能）
                    element.classList.remove('bound');
                    delete element.dataset.boundField;
                    element.style.backgroundColor = '';
                    element.style.borderColor = '';
                    element.style.borderWidth = '';
                    element.style.borderStyle = '';
                    element.title = '';

                    // 恢复原始placeholder
                    const originalPlaceholder = element.getAttribute('data-original-placeholder') || '';
                    element.placeholder = originalPlaceholder;

                    // 移除绑定指示器
                    const indicator = element.querySelector('.binding-indicator');
                    if (indicator) {
                        indicator.remove();
                    }
                }
            }

            // 更新字段列表状态
            const fieldItem = document.querySelector(`[data-field="${binding.fieldKey}"]`);
            if (fieldItem) {
                fieldItem.classList.remove('bound');
            }

            // 更新计数和列表
            this.updateBoundFieldsCount();
            this.updateBindingsList();

            this.showAlert('绑定已移除', 'info');

            // 关闭详情模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('bindingDetailsModal'));
            if (modal) {
                modal.hide();
            }
        }
    }

    // 页面切换时重置绑定状态
    resetBindingsOnPageChange() {
        console.log('🧹 页面切换，重置绑定状态');

        // 强制退出绑定模式
        if (this.bindingMode) {
            console.log('🚪 强制退出绑定模式');
            this.exitBindingMode();
        }

        // 强制退出动作模式
        if (this.actionMode) {
            console.log('🚪 强制退出动作模式');
            this.exitActionMode();
        }

        // 清理iframe引用和相关资源
        this.cleanupIframeReferences();

        // 清空当前配置的绑定
        if (this.configType === 'single') {
            if (this.currentConfig.fieldMappings) {
                this.currentConfig.fieldMappings = [];
            }
        } else {
            // 多步骤模式，清空当前步骤的绑定
            if (this.currentConfig.steps && this.currentConfig.steps[this.currentStepIndex]) {
                this.currentConfig.steps[this.currentStepIndex].fieldMappings = [];
            }
        }

        // 重置字段状态
        const fieldItems = document.querySelectorAll('.field-item');
        fieldItems.forEach(item => {
            item.classList.remove('bound');
        });

        // 更新计数显示
        this.updateBoundFieldsCount();

        console.log('✅ 绑定状态已重置');
    }

    // 清理iframe引用和相关资源
    cleanupIframeReferences() {
        console.log('🧹 清理iframe引用...');

        // 移除旧的监听器
        if (this.currentIframeDoc) {
            try {
                this.removeElementClickListeners(this.currentIframeDoc);
                console.log('✅ 已移除iframe监听器');
            } catch (error) {
                console.warn('⚠️ 清理iframe监听器时出错:', error);
            }
        }

        // 移除ESC键监听器
        this.removeEscapeKeyListener();

        // 清空iframe引用
        this.currentIframe = null;
        this.currentIframeDoc = null;

        // 清理加载遮罩
        this.hideBindingLoadingMask();
        this.stopLoadingDotsAnimation();

        console.log('✅ iframe引用已清理');
    }

    // 根据字段键查找绑定
    findBindingByFieldKey(fieldKey) {
        let bindings;
        if (this.configType === 'single') {
            bindings = this.currentConfig.fieldMappings || [];
        } else {
            const currentStep = this.currentConfig.steps[this.currentStepIndex];
            bindings = currentStep.fieldMappings || [];
        }

        return bindings.find(b => b.fieldKey === fieldKey);
    }

    // 根据字段键移除绑定
    removeBindingByFieldKey(fieldKey) {
        const binding = this.findBindingByFieldKey(fieldKey);
        if (binding) {
            this.removeBinding(binding.selector);
        }
    }

    // 显示绑定列表模态框
    showBindingsListModal() {
        let bindings;
        if (this.configType === 'single') {
            bindings = this.currentConfig.fieldMappings || [];
        } else {
            const currentStep = this.currentConfig.steps[this.currentStepIndex];
            bindings = currentStep.fieldMappings || [];
        }

        const modalHtml = `
            <div class="modal fade" id="bindingsListModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="bi bi-list-check me-2"></i>已绑定字段列表
                                <span class="badge bg-primary ms-2">${bindings.length}</span>
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            ${bindings.length === 0 ? `
                                <div class="text-center text-muted py-4">
                                    <i class="bi bi-inbox display-4"></i>
                                    <p class="mt-3">暂无绑定字段</p>
                                    <p class="text-muted">点击页面中的输入框开始绑定字段</p>
                                </div>
                            ` : `
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>字段名称</th>
                                                <th>元素类型</th>
                                                <th>元素标识</th>
                                                <th>选择器</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            ${bindings.map((binding, index) => `
                                                <tr>
                                                    <td>
                                                        <strong>${binding.fieldName}</strong>
                                                        <br><small class="text-muted">${binding.fieldKey}</small>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-info">${binding.elementType}</span>
                                                    </td>
                                                    <td>
                                                        ${binding.elementId ? `ID: ${binding.elementId}` : ''}
                                                        ${binding.elementName ? `<br>Name: ${binding.elementName}` : ''}
                                                        ${!binding.elementId && !binding.elementName ? '无' : ''}
                                                    </td>
                                                    <td>
                                                        <code class="small">${binding.selector}</code>
                                                    </td>
                                                    <td>
                                                        <button class="btn btn-sm btn-outline-primary me-1"
                                                                onclick="smartFormManager.highlightBinding('${binding.selector}')"
                                                                title="高亮显示">
                                                            <i class="bi bi-eye"></i>
                                                        </button>
                                                        <button class="btn btn-sm btn-outline-danger"
                                                                onclick="smartFormManager.removeBindingFromList('${binding.selector}')"
                                                                title="移除绑定">
                                                            <i class="bi bi-trash"></i>
                                                        </button>
                                                    </td>
                                                </tr>
                                            `).join('')}
                                        </tbody>
                                    </table>
                                </div>
                            `}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-outline-danger" onclick="smartFormManager.clearAllBindings()" ${bindings.length === 0 ? 'disabled' : ''}>
                                <i class="bi bi-trash"></i> 清空所有绑定
                            </button>
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 移除已存在的模态框
        const existingModal = document.getElementById('bindingsListModal');
        if (existingModal) {
            existingModal.remove();
        }

        // 添加新模态框
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('bindingsListModal'));
        modal.show();
    }

    // 高亮显示绑定的元素
    highlightBinding(selector) {
        if (!this.currentIframeDoc) return;

        // 移除之前的高亮
        this.currentIframeDoc.querySelectorAll('.highlight-binding').forEach(el => {
            el.classList.remove('highlight-binding');
        });

        // 添加高亮样式
        const element = this.currentIframeDoc.querySelector(selector);
        if (element) {
            element.classList.add('highlight-binding');

            // 滚动到元素位置
            element.scrollIntoView({ behavior: 'smooth', block: 'center' });

            // 3秒后移除高亮
            setTimeout(() => {
                element.classList.remove('highlight-binding');
            }, 3000);

            this.showAlert('已高亮显示绑定元素', 'info');
        } else {
            this.showAlert('未找到绑定元素', 'warning');
        }
    }

    // 从列表中移除绑定
    removeBindingFromList(selector) {
        if (confirm('确定要移除这个绑定吗？')) {
            this.removeBinding(selector);

            // 刷新绑定列表模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('bindingsListModal'));
            if (modal) {
                modal.hide();
                setTimeout(() => {
                    this.showBindingsListModal();
                }, 300);
            }
        }
    }

    // 清空所有绑定
    clearAllBindings() {
        if (confirm('确定要清空所有绑定吗？此操作不可恢复。')) {
            let bindings;
            if (this.configType === 'single') {
                bindings = this.currentConfig.fieldMappings || [];
                this.currentConfig.fieldMappings = [];
            } else {
                const currentStep = this.currentConfig.steps[this.currentStepIndex];
                bindings = currentStep.fieldMappings || [];
                currentStep.fieldMappings = [];
            }

            // 移除页面上的所有绑定指示器和样式
            if (this.currentIframeDoc) {
                bindings.forEach(binding => {
                    const element = this.currentIframeDoc.querySelector(binding.selector);
                    if (element) {
                        // 恢复元素原始状态（参考页面绑定功能）
                        element.classList.remove('bound');
                        delete element.dataset.boundField;
                        element.style.backgroundColor = '';
                        element.style.borderColor = '';
                        element.style.borderWidth = '';
                        element.style.borderStyle = '';
                        element.title = '';

                        // 恢复原始placeholder
                        const originalPlaceholder = element.getAttribute('data-original-placeholder') || '';
                        element.placeholder = originalPlaceholder;

                        // 移除绑定指示器
                        const indicator = element.querySelector('.binding-indicator');
                        if (indicator) {
                            indicator.remove();
                        }
                    }
                });
            }

            // 更新字段列表状态
            document.querySelectorAll('.field-item').forEach(item => {
                item.classList.remove('bound');
            });

            // 更新计数
            this.updateBoundFieldsCount();

            this.showAlert('所有绑定已清空', 'info');

            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('bindingsListModal'));
            if (modal) {
                modal.hide();
            }
        }
    }

    // 生成CSS选择器 - 增强版智能规则
    generateSelector(element) {
        const tagName = element.tagName.toLowerCase();
        console.log(`🎯 开始为${tagName}元素生成选择器`, {
            id: element.id,
            name: element.name,
            className: element.className,
            placeholder: element.getAttribute('placeholder'),
            text: element.textContent?.trim().substring(0, 30)
        });

        // 规则1: 优先使用ID（如果存在且唯一）
        if (element.id) {
            const idSelector = `#${element.id}`;
            if (this.isUniqueSelector(idSelector)) {
                console.log(`✅ 使用ID选择器: ${idSelector}`);
                return idSelector;
            } else {
                console.log(`⚠️ ID选择器不唯一: ${idSelector}`);
            }
        }

        // 规则2: 检查name属性（如果唯一或记录位置）
        if (element.name && ['input', 'select', 'textarea'].includes(tagName)) {
            const nameResult = this.generateNameBasedSelector(element);
            if (nameResult) {
                console.log(`✅ 使用Name选择器: ${nameResult}`);
                return nameResult;
            }
        }

        // 规则3: 优先使用label关联的精确选择器
        if (['input', 'select', 'textarea'].includes(tagName)) {
            const labelResult = this.generateEnhancedLabelBasedSelector(element);
            if (labelResult) {
                console.log(`✅ 使用增强Label关联选择器: ${labelResult}`);
                return labelResult;
            }
        }

        // 规则4: 对于表单元素，检查placeholder唯一性（增强版）
        if (['input', 'select', 'textarea'].includes(tagName)) {
            const placeholderResult = this.generateEnhancedPlaceholderBasedSelector(element);
            if (placeholderResult) {
                console.log(`✅ 使用增强Placeholder选择器: ${placeholderResult}`);
                return placeholderResult;
            }
        }

        // 规则5: 检查自定义唯一属性（data-*, aria-*, 等）
        const customAttrResult = this.generateCustomAttributeSelector(element);
        if (customAttrResult) {
            console.log(`✅ 使用自定义属性选择器: ${customAttrResult}`);
            return customAttrResult;
        }

        // 规则6: 对于按钮，检查文本内容唯一性
        if (tagName === 'button' || (tagName === 'a' && element.getAttribute('role') === 'button')) {
            const textResult = this.generateTextBasedSelector(element);
            if (textResult) {
                console.log(`✅ 使用文本选择器: ${textResult}`);
                return textResult;
            }
        }

        // 规则7: 生成多层级上下文选择器
        const contextResult = this.generateMultiLevelContextSelector(element);
        console.log(`✅ 使用多层级上下文选择器: ${contextResult}`);
        return contextResult;
    }

    // 生成基于name的选择器
    generateNameBasedSelector(element) {
        const name = element.name;
        const tagName = element.tagName.toLowerCase();

        // 查找所有同name的元素
        const doc = this.currentIframeDoc || document;
        const sameNameElements = doc.querySelectorAll(`${tagName}[name="${name}"]`);

        if (sameNameElements.length === 1) {
            // 如果只有一个，直接使用name选择器
            return `${tagName}[name="${name}"]`;
        } else if (sameNameElements.length > 1) {
            // 如果有多个，记录是第几个
            const index = Array.from(sameNameElements).indexOf(element);
            if (index >= 0) {
                return `${tagName}[name="${name}"]:nth-of-type(${index + 1})`;
            }
        }

        return null;
    }

    // 生成基于自定义属性的选择器（通用版本）
    generateCustomAttributeSelector(element) {
        const tagName = element.tagName.toLowerCase();
        const doc = this.currentIframeDoc || document;

        // 收集元素的所有属性，按重要性排序
        const allAttributes = [];

        // 遍历元素的所有原始属性（过滤掉临时添加的属性）
        for (let i = 0; i < element.attributes.length; i++) {
            const attr = element.attributes[i];
            const name = attr.name;
            const value = attr.value;

            // 跳过不适合做选择器的属性（包括我们临时添加的属性）
            if (this.shouldSkipAttribute(name, value)) {
                console.log(`🔍 跳过属性: ${name}="${value}" (临时或无效属性)`);
                continue;
            }

            // 计算属性的重要性得分
            const importance = this.calculateAttributeImportance(name, value);
            allAttributes.push({ name, value, importance });
            console.log(`🔍 收集原始属性: ${name}="${value}" (重要性:${importance})`);
        }

        // 按重要性排序
        allAttributes.sort((a, b) => b.importance - a.importance);

        console.log(`🔍 元素的所有有效属性:`, allAttributes.map(a => `${a.name}="${a.value}" (重要性:${a.importance})`));

        // 尝试每个属性作为唯一选择器
        for (const attr of allAttributes) {
            const selector = `${tagName}[${attr.name}="${attr.value}"]`;
            const matchingElements = doc.querySelectorAll(selector);

            if (matchingElements.length === 1) {
                console.log(`🎯 找到唯一属性选择器: ${selector}`);
                return selector;
            }

            // 如果不唯一但匹配数量合理，尝试组合
            if (matchingElements.length > 1 && matchingElements.length <= 5) {
                console.log(`🔍 属性 ${attr.name} 有 ${matchingElements.length} 个匹配，尝试组合`);

                const combinedSelector = this.generateCombinedAttributeSelector(element, attr.name, attr.value);
                if (combinedSelector) {
                    return combinedSelector;
                }
            }
        }

        return null;
    }

    // 判断是否应该跳过某个属性
    shouldSkipAttribute(name, value) {
        // 跳过样式相关
        if (name === 'style' || name === 'class') return true;

        // 跳过空值
        if (!value || value.trim() === '') return true;

        // 跳过智能表单添加的临时属性（重要：这些不是原始页面的属性）
        if (name.includes('smart-form') || name.includes('action-mode') || name.includes('clickable')) return true;

        // 跳过绑定标识属性（这是我们临时添加的，不应该用于选择器）
        if (name === 'data-bound-field') return true;

        // 跳过原始值保存属性（我们修改元素时临时保存的原始值）
        if (name === 'data-original-placeholder') return true;
        if (name === 'data-original-value') return true;
        if (name === 'data-original-text') return true;
        if (name === 'data-original-title') return true;

        // 跳过其他临时标识属性
        if (name.includes('binding-mode') || name.includes('selected-element')) return true;

        // 跳过一些动态属性
        if (name.startsWith('aria-describedby') && value.includes('tooltip')) return true;

        return false;
    }

    // 计算属性的重要性得分
    calculateAttributeImportance(name, value) {
        let score = 0;

        // 测试相关属性（最高优先级）
        if (name.match(/^data-(testid|cy|test|automation|qa)$/)) score += 100;

        // 唯一标识属性
        if (name === 'id') score += 90;
        if (name.match(/^data-(id|uuid|key)$/)) score += 80;

        // 功能相关属性
        if (name.match(/^data-(toggle|target|action|command)$/)) score += 70;
        if (name.match(/^(role|type)$/)) score += 60;

        // 框架相关属性
        if (name.match(/^(ng-|v-|x-|data-bind)/)) score += 50;

        // 无障碍属性
        if (name.match(/^aria-/)) score += 40;

        // 其他data属性
        if (name.startsWith('data-')) score += 30;

        // 表单相关属性
        if (name.match(/^(name|for|autocomplete)$/)) score += 20;

        // 值的唯一性加分
        if (value.length > 10) score += 10; // 长值通常更唯一
        if (value.includes('#') || value.includes('.')) score += 5; // 选择器样式的值

        return score;
    }

    // 生成组合属性选择器（通用版本）
    generateCombinedAttributeSelector(element, primaryAttr, primaryValue) {
        const tagName = element.tagName.toLowerCase();
        const doc = this.currentIframeDoc || document;

        // 获取元素的其他有效属性
        const otherAttributes = [];
        for (let i = 0; i < element.attributes.length; i++) {
            const attr = element.attributes[i];
            if (attr.name !== primaryAttr && !this.shouldSkipAttribute(attr.name, attr.value)) {
                const importance = this.calculateAttributeImportance(attr.name, attr.value);
                otherAttributes.push({ name: attr.name, value: attr.value, importance });
            }
        }

        // 按重要性排序
        otherAttributes.sort((a, b) => b.importance - a.importance);

        // 尝试与其他属性组合
        for (const secondaryAttr of otherAttributes) {
            if (secondaryAttr.name === 'class') {
                // 对于class，使用有意义的类名
                const meaningfulClasses = this.getMeaningfulClasses(element);
                if (meaningfulClasses.length > 0) {
                    const selector = `${tagName}[${primaryAttr}="${primaryValue}"].${meaningfulClasses[0]}`;
                    const matchingElements = doc.querySelectorAll(selector);
                    if (matchingElements.length === 1) {
                        console.log(`🎯 组合属性选择器成功: ${selector}`);
                        return selector;
                    }
                }
            } else {
                const selector = `${tagName}[${primaryAttr}="${primaryValue}"][${secondaryAttr.name}="${secondaryAttr.value}"]`;
                const matchingElements = doc.querySelectorAll(selector);
                if (matchingElements.length === 1) {
                    console.log(`🎯 组合属性选择器成功: ${selector}`);
                    return selector;
                }
            }
        }

        return null;
    }

    // 生成增强的基于placeholder的选择器
    generateEnhancedPlaceholderBasedSelector(element) {
        const placeholder = element.getAttribute('placeholder');
        if (!placeholder) return null;

        const tagName = element.tagName.toLowerCase();
        // 清理placeholder中的字段名前缀
        const cleanPlaceholder = placeholder.replace(/^\[[\w.]+\]\s*/, '');

        console.log(`📝 处理placeholder: "${cleanPlaceholder}"`);

        // 检查清理后的placeholder是否唯一
        const doc = this.currentIframeDoc || document;
        const samePlaceholderElements = doc.querySelectorAll(`${tagName}[placeholder*="${cleanPlaceholder}"]`);

        if (samePlaceholderElements.length === 1) {
            return `${tagName}[placeholder="${cleanPlaceholder}"]`;
        }

        console.log(`⚠️ Placeholder选择器不唯一，找到${samePlaceholderElements.length}个相同元素，尝试增强选择器`);

        // 如果不唯一，生成多种增强选择器
        const candidateSelectors = [];

        // 1. 结合type属性
        const type = element.getAttribute('type');
        if (type) {
            candidateSelectors.push(`${tagName}[type="${type}"][placeholder="${cleanPlaceholder}"]`);
        }

        // 2. 结合name属性
        const name = element.getAttribute('name');
        if (name) {
            candidateSelectors.push(`${tagName}[name="${name}"][placeholder="${cleanPlaceholder}"]`);
        }

        // 3. 结合父容器
        const container = this.findFormItemContainer(element);
        if (container) {
            const containerSelector = this.generateElementBasicSelector(container);
            candidateSelectors.push(`${containerSelector} ${tagName}[placeholder="${cleanPlaceholder}"]`);

            // 4. 结合容器和位置
            const inputsInContainer = Array.from(container.querySelectorAll(`${tagName}[placeholder*="${cleanPlaceholder}"]`));
            const elementIndex = inputsInContainer.indexOf(element);
            if (elementIndex >= 0 && inputsInContainer.length > 1) {
                candidateSelectors.push(`${containerSelector} ${tagName}[placeholder="${cleanPlaceholder}"]:nth-of-type(${elementIndex + 1})`);
            }
        }

        // 5. 结合兄弟元素位置
        const parent = element.parentElement;
        if (parent) {
            const siblings = Array.from(parent.querySelectorAll(`${tagName}[placeholder*="${cleanPlaceholder}"]`));
            const siblingIndex = siblings.indexOf(element);
            if (siblingIndex >= 0 && siblings.length > 1) {
                const parentSelector = this.generateElementBasicSelector(parent);
                candidateSelectors.push(`${parentSelector} > ${tagName}[placeholder="${cleanPlaceholder}"]:nth-of-type(${siblingIndex + 1})`);
            }
        }

        // 检查哪个选择器唯一
        for (const selector of candidateSelectors) {
            if (this.isUniqueSelector(selector)) {
                console.log(`✅ 找到唯一的增强placeholder选择器: ${selector}`);
                return selector;
            }
        }

        console.log(`⚠️ 所有增强placeholder选择器都不唯一`);
        return null;
    }

    // 生成增强的基于label关联的选择器
    generateEnhancedLabelBasedSelector(element) {
        const labelInfo = this.findAssociatedLabel(element);
        if (!labelInfo) return null;

        const tagName = element.tagName.toLowerCase();
        const labelText = labelInfo.text.replace(/[:\s()（）]+$/, '').trim(); // 移除末尾的冒号、空格、括号

        console.log(`🏷️ 找到关联label: "${labelText}", 类型: ${labelInfo.type}`);

        if (labelInfo.type === 'for' && element.id) {
            // 使用for关联的选择器
            const forSelector = `${tagName}#${element.id}`;
            if (this.isUniqueSelector(forSelector)) {
                return forSelector;
            }
        }

        if (labelInfo.type === 'sibling' && labelInfo.container) {
            // 使用容器 + label文本的组合，生成多种精确选择器
            const containerSelector = this.generateElementBasicSelector(labelInfo.container);

            // 生成多种候选选择器，按精确度排序
            const candidateSelectors = [];

            // 1. 基于label文本的精确定位（通过for属性）
            if (labelInfo.element.getAttribute('for')) {
                const forId = labelInfo.element.getAttribute('for');
                candidateSelectors.push(`${containerSelector} ${tagName}#${forId}`);
            }

            // 2. 基于容器内label文本 + 输入框位置
            const labelSelector = this.generateElementBasicSelector(labelInfo.element);
            candidateSelectors.push(`${containerSelector} ${labelSelector} ~ ${tagName}`);
            candidateSelectors.push(`${containerSelector} ${labelSelector} + * ${tagName}`);

            // 3. 基于容器内的输入框顺序
            const inputsInContainer = Array.from(labelInfo.container.querySelectorAll(tagName));
            const elementIndex = inputsInContainer.indexOf(element);
            if (elementIndex >= 0 && inputsInContainer.length > 1) {
                candidateSelectors.push(`${containerSelector} ${tagName}:nth-of-type(${elementIndex + 1})`);
            }

            // 4. 基于placeholder和容器的组合
            const placeholder = element.getAttribute('placeholder');
            if (placeholder) {
                candidateSelectors.push(`${containerSelector} ${tagName}[placeholder="${placeholder}"]`);
            }

            // 5. 基本容器选择器
            candidateSelectors.push(`${containerSelector} ${tagName}`);

            // 检查哪个选择器唯一且有效
            for (const selector of candidateSelectors) {
                if (this.isUniqueSelector(selector)) {
                    console.log(`✅ 找到唯一的label关联选择器: ${selector}`);
                    return selector;
                }
            }

            // 如果都不唯一，返回最具体的一个
            console.log(`⚠️ 所有label关联选择器都不唯一，返回最具体的: ${candidateSelectors[0]}`);
            return candidateSelectors[0] || `${containerSelector} ${tagName}`;
        }

        return null;
    }

    // 生成基于文本内容的选择器（主要用于按钮）
    generateTextBasedSelector(element) {
        const text = element.textContent?.trim();
        if (!text || text.length === 0) {
            console.log('⚠️ 元素没有文本内容，跳过文本选择器');
            return null;
        }

        const tagName = element.tagName.toLowerCase();
        const cleanText = text.replace(/\s+/g, ' ').trim();
        console.log(`🔍 检查文本选择器: "${cleanText}"`);

        // 检查相同文本的按钮数量（包括所有iframe）
        const doc = this.currentIframeDoc || document;
        let sameTextButtons = Array.from(doc.querySelectorAll(tagName)).filter(btn => {
            const btnText = btn.textContent?.trim();
            if (!btnText) return false;
            // 只检查可见的按钮
            const style = window.getComputedStyle(btn);
            const isVisible = style.display !== 'none' && style.visibility !== 'hidden' && style.opacity !== '0';
            return isVisible && btnText.replace(/\s+/g, ' ') === cleanText;
        });

        // 如果在主文档中，还需要检查所有iframe
        if (doc === document) {
            const iframes = document.querySelectorAll('iframe');
            for (const iframe of iframes) {
                try {
                    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                    const iframeButtons = Array.from(iframeDoc.querySelectorAll(tagName)).filter(btn => {
                        const btnText = btn.textContent?.trim();
                        if (!btnText) return false;
                        const style = iframe.contentWindow.getComputedStyle(btn);
                        const isVisible = style.display !== 'none' && style.visibility !== 'hidden' && style.opacity !== '0';
                        return isVisible && btnText.replace(/\s+/g, ' ') === cleanText;
                    });
                    sameTextButtons = sameTextButtons.concat(iframeButtons);
                } catch (error) {
                    console.warn('无法访问iframe内容:', error);
                }
            }
        }

        console.log(`🔍 找到${sameTextButtons.length}个相同文本的可见${tagName}元素`);

        if (sameTextButtons.length === 1) {
            // 如果文本唯一，结合类名生成选择器
            const meaningfulClasses = this.getMeaningfulClasses(element);
            if (meaningfulClasses.length > 0) {
                const selector = `${tagName}.${meaningfulClasses.slice(0, 2).join('.')}`;
                console.log(`✅ 生成文本+类名选择器: ${selector}`);
                return selector;
            } else {
                // 没有有意义的类名，使用type属性
                const type = element.getAttribute('type');
                if (type) {
                    const selector = `${tagName}[type="${type}"]`;
                    console.log(`✅ 生成文本+type选择器: ${selector}`);
                    return selector;
                }
            }
        }

        console.log('⚠️ 文本不唯一或无法生成有效选择器');
        return null;
    }

    // 生成多层级上下文选择器
    generateMultiLevelContextSelector(element) {
        const tagName = element.tagName.toLowerCase();
        console.log(`🔍 生成多层级上下文选择器`);

        // 收集元素的所有可用信息
        const elementInfo = {
            id: element.id,
            name: element.name,
            className: element.className,
            type: element.getAttribute('type'),
            placeholder: element.getAttribute('placeholder'),
            text: element.textContent?.trim()
        };

        // 生成候选选择器列表，按精确度排序
        const candidateSelectors = [];

        // 1. 基于多层级父容器的精确定位
        const multiLevelSelectors = this.generateMultiLevelParentSelectors(element);
        candidateSelectors.push(...multiLevelSelectors);

        // 2. 基于兄弟元素位置的精确定位
        const siblingSelectors = this.generateSiblingPositionSelectors(element);
        candidateSelectors.push(...siblingSelectors);

        // 3. 基于属性组合的选择器
        const attributeSelectors = this.generateCombinedAttributeSelectors(element);
        candidateSelectors.push(...attributeSelectors);

        // 4. 基础选择器作为后备
        const baseSelector = this.generateBaseSelector(element);
        if (baseSelector) {
            candidateSelectors.push(baseSelector);
        }

        // 检查每个候选选择器的唯一性
        for (const selector of candidateSelectors) {
            if (selector && this.isUniqueSelector(selector)) {
                console.log(`✅ 找到唯一的多层级选择器: ${selector}`);
                return selector;
            }
        }

        // 如果都不唯一，返回最具体的一个
        const fallbackSelector = candidateSelectors[0] || tagName;
        console.log(`⚠️ 所有选择器都不唯一，使用后备选择器: ${fallbackSelector}`);
        return fallbackSelector;
    }

    // 生成表单元素选择器（基于label关联和上下文）
    generateFormElementSelector(element) {
        const tagName = element.tagName.toLowerCase();
        const selectors = [];

        // 1. 查找关联的label
        const labelInfo = this.findAssociatedLabel(element);
        if (labelInfo) {
            // 基于label生成选择器
            const labelSelector = this.generateLabelBasedSelectorWithInfo(element, labelInfo);
            if (labelSelector) {
                selectors.push(labelSelector);
            }
        }

        // 2. 基于父容器和属性组合
        const containerSelector = this.generateContainerBasedSelector(element);
        if (containerSelector) {
            selectors.push(containerSelector);
        }

        // 3. 基于多重属性组合
        const attributeSelector = this.generateMultiAttributeSelector(element);
        if (attributeSelector) {
            selectors.push(attributeSelector);
        }

        // 返回第一个唯一的选择器
        for (const selector of selectors) {
            if (this.isUniqueSelector(selector)) {
                return selector;
            }
        }

        return selectors[0] || null;
    }

    // 生成按钮选择器（基于文本内容和上下文）
    generateButtonSelector(element) {
        const selectors = [];
        const buttonText = element.textContent?.trim() || '';

        // 1. 基于文本内容生成选择器（存储文本信息用于后续匹配）
        if (buttonText) {
            const textSelector = this.generateTextBasedSelector(element);
            if (textSelector) {
                selectors.push(textSelector);
            }
        }

        // 2. 基于父容器和类名组合
        const containerSelector = this.generateContainerBasedSelector(element);
        if (containerSelector) {
            selectors.push(containerSelector);
        }

        // 3. 基于多重属性组合
        const attributeSelector = this.generateMultiAttributeSelector(element);
        if (attributeSelector) {
            selectors.push(attributeSelector);
        }

        // 返回第一个唯一的选择器
        for (const selector of selectors) {
            if (this.isUniqueSelector(selector)) {
                return selector;
            }
        }

        return selectors[0] || null;
    }

    // 获取表单元素选择器（包含第三方控件）
    getFormElementSelector() {
        return `
            input, select, textarea,
            .select2-container,
            .bootstrap-select,
            .selectpicker,
            .chosen-container,
            .multiselect-container,
            [data-toggle="dropdown"],
            [data-bs-toggle="dropdown"],
            .dropdown-toggle,
            .form-control.dropdown,
            .custom-select,
            .nice-select,
            .selectric,
            .selectize-control,
            .ui-selectmenu-button,
            [role="combobox"],
            [role="listbox"],
            [aria-haspopup="listbox"],
            [class*="select"],
            [class*="dropdown"],
            [class*="picker"]
        `.replace(/\s+/g, ' ').trim();
    }

    // 检查是否为可编辑的表单元素（包含第三方控件）
    isEditableFormElement(element) {
        const tagName = element.tagName.toLowerCase();
        const type = element.type ? element.type.toLowerCase() : '';

        // 原生表单元素检查
        if (tagName === 'textarea') {
            return !element.readOnly && !element.disabled;
        }

        if (tagName === 'select') {
            return !element.disabled;
        }

        if (tagName === 'input') {
            // 排除不需要填充的input类型
            const excludedTypes = [
                'submit', 'button', 'reset', 'image', 'file', 'hidden'
            ];

            if (excludedTypes.includes(type)) {
                return false;
            }

            return !element.readOnly && !element.disabled;
        }

        // 第三方控件检查
        return this.isThirdPartyFormControl(element);
    }

    // 检查是否为第三方表单控件
    isThirdPartyFormControl(element) {
        const classList = Array.from(element.classList);
        const className = element.className.toLowerCase();

        // Select2控件
        if (classList.includes('select2-container') ||
            element.querySelector('.select2-selection')) {
            return true;
        }

        // Bootstrap Select控件
        if (classList.includes('bootstrap-select') ||
            classList.includes('selectpicker') ||
            element.querySelector('.dropdown-toggle')) {
            return true;
        }

        // Chosen控件
        if (classList.includes('chosen-container')) {
            return true;
        }

        // 其他常见的第三方控件
        const thirdPartyPatterns = [
            'multiselect', 'nice-select', 'selectric', 'selectize',
            'ui-selectmenu', 'custom-select'
        ];

        for (const pattern of thirdPartyPatterns) {
            if (className.includes(pattern)) {
                return true;
            }
        }

        // 检查ARIA属性
        if (element.getAttribute('role') === 'combobox' ||
            element.getAttribute('role') === 'listbox' ||
            element.getAttribute('aria-haspopup') === 'listbox') {
            return true;
        }

        // 检查data属性
        if (element.getAttribute('data-toggle') === 'dropdown' ||
            element.getAttribute('data-bs-toggle') === 'dropdown') {
            return true;
        }

        return false;
    }

    // 检测控件类型
    detectControlType(element) {
        const tagName = element.tagName.toLowerCase();
        const classList = Array.from(element.classList);
        const className = element.className.toLowerCase();

        // 原生HTML控件
        if (tagName === 'input') {
            return `input-${element.type || 'text'}`;
        }
        if (tagName === 'select') {
            return 'select-native';
        }
        if (tagName === 'textarea') {
            return 'textarea';
        }

        // Select2控件
        if (classList.includes('select2-container')) {
            return 'select2';
        }

        // Bootstrap Select控件
        if (classList.includes('bootstrap-select') || classList.includes('selectpicker')) {
            return 'bootstrap-select';
        }

        // Chosen控件
        if (classList.includes('chosen-container')) {
            return 'chosen';
        }

        // 其他第三方控件
        const controlTypes = [
            { pattern: 'multiselect', type: 'multiselect' },
            { pattern: 'nice-select', type: 'nice-select' },
            { pattern: 'selectric', type: 'selectric' },
            { pattern: 'selectize', type: 'selectize' },
            { pattern: 'ui-selectmenu', type: 'jquery-ui-selectmenu' },
            { pattern: 'custom-select', type: 'custom-select' }
        ];

        for (const control of controlTypes) {
            if (className.includes(control.pattern)) {
                return control.type;
            }
        }

        // 基于ARIA属性判断
        if (element.getAttribute('role') === 'combobox') {
            return 'aria-combobox';
        }
        if (element.getAttribute('role') === 'listbox') {
            return 'aria-listbox';
        }

        // 基于data属性判断
        if (element.getAttribute('data-toggle') === 'dropdown' ||
            element.getAttribute('data-bs-toggle') === 'dropdown') {
            return 'dropdown';
        }

        // 默认返回未知类型
        return 'unknown';
    }

    // 获取第三方控件的原始select元素
    getOriginalSelectElement(element) {
        const controlType = this.detectControlType(element);

        switch (controlType) {
            case 'select2':
                // Select2通常会隐藏原始select，通过data-select2-id或相邻元素查找
                const select2Id = element.getAttribute('data-select2-id');
                if (select2Id) {
                    return document.getElementById(select2Id);
                }
                // 查找相邻的隐藏select
                const prevSelect = element.previousElementSibling;
                if (prevSelect && prevSelect.tagName.toLowerCase() === 'select') {
                    return prevSelect;
                }
                break;

            case 'bootstrap-select':
                // Bootstrap Select通常将原始select隐藏在容器内
                const hiddenSelect = element.querySelector('select');
                if (hiddenSelect) {
                    return hiddenSelect;
                }
                break;

            case 'chosen':
                // Chosen控件的原始select通常在前面
                const chosenSelect = element.previousElementSibling;
                if (chosenSelect && chosenSelect.tagName.toLowerCase() === 'select') {
                    return chosenSelect;
                }
                break;

            default:
                // 尝试在父容器中查找select
                const parentSelect = element.closest('div').querySelector('select');
                if (parentSelect) {
                    return parentSelect;
                }
        }

        return null;
    }

    // 查找关联的label
    findAssociatedLabel(element) {
        // 1. 通过for属性查找label
        if (element.id) {
            const doc = this.currentIframeDoc || document;
            const label = doc.querySelector(`label[for="${element.id}"]`);
            if (label) {
                return {
                    type: 'for',
                    element: label,
                    text: label.textContent?.trim() || '',
                    selector: this.generateElementSelector(label)
                };
            }
        }

        // 2. 查找父级label
        let parent = element.parentElement;
        while (parent && parent.tagName.toLowerCase() !== 'body') {
            if (parent.tagName.toLowerCase() === 'label') {
                return {
                    type: 'parent',
                    element: parent,
                    text: parent.textContent?.trim() || '',
                    selector: this.generateElementSelector(parent)
                };
            }
            parent = parent.parentElement;
        }

        // 3. 查找同级或相邻的label
        const formItem = this.findFormItemContainer(element);
        if (formItem) {
            const label = formItem.querySelector('label');
            if (label) {
                return {
                    type: 'sibling',
                    element: label,
                    text: label.textContent?.trim() || '',
                    selector: this.generateElementSelector(label),
                    container: formItem
                };
            }
        }

        return null;
    }

    // 查找表单项容器（如.el-form-item）
    findFormItemContainer(element) {
        let parent = element.parentElement;
        while (parent && parent.tagName.toLowerCase() !== 'body') {
            const classes = Array.from(parent.classList);
            // 检查是否为表单项容器
            if (classes.some(cls =>
                cls.includes('form-item') ||
                cls.includes('field') ||
                cls.includes('input-group') ||
                cls.includes('form-group')
            )) {
                return parent;
            }
            parent = parent.parentElement;
        }
        return null;
    }

    // 基于label生成选择器（带label信息参数版本）
    generateLabelBasedSelectorWithInfo(element, labelInfo) {
        const tagName = element.tagName.toLowerCase();
        const selectors = [];

        if (labelInfo.type === 'for' && element.id) {
            // 使用label[for] + input[id]的组合
            selectors.push(`label[for="${element.id}"] ~ * ${tagName}#${element.id}`);
            selectors.push(`${tagName}#${element.id}`);
        } else if (labelInfo.type === 'sibling' && labelInfo.container) {
            // 使用容器内的label + input组合
            const containerSelector = this.generateElementSelector(labelInfo.container);
            const labelText = labelInfo.text.replace(/[:\s]+$/, ''); // 移除末尾的冒号和空格

            // 生成基于容器和label文本的选择器
            const elementSelector = this.generateElementSelector(element);
            selectors.push(`${containerSelector} ${elementSelector}`);

            // 如果有placeholder，结合使用
            const placeholder = element.getAttribute('placeholder');
            if (placeholder) {
                const cleanPlaceholder = placeholder.replace(/^\[[\w.]+\]\s*/, '');
                selectors.push(`${containerSelector} ${tagName}[placeholder="${cleanPlaceholder}"]`);
            }
        }

        return selectors.find(s => s && this.isUniqueSelector(s)) || selectors[0];
    }

    // 基于容器生成选择器
    generateContainerBasedSelector(element) {
        const tagName = element.tagName.toLowerCase();
        const containers = [];

        // 查找有意义的父容器
        let parent = element.parentElement;
        let depth = 0;
        while (parent && depth < 5) {
            const parentClasses = this.getMeaningfulClasses(parent);
            if (parentClasses.length > 0) {
                const parentSelector = `${parent.tagName.toLowerCase()}.${parentClasses[0]}`;
                containers.push(parentSelector);
            }
            parent = parent.parentElement;
            depth++;
        }

        if (containers.length === 0) return null;

        // 生成元素自身的选择器
        const elementSelector = this.generateElementSelector(element);

        // 组合容器和元素选择器
        const containerPath = containers.slice(0, 2).join(' > '); // 最多使用2层容器
        return `${containerPath} ${elementSelector}`;
    }

    // 基于多重属性生成选择器
    generateMultiAttributeSelector(element) {
        const tagName = element.tagName.toLowerCase();
        const attributes = [];

        // 收集所有有用的属性
        const importantAttrs = [
            'type', 'placeholder', 'name', 'class', 'role', 'aria-label',
            'data-testid', 'data-cy', 'data-test', 'autocomplete'
        ];

        importantAttrs.forEach(attr => {
            let value = element.getAttribute(attr);
            if (value) {
                if (attr === 'placeholder') {
                    // 清理placeholder中的字段名前缀
                    value = value.replace(/^\[[\w.]+\]\s*/, '');
                }
                if (attr === 'class') {
                    // 只使用有意义的类名
                    const meaningfulClasses = this.getMeaningfulClasses(element);
                    if (meaningfulClasses.length > 0) {
                        attributes.push(`.${meaningfulClasses.slice(0, 3).join('.')}`);
                    }
                } else {
                    attributes.push(`[${attr}="${value}"]`);
                }
            }
        });

        if (attributes.length === 0) return null;

        // 组合标签名和属性
        return `${tagName}${attributes.join('')}`;
    }



    // 生成元素的基本选择器
    generateElementSelector(element) {
        const tagName = element.tagName.toLowerCase();

        if (element.id) {
            return `${tagName}#${element.id}`;
        }

        const classes = this.getMeaningfulClasses(element);
        if (classes.length > 0) {
            return `${tagName}.${classes.slice(0, 2).join('.')}`;
        }

        return tagName;
    }

    // 生成元素的基础选择器（不包含标签名）
    generateElementBasicSelector(element) {
        if (element.id) {
            return `#${element.id}`;
        }

        const classes = this.getMeaningfulClasses(element);
        if (classes.length > 0) {
            return `.${classes[0]}`;
        }

        return element.tagName.toLowerCase();
    }

    // 生成多层级父容器选择器
    generateMultiLevelParentSelectors(element) {
        const tagName = element.tagName.toLowerCase();
        const selectors = [];

        // 收集多层级父容器信息
        const parentChain = [];
        let current = element.parentElement;
        let depth = 0;

        while (current && depth < 5 && current.tagName.toLowerCase() !== 'body') {
            const parentInfo = {
                element: current,
                selector: this.generateElementBasicSelector(current),
                classes: this.getMeaningfulClasses(current),
                depth: depth
            };
            parentChain.push(parentInfo);
            current = current.parentElement;
            depth++;
        }

        if (parentChain.length === 0) return selectors;

        // 生成基于不同层级组合的选择器
        for (let i = 0; i < Math.min(parentChain.length, 3); i++) {
            const parentPath = parentChain.slice(0, i + 1)
                .map(p => p.selector)
                .reverse()
                .join(' ');

            // 1. 直接子元素选择器
            const elementSelector = this.generateElementBasicSelector(element);
            selectors.push(`${parentPath} ${elementSelector}`);

            // 2. 基于位置的选择器
            const siblings = Array.from(parentChain[i].element.querySelectorAll(tagName));
            const elementIndex = siblings.indexOf(element);
            if (elementIndex >= 0 && siblings.length > 1) {
                selectors.push(`${parentPath} ${tagName}:nth-of-type(${elementIndex + 1})`);
            }

            // 3. 基于属性的选择器
            const placeholder = element.getAttribute('placeholder');
            if (placeholder) {
                const cleanPlaceholder = placeholder.replace(/^\[[\w.]+\]\s*/, '');
                selectors.push(`${parentPath} ${tagName}[placeholder="${cleanPlaceholder}"]`);
            }
        }

        return selectors;
    }

    // 生成基于兄弟元素位置的选择器
    generateSiblingPositionSelectors(element) {
        const tagName = element.tagName.toLowerCase();
        const selectors = [];
        const parent = element.parentElement;

        if (!parent) return selectors;

        const parentSelector = this.generateElementBasicSelector(parent);

        // 1. 基于同类型兄弟元素的位置
        const sameTypeSiblings = Array.from(parent.children).filter(el =>
            el.tagName.toLowerCase() === tagName
        );

        if (sameTypeSiblings.length > 1) {
            const index = sameTypeSiblings.indexOf(element);
            if (index >= 0) {
                selectors.push(`${parentSelector} > ${tagName}:nth-of-type(${index + 1})`);
            }
        }

        // 2. 基于所有兄弟元素的位置
        const allSiblings = Array.from(parent.children);
        if (allSiblings.length > 1) {
            const index = allSiblings.indexOf(element);
            if (index >= 0) {
                selectors.push(`${parentSelector} > :nth-child(${index + 1})`);
                selectors.push(`${parentSelector} > ${tagName}:nth-child(${index + 1})`);
            }
        }

        return selectors;
    }

    // 检查选择器是否唯一
    isUniqueSelector(selector) {
        try {
            const doc = this.currentIframeDoc || document;
            const elements = doc.querySelectorAll(selector);
            return elements.length === 1;
        } catch (e) {
            console.warn('选择器语法错误:', selector, e);
            return false;
        }
    }

    // 生成精确的组合选择器
    generatePreciseSelector(element) {
        const tagName = element.tagName.toLowerCase();
        let selectors = [tagName];

        // 添加有意义的类名
        const meaningfulClasses = this.getMeaningfulClasses(element);
        if (meaningfulClasses.length > 0) {
            selectors.push(`.${meaningfulClasses.join('.')}`);
        }

        // 添加重要属性
        const attributes = this.getImportantAttributes(element);
        selectors.push(...attributes);

        // 对于表单元素，添加label关联信息
        if (['input', 'select', 'textarea'].includes(tagName)) {
            const labelInfo = this.getLabelInfo(element);
            if (labelInfo) {
                selectors.push(labelInfo);
            }
        }

        // 对于按钮等可点击元素，添加文本信息
        if (['button', 'a', 'span', 'div'].includes(tagName)) {
            const textInfo = this.getTextInfo(element);
            if (textInfo) {
                selectors.push(textInfo);
            }
        }

        return selectors.join('');
    }

    // 生成组合属性选择器
    generateCombinedAttributeSelectors(element) {
        const tagName = element.tagName.toLowerCase();
        const selectors = [];

        // 收集所有可用属性
        const attributes = {
            id: element.id,
            name: element.name,
            type: element.getAttribute('type'),
            placeholder: element.getAttribute('placeholder'),
            class: element.className,
            value: element.value,
            title: element.getAttribute('title'),
            'data-field': element.getAttribute('data-field'),
            'data-key': element.getAttribute('data-key')
        };

        // 生成不同属性组合的选择器
        const attributePairs = [];

        // 1. 基础属性组合
        if (attributes.type && attributes.placeholder) {
            const cleanPlaceholder = attributes.placeholder.replace(/^\[[\w.]+\]\s*/, '');
            attributePairs.push(`[type="${attributes.type}"][placeholder="${cleanPlaceholder}"]`);
        }

        if (attributes.name && attributes.type) {
            attributePairs.push(`[name="${attributes.name}"][type="${attributes.type}"]`);
        }

        if (attributes.class) {
            const meaningfulClasses = this.getMeaningfulClasses(element);
            if (meaningfulClasses.length > 0) {
                attributePairs.push(`.${meaningfulClasses.slice(0, 2).join('.')}`);

                // 结合其他属性
                if (attributes.type) {
                    attributePairs.push(`.${meaningfulClasses[0]}[type="${attributes.type}"]`);
                }
            }
        }

        // 2. 生成完整选择器
        for (const attrPair of attributePairs) {
            selectors.push(`${tagName}${attrPair}`);
        }

        return selectors;
    }

    // 生成基础选择器
    generateBaseSelector(element) {
        const tagName = element.tagName.toLowerCase();
        let selector = tagName;

        // 添加最重要的属性
        if (element.id) {
            selector += `#${element.id}`;
        } else {
            const meaningfulClasses = this.getMeaningfulClasses(element);
            if (meaningfulClasses.length > 0) {
                selector += `.${meaningfulClasses[0]}`;
            }

            const type = element.getAttribute('type');
            if (type) {
                selector += `[type="${type}"]`;
            }
        }

        return selector;
    }

    // 获取有意义的类名
    getMeaningfulClasses(element) {
        if (!element.className) return [];

        const classes = element.className.split(' ').filter(c => c.trim());
        return classes.filter(c =>
            c &&
            c.length > 2 &&
            !c.includes('smart-form') &&
            !c.includes('bound') &&
            !c.includes('clickable') &&
            !c.includes('hover') &&
            !c.includes('active') &&
            !c.includes('focus') &&
            !c.startsWith('action-') &&
            !c.startsWith('clickable-')
        ).slice(0, 3); // 最多取3个类名
    }

    // 获取重要属性
    getImportantAttributes(element) {
        const attributes = [];

        // Vue相关属性
        const vueAttrs = ['v-model', 'v-bind', 'v-on', ':class', ':style'];
        vueAttrs.forEach(attr => {
            if (element.hasAttribute(attr)) {
                const value = element.getAttribute(attr);
                if (value) {
                    attributes.push(`[${attr}="${value}"]`);
                }
            }
        });

        // 其他重要属性，但要特殊处理placeholder
        const importantAttrs = [
            'data-toggle', 'data-bs-toggle', 'data-target', 'data-bs-target',
            'role', 'aria-label', 'aria-labelledby', 'type',
            'data-testid', 'data-cy', 'data-test'
        ];

        importantAttrs.forEach(attr => {
            const value = element.getAttribute(attr);
            if (value) {
                attributes.push(`[${attr}="${value}"]`);
            }
        });

        // 特殊处理placeholder - 清理字段名前缀
        const placeholder = element.getAttribute('placeholder');
        if (placeholder) {
            // 移除字段名前缀，如 [preconfigured_entry.master_airwaybill_number]
            const cleanPlaceholder = placeholder.replace(/^\[[\w.]+\]\s*/, '');
            if (cleanPlaceholder && cleanPlaceholder !== placeholder) {
                // 如果清理后的placeholder不同，使用清理后的版本
                attributes.push(`[placeholder="${cleanPlaceholder}"]`);
            } else {
                // 否则使用原始placeholder
                attributes.push(`[placeholder="${placeholder}"]`);
            }
        }

        return attributes;
    }

    // 获取label关联信息
    getLabelInfo(element) {
        // 通过for属性查找label
        if (element.id) {
            const doc = this.currentIframeDoc || document;
            const label = doc.querySelector(`label[for="${element.id}"]`);
            if (label && label.textContent?.trim()) {
                const labelText = label.textContent?.trim().substring(0, 20) || '';
                return `[aria-labelledby*="${element.id}"], [id="${element.id}"]`;
            }
        }

        // 查找父级label
        let parent = element.parentElement;
        while (parent && parent.tagName.toLowerCase() !== 'body') {
            if (parent.tagName.toLowerCase() === 'label') {
                const labelText = parent.textContent?.trim();
                if (labelText && labelText.length > 0) {
                    // 不能直接使用:contains，但可以通过其他方式标识
                    break;
                }
            }
            parent = parent.parentElement;
        }

        // 查找相邻的label或文本节点
        const prevSibling = element.previousElementSibling;
        if (prevSibling && (prevSibling.tagName.toLowerCase() === 'label' ||
                           (prevSibling.textContent?.trim() && prevSibling.textContent?.trim().length > 0))) {
            // 可以通过相邻选择器来定位
            return null; // 在contextual selector中处理
        }

        return null;
    }

    // 获取文本信息（用于按钮等）
    getTextInfo(element) {
        const text = element.textContent?.trim();
        if (!text || text.length === 0 || text.length > 50) {
            return null;
        }

        // 对于包含特殊字符的文本，使用属性选择器
        if (element.getAttribute('title') === text) {
            return `[title="${text}"]`;
        }
        if (element.getAttribute('aria-label') === text) {
            return `[aria-label="${text}"]`;
        }
        if (element.getAttribute('alt') === text) {
            return `[alt="${text}"]`;
        }

        // 对于按钮，尝试使用文本内容作为识别特征
        const tagName = element.tagName.toLowerCase();
        if (['button', 'a'].includes(tagName)) {
            // 清理文本，移除多余的空格和特殊字符
            const cleanText = text.replace(/\s+/g, ' ').trim();
            if (cleanText.length > 0 && cleanText.length <= 20) {
                // 不直接返回:contains选择器，而是在上下文选择器中使用
                return null; // 让上下文选择器处理
            }
        }

        return null;
    }

    // 生成包含上下文的选择器
    generateContextualSelector(element) {
        const selectors = [];
        const tagName = element.tagName.toLowerCase();

        // 对于按钮，优先使用文本内容结合类名
        if (tagName === 'button') {
            const text = element.textContent?.trim();
            if (text && text.length > 0 && text.length <= 30) {
                // 清理文本，移除多余空格
                const cleanText = text.replace(/\s+/g, ' ').trim();
                const meaningfulClasses = this.getMeaningfulClasses(element);

                if (meaningfulClasses.length > 0) {
                    // 使用类名 + 文本内容的组合
                    const classSelector = meaningfulClasses.slice(0, 2).map(c => `.${c}`).join('');
                    // 由于CSS不支持:contains，我们生成一个基于类名和位置的选择器
                    selectors.push(`button${classSelector}`);

                    // 如果有父容器，加上父容器信息
                    const parent = element.parentElement;
                    if (parent) {
                        const parentClasses = this.getMeaningfulClasses(parent);
                        if (parentClasses.length > 0) {
                            const parentSelector = `.${parentClasses[0]}`;
                            selectors.push(`${parentSelector} button${classSelector}`);
                        }
                    }
                }
            }
        }

        // 获取父元素上下文
        const parent = element.parentElement;
        if (parent && parent.tagName.toLowerCase() !== 'body') {
            const parentSelector = this.getElementBasicSelector(parent);
            if (parentSelector) {
                // 生成父子选择器
                const childSelector = this.getElementBasicSelector(element);
                if (childSelector) {
                    selectors.push(`${parentSelector} > ${childSelector}`);
                }

                // 如果有兄弟元素，使用nth-child
                const siblings = Array.from(parent.children);
                const index = siblings.indexOf(element);
                if (siblings.length > 1) {
                    selectors.push(`${parentSelector} > ${tagName}:nth-child(${index + 1})`);
                }
            }
        }

        // 查找相邻的label或描述性元素
        const prevElement = element.previousElementSibling;
        if (prevElement) {
            const prevSelector = this.getElementBasicSelector(prevElement);
            if (prevSelector) {
                const currentSelector = this.getElementBasicSelector(element);
                if (currentSelector) {
                    selectors.push(`${prevSelector} + ${currentSelector}`);
                }
            }
        }

        // 返回最短的有效选择器
        return selectors.find(selector => selector && this.isUniqueSelector(selector)) || selectors[0];
    }

    // 生成完整路径选择器
    generateFullPathSelector(element) {
        const path = [];
        let current = element;
        let depth = 0;
        const maxDepth = 8; // 限制深度避免选择器过长

        while (current && current !== document.body && depth < maxDepth) {
            const selector = this.getElementPathSelector(current);
            if (selector) {
                path.unshift(selector);
            }
            current = current.parentElement;
            depth++;
        }

        return path.length > 0 ? path.join(' > ') : element.tagName.toLowerCase();
    }

    // 获取元素的基本选择器
    getElementBasicSelector(element) {
        const tagName = element.tagName.toLowerCase();

        // 优先使用ID
        if (element.id) {
            return `#${element.id}`;
        }

        // 使用有意义的类名
        const meaningfulClasses = this.getMeaningfulClasses(element);
        if (meaningfulClasses.length > 0) {
            return `${tagName}.${meaningfulClasses[0]}`;
        }

        // 使用重要属性
        const attributes = this.getImportantAttributes(element);
        if (attributes.length > 0) {
            return `${tagName}${attributes[0]}`;
        }

        return tagName;
    }

    // 获取元素在路径中的选择器
    getElementPathSelector(element) {
        const tagName = element.tagName.toLowerCase();

        // 优先使用ID
        if (element.id) {
            return `#${element.id}`;
        }

        // 使用类名
        const meaningfulClasses = this.getMeaningfulClasses(element);
        if (meaningfulClasses.length > 0) {
            return `${tagName}.${meaningfulClasses[0]}`;
        }

        // 使用nth-child定位
        const parent = element.parentElement;
        if (parent) {
            const siblings = Array.from(parent.children).filter(el =>
                el.tagName.toLowerCase() === tagName
            );
            if (siblings.length > 1) {
                const index = siblings.indexOf(element);
                return `${tagName}:nth-of-type(${index + 1})`;
            }
        }

        return tagName;
    }

    // 验证选择器的准确性
    validateSelector(element, selector) {
        try {
            const doc = this.currentIframeDoc || document;
            const foundElements = doc.querySelectorAll(selector);

            // 检查是否能找到元素
            if (foundElements.length === 0) {
                console.warn('选择器无法找到任何元素:', selector);
                return false;
            }

            // 检查是否唯一（严格模式：必须唯一）
            if (foundElements.length > 1) {
                console.warn(`选择器找到${foundElements.length}个元素，不够精确:`, selector);
                console.warn('找到的元素:', Array.from(foundElements).map(el => ({
                    tagName: el.tagName,
                    className: el.className,
                    id: el.id,
                    placeholder: el.placeholder
                })));
                return false; // 严格要求唯一性
            }

            // 检查第一个找到的元素是否就是目标元素
            if (foundElements[0] !== element) {
                console.warn('选择器找到的第一个元素不是目标元素:', selector);
                return false;
            }

            return true;
        } catch (error) {
            console.error('选择器验证失败:', selector, error);
            return false;
        }
    }

    // 生成并验证选择器
    generateAndValidateSelector(element) {
        const selector = this.generateSelector(element);
        const isValid = this.validateSelector(element, selector);

        if (!isValid) {
            console.warn('生成的选择器不准确，尝试生成更精确的选择器');
            // 可以在这里添加备用选择器生成逻辑
            const fallbackSelector = this.generateFallbackSelector(element);
            if (fallbackSelector && this.validateSelector(element, fallbackSelector)) {
                console.log('使用备用选择器:', fallbackSelector);
                return fallbackSelector;
            }
        }

        return selector;
    }

    // 生成备用选择器（更保守但更准确）
    generateFallbackSelector(element) {
        // 使用完整路径作为备用选择器
        const path = [];
        let current = element;
        let depth = 0;
        const maxDepth = 10;

        while (current && current !== document.body && depth < maxDepth) {
            let selector = current.tagName.toLowerCase();

            // 添加ID（如果有）
            if (current.id) {
                selector += `#${current.id}`;
                path.unshift(selector);
                break; // ID是唯一的，可以停止
            }

            // 添加最重要的类名
            const meaningfulClasses = this.getMeaningfulClasses(current);
            if (meaningfulClasses.length > 0) {
                selector += `.${meaningfulClasses[0]}`;
            }

            // 添加nth-child定位
            const parent = current.parentElement;
            if (parent) {
                const siblings = Array.from(parent.children).filter(el =>
                    el.tagName.toLowerCase() === current.tagName.toLowerCase()
                );
                if (siblings.length > 1) {
                    const index = siblings.indexOf(current);
                    selector += `:nth-of-type(${index + 1})`;
                }
            }

            path.unshift(selector);
            current = current.parentElement;
            depth++;
        }

        return path.length > 0 ? path.join(' > ') : element.tagName.toLowerCase();
    }

    // 更新已绑定字段计数
    updateBoundFieldsCount() {
        let count = 0;
        if (this.configType === 'single') {
            count = this.currentConfig.fieldMappings ? this.currentConfig.fieldMappings.length : 0;
            const countElement = document.getElementById('boundFieldsCount');
            if (countElement) {
                countElement.textContent = count;
            }
        } else {
            const currentStep = this.currentConfig.steps[this.currentStepIndex];
            count = currentStep.fieldMappings.length;
            const countElement = document.getElementById('stepBoundFieldsCount');
            if (countElement) {
                countElement.textContent = count;
            }
        }
    }

    // 多步骤配置相关方法
    prevConfigStep() {
        if (this.currentStepIndex > 0) {
            // 保存当前步骤的绑定数据
            this.saveCurrentStepBindings();

            this.currentStepIndex--;
            this.renderMultiStepConfig();

            // 加载上一步的绑定数据
            this.loadStepBindings();
        }
    }

    nextConfigStep() {
        if (this.currentStepIndex < this.currentConfig.steps.length - 1) {
            // 保存当前步骤的绑定数据
            this.saveCurrentStepBindings();

            this.currentStepIndex++;
            this.renderMultiStepConfig();

            // 加载下一步的绑定数据
            this.loadStepBindings();
        }
    }

    updateStepName(name) {
        const currentStep = this.currentConfig.steps[this.currentStepIndex];
        currentStep.stepName = name;
    }

    addNewStep() {
        console.log('➕ 开始添加新步骤...');

        // 检查当前步骤的绑定数据（已经在bindFieldToElement时实时保存了）
        if (this.currentStepIndex >= 0 && this.currentConfig.steps[this.currentStepIndex]) {
            const currentStep = this.currentConfig.steps[this.currentStepIndex];
            console.log(`📊 当前步骤${this.currentStepIndex + 1}已有${currentStep.fieldMappings.length}个绑定字段`);
            console.log('📋 绑定详情:', currentStep.fieldMappings.map(b => ({
                field: b.fieldKey,
                selector: b.selector,
                type: b.controlType
            })));

            // 标记当前步骤的绑定数据已经最终确定
            currentStep._bindingsSaved = true;
            console.log(`✅ 步骤${this.currentStepIndex + 1}的绑定数据已确认，无需重新保存`);
        }

        const newStep = {
            stepName: `步骤${this.currentConfig.steps.length + 1}`,
            urlPattern: '',
            fieldMappings: [],
            nextAction: null,
            _bindingsSaved: false // 标记新步骤的绑定数据尚未保存
        };

        this.currentConfig.steps.push(newStep);
        this.currentStepIndex = this.currentConfig.steps.length - 1;

        console.log(`📝 创建新步骤: ${newStep.stepName}，当前步骤索引: ${this.currentStepIndex}`);

        // 清空预览内容（因为下一步通常是新页面）
        this.clearPreview();

        this.renderMultiStepConfig();

        this.showAlert('新步骤已添加，请粘贴新页面的HTML内容', 'success');
    }

    // 保存当前步骤的绑定数据
    saveCurrentStepBindings() {
        if (this.currentStepIndex >= 0 && this.currentConfig.steps[this.currentStepIndex]) {
            const currentStep = this.currentConfig.steps[this.currentStepIndex];

            // 检查绑定数据是否已经被锁定（避免覆盖精准的绑定数据）
            if (currentStep._bindingsSaved) {
                console.log(`🔒 步骤${this.currentStepIndex + 1}的绑定数据已锁定，跳过重新保存`);
                console.log('📊 已锁定的绑定数据:', currentStep.fieldMappings.map(b => ({
                    field: b.fieldKey,
                    selector: b.selector,
                    type: b.controlType
                })));
                return;
            }

            // 从DOM中收集当前绑定的字段
            const boundElements = [];
            if (this.currentIframeDoc) {
                const elements = this.currentIframeDoc.querySelectorAll('[data-bound-field]');
                console.log(`🔍 在DOM中找到${elements.length}个绑定元素`);

                elements.forEach(element => {
                    const fieldKey = element.dataset.boundField;
                    if (fieldKey) {
                        console.log(`🔍 处理绑定元素: ${fieldKey}`);
                        console.log(`📊 绑定前状态:`, {
                            class: element.className,
                            placeholder: element.placeholder,
                            title: element.title,
                            hasBoundField: element.hasAttribute('data-bound-field')
                        });

                        // 确保元素处于原始状态再生成选择器
                        this.ensureElementInOriginalState(element);

                        console.log(`📊 恢复原始状态后:`, {
                            class: element.className,
                            placeholder: element.placeholder,
                            title: element.title,
                            hasBoundField: element.hasAttribute('data-bound-field')
                        });

                        // 使用增强的选择器生成方法，确保精准度
                        const selector = this.generateAndValidateSelector(element);

                        console.log(`📊 生成的选择器: ${selector}`);

                        // 在恢复绑定状态之前收集元素信息（基于原始状态）
                        const elementInfo = this.collectElementInfo(element);
                        const controlType = this.detectControlType(element);

                        // 最后恢复绑定状态
                        this.restoreElementBindingState(element);

                        console.log(`📊 恢复绑定状态后:`, {
                            class: element.className,
                            placeholder: element.placeholder,
                            title: element.title,
                            hasBoundField: element.hasAttribute('data-bound-field')
                        });

                        boundElements.push({
                            fieldKey: fieldKey,
                            fieldName: fieldKey,
                            elementId: element.id || '',
                            elementName: element.name || '',
                            selector: selector,
                            elementType: element.tagName.toLowerCase(),
                            controlType: controlType, // 新增：控件类型
                            elementInfo: elementInfo  // 新增：详细元素信息
                        });

                        console.log(`📝 保存字段绑定: ${fieldKey} -> ${selector}`);
                    }
                });
            } else {
                console.warn('⚠️ 当前iframe文档不存在，无法收集绑定数据');
            }

            // 更新步骤的字段映射
            currentStep.fieldMappings = boundElements;

            console.log(`💾 保存步骤${this.currentStepIndex + 1}的绑定数据:`, boundElements.length, '个字段');
            console.log('📊 绑定详情:', boundElements.map(b => ({
                field: b.fieldKey,
                selector: b.selector,
                type: b.controlType
            })));
        }
    }

    // 加载步骤的绑定数据
    loadStepBindings() {
        if (this.currentStepIndex >= 0 && this.currentConfig.steps[this.currentStepIndex]) {
            const currentStep = this.currentConfig.steps[this.currentStepIndex];

            if (currentStep.fieldMappings && currentStep.fieldMappings.length > 0 && this.currentIframeDoc) {
                console.log(`📂 加载步骤${this.currentStepIndex + 1}的绑定数据:`, currentStep.fieldMappings.length, '个字段');

                // 恢复字段绑定状态
                currentStep.fieldMappings.forEach(mapping => {
                    try {
                        const element = this.currentIframeDoc.querySelector(mapping.selector);
                        if (element) {
                            element.dataset.boundField = mapping.fieldKey;
                            element.classList.add('smart-form-element', 'bound-element');
                            element.style.border = '2px solid #28a745';
                            element.style.backgroundColor = 'rgba(40, 167, 69, 0.1)';

                            console.log(`✅ 恢复字段绑定: ${mapping.fieldKey} -> ${mapping.selector}`);
                        } else {
                            console.warn(`⚠️ 未找到绑定元素: ${mapping.selector}`);
                        }
                    } catch (error) {
                        console.warn(`❌ 恢复绑定失败: ${mapping.fieldKey}`, error);
                    }
                });

                // 更新字段列表显示
                this.updateFieldBindingStatus();
            } else {
                console.log(`📂 步骤${this.currentStepIndex + 1}暂无绑定数据`);
            }
        }
    }

    // 清空预览内容
    clearPreview() {
        console.log('🧹 开始清空预览内容...');

        // 重置iframe预览为初始状态
        const previewFrame = document.getElementById('previewFrame');
        if (previewFrame) {
            previewFrame.srcdoc = `
                <html>
                    <head>
                        <meta charset="UTF-8">
                        <style>
                            body {
                                font-family: Arial, sans-serif;
                                padding: 20px;
                                color: #666;
                                background: #f8f9fa;
                                margin: 0;
                            }
                            .welcome-container {
                                max-width: 600px;
                                margin: 0 auto;
                                text-align: center;
                            }
                            .placeholder {
                                border: 2px dashed #ddd;
                                border-radius: 12px;
                                padding: 40px 20px;
                                margin: 20px 0;
                                background: white;
                            }
                            .icon {
                                font-size: 48px;
                                margin-bottom: 16px;
                            }
                            .steps {
                                text-align: left;
                                background: white;
                                border-radius: 8px;
                                padding: 20px;
                                margin: 20px 0;
                                border: 1px solid #e9ecef;
                            }
                            .step-item {
                                margin: 8px 0;
                                padding: 8px 0;
                                border-bottom: 1px solid #f1f3f4;
                            }
                            .step-item:last-child {
                                border-bottom: none;
                            }
                            .step-number {
                                display: inline-block;
                                width: 24px;
                                height: 24px;
                                background: #007bff;
                                color: white;
                                border-radius: 50%;
                                text-align: center;
                                line-height: 24px;
                                font-size: 12px;
                                margin-right: 10px;
                            }
                        </style>
                    </head>
                    <body>
                        <div class="welcome-container">
                            <div class="placeholder">
                                <div class="icon">🎯</div>
                                <h3>智能表单配置</h3>
                                <p>请按照以下步骤开始配置表单绑定</p>
                            </div>

                            <div class="steps">
                                <h5 style="margin-bottom: 16px; color: #333;">📋 操作指引</h5>
                                <div class="step-item">
                                    <span class="step-number">1</span>
                                    在左侧输入框中粘贴页面HTML内容
                                </div>
                                <div class="step-item">
                                    <span class="step-number">2</span>
                                    点击"粘贴HTML内容"按钮加载页面预览
                                </div>
                                <div class="step-item">
                                    <span class="step-number">3</span>
                                    直接点击页面中的输入框进行字段绑定
                                </div>
                                <div class="step-item">
                                    <span class="step-number">4</span>
                                    配置下一步动作（如需要多步骤）
                                </div>
                            </div>

                            <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-top: 20px;">
                                <small style="color: #1976d2;">
                                    💡 <strong>提示：</strong>现在支持直接点击输入框进行绑定，无需先点击"开始绑定"按钮
                                </small>
                            </div>
                        </div>
                    </body>
                </html>
            `;
            // 移除iframe的src属性
            previewFrame.removeAttribute('src');
        }

        // 清空HTML输入框
        const htmlInput = document.getElementById('htmlInput');
        if (htmlInput) {
            htmlInput.value = '';
        }

        // 清空预览内容容器
        const previewContent = document.getElementById('previewContent');
        if (previewContent) {
            previewContent.innerHTML = '';
        }

        // 重置iframe文档引用
        this.currentIframeDoc = null;

        // 清空任何缓存的HTML数据
        this.extractedHTMLData = null;

        // 退出绑定模式（如果正在绑定）
        if (this.bindingMode) {
            this.exitBindingMode();
        }

        // 退出动作模式（如果正在设置动作）
        if (this.actionMode) {
            this.actionMode = false;
        }

        console.log('🧹 预览内容已完全清空');
    }

    // 生成配置摘要
    generateConfigSummary() {
        const summary = document.getElementById('configSummary');
        if (!summary) return;

        let summaryHtml = `
            <div class="card">
                <div class="card-body">
                    <h6>基本信息</h6>
                    <ul class="list-unstyled">
                        <li><strong>业务模板:</strong> ${this.selectedTemplate.templateName}</li>
                        <li><strong>配置类型:</strong> ${this.configType === 'single' ? '单页面表单' : '多步骤表单'}</li>
                    </ul>
        `;

        if (this.configType === 'single') {
            const fieldCount = this.currentConfig.fieldMappings ? this.currentConfig.fieldMappings.length : 0;
            summaryHtml += `
                    <h6>字段绑定</h6>
                    <p>已绑定 <span class="badge bg-success">${fieldCount}</span> 个字段</p>
            `;
        } else {
            summaryHtml += `
                    <h6>步骤配置</h6>
                    <p>共 <span class="badge bg-primary">${this.currentConfig.steps.length}</span> 个步骤</p>
                    <ul class="list-group list-group-flush">
            `;

            this.currentConfig.steps.forEach((step, index) => {
                summaryHtml += `
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            ${step.stepName}
                            <span class="badge bg-secondary">${step.fieldMappings.length} 个字段</span>
                        </li>
                `;
            });

            summaryHtml += '</ul>';
        }

        summaryHtml += `
                </div>
            </div>
        `;

        summary.innerHTML = summaryHtml;
    }

    // 保存配置
    async saveConfig() {
        const configName = document.getElementById('configName').value.trim();
        const configDescription = document.getElementById('configDescription').value.trim();
        const urlPattern = document.getElementById('urlPattern').value.trim();
        const targetUrl = document.getElementById('targetUrl').value.trim();

        if (!configName) {
            this.showAlert('请输入配置名称', 'warning');
            return;
        }

        if (!urlPattern) {
            this.showAlert('请输入URL匹配规则', 'warning');
            return;
        }

        this.currentConfig.name = configName;
        this.currentConfig.description = configDescription;
        this.currentConfig.urlPattern = urlPattern;
        this.currentConfig.targetUrl = targetUrl;

        try {
            // 根据配置类型调用不同的API
            let result;
            if (this.configType === 'single') {
                result = await this.saveSingleStepConfig();
            } else {
                result = await this.saveMultiStepConfig();
            }

            if (result.code === 200) {
                this.showAlert('配置保存成功！', 'success');
                // 关闭弹窗并刷新列表
                setTimeout(() => {
                    this.exitWizard();
                }, 1500);
            } else {
                this.showAlert('保存失败: ' + result.message, 'danger');
            }
        } catch (error) {
            console.error('保存配置失败:', error);
            this.showAlert('保存配置失败', 'danger');
        }
    }

    // 保存单步骤配置
    async saveSingleStepConfig() {
        const configData = {
            bindingName: this.currentConfig.name,
            description: this.currentConfig.description,
            templateId: this.currentConfig.templateId,
            targetUrl: this.currentConfig.targetUrl || '',
            urlPattern: this.currentConfig.urlPattern,
            bindingConfig: JSON.stringify({
                fieldMappings: this.currentConfig.fieldMappings || []
            }),
            isMultiStep: 0,
            status: 1
        };

        return await apiCall('/api/v1/page-bindings', 'POST', configData);
    }

    // 保存多步骤配置
    async saveMultiStepConfig() {
        console.log('💾 开始保存多步骤配置...');

        // 确保当前步骤的绑定数据被保存（如果尚未锁定）
        this.saveCurrentStepBindings();

        console.log('📊 多步骤配置概览:');
        this.currentConfig.steps.forEach((step, index) => {
            console.log(`  步骤${index + 1}: ${step.stepName}, 字段数: ${step.fieldMappings?.length || 0}, 已锁定: ${step._bindingsSaved || false}`);
        });

        const mainBinding = this.currentConfig.steps[0];
        const subSteps = this.currentConfig.steps.slice(1);

        const configData = {
            bindingName: this.currentConfig.name,
            mainBinding: {
                bindingName: this.currentConfig.name,
                description: this.currentConfig.description,
                stepName: mainBinding.stepName,
                urlPattern: this.currentConfig.urlPattern,
                targetUrl: this.currentConfig.targetUrl || '',
                templateId: this.currentConfig.templateId,
                bindingConfig: JSON.stringify({
                    fieldMappings: mainBinding.fieldMappings || []
                }),
                nextAction: JSON.stringify(mainBinding.nextAction || {}),
                waitTime: 3000,
                status: 1
            },
            subSteps: subSteps.map((step, index) => ({
                bindingName: `${this.currentConfig.name} - 步骤${index + 2}`,
                description: `${this.currentConfig.description} - 第${index + 2}步`,
                stepName: step.stepName,
                urlPattern: this.currentConfig.urlPattern,
                targetUrl: this.currentConfig.targetUrl || '',
                templateId: this.currentConfig.templateId,
                bindingConfig: JSON.stringify({
                    fieldMappings: step.fieldMappings || []
                }),
                nextAction: JSON.stringify(step.nextAction || {}),
                waitTime: 3000,
                status: 1
            }))
        };

        return await apiCall('/api/v1/multi-step-bindings', 'POST', configData);
    }

    // 动作配置相关方法
    updateActionType(actionType) {
        const currentStep = this.currentConfig.steps[this.currentStepIndex];
        if (!currentStep.nextAction) {
            currentStep.nextAction = {};
        }
        currentStep.nextAction.type = actionType;
    }

    selectActionTarget() {
        if (!this.currentConfig.steps[this.currentStepIndex].nextAction?.type) {
            this.showAlert('请先选择动作类型', 'warning');
            return;
        }

        // 如果当前在绑定模式，先退出绑定模式
        if (this.bindingMode) {
            console.log('🚪 退出绑定模式，进入动作选择模式');
            this.exitBindingMode();
        }

        console.log('🎯 进入动作选择模式...');
        this.actionMode = true;
        this.showAlert('请点击页面中的目标元素', 'info');

        // 为当前iframe添加动作选择监听器
        if (this.currentIframeDoc) {
            // 先移除之前的监听器，再添加新的
            this.removeElementClickListeners(this.currentIframeDoc);
            this.addElementClickListeners(this.currentIframeDoc);

            // 为页面元素添加动作选择样式和额外的事件阻止
            const actionSelector = `
                button,
                a,
                input[type="button"],
                input[type="submit"],
                input[type="reset"],
                [onclick],
                [role="button"],
                [role="tab"],
                [role="link"],
                .btn,
                .button,
                .tab,
                .nav-link,
                .nav-item,
                [data-toggle],
                [data-bs-toggle],
                span[onclick],
                div[onclick],
                li[onclick]
            `.replace(/\s+/g, ' ').trim();

            const elements = this.currentIframeDoc.querySelectorAll(actionSelector);
            console.log('🎯 为', elements.length, '个可点击元素添加动作模式样式');

            elements.forEach(el => {
                el.classList.add('clickable-element');

                // 为动作模式添加额外的事件阻止
                const actionPreventHandler = (e) => {
                    if (this.actionMode) {
                        console.log('🛡️ 阻止动作模式下的事件:', e.type, el.tagName);
                        e.preventDefault();
                        e.stopPropagation();
                        e.stopImmediatePropagation();
                        return false;
                    }
                };

                // 添加多种事件的阻止
                ['mousedown', 'mouseup', 'submit', 'focus'].forEach(eventType => {
                    el.addEventListener(eventType, actionPreventHandler, true);
                });

                el._actionPreventHandler = actionPreventHandler;
            });
        }
    }

    setActionTarget(element) {
        const elementText = element.textContent?.trim() || '';
        console.log('🎯 设置动作目标:', element.tagName, elementText);

        const currentStep = this.currentConfig.steps[this.currentStepIndex];

        // 确保元素处于原始状态再生成选择器
        this.ensureElementInOriginalState(element);

        // 使用增强的选择器生成方法，确保精准度
        const selector = this.generateAndValidateSelector(element);

        this.restoreElementBindingState(element);

        // 收集详细的元素信息
        const elementInfo = this.collectElementInfo(element);
        const controlType = this.detectControlType(element);

        currentStep.nextAction.selector = selector;
        currentStep.nextAction.element = {
            tagName: element.tagName.toLowerCase(),
            text: elementText,
            id: element.id || '',
            className: element.className || '',
            controlType: controlType, // 新增：控件类型
            elementInfo: elementInfo, // 新增：详细元素信息
            // 增强：收集更多属性信息
            attributes: this.collectElementAttributes(element)
        };

        console.log('🎯 动作目标已设置:', currentStep.nextAction);

        // 更新UI
        element.classList.add('action-target');
        element.insertAdjacentHTML('afterbegin', '<div class="action-indicator">→</div>');

        // 退出动作模式
        this.actionMode = false;

        // 移除所有元素的点击监听器和样式
        if (this.currentIframeDoc) {
            this.removeElementClickListeners(this.currentIframeDoc);

            // 移除所有可点击元素的样式和额外监听器
            const elements = this.currentIframeDoc.querySelectorAll('.clickable-element');
            elements.forEach(el => {
                el.classList.remove('clickable-element');

                // 移除动作模式的额外事件监听器
                if (el._actionPreventHandler) {
                    ['mousedown', 'mouseup', 'submit', 'focus'].forEach(eventType => {
                        el.removeEventListener(eventType, el._actionPreventHandler, true);
                    });
                    delete el._actionPreventHandler;
                }
            });
        }

        this.showAlert('下一步动作已设置', 'success');
    }

    // 退出向导
    exitWizard() {
        console.log('🚪 退出向导...');

        // 关闭模态框
        const modal = document.getElementById('configWizardModal');
        if (modal) {
            const modalInstance = bootstrap.Modal.getInstance(modal);
            if (modalInstance) {
                console.log('🚪 使用Bootstrap实例关闭模态框');
                modalInstance.hide();
            } else {
                console.log('🚪 直接关闭模态框');
                // 如果没有实例，直接隐藏
                modal.style.display = 'none';
                modal.classList.remove('show');
                modal.setAttribute('aria-hidden', 'true');
                modal.removeAttribute('aria-modal');
                modal.removeAttribute('role');

                // 移除遮罩层
                const backdrops = document.querySelectorAll('.modal-backdrop');
                backdrops.forEach(backdrop => backdrop.remove());

                // 恢复body的样式
                document.body.classList.remove('modal-open');
                document.body.style.overflow = '';
                document.body.style.paddingRight = '';
            }
        }

        // 延迟重置配置状态，确保模态框完全关闭
        setTimeout(() => {
            this.resetWizard();
            // 刷新配置列表
            this.loadConfigList();
        }, 300);
    }

    // 重置向导状态
    resetWizard() {
        this.currentStep = 1;
        this.selectedTemplate = null;
        this.configType = null;
        this.currentConfig = {
            name: '',
            description: '',
            templateId: null,
            type: 'single',
            steps: []
        };
        this.currentStepIndex = 0;
        this.bindingMode = false;
        this.actionMode = false;
        this.selectedField = null;
    }

    // 加载配置进行编辑
    loadConfigForEdit(config) {
        console.log('📝 加载配置进行编辑:', config);

        try {
            // 重置状态
            this.resetWizard();

            // 重置预览内容
            this.clearPreview();

            // 设置基本信息
            this.currentConfig.name = config.bindingName;
            this.currentConfig.description = config.description || '';
            this.currentConfig.templateId = config.templateId;
            this.currentConfig.urlPattern = config.urlPattern;
            this.currentConfig.targetUrl = config.targetUrl;

            // 查找对应的业务模板
            this.selectedTemplate = this.businessTemplates.find(t => t.id === config.templateId);

            if (config.isMultiStep === 1) {
                // 多步骤配置
                this.currentConfig.type = 'multi';
                this.configType = 'multi';

                // 构建步骤数据
                this.currentConfig.steps = [];

                // 主步骤
                const mainStepConfig = JSON.parse(config.bindingConfig || '[]');
                const mainFieldMappings = Array.isArray(mainStepConfig) ? mainStepConfig : (mainStepConfig.fieldMappings || []);

                this.currentConfig.steps.push({
                    stepName: config.stepName || '步骤1',
                    urlPattern: config.urlPattern,
                    fieldMappings: mainFieldMappings,
                    nextAction: config.nextAction ? JSON.parse(config.nextAction) : null
                });

                // 子步骤
                if (config.subSteps && config.subSteps.length > 0) {
                    config.subSteps.forEach((subStep, index) => {
                        const subStepConfig = JSON.parse(subStep.bindingConfig || '[]');
                        const subFieldMappings = Array.isArray(subStepConfig) ? subStepConfig : (subStepConfig.fieldMappings || []);

                        this.currentConfig.steps.push({
                            stepName: subStep.stepName || `步骤${index + 2}`,
                            urlPattern: subStep.urlPattern || config.urlPattern,
                            fieldMappings: subFieldMappings,
                            nextAction: subStep.nextAction ? JSON.parse(subStep.nextAction) : null
                        });
                    });
                }

                this.currentStepIndex = 0;

            } else {
                // 单步骤配置
                this.currentConfig.type = 'single';
                this.configType = 'single';

                const bindingConfig = JSON.parse(config.bindingConfig || '[]');
                const fieldMappings = Array.isArray(bindingConfig) ? bindingConfig : (bindingConfig.fieldMappings || []);

                this.currentConfig.fieldMappings = fieldMappings;
                this.currentConfig.urlPattern = config.urlPattern;
            }

            // 跳转到配置步骤
            this.currentStep = 3; // 跳到可视化配置步骤

            // 显示向导模态框
            const modal = new bootstrap.Modal(document.getElementById('configWizardModal'));
            modal.show();

            // 显示正确的步骤
            for (let i = 1; i <= 4; i++) {
                const stepElement = document.getElementById(`step${i}`);
                if (stepElement) {
                    stepElement.style.display = i === 3 ? 'block' : 'none';
                }
            }

            // 更新进度
            this.updateWizardProgress();

            // 初始化可视化配置
            this.initVisualConfig();

            this.showAlert('配置已加载，可以进行编辑', 'success');

        } catch (error) {
            console.error('加载配置进行编辑失败:', error);
            this.showAlert('加载配置失败: ' + error.message, 'danger');
        }
    }

    // 收集元素的详细信息
    collectElementInfo(element) {
        const tagName = element.tagName.toLowerCase();
        const info = {
            tagName: tagName,
            attributes: {},
            text: element.textContent?.trim() || '',
            label: null,
            container: null,
            siblings: []
        };

        // 收集所有重要属性（增强版）
        const importantAttrs = [
            'id', 'name', 'type', 'placeholder', 'class', 'role', 'aria-label', 'aria-labelledby',
            'data-testid', 'data-cy', 'data-test', 'data-automation', 'data-qa', 'data-id',
            'data-field', 'data-name', 'data-bind', 'autocomplete', 'for',
            'ng-model', 'v-model', 'x-model', 'formcontrolname', 'data-vv-name'
        ];

        // 同时收集所有data-*属性
        for (let i = 0; i < element.attributes.length; i++) {
            const attr = element.attributes[i];
            if (attr.name.startsWith('data-') && !importantAttrs.includes(attr.name)) {
                importantAttrs.push(attr.name);
            }
        }

        importantAttrs.forEach(attr => {
            const value = element.getAttribute(attr);
            if (value) {
                if (attr === 'placeholder') {
                    // 同时保存原始和清理后的placeholder
                    info.attributes[attr] = value;
                    info.attributes.cleanPlaceholder = value.replace(/^\[[\w.]+\]\s*/, '');
                } else {
                    info.attributes[attr] = value;
                }
            }
        });

        // 查找关联的label
        const labelInfo = this.findAssociatedLabel(element);
        if (labelInfo) {
            info.label = {
                type: labelInfo.type,
                text: labelInfo.text,
                selector: labelInfo.selector
            };
        }

        // 查找容器信息
        const container = this.findFormItemContainer(element);
        if (container) {
            info.container = {
                tagName: container.tagName.toLowerCase(),
                classes: Array.from(container.classList),
                selector: this.generateElementSelector(container)
            };
        }

        // 收集兄弟元素信息（用于定位）
        if (element.parentElement) {
            const siblings = Array.from(element.parentElement.children);
            const sameTagSiblings = siblings.filter(el => el.tagName.toLowerCase() === tagName);
            if (sameTagSiblings.length > 1) {
                const index = sameTagSiblings.indexOf(element);
                info.siblings = {
                    total: sameTagSiblings.length,
                    index: index,
                    nthOfType: index + 1
                };
            }
        }

        return info;
    }

    // 收集元素的关键属性（轻量版，用于按钮等）
    collectElementAttributes(element) {
        const attributes = {};

        // 收集关键属性（增强版，包含更多按钮相关属性）
        const keyAttrs = [
            'type', 'role', 'aria-label', 'data-testid', 'data-cy', 'data-test',
            'data-automation', 'data-qa', 'data-id', 'data-action', 'onclick',
            'data-toggle', 'data-target', 'data-dismiss', 'data-bs-toggle', 'data-bs-target',
            'formaction', 'formmethod', 'value', 'name'
        ];

        keyAttrs.forEach(attr => {
            const value = element.getAttribute(attr);
            if (value) {
                attributes[attr] = value;
            }
        });

        // 收集有意义的类名
        const meaningfulClasses = this.getMeaningfulClasses(element);
        if (meaningfulClasses.length > 0) {
            attributes.meaningfulClasses = meaningfulClasses;
        }

        return attributes;
    }

    // 确保元素处于原始状态（用于生成准确的选择器）
    ensureElementInOriginalState(element) {
        console.log(`🔍 确保元素处于原始状态...`);

        // 临时恢复真正的原始placeholder
        const originalPlaceholder = element.getAttribute('data-original-placeholder');
        console.log(`🔍 data-original-placeholder: "${originalPlaceholder}"`);
        console.log(`🔍 当前placeholder: "${element.placeholder}"`);

        if (originalPlaceholder !== null) {
            // 临时保存当前的placeholder
            const currentPlaceholder = element.placeholder;

            // 恢复真正的原始placeholder（去掉字段名前缀）
            const cleanOriginalPlaceholder = this.extractOriginalPlaceholder(originalPlaceholder);
            element.placeholder = cleanOriginalPlaceholder;

            console.log(`✅ 临时设置placeholder为: "${cleanOriginalPlaceholder}"`);

            // 生成选择器后需要恢复
            element._tempCurrentPlaceholder = currentPlaceholder;
        }

        // 临时恢复原始title
        console.log(`🔍 当前title: "${element.title}"`);
        console.log(`🔍 data-original-title: "${element.getAttribute('data-original-title')}"`);

        if (element.title && (element.title.includes('绑定字段') || element.title.includes('点击绑定'))) {
            // 保存当前title
            element._tempCurrentTitle = element.title;
            // 恢复原始title（移除绑定信息）
            const originalTitle = element.getAttribute('data-original-title') || '';
            element.title = originalTitle;
            console.log(`✅ 临时恢复title: "${originalTitle}"`);
        } else if (element.title) {
            console.log(`🔍 title不包含绑定信息，无需恢复: "${element.title}"`);
        }

        // 临时恢复原始CSS类名
        console.log(`🔍 当前class: "${element.className}"`);

        if (element.classList.contains('bound')) {
            // 保存当前的类名
            element._tempCurrentClassName = element.className;

            // 移除绑定相关的类名，恢复原始类名
            element.classList.remove('bound');

            // 如果没有原始的smart-form-element类，添加它
            if (!element.classList.contains('smart-form-element')) {
                element.classList.add('smart-form-element', 'binding-mode');
            }

            console.log(`✅ 临时恢复class为: "${element.className}"`);
        }

        // 临时移除其他绑定相关的属性
        const tempAttributes = {};
        const attributesToRemove = ['data-bound-field', 'data-original-value', 'data-original-text', 'data-original-title'];

        attributesToRemove.forEach(attr => {
            if (element.hasAttribute(attr)) {
                tempAttributes[attr] = element.getAttribute(attr);
                element.removeAttribute(attr);
            }
        });

        // 保存临时移除的属性，以便后续恢复
        element._tempRemovedAttributes = tempAttributes;
    }

    // 提取真正的原始placeholder（去掉字段名前缀）
    extractOriginalPlaceholder(placeholder) {
        console.log(`🔍 提取原始placeholder: "${placeholder}"`);
        // 如果placeholder包含字段名前缀 [字段名]，则提取后面的部分
        const match = placeholder.match(/^\[([^\]]+)\]\s*(.*)$/);
        if (match) {
            const extracted = match[2].trim(); // 返回字段名后面的部分并去掉空格
            console.log(`✅ 提取到原始placeholder: "${extracted}"`);
            return extracted;
        }
        console.log(`✅ 无需提取，直接返回: "${placeholder}"`);
        return placeholder; // 如果没有前缀，直接返回
    }

    // 恢复元素的绑定状态（在生成选择器后调用）
    restoreElementBindingState(element) {
        console.log(`🔄 恢复元素绑定状态...`);

        // 恢复CSS类名
        if (element._tempCurrentClassName !== undefined) {
            element.className = element._tempCurrentClassName;
            delete element._tempCurrentClassName;
            console.log(`✅ 恢复class为: "${element.className}"`);
        }

        // 恢复placeholder
        if (element._tempCurrentPlaceholder !== undefined) {
            element.placeholder = element._tempCurrentPlaceholder;
            delete element._tempCurrentPlaceholder;
            console.log(`✅ 恢复placeholder为: "${element.placeholder}"`);
        }

        // 恢复title
        if (element._tempCurrentTitle !== undefined) {
            element.title = element._tempCurrentTitle;
            delete element._tempCurrentTitle;
            console.log(`✅ 恢复title为: "${element.title}"`);
        }

        // 恢复临时移除的属性
        if (element._tempRemovedAttributes) {
            Object.entries(element._tempRemovedAttributes).forEach(([attr, value]) => {
                element.setAttribute(attr, value);
            });
            delete element._tempRemovedAttributes;
        }
    }
}

// 全局变量
let smartFormManager;

// 全局函数
function selectTemplate(templateId) {
    const manager = smartFormManager || window.smartFormConfigManager;
    if (manager) {
        manager.selectTemplate(templateId);
    } else {
        // 尝试直接创建实例
        if (window.SmartFormConfigManager) {
            console.log('🔄 尝试创建新的manager实例');
            window.smartFormConfigManager = new SmartFormConfigManager();
            window.smartFormConfigManager.selectTemplate(templateId);
        }
    }
}

function selectConfigType(type) {
    const manager = smartFormManager || window.smartFormConfigManager;
    if (manager) {
        manager.selectConfigType(type);
    } else {
        // 尝试直接创建实例
        if (window.SmartFormConfigManager) {
            console.log('🔄 尝试创建新的manager实例');
            window.smartFormConfigManager = new SmartFormConfigManager();
            window.smartFormConfigManager.selectConfigType(type);
        }
    }
}

function nextStep() {

    const manager = smartFormManager || window.smartFormConfigManager;
    if (manager) {
        // 检查当前步骤的必要条件
        if (manager.currentStep === 1 && !manager.selectedTemplate) {
            manager.showAlert('请先选择一个业务模板', 'warning');
            return;
        }
        if (manager.currentStep === 2 && !manager.configType) {
            manager.showAlert('请先选择配置类型', 'warning');
            return;
        }

        manager.nextStep();
    } else {

        // 创建临时提示
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-danger alert-dismissible fade show';
        alertDiv.style.cssText = 'position: fixed; top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
        alertDiv.innerHTML = `
            系统错误，请刷新页面重试
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        document.body.appendChild(alertDiv);
        setTimeout(() => alertDiv.remove(), 5000);
    }
}

function prevStep() {
    console.log('🎯 prevStep被调用');
    const manager = smartFormManager || window.smartFormConfigManager;
    if (manager) {
        console.log('✅ 调用manager.prevStep');
        manager.prevStep();
    } else {
        console.error('❌ 没有找到可用的manager实例');
        // 创建临时提示
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-danger alert-dismissible fade show';
        alertDiv.style.cssText = 'position: fixed; top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
        alertDiv.innerHTML = `
            系统错误，请刷新页面重试
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        document.body.appendChild(alertDiv);
        setTimeout(() => alertDiv.remove(), 5000);
    }
}

function startNewConfig() {
    console.log('🚀 开始新建配置...');
    console.log('🚀 smartFormConfigManager存在:', !!window.smartFormConfigManager);

    // 显示配置向导模态框
    const modal = new bootstrap.Modal(document.getElementById('configWizardModal'));
    modal.show();

    // 重置向导状态
    if (window.smartFormConfigManager) {
        console.log('🚀 重置向导状态...');
        console.log('🚀 业务模板数量:', window.smartFormConfigManager.businessTemplates?.length || 0);

        window.smartFormConfigManager.currentStep = 1;
        window.smartFormConfigManager.selectedTemplate = null;
        window.smartFormConfigManager.configType = null;

        // 显示第一步，隐藏其他步骤
        for (let i = 1; i <= 4; i++) {
            const step = document.getElementById(`step${i}`);
            if (step) {
                step.style.display = i === 1 ? 'block' : 'none';
            }
        }

        // 重置按钮状态
        const step1NextBtn = document.getElementById('step1NextBtn');
        const step2NextBtn = document.getElementById('step2NextBtn');
        if (step1NextBtn) step1NextBtn.disabled = true;
        if (step2NextBtn) step2NextBtn.disabled = true;

        // 重置选中状态
        document.querySelectorAll('.template-card, .config-type-card').forEach(card => {
            card.classList.remove('border-primary', 'bg-light', 'selected');
        });

        // 重置预览内容
        if (typeof window.smartFormConfigManager.clearPreview === 'function') {
            window.smartFormConfigManager.clearPreview();
        }

        // 渲染模板选择
        console.log('🚀 调用renderTemplateSelection...');
        window.smartFormConfigManager.renderTemplateSelection();
        window.smartFormConfigManager.updateWizardProgress();
    }
}

function showConfigList() {
    const modal = new bootstrap.Modal(document.getElementById('configListModal'));
    modal.show();
    loadConfigListData();
}

function loadPagePreview() {
    if (smartFormManager) {
        smartFormManager.loadPagePreview();
    }
}

function loadStepPage() {
    if (smartFormManager) {
        smartFormManager.loadPagePreview();
    }
}

function loadStepPageFromPanel() {
    if (smartFormManager) {
        const stepUrlInput = document.getElementById('stepTargetUrl');
        const url = stepUrlInput ? stepUrlInput.value.trim() : '';
        if (url) {
            // 同步URL到预览输入框
            const previewUrlInput = document.getElementById('previewUrlInput');
            if (previewUrlInput) {
                previewUrlInput.value = url;
            }
            smartFormManager.loadPagePreview(url);
        } else {
            smartFormManager.showAlert('请输入目标URL', 'warning');
        }
    }
}

function togglePreviewMode() {
    const preview = document.getElementById('pagePreview');
    if (preview.classList.contains('fullscreen')) {
        preview.classList.remove('fullscreen');
        preview.style.cssText = '';
    } else {
        preview.classList.add('fullscreen');
        preview.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            z-index: 1060;
            background: white;
        `;
    }
}

// previewConfig 函数已移除，不再需要预览配置功能

function testConfig() {
    if (smartFormManager) {
        smartFormManager.showAlert('配置测试功能开发中...', 'info');
    }
}

function saveConfig() {
    if (smartFormManager) {
        smartFormManager.saveConfig();
    }
}

async function loadConfigListData() {
    try {
        // 加载所有配置（只显示主绑定，避免重复）
        const result = await apiCall('/api/v1/page-bindings');

        const tbody = document.querySelector('#configListTable tbody');
        tbody.innerHTML = '';

        // 处理数据
        const allConfigs = [];

        if (result.code === 200 && result.data) {
            // 过滤出主绑定（parent_binding_id为null或0的记录）
            const mainBindings = result.data.filter(config =>
                !config.parentBindingId || config.parentBindingId === 0
            );

            mainBindings.forEach(config => {
                allConfigs.push({
                    ...config,
                    type: config.isMultiStep === 1 ? '多步骤' : '单页面',
                    stepCount: config.isMultiStep === 1 ? (config.subSteps?.length || 0) + 1 : 1
                });
            });
        }

        if (allConfigs.length === 0) {
            tbody.innerHTML = '<tr><td colspan="7" class="text-center text-muted">暂无配置数据</td></tr>';
            return;
        }

        allConfigs.forEach(config => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${config.bindingName || '未命名'}</td>
                <td><span class="badge ${config.type === '单页面' ? 'bg-primary' : 'bg-info'}">${config.type}</span></td>
                <td>${config.templateName || '未知'}</td>
                <td>${config.stepCount}</td>
                <td>
                    <span class="badge ${config.status === 1 ? 'bg-success' : 'bg-secondary'}">
                        ${config.status === 1 ? '启用' : '禁用'}
                    </span>
                </td>
                <td>${config.createdTime ? new Date(config.createdTime).toLocaleString() : '未知'}</td>
                <td>
                    <button class="btn btn-sm btn-outline-info me-1" onclick="viewConfig(${config.id}, '${config.type}')" title="查看">
                        <i class="bi bi-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-primary me-1" onclick="editConfig(${config.id}, '${config.type}')" title="编辑">
                        <i class="bi bi-pencil"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="deleteConfig(${config.id}, '${config.type}')" title="删除">
                        <i class="bi bi-trash"></i>
                    </button>
                </td>
            `;
            tbody.appendChild(row);
        });

    } catch (error) {
        console.error('加载配置列表失败:', error);
        const tbody = document.querySelector('#configListTable tbody');
        tbody.innerHTML = '<tr><td colspan="7" class="text-center text-danger">加载失败</td></tr>';
    }
}

// 查看配置详情
async function viewConfig(id, type) {
    try {
        console.log(`📋 查看配置详情: ID=${id}, Type=${type}`);

        // 获取配置详情
        const result = await apiCall(`/api/v1/page-bindings/${id}`);
        if (result.code !== 200) {
            throw new Error(result.message || '获取配置详情失败');
        }

        const config = result.data;

        // 构建详情HTML
        let detailsHtml = `
            <div class="config-details">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <strong>配置名称：</strong>${config.bindingName || '未命名'}
                    </div>
                    <div class="col-md-6">
                        <strong>配置类型：</strong>
                        <span class="badge ${config.isMultiStep === 1 ? 'bg-info' : 'bg-primary'}">
                            ${config.isMultiStep === 1 ? '多步骤' : '单步骤'}
                        </span>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <strong>业务模板：</strong>${config.templateName || '未知'}
                    </div>
                    <div class="col-md-6">
                        <strong>URL模式：</strong>${config.urlPattern || '未设置'}
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <strong>状态：</strong>
                        <span class="badge ${config.status === 1 ? 'bg-success' : 'bg-secondary'}">
                            ${config.status === 1 ? '启用' : '禁用'}
                        </span>
                    </div>
                    <div class="col-md-6">
                        <strong>创建时间：</strong>${config.createdTime ? new Date(config.createdTime).toLocaleString() : '未知'}
                    </div>
                </div>
        `;

        // 如果是多步骤，显示步骤信息
        if (config.isMultiStep === 1 && config.subSteps && config.subSteps.length > 0) {
            detailsHtml += `
                <div class="mb-3">
                    <strong>步骤信息：</strong>
                    <div class="mt-2">
                        <div class="step-item mb-2 p-2 border rounded">
                            <strong>步骤1：</strong>${config.stepName || '主步骤'}
                            <div class="text-muted small">字段数量: ${JSON.parse(config.bindingConfig || '[]').length}</div>
                        </div>
            `;

            config.subSteps.forEach((step, index) => {
                const stepConfig = JSON.parse(step.bindingConfig || '[]');
                const fieldCount = Array.isArray(stepConfig) ? stepConfig.length : (stepConfig.fieldMappings?.length || 0);

                detailsHtml += `
                    <div class="step-item mb-2 p-2 border rounded">
                        <strong>步骤${index + 2}：</strong>${step.stepName || `步骤${index + 2}`}
                        <div class="text-muted small">字段数量: ${fieldCount}</div>
                    </div>
                `;
            });

            detailsHtml += `
                    </div>
                </div>
            `;
        } else {
            // 单步骤显示字段信息
            const bindingConfig = JSON.parse(config.bindingConfig || '[]');
            const fieldCount = Array.isArray(bindingConfig) ? bindingConfig.length : (bindingConfig.fieldMappings?.length || 0);

            detailsHtml += `
                <div class="mb-3">
                    <strong>字段信息：</strong>
                    <div class="text-muted">绑定字段数量: ${fieldCount}</div>
                </div>
            `;
        }

        detailsHtml += `</div>`;

        // 显示模态框
        const modalHtml = `
            <div class="modal fade" id="viewConfigModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">配置详情</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            ${detailsHtml}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                            <button type="button" class="btn btn-primary" onclick="editConfig(${id}, '${type}')">编辑配置</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 移除已存在的模态框
        const existingModal = document.getElementById('viewConfigModal');
        if (existingModal) {
            existingModal.remove();
        }

        // 添加新模态框
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('viewConfigModal'));
        modal.show();

    } catch (error) {
        console.error('查看配置失败:', error);
        if (smartFormManager) {
            smartFormManager.showAlert('查看配置失败: ' + error.message, 'danger');
        }
    }
}

// 编辑配置
async function editConfig(id, type) {
    try {
        console.log(`✏️ 编辑配置: ID=${id}, Type=${type}`);

        // 获取配置详情
        const result = await apiCall(`/api/v1/page-bindings/${id}`);
        if (result.code !== 200) {
            throw new Error(result.message || '获取配置详情失败');
        }

        const config = result.data;

        // 关闭列表模态框
        const listModal = bootstrap.Modal.getInstance(document.getElementById('configListModal'));
        if (listModal) {
            listModal.hide();
        }

        // 关闭查看模态框（如果存在）
        const viewModal = bootstrap.Modal.getInstance(document.getElementById('viewConfigModal'));
        if (viewModal) {
            viewModal.hide();
        }

        // 等待模态框关闭
        setTimeout(() => {
            if (smartFormManager) {
                smartFormManager.loadConfigForEdit(config);
            }
        }, 300);

    } catch (error) {
        console.error('编辑配置失败:', error);
        if (smartFormManager) {
            smartFormManager.showAlert('编辑配置失败: ' + error.message, 'danger');
        }
    }
}

// 删除配置
async function deleteConfig(id, type) {
    const confirmed = await showConfirmDialog('确定要删除这个配置吗？此操作不可恢复。');
    if (!confirmed) {
        return;
    }

    try {
        console.log(`🗑️ 删除配置: ID=${id}, Type=${type}`);

        // 发送删除请求
        const result = await apiCall(`/api/v1/page-bindings/${id}`, 'DELETE');
        if (result.code !== 200) {
            throw new Error(result.message || '删除配置失败');
        }

        if (smartFormManager) {
            smartFormManager.showAlert('配置删除成功', 'success');
        }

        // 刷新列表
        await loadConfigListData();

        // 刷新主页面列表
        if (smartFormManager) {
            await smartFormManager.loadConfigList();
        }

    } catch (error) {
        console.error('删除配置失败:', error);
        if (smartFormManager) {
            smartFormManager.showAlert('删除配置失败: ' + error.message, 'danger');
        }
    }
}

// 切换配置状态（启用/禁用）
async function toggleConfigStatus(id, type, currentStatus) {
    const action = currentStatus === 1 ? '禁用' : '启用';
    const confirmed = await showConfirmDialog(`确定要${action}这个配置吗？`);
    if (!confirmed) {
        return;
    }

    try {
        console.log(`🔄 ${action}配置: ID=${id}, Type=${type}`);

        // 发送状态切换请求
        const newStatus = currentStatus === 1 ? 0 : 1;
        const result = await apiCall(`/api/v1/page-bindings/${id}/status`, 'PUT', { status: newStatus });

        if (result.code !== 200) {
            throw new Error(result.message || `${action}配置失败`);
        }

        if (smartFormManager) {
            smartFormManager.showAlert(`配置${action}成功`, 'success');
        }

        // 刷新列表
        await loadConfigListData();

        // 刷新主页面列表
        if (smartFormManager) {
            await smartFormManager.loadConfigList();
        }

    } catch (error) {
        console.error(`${action}配置失败:`, error);
        if (smartFormManager) {
            smartFormManager.showAlert(`${action}配置失败: ` + error.message, 'danger');
        }
    }
}

// 智能表单配置相关的全局函数
function updateStepName(name) {
    if (smartFormManager) {
        smartFormManager.updateStepName(name);
    }
}

function updateActionType(actionType) {
    if (smartFormManager) {
        smartFormManager.updateActionType(actionType);
    }
}

function selectActionTarget() {
    if (smartFormManager) {
        smartFormManager.selectActionTarget();
    }
}

function addNewStep() {
    if (smartFormManager) {
        smartFormManager.addNewStep();
    }
}

function exitBindingMode() {
    if (smartFormManager) {
        smartFormManager.exitBindingMode();
    }
}

function loadManualHtml() {
    console.log('📋 加载手动HTML内容...');

    // 先清理之前的状态
    if (smartFormManager) {
        console.log('🧹 清理之前的状态...');
        smartFormManager.resetBindingsOnPageChange();
    }

    const htmlInput = document.getElementById('manualHtmlInput');
    const html = htmlInput ? htmlInput.value.trim() : '';

    if (!html) {
        if (smartFormManager) {
            smartFormManager.showAlert('请输入HTML代码', 'warning');
        }
        return;
    }

    if (smartFormManager) {
        try {
            // 尝试解析为JSON（Chrome插件复制的格式）
            const data = JSON.parse(html);
            if (data.html && data.url) {
                console.log('📋 检测到Chrome插件复制的JSON数据');
                console.log('🎯 内容类型:', data.contentType || 'unknown');

                smartFormManager.renderPageContent(data.html, data.url);

                let contentTypeText = '';
                if (data.contentType === 'iframe') {
                    contentTypeText = ' (智能检测到iframe内容)';
                } else if (data.contentType === 'modal') {
                    contentTypeText = ' (智能检测到模态框内容)';
                } else if (data.contentType === 'overlay') {
                    contentTypeText = ' (智能检测到覆盖层内容)';
                } else {
                    contentTypeText = ' (完整页面内容)';
                }

                smartFormManager.showAlert(`✅ HTML内容已加载成功${contentTypeText}！现在可以开始绑定字段`, 'success');

                // 清空输入框
                htmlInput.value = '';
                return;
            }
        } catch (e) {
            // 不是JSON格式，继续检查是否是纯HTML
            console.log('📋 不是JSON格式，检查是否为纯HTML');
        }

        // 验证HTML格式
        if (!html.toLowerCase().includes('<html') || !html.toLowerCase().includes('</html>')) {
            smartFormManager.showAlert('请输入完整的HTML代码（包含&lt;html&gt;标签）或Chrome插件复制的JSON数据', 'warning');
            return;
        }

        smartFormManager.renderPageContent(html);
        smartFormManager.showAlert('HTML内容已加载成功！现在可以开始绑定字段', 'success');

        // 清空输入框
        htmlInput.value = '';
    }
}





















// 处理来自预览窗口的消息
function handlePreviewMessage(data) {
    console.log('🔄 开始处理预览消息:', data);

    if (!smartFormManager) {
        console.error('❌ smartFormManager未初始化');
        return;
    }

    switch (data.type) {
        case 'html-copied':
            console.log('📋 处理HTML复制消息');
            console.log('📏 HTML长度:', data.html ? data.html.length : 0);
            console.log('🌐 来源URL:', data.url);
            console.log('📡 消息来源:', data.source || 'unknown');
            console.log('🎯 内容类型:', data.contentType || 'unknown');

            try {
                // 根据内容类型显示不同的提示
                let contentTypeText = '';
                if (data.contentType === 'iframe') {
                    contentTypeText = ' (智能检测到iframe内容)';
                    console.log('🖼️ iframe信息:', data.iframeInfo);
                } else if (data.contentType === 'modal') {
                    contentTypeText = ' (智能检测到模态框内容)';
                } else if (data.contentType === 'overlay') {
                    contentTypeText = ' (智能检测到覆盖层内容)';
                } else {
                    contentTypeText = ' (完整页面内容)';
                }

                // 自动加载复制的HTML
                smartFormManager.renderPageContent(data.html, data.url);

                const sourceText = data.source === 'plugin' ? 'Chrome插件' : '预览窗口';
                smartFormManager.showAlert(`✅ HTML已从${sourceText}导入成功${contentTypeText}！现在可以开始绑定字段`, 'success');

                console.log('✅ HTML内容已成功渲染到预览区域');
            } catch (error) {
                console.error('❌ 渲染HTML失败:', error);
                smartFormManager.showAlert('❌ HTML导入失败: ' + error.message, 'danger');
            }
            break;

        case 'preview-closed':
            smartFormManager.showAlert('预览窗口已关闭', 'info');
            break;

        case 'preview-cancelled':
            smartFormManager.showAlert('预览已取消', 'info');
            break;

        default:
            console.log('⚠️ 未知的消息类型:', data.type);
    }
}

// 切换绑定模式
async function toggleBindingMode() {
    const manager = smartFormManager || window.smartFormManager || window.smartFormConfigManager;

    if (manager) {
        try {
            await manager.toggleBindingMode();
        } catch (error) {
            console.error('绑定模式切换失败:', error);
            alert('绑定模式切换失败: ' + error.message);
        }
    } else {
        alert('页面管理器未初始化，请刷新页面重试');

        // 尝试重新初始化
        if (window.SmartFormConfigManager) {
            try {
                window.smartFormConfigManager = new SmartFormConfigManager();
                window.smartFormManager = window.smartFormConfigManager;
                smartFormManager = window.smartFormConfigManager;
                await window.smartFormConfigManager.toggleBindingMode();
            } catch (error) {
                console.error('管理器初始化失败:', error);
                alert('管理器初始化失败: ' + error.message);
            }
        }
    }
}



    const previewContent = document.getElementById('previewContent');
    const iframe = previewContent ? previewContent.querySelector('iframe.preview-iframe') : null;
    console.log('  - DOM中的iframe:', !!iframe);

    if (iframe) {
        try {
            const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
            console.log('  - iframe文档可访问:', !!iframeDoc);
            if (iframeDoc) {
                console.log('  - iframe readyState:', iframeDoc.readyState);
                console.log('  - iframe body存在:', !!iframeDoc.body);
                console.log('  - iframe URL:', iframeDoc.URL || 'unknown');
                const inputs = iframeDoc.querySelectorAll('input, select, textarea');
                console.log('  - 可绑定元素数量:', inputs.length);
                if (inputs.length > 0) {
                    console.log('  - 前3个元素:', Array.from(inputs).slice(0, 3).map(el => ({
                        tag: el.tagName,
                        type: el.type,
                        name: el.name,
                        id: el.id
                    })));
                }
            }
        } catch (error) {
            console.log('  - iframe访问错误:', error.message);
            console.log('  - 错误类型:', error.name);
        }
    }
}

// 强制刷新iframe引用
function refreshIframeReference() {
    if (!smartFormManager) {
        console.log('❌ smartFormManager未初始化');
        return;
    }

    console.log('🔄 强制刷新iframe引用...');
    const previewContent = document.getElementById('previewContent');
    const iframe = previewContent ? previewContent.querySelector('iframe.preview-iframe') : null;

    if (iframe) {
        try {
            const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
            if (iframeDoc && iframeDoc.body) {
                smartFormManager.currentIframe = iframe;
                smartFormManager.currentIframeDoc = iframeDoc;
                console.log('✅ iframe引用已刷新');
                return true;
            } else {
                console.log('⚠️ iframe文档仍然不可访问');
                return false;
            }
        } catch (error) {
            console.log('❌ 刷新iframe引用失败:', error.message);
            return false;
        }
    } else {
        console.log('❌ 未找到iframe元素');
        return false;
    }
}

function showBindingHelp() {
    const manager = smartFormManager || window.smartFormManager || window.smartFormConfigManager;
    if (manager) {
        manager.showAlert(`
            <strong>绑定帮助：</strong><br>
            1. 在左侧选择要绑定的字段<br>
            2. 点击"开始绑定"按钮<br>
            3. 在页面预览中点击对应的输入框<br>
            4. 系统会自动建立绑定关系<br>
            <br>
            <strong>提示：</strong>可以点击提示条上的"-"按钮最小化提示
        `, 'info');
    } else {
        alert('页面管理器未初始化，请刷新页面重试');
    }
}

function minimizeBindingMode() {
    const bindingMode = document.getElementById('bindingMode');
    const minimizeBtn = document.getElementById('minimizeBindingBtn');

    if (bindingMode && minimizeBtn) {
        if (bindingMode.classList.contains('minimized')) {
            // 展开
            bindingMode.classList.remove('minimized');
            minimizeBtn.innerHTML = '<i class="bi bi-dash"></i>';
            minimizeBtn.title = '最小化提示';
        } else {
            // 最小化
            bindingMode.classList.add('minimized');
            minimizeBtn.innerHTML = '<i class="bi bi-plus"></i>';
            minimizeBtn.title = '展开提示';
        }
    }
}

function showBindingsList() {
    const manager = smartFormManager || window.smartFormManager || window.smartFormConfigManager;
    if (manager) {
        manager.showBindingsListModal();
    } else {
        alert('页面管理器未初始化，请刷新页面重试');
    }
}

// 模板搜索和筛选功能
function filterTemplates() {
    const searchInput = document.getElementById('templateSearchInput');
    const categoryFilter = document.getElementById('templateCategoryFilter');
    const templateCards = document.querySelectorAll('.template-card');
    const noTemplatesFound = document.getElementById('noTemplatesFound');

    if (!searchInput || !categoryFilter) return;

    const searchTerm = searchInput.value.toLowerCase().trim();
    const selectedCategory = categoryFilter.value;

    let visibleCount = 0;

    templateCards.forEach(card => {
        const titleElement = card.querySelector('.template-title');
        const descElement = card.querySelector('.template-desc');
        const title = (titleElement && titleElement.textContent ? titleElement.textContent.toLowerCase() : '') || '';
        const desc = (descElement && descElement.textContent ? descElement.textContent.toLowerCase() : '') || '';
        const category = card.dataset.category || '';

        const matchesSearch = !searchTerm || title.includes(searchTerm) || desc.includes(searchTerm);
        const matchesCategory = !selectedCategory || category === selectedCategory;

        if (matchesSearch && matchesCategory) {
            card.style.display = '';
            visibleCount++;
        } else {
            card.style.display = 'none';
        }
    });

    // 显示/隐藏无结果提示
    if (noTemplatesFound) {
        noTemplatesFound.style.display = visibleCount === 0 ? 'block' : 'none';
    }
}

// 粘贴HTML内容功能
function pasteHtmlContent() {
    // 方案1: 优先尝试现代剪贴板API（HTTPS或localhost）
    if (navigator.clipboard && navigator.clipboard.readText && window.isSecureContext) {

        navigator.clipboard.readText().then(text => {
            console.log('📋 从剪贴板读取到内容，长度:', text?.length || 0);

            if (text && text.trim()) {
                processClipboardContent(text.trim());
            } else {
                console.log('⚠️ 剪贴板内容为空，尝试用户交互方式');
                tryUserInteractionPaste();
            }
        }).catch(error => {
            console.error('❌ 剪贴板API读取失败:', error);
            console.log('🔄 尝试用户交互方式');
            tryUserInteractionPaste();
        });
    } else {
        console.log('⚠️ 剪贴板API不可用，尝试用户交互方式');
        tryUserInteractionPaste();
    }
}

// 尝试用户交互方式粘贴（适用于HTTP环境）
function tryUserInteractionPaste() {
    console.log('🔄 尝试用户交互方式粘贴');

    // 创建一个临时的可编辑区域
    const tempTextarea = document.createElement('textarea');
    tempTextarea.style.position = 'fixed';
    tempTextarea.style.top = '50%';
    tempTextarea.style.left = '50%';
    tempTextarea.style.transform = 'translate(-50%, -50%)';
    tempTextarea.style.width = '400px';
    tempTextarea.style.height = '100px';
    tempTextarea.style.zIndex = '1000000'; // 更高的z-index
    tempTextarea.style.border = '2px solid #007bff';
    tempTextarea.style.borderRadius = '8px';
    tempTextarea.style.padding = '10px';
    tempTextarea.style.fontSize = '14px';
    tempTextarea.style.backgroundColor = '#ffffff'; // 改为白色背景，更明显
    tempTextarea.style.outline = 'none';
    tempTextarea.style.resize = 'none';
    tempTextarea.style.fontFamily = 'monospace';
    tempTextarea.style.pointerEvents = 'auto'; // 确保可以接收所有交互事件
    tempTextarea.style.userSelect = 'text'; // 确保可以选择文本
    tempTextarea.style.cursor = 'text'; // 显示文本光标
    tempTextarea.placeholder = '请按 Ctrl+V 粘贴HTML内容，然后按回车确认';
    tempTextarea.setAttribute('autocomplete', 'off');
    tempTextarea.setAttribute('spellcheck', 'false');
    tempTextarea.setAttribute('tabindex', '0');

    // 创建提示文本
    const tipDiv = document.createElement('div');
    tipDiv.style.position = 'fixed';
    tipDiv.style.top = 'calc(50% - 80px)';
    tipDiv.style.left = '50%';
    tipDiv.style.transform = 'translateX(-50%)';
    tipDiv.style.zIndex = '1000001'; // 比文本框更高
    tipDiv.style.backgroundColor = '#007bff';
    tipDiv.style.color = 'white';
    tipDiv.style.padding = '8px 16px';
    tipDiv.style.borderRadius = '4px';
    tipDiv.style.fontSize = '12px';
    tipDiv.innerHTML = '💡 请按 <kbd>Ctrl+V</kbd> 粘贴内容，然后按 <kbd>Enter</kbd> 确认';

    // 创建遮罩层
    const overlay = document.createElement('div');
    overlay.style.position = 'fixed';
    overlay.style.top = '0';
    overlay.style.left = '0';
    overlay.style.width = '100%';
    overlay.style.height = '100%';
    overlay.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
    overlay.style.zIndex = '999998';
    overlay.style.pointerEvents = 'auto'; // 确保可以接收点击事件

    // 检查是否在模态框内，如果是，添加到模态框而不是body
    const modal = document.querySelector('#configWizardModal');
    const appendTarget = (modal && modal.style.display !== 'none') ? modal : document.body;

    appendTarget.appendChild(overlay);
    appendTarget.appendChild(tipDiv);
    appendTarget.appendChild(tempTextarea);

    console.log('📦 粘贴界面已添加到:', appendTarget === modal ? '模态框内' : 'document.body');

    // 确保元素完全渲染后再聚焦
    setTimeout(() => {
        tempTextarea.focus();
        tempTextarea.select(); // 选中所有内容（如果有的话）
        console.log('✅ 文本框已聚焦');
    }, 50);

    // 清理界面的统一方法
    const cleanup = () => {
        try {
            // 移除所有事件监听器
            tempTextarea.removeEventListener('keydown', handleKeydown);
            tempTextarea.removeEventListener('paste', handlePaste);
            tempTextarea.removeEventListener('click', handleTextareaClick);
            tempTextarea.removeEventListener('mousedown', handleTextareaEvent);
            tempTextarea.removeEventListener('mouseup', handleTextareaEvent);
            tempTextarea.removeEventListener('focus', handleTextareaEvent);
            tempTextarea.removeEventListener('input', handleTextareaEvent);

            overlay.removeEventListener('click', handleOverlayClick);
            overlay.removeEventListener('keydown', handleOverlayEvent);
            overlay.removeEventListener('keyup', handleOverlayEvent);
            overlay.removeEventListener('keypress', handleOverlayEvent);

            // 移除DOM元素（从正确的父容器中移除）
            if (overlay.parentNode) overlay.parentNode.removeChild(overlay);
            if (tipDiv.parentNode) tipDiv.parentNode.removeChild(tipDiv);
            if (tempTextarea.parentNode) tempTextarea.parentNode.removeChild(tempTextarea);

            console.log('✅ 粘贴界面已清理');
        } catch (error) {
            console.warn('⚠️ 清理界面时出错:', error);
        }
    };

    // 处理键盘事件
    const handleKeydown = (e) => {
        console.log('⌨️ 键盘事件:', e.key);

        // 阻止事件冒泡，防止触发父级元素的事件
        e.stopPropagation();

        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            e.stopImmediatePropagation(); // 阻止同级事件监听器

            const content = tempTextarea.value.trim();

            cleanup();

            if (content) {
                console.log('✅ 用户交互方式获取到内容，长度:', content.length);
                processClipboardContent(content);
            } else {
                if (smartFormManager) {
                    smartFormManager.showAlert('未检测到粘贴内容，请重试', 'warning');
                }
            }
        } else if (e.key === 'Escape') {
            e.preventDefault();
            e.stopImmediatePropagation();
            console.log('🚫 用户取消粘贴操作');
            cleanup();
        }
    };

    // 处理粘贴事件
    const handlePaste = (e) => {
        console.log('📋 检测到粘贴事件');
        // 让浏览器正常处理粘贴，然后在下一个事件循环中处理内容
        setTimeout(() => {
            const content = tempTextarea.value.trim();
            if (content) {
                console.log('✅ 粘贴事件获取到内容，长度:', content.length);

                cleanup();
                processClipboardContent(content);
            } else {
                console.log('⚠️ 粘贴内容为空');
            }
        }, 100);
    };

    // 点击遮罩层取消（但不要阻挡文本框）
    const handleOverlayClick = (e) => {
        // 检查点击的是否是遮罩层本身，而不是文本框或提示
        if (e.target === overlay) {
            console.log('🚫 用户点击遮罩取消');
            cleanup();
        }
    };

    // 阻止文本框的点击事件冒泡
    const handleTextareaClick = (e) => {
        e.stopPropagation();
        e.stopImmediatePropagation();
        console.log('📝 文本框被点击');
    };

    // 阻止文本框的所有事件冒泡
    const handleTextareaEvent = (e) => {
        e.stopPropagation();
        e.stopImmediatePropagation();
    };

    // 阻止遮罩层的所有事件冒泡
    const handleOverlayEvent = (e) => {
        e.stopPropagation();
        e.stopImmediatePropagation();
    };

    // 添加事件监听器
    tempTextarea.addEventListener('keydown', handleKeydown);
    tempTextarea.addEventListener('paste', handlePaste);
    tempTextarea.addEventListener('click', handleTextareaClick);
    tempTextarea.addEventListener('mousedown', handleTextareaEvent);
    tempTextarea.addEventListener('mouseup', handleTextareaEvent);
    tempTextarea.addEventListener('focus', handleTextareaEvent);
    tempTextarea.addEventListener('input', handleTextareaEvent);

    // 遮罩层事件
    overlay.addEventListener('click', handleOverlayClick);
    overlay.addEventListener('keydown', handleOverlayEvent);
    overlay.addEventListener('keyup', handleOverlayEvent);
    overlay.addEventListener('keypress', handleOverlayEvent);

    console.log('✅ 粘贴界面已创建，等待用户操作...');
}

// 注意：showHtmlInputDialog 已被 tryUserInteractionPaste 替代

// 处理剪贴板内容
function processClipboardContent(text) {
    if (!text || !text.trim()) {
        if (smartFormManager) {
            smartFormManager.showAlert('剪贴板为空，请先复制HTML内容', 'warning');
        }
        return;
    }

    try {
        // 尝试解析为JSON（Chrome插件复制的格式）
        const data = JSON.parse(text);
        if (data.html && data.url) {
            console.log('📋 检测到Chrome插件复制的JSON数据');
            console.log('🎯 内容类型:', data.contentType || 'unknown');

            if (smartFormManager) {
                const previewContent = document.getElementById('previewContent');
                if (!previewContent) {
                    smartFormManager.showAlert('❌ 预览区域不存在，请刷新页面重试', 'danger');
                    return;
                }

                try {
                    smartFormManager.renderPageContent(data.html, data.url);
                } catch (error) {
                    console.error('页面渲染失败:', error);
                    smartFormManager.showAlert('❌ 页面渲染失败: ' + error.message, 'danger');
                    return;
                }

                let contentTypeText = '';
                if (data.contentType === 'iframe') {
                    contentTypeText = ' (智能检测到iframe内容)';
                } else if (data.contentType === 'modal') {
                    contentTypeText = ' (智能检测到模态框内容)';
                } else if (data.contentType === 'overlay') {
                    contentTypeText = ' (智能检测到覆盖层内容)';
                } else {
                    contentTypeText = ' (完整页面内容)';
                }

                smartFormManager.showAlert(`✅ HTML内容已从剪贴板加载成功${contentTypeText}！现在可以开始绑定字段`, 'success');
            } else {
                // 尝试使用全局变量
                const manager = window.smartFormManager || window.smartFormConfigManager;
                if (manager) {
                    manager.renderPageContent(data.html, data.url);
                    manager.showAlert('✅ HTML内容已加载成功！现在可以开始绑定字段', 'success');
                } else {
                    alert('页面管理器未初始化，请刷新页面重试');
                }
            }
            return;
        }
    } catch (e) {
        // 不是JSON格式，继续检查是否是纯HTML
        console.log('📋 不是JSON格式，检查是否为纯HTML');
    }

    // 检查是否看起来像HTML
    if (text.includes('<html') || text.includes('<!DOCTYPE') || text.includes('<body') || text.includes('<div')) {
        if (smartFormManager) {
            smartFormManager.renderPageContent(text.trim());
            smartFormManager.showAlert('HTML内容已从剪贴板加载成功！现在可以开始绑定字段', 'success');
        }
    } else {
        if (smartFormManager) {
            smartFormManager.showAlert('剪贴板中的内容不像是HTML代码，请确认内容正确', 'warning');
        }
    }
}

function exitWizard() {
    // 退出向导时刷新配置列表
    if (smartFormManager) {
        smartFormManager.loadConfigList();
    }
}



// 页面初始化函数 - 按照admin.js的命名规则
async function initSmartFormConfigPage() {
    console.log('🚀 初始化智能表单配置页面');
    console.log('🚀 当前smartFormManager状态:', !!smartFormManager);

    try {
        // 如果已经存在实例，先完全清理
        if (smartFormManager) {
            console.log('🧹 清理现有实例...');

            // 强制退出所有模式
            if (smartFormManager.bindingMode) {
                smartFormManager.exitBindingMode();
            }
            if (smartFormManager.actionMode) {
                smartFormManager.exitActionMode();
            }

            // 清理iframe引用
            smartFormManager.cleanupIframeReferences();

            // 清理全局事件监听器
            if (smartFormManager.escapeKeyHandler) {
                document.removeEventListener('keydown', smartFormManager.escapeKeyHandler);
            }

            console.log('✅ 现有实例已清理');
        }

        // 清理页面状态
        console.log('🧹 清理页面状态...');

        // 清理预览区域
        const previewContent = document.getElementById('previewContent');
        if (previewContent) {
            previewContent.innerHTML = `
                <div class="text-center py-5 text-muted">
                    <i class="bi bi-file-earmark-text" style="font-size: 3rem;"></i>
                    <p class="mt-3">请粘贴HTML内容到上方文本框</p>
                </div>
            `;
            previewContent.classList.remove('binding-active');
        }

        // 重置按钮状态
        const bindingToggleBtn = document.getElementById('bindingToggleBtn');
        if (bindingToggleBtn) {
            bindingToggleBtn.innerHTML = '<i class="bi bi-magic"></i> 开始绑定';
            bindingToggleBtn.classList.remove('btn-outline-danger');
            bindingToggleBtn.classList.add('btn-outline-success');
        }

        // 清理任何残留的遮罩
        const existingMask = document.getElementById('bindingLoadingMask');
        if (existingMask) {
            existingMask.remove();
        }

        console.log('✅ 页面状态已清理');

        // 创建新实例 - 避免重复创建
        if (window.smartFormConfigManager) {
            console.log('🔄 SmartFormConfigManager实例已存在，重新初始化...');
            window.smartFormConfigManager.init();
        } else {
            window.smartFormConfigManager = new SmartFormConfigManager();
            console.log('✅ SmartFormConfigManager实例创建成功');
        }

        // 创建全局别名以保持兼容性
        window.smartFormManager = window.smartFormConfigManager;
        smartFormManager = window.smartFormConfigManager; // 同时赋值给全局变量

        // 等待初始化完成
        console.log('⏳ 等待初始化完成...');
        // 由于构造函数中已经调用了init()，这里不需要再次调用
    } catch (error) {
        console.error('❌ SmartFormConfigManager实例创建失败:', error);
    }
}

// 兼容性函数
function initSmartFormConfig() {
    return initSmartFormConfigPage();
}

// 页面加载完成后初始化消息监听器
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 初始化配置页面消息监听器...');

    // 注意：不在这里初始化smartFormManager，而是等待admin.js调用initSmartFormConfigPage()

    // 添加全局消息监听器（只添加一次）
    window.addEventListener('message', function(event) {
        console.log('📨 收到postMessage消息:', event.data);

        // 处理来自任何窗口的HTML复制消息
        if (event.data && event.data.type === 'html-copied') {
            console.log('✅ 处理HTML复制消息');
            handlePreviewMessage(event.data);
        }
    });

    // 监听Chrome插件消息
    window.addEventListener('message', function(event) {
        console.log('📦 收到postMessage消息:', event.data);

        if (event.data && event.data.type === 'HTML_EXTRACTED') {
            try {
                console.log('📦 处理Chrome插件HTML消息:', event.data);
                handlePreviewMessage(event.data);
            } catch (e) {
                console.error('❌ 处理Chrome插件消息失败:', e);
            }
        }
    });

    // 检查Chrome插件存储的HTML数据
    if (typeof chrome !== 'undefined' && chrome.storage) {
        chrome.storage.local.get(['extractedHTML'], function(result) {
            if (result.extractedHTML) {
                console.log('🔄 发现Chrome插件提取的HTML:', result.extractedHTML);

                // 延迟处理，确保页面完全加载
                setTimeout(() => {
                    console.log('✅ 处理Chrome插件HTML数据');
                    handlePreviewMessage({
                        type: 'HTML_EXTRACTED',
                        data: result.extractedHTML
                    });
                    // 清除已处理的数据
                    chrome.storage.local.remove(['extractedHTML']);
                }, 1000);
            }
        });
    }

    console.log('✅ 全局消息监听器已添加完成');
});

// 显示确认对话框
function showConfirmDialog(message, title = '确认操作') {
    return new Promise((resolve) => {
        // 创建确认模态框
        const modalHtml = `
            <div class="modal fade" id="confirmDialog" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="bi bi-question-circle me-2"></i>${title}
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <p>${message}</p>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" id="cancelBtn">取消</button>
                            <button type="button" class="btn btn-primary" id="confirmBtn">确定</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 移除已存在的确认对话框
        const existingModal = document.getElementById('confirmDialog');
        if (existingModal) {
            existingModal.remove();
        }

        // 添加模态框到页面
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // 获取模态框元素
        const modalElement = document.getElementById('confirmDialog');
        const modal = new bootstrap.Modal(modalElement);

        // 绑定按钮事件
        const confirmBtn = document.getElementById('confirmBtn');
        const cancelBtn = document.getElementById('cancelBtn');

        confirmBtn.addEventListener('click', () => {
            modal.hide();
            resolve(true);
        });

        cancelBtn.addEventListener('click', () => {
            modal.hide();
            resolve(false);
        });

        // 模态框关闭时也返回false
        modalElement.addEventListener('hidden.bs.modal', () => {
            modalElement.remove();
        });

        // 显示模态框
        modal.show();
    });
}

// 智能表单配置页面全局函数 - 使用命名空间避免冲突
(function() {
    'use strict';

    // 全局变量 - 避免重复声明
    if (typeof window.SmartFormConfigGlobals === 'undefined') {
        window.SmartFormConfigGlobals = {
            currentViewMode: 'card'
        };
    }

    // 视图切换函数
    window.switchView = function(viewMode) {
        window.SmartFormConfigGlobals.currentViewMode = viewMode;

        // 更新按钮状态
        const cardBtn = document.getElementById('cardViewBtn');
        const tableBtn = document.getElementById('tableViewBtn');

        if (cardBtn && tableBtn) {
            if (viewMode === 'card') {
                cardBtn.classList.add('active');
                tableBtn.classList.remove('active');
            } else {
                tableBtn.classList.add('active');
                cardBtn.classList.remove('active');
            }
        }

        // 更新管理器的视图模式
        if (window.smartFormConfigManager) {
            window.smartFormConfigManager.currentViewMode = viewMode;
            // 重新渲染当前数据
            window.smartFormConfigManager.renderConfigList();
        }
    };

    // 搜索配置
    window.searchConfigs = function() {
        const searchInput = document.getElementById('configSearchInput');
        const statusFilter = document.getElementById('statusFilter');
        const typeFilter = document.getElementById('typeFilter');

        if (!window.smartFormConfigManager) return;

        const searchTerm = searchInput ? searchInput.value.trim().toLowerCase() : '';
        const statusValue = statusFilter ? statusFilter.value : '';
        const typeValue = typeFilter ? typeFilter.value : '';

        let filteredConfigs = window.smartFormConfigManager.allConfigs || [];

        // 按名称搜索
        if (searchTerm) {
            filteredConfigs = filteredConfigs.filter(config =>
                (config.bindingName || '').toLowerCase().includes(searchTerm) ||
                (config.description || '').toLowerCase().includes(searchTerm) ||
                (config.templateName || '').toLowerCase().includes(searchTerm)
            );
        }

        // 按状态过滤
        if (statusValue !== '') {
            filteredConfigs = filteredConfigs.filter(config =>
                config.status === parseInt(statusValue)
            );
        }

        // 按类型过滤
        if (typeValue) {
            filteredConfigs = filteredConfigs.filter(config =>
                config.type === typeValue
            );
        }

        window.smartFormConfigManager.renderConfigList(filteredConfigs);
    };

    // 重置搜索
    window.resetSearch = function() {
        const searchInput = document.getElementById('configSearchInput');
        const statusFilter = document.getElementById('statusFilter');
        const typeFilter = document.getElementById('typeFilter');

        if (searchInput) searchInput.value = '';
        if (statusFilter) statusFilter.value = '';
        if (typeFilter) typeFilter.value = '';

        if (window.smartFormConfigManager) {
            window.smartFormConfigManager.renderConfigList();
        }
    };

    // 过滤配置（下拉框变化时调用）
    window.filterConfigs = function() {
        window.searchConfigs();
    };

    // 页面清理函数 - 在页面切换时调用
    window.cleanupSmartFormConfigPage = function() {
        console.log('🧹 智能表单配置页面资源清理开始...');

        try {
            // 清理管理器实例
            if (window.smartFormConfigManager && typeof window.smartFormConfigManager.cleanup === 'function') {
                window.smartFormConfigManager.cleanup();
            }

            // 清理全局变量
            if (window.SmartFormConfigGlobals) {
                window.SmartFormConfigGlobals = null;
            }

            console.log('✅ 智能表单配置页面资源清理完成');
        } catch (error) {
            console.error('❌ 智能表单配置页面资源清理失败:', error);
        }
    };

})();
