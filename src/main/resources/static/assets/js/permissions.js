/**
 * 权限管理页面JavaScript
 */

let permissionTreeData = [];
let currentPermissionId = null;

// 权限管理页面初始化函数
function initPermissionsPage() {
    console.log('权限管理页面初始化');
    loadPermissionTree();

    // 绑定搜索框回车事件
    const searchInput = document.getElementById('searchKeyword');
    if (searchInput) {
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                filterPermissions();
            }
        });
    }
}

/**
 * 加载权限树
 */
function loadPermissionTree() {
    const container = document.getElementById('permissionTreeContainer');
    if (!container) return;

    // 显示加载状态
    container.innerHTML = `
        <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <div class="mt-2 text-muted">正在加载权限树...</div>
        </div>
    `;

    fetch('/api/v1/permissions/tree', {
        method: 'GET',
        headers: {
            'Authorization': 'Bearer ' + getToken(),
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(result => {
        console.log('权限树API响应:', result); // 添加调试信息
        if (result.code === 200) {
            permissionTreeData = result.data;
            displayPermissionTree(permissionTreeData);
            loadParentOptions(permissionTreeData);
        } else {
            container.innerHTML = '<div class="alert alert-danger">加载权限树失败: ' + result.message + '</div>';
        }
    })
    .catch(error => {
        console.error('加载权限树失败:', error);

        // 如果API调用失败，显示测试数据
        console.log('权限树API调用失败，使用测试数据');
        const testData = [
            {
                id: 1,
                permissionName: '系统管理',
                permissionCode: 'SYSTEM',
                permissionType: 1,
                status: 1,
                description: '系统管理模块',
                children: [
                    {
                        id: 2,
                        permissionName: '用户管理',
                        permissionCode: 'USER_MANAGE',
                        permissionType: 1,
                        status: 1,
                        description: '用户管理页面',
                        children: [
                            {
                                id: 3,
                                permissionName: '新增用户',
                                permissionCode: 'USER_ADD',
                                permissionType: 2,
                                status: 1,
                                description: '新增用户按钮'
                            },
                            {
                                id: 4,
                                permissionName: '删除用户',
                                permissionCode: 'USER_DELETE',
                                permissionType: 2,
                                status: 1,
                                description: '删除用户按钮'
                            }
                        ]
                    },
                    {
                        id: 5,
                        permissionName: '角色管理',
                        permissionCode: 'ROLE_MANAGE',
                        permissionType: 1,
                        status: 1,
                        description: '角色管理页面'
                    }
                ]
            }
        ];
        permissionTreeData = testData;
        displayPermissionTree(permissionTreeData);
        loadParentOptions(permissionTreeData);
    });
}

/**
 * 显示权限树
 */
function displayPermissionTree(permissions) {
    const container = document.getElementById('permissionTreeContainer');
    if (!container) return;

    console.log('displayPermissionTree接收到的数据:', permissions); // 添加调试信息

    if (!permissions || permissions.length === 0) {
        container.innerHTML = '<div class="text-center py-5 text-muted">暂无权限数据</div>';
        return;
    }

    container.innerHTML = '';
    permissions.forEach(permission => {
        const node = createPermissionTreeNode(permission);
        container.appendChild(node);
    });
}

/**
 * 创建权限树节点
 */
function createPermissionTreeNode(permission, level = 0) {
    const node = document.createElement('div');
    node.className = `permission-node level-${level}`;
    node.setAttribute('data-permission-id', permission.id);

    const typeText = getPermissionTypeText(permission.permissionType);
    const typeClass = getPermissionTypeClass(permission.permissionType);
    const icon = getPermissionIcon(permission.permissionType);

    const hasChildren = permission.children && permission.children.length > 0;

    node.innerHTML = `
        <div class="permission-node-header">
            <div class="permission-node-content">
                <button class="permission-toggle ${hasChildren ? '' : 'no-children'}"
                        onclick="togglePermissionNode(this)"
                        ${hasChildren ? '' : 'style="visibility: hidden;"'}>
                    <i class="bi bi-chevron-down"></i>
                </button>
                <i class="bi bi-${icon} me-2"></i>
                <div class="permission-info">
                    <div>
                        <strong>${permission.permissionName}</strong>
                        <code class="ms-2">${permission.permissionCode}</code>
                        <span class="badge ${typeClass} permission-type-badge ms-2">${typeText}</span>
                        <span class="badge ${permission.status === 1 ? 'bg-success' : 'bg-secondary'} ms-2">
                            ${permission.status === 1 ? '启用' : '禁用'}
                        </span>
                    </div>
                    ${permission.description ? `<div class="text-muted mt-1">${permission.description}</div>` : ''}
                </div>
            </div>
            <div class="permission-actions">
                <button class="btn btn-outline-primary btn-sm" onclick="editPermission(${permission.id})" title="编辑" data-permission="PERMISSION_EDIT">
                    <i class="bi bi-pencil"></i>
                </button>
                <button class="btn btn-outline-success btn-sm" onclick="addChildPermission(${permission.id})" title="添加子权限" data-permission="PERMISSION_CREATE">
                    <i class="bi bi-plus"></i>
                </button>
                <button class="btn btn-outline-warning btn-sm" onclick="togglePermissionStatus(${permission.id}, ${permission.status})" title="${permission.status === 1 ? '禁用' : '启用'}" data-permission="PERMISSION_EDIT">
                    <i class="bi bi-${permission.status === 1 ? 'pause' : 'play'}"></i>
                </button>
                <button class="btn btn-outline-danger btn-sm" onclick="deletePermission(${permission.id})" title="删除" data-permission="PERMISSION_DELETE">
                    <i class="bi bi-trash"></i>
                </button>
            </div>
        </div>
    `;

    // 添加子权限
    if (hasChildren) {
        const childrenContainer = document.createElement('div');
        childrenContainer.className = 'permission-children';

        permission.children.forEach(child => {
            const childNode = createPermissionTreeNode(child, level + 1);
            childrenContainer.appendChild(childNode);
        });

        node.appendChild(childrenContainer);
    }

    return node;
}

/**
 * 切换权限节点的展开/折叠状态
 */
function togglePermissionNode(toggleButton) {
    const node = toggleButton.closest('.permission-node');
    const childrenContainer = node.querySelector('.permission-children');

    if (!childrenContainer) return;

    const isCollapsed = childrenContainer.classList.contains('collapsed');

    if (isCollapsed) {
        // 展开
        childrenContainer.classList.remove('collapsed');
        toggleButton.classList.remove('collapsed');
        toggleButton.querySelector('i').className = 'bi bi-chevron-down';
    } else {
        // 折叠
        childrenContainer.classList.add('collapsed');
        toggleButton.classList.add('collapsed');
        toggleButton.querySelector('i').className = 'bi bi-chevron-right';
    }
}

/**
 * 展开所有权限节点
 */
function expandAllPermissions() {
    document.querySelectorAll('.permission-children').forEach(container => {
        container.classList.remove('collapsed');
    });
    document.querySelectorAll('.permission-toggle').forEach(toggle => {
        toggle.classList.remove('collapsed');
        const icon = toggle.querySelector('i');
        if (icon) {
            icon.className = 'bi bi-chevron-down';
        }
    });
}

/**
 * 折叠所有权限节点
 */
function collapseAllPermissions() {
    document.querySelectorAll('.permission-children').forEach(container => {
        container.classList.add('collapsed');
    });
    document.querySelectorAll('.permission-toggle').forEach(toggle => {
        if (!toggle.classList.contains('no-children')) {
            toggle.classList.add('collapsed');
            const icon = toggle.querySelector('i');
            if (icon) {
                icon.className = 'bi bi-chevron-right';
            }
        }
    });
}

/**
 * 获取权限类型文本
 */
function getPermissionTypeText(type) {
    switch (type) {
        case 1: return '菜单';
        case 2: return '按钮';
        case 3: return '接口';
        default: return '未知';
    }
}

/**
 * 获取权限类型样式类
 */
function getPermissionTypeClass(type) {
    switch (type) {
        case 1: return 'bg-primary';
        case 2: return 'bg-success';
        case 3: return 'bg-info';
        default: return 'bg-secondary';
    }
}

/**
 * 获取权限图标
 */
function getPermissionIcon(type) {
    switch (type) {
        case 1: return 'folder';
        case 2: return 'cursor';
        case 3: return 'gear';
        default: return 'question-circle';
    }
}

/**
 * 筛选权限
 */
function filterPermissions() {
    const typeFilter = document.getElementById('typeFilter')?.value || '';
    const statusFilter = document.getElementById('statusFilter')?.value || '';
    const keyword = document.getElementById('searchKeyword')?.value?.toLowerCase() || '';

    const filteredTree = filterPermissionTree(permissionTreeData, typeFilter, statusFilter, keyword);
    displayPermissionTree(filteredTree);
}

/**
 * 递归筛选权限树
 */
function filterPermissionTree(permissions, typeFilter, statusFilter, keyword) {
    return permissions.filter(permission => {
        // 类型筛选
        if (typeFilter && permission.permissionType.toString() !== typeFilter) {
            return false;
        }
        
        // 状态筛选
        if (statusFilter && permission.status.toString() !== statusFilter) {
            return false;
        }
        
        // 关键词筛选
        if (keyword && 
            !permission.permissionName.toLowerCase().includes(keyword) &&
            !permission.permissionCode.toLowerCase().includes(keyword)) {
            return false;
        }
        
        return true;
    }).map(permission => {
        // 递归筛选子权限
        const filteredChildren = permission.children ? 
            filterPermissionTree(permission.children, typeFilter, statusFilter, keyword) : [];
        
        return {
            ...permission,
            children: filteredChildren
        };
    });
}

/**
 * 刷新权限树
 */
function refreshPermissionTree() {
    loadPermissionTree();
}

/**
 * 显示创建权限模态框
 */
function showCreatePermissionModal() {
    console.log('显示创建权限模态框');
    currentPermissionId = null;

    // 先清理现有模态框
    const existingModal = document.getElementById('permissionModal');
    if (existingModal) {
        const modalInstance = bootstrap.Modal.getInstance(existingModal);
        if (modalInstance) {
            modalInstance.hide();
        }
        existingModal.remove();
    }

    // 创建权限模态框HTML
    const modalHtml = `
        <div class="modal fade" id="permissionModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="permissionModalTitle">新增权限</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="permissionForm">
                            <input type="hidden" id="permissionId">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="permissionName" class="form-label">权限名称 <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="permissionName" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="permissionCode" class="form-label">权限编码 <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="permissionCode" required>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="permissionType" class="form-label">权限类型 <span class="text-danger">*</span></label>
                                        <select class="form-select" id="permissionType" onchange="handleTypeChange()" required>
                                            <option value="">请选择权限类型</option>
                                            <option value="1">菜单权限</option>
                                            <option value="2">按钮权限</option>
                                            <option value="3">接口权限</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="parentId" class="form-label">父权限</label>
                                        <select class="form-select" id="parentId">
                                            <option value="0">顶级权限</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div id="menuFields" style="display: none;">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="path" class="form-label">路由路径</label>
                                            <input type="text" class="form-control" id="path">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="component" class="form-label">组件路径</label>
                                            <input type="text" class="form-control" id="component">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="icon" class="form-label">图标</label>
                                            <input type="text" class="form-control" id="icon">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="sortOrder" class="form-label">排序</label>
                                            <input type="number" class="form-control" id="sortOrder" value="0">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="permissionStatus" class="form-label">状态</label>
                                        <select class="form-select" id="permissionStatus">
                                            <option value="1">启用</option>
                                            <option value="0">禁用</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="permissionDescription" class="form-label">描述</label>
                                <textarea class="form-control" id="permissionDescription" rows="3"></textarea>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" onclick="savePermission()">保存</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 确保没有重复的模态框
    const existingModal2 = document.getElementById('permissionModal');
    if (existingModal2) {
        existingModal2.remove();
    }

    // 添加新模态框到页面
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // 加载父权限选项
    loadParentOptions(permissionTreeData);

    // 等待DOM更新后显示模态框
    setTimeout(() => {
        const modal = new bootstrap.Modal(document.getElementById('permissionModal'));
        modal.show();
        console.log('新增权限模态框已显示');
    }, 100);
}

/**
 * 处理权限类型变化
 */
function handleTypeChange() {
    const type = document.getElementById('permissionType').value;
    const menuFields = document.getElementById('menuFields');
    
    if (type === '1') { // 菜单权限
        menuFields.style.display = 'block';
    } else {
        menuFields.style.display = 'none';
    }
}

/**
 * 加载父权限选项
 */
function loadParentOptions(permissions, parentSelect = null, level = 0) {
    if (!parentSelect) {
        parentSelect = document.getElementById('parentId');
        parentSelect.innerHTML = '<option value="0">顶级权限</option>';
    }
    
    permissions.forEach(permission => {
        if (permission.permissionType === 1) { // 只有菜单权限可以作为父权限
            const option = document.createElement('option');
            option.value = permission.id;
            option.textContent = '　'.repeat(level) + permission.permissionName;
            parentSelect.appendChild(option);
            
            if (permission.children && permission.children.length > 0) {
                loadParentOptions(permission.children, parentSelect, level + 1);
            }
        }
    });
}

/**
 * 添加子权限
 */
function addChildPermission(parentId) {
    console.log('添加子权限，父权限ID:', parentId);
    currentPermissionId = null;

    // 先清理现有模态框
    const existingModal = document.getElementById('permissionModal');
    if (existingModal) {
        const modalInstance = bootstrap.Modal.getInstance(existingModal);
        if (modalInstance) {
            modalInstance.hide();
        }
        existingModal.remove();
    }

    // 创建模态框HTML
    const modalHtml = `
        <div class="modal fade" id="permissionModal" tabindex="-1" aria-labelledby="permissionModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="permissionModalLabel">添加子权限</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <form id="permissionForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="permissionName" class="form-label">权限名称 <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="permissionName" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="permissionCode" class="form-label">权限编码 <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="permissionCode" required>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="permissionType" class="form-label">权限类型 <span class="text-danger">*</span></label>
                                        <select class="form-select" id="permissionType" onchange="handleTypeChange()" required>
                                            <option value="">请选择权限类型</option>
                                            <option value="1">菜单权限</option>
                                            <option value="2">按钮权限</option>
                                            <option value="3">数据权限</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="parentId" class="form-label">父权限</label>
                                        <select class="form-select" id="parentId">
                                            <option value="0">顶级权限</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div id="menuFields" style="display: none;">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="menuUrl" class="form-label">菜单URL</label>
                                            <input type="text" class="form-control" id="menuUrl">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="menuIcon" class="form-label">菜单图标</label>
                                            <input type="text" class="form-control" id="menuIcon" placeholder="如: bi-house">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="sortOrder" class="form-label">排序</label>
                                        <input type="number" class="form-control" id="sortOrder" value="0">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="status" class="form-label">状态</label>
                                        <select class="form-select" id="status">
                                            <option value="1">启用</option>
                                            <option value="0">禁用</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="description" class="form-label">描述</label>
                                <textarea class="form-control" id="description" rows="3"></textarea>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" onclick="savePermission()">保存</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 添加模态框到页面
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // 加载父权限选项
    if (permissionTreeData) {
        loadParentOptions(permissionTreeData);
    }

    // 设置父权限为指定的parentId
    setTimeout(() => {
        const parentSelect = document.getElementById('parentId');
        if (parentSelect && parentId) {
            parentSelect.value = parentId;
        }
    }, 100);

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('permissionModal'));
    modal.show();
}

/**
 * 编辑权限
 */
function editPermission(permissionId) {
    console.log('编辑权限，ID:', permissionId);

    // 先清理现有模态框
    const existingModal = document.getElementById('permissionModal');
    if (existingModal) {
        const modalInstance = bootstrap.Modal.getInstance(existingModal);
        if (modalInstance) {
            modalInstance.hide();
        }
        existingModal.remove();
    }

    fetch(`/api/v1/permissions/${permissionId}`, {
        method: 'GET',
        headers: {
            'Authorization': 'Bearer ' + getToken(),
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(result => {
        if (result.code === 200) {
            const permission = result.data;
            currentPermissionId = permissionId;
            console.log('获取到权限数据:', permission);

            // 创建编辑权限模态框HTML
            const modalHtml = `
                <div class="modal fade" id="permissionModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="permissionModalTitle">编辑权限</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <form id="permissionForm">
                                    <input type="hidden" id="permissionId" value="${permission.id}">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="permissionName" class="form-label">权限名称 <span class="text-danger">*</span></label>
                                                <input type="text" class="form-control" id="permissionName" value="${permission.permissionName}" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="permissionCode" class="form-label">权限编码 <span class="text-danger">*</span></label>
                                                <input type="text" class="form-control" id="permissionCode" value="${permission.permissionCode}" required>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="permissionType" class="form-label">权限类型 <span class="text-danger">*</span></label>
                                                <select class="form-select" id="permissionType" onchange="handleTypeChange()" required>
                                                    <option value="">请选择权限类型</option>
                                                    <option value="1" ${permission.permissionType === 1 ? 'selected' : ''}>菜单权限</option>
                                                    <option value="2" ${permission.permissionType === 2 ? 'selected' : ''}>按钮权限</option>
                                                    <option value="3" ${permission.permissionType === 3 ? 'selected' : ''}>接口权限</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="parentId" class="form-label">父权限</label>
                                                <select class="form-select" id="parentId">
                                                    <option value="0">顶级权限</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="menuFields" style="display: ${permission.permissionType === 1 ? 'block' : 'none'};">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="path" class="form-label">路由路径</label>
                                                    <input type="text" class="form-control" id="path" value="${permission.path || ''}">
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="component" class="form-label">组件路径</label>
                                                    <input type="text" class="form-control" id="component" value="${permission.component || ''}">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="icon" class="form-label">图标</label>
                                                    <input type="text" class="form-control" id="icon" value="${permission.icon || ''}">
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="sortOrder" class="form-label">排序</label>
                                                    <input type="number" class="form-control" id="sortOrder" value="${permission.sortOrder || 0}">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="permissionStatus" class="form-label">状态</label>
                                                <select class="form-select" id="permissionStatus">
                                                    <option value="1" ${permission.status === 1 ? 'selected' : ''}>启用</option>
                                                    <option value="0" ${permission.status === 0 ? 'selected' : ''}>禁用</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="permissionDescription" class="form-label">描述</label>
                                        <textarea class="form-control" id="permissionDescription" rows="3">${permission.description || ''}</textarea>
                                    </div>
                                </form>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                <button type="button" class="btn btn-primary" onclick="savePermission()">保存</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // 确保没有重复的模态框
            const existingModal2 = document.getElementById('permissionModal');
            if (existingModal2) {
                existingModal2.remove();
            }

            // 添加新模态框到页面
            document.body.insertAdjacentHTML('beforeend', modalHtml);

            // 加载父权限选项并设置当前值
            loadParentOptions(permissionTreeData);

            // 等待DOM更新后设置值
            setTimeout(() => {
                const parentSelect = document.getElementById('parentId');
                if (parentSelect) {
                    parentSelect.value = permission.parentId || 0;
                }

                // 显示模态框
                const modal = new bootstrap.Modal(document.getElementById('permissionModal'));
                modal.show();
                console.log('编辑权限模态框已显示');
            }, 200);
        } else {
            showAlert('获取权限信息失败: ' + result.message, 'danger');
        }
    })
    .catch(error => {
        console.error('获取权限信息失败:', error);
        showAlert('获取权限信息失败', 'danger');
    });
}

/**
 * 保存权限
 */
function savePermission() {
    const permissionData = {
        permissionName: document.getElementById('permissionName').value,
        permissionCode: document.getElementById('permissionCode').value,
        permissionType: parseInt(document.getElementById('permissionType').value),
        parentId: parseInt(document.getElementById('parentId').value) || 0,
        path: document.getElementById('path').value || null,
        component: document.getElementById('component').value || null,
        icon: document.getElementById('icon').value || null,
        sortOrder: parseInt(document.getElementById('sortOrder').value) || 0,
        status: parseInt(document.getElementById('permissionStatus').value),
        description: document.getElementById('permissionDescription').value || null
    };

    // 如果是编辑模式，添加ID
    if (currentPermissionId) {
        permissionData.id = currentPermissionId;
    }

    console.log('保存权限数据:', {
        isEdit: !!currentPermissionId,
        permissionId: currentPermissionId,
        permissionData: permissionData
    });

    // 验证必填字段
    if (!permissionData.permissionName || !permissionData.permissionCode || !permissionData.permissionType) {
        showAlert('请填写权限名称、权限编码和权限类型', 'warning');
        return;
    }

    const url = currentPermissionId ? `/api/v1/permissions/${currentPermissionId}` : '/api/v1/permissions';
    const method = currentPermissionId ? 'PUT' : 'POST';
    
    fetch(url, {
        method: method,
        headers: {
            'Authorization': 'Bearer ' + getToken(),
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(permissionData)
    })
    .then(response => response.json())
    .then(result => {
        if (result.code === 200) {
            showAlert(currentPermissionId ? '权限更新成功' : '权限创建成功', 'success');
            bootstrap.Modal.getInstance(document.getElementById('permissionModal')).hide();
            loadPermissionTree();
        } else {
            showAlert('保存权限失败: ' + result.message, 'danger');
        }
    })
    .catch(error => {
        console.error('保存权限失败:', error);
        showAlert('保存权限失败', 'danger');
    });
}

/**
 * 切换权限状态
 */
function togglePermissionStatus(permissionId, currentStatus) {
    const newStatus = currentStatus === 1 ? 0 : 1;
    const action = newStatus === 1 ? '启用' : '禁用';

    if (!confirm(`确定要${action}这个权限吗？`)) {
        return;
    }

    fetch(`/api/v1/permissions/${permissionId}/status`, {
        method: 'PUT',
        headers: {
            'Authorization': 'Bearer ' + getToken(),
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ status: newStatus })
    })
    .then(response => response.json())
    .then(result => {
        if (result.code === 200) {
            showAlert(`权限${action}成功`, 'success');
            loadPermissionTree();
        } else {
            showAlert(`权限${action}失败: ` + result.message, 'danger');
        }
    })
    .catch(error => {
        console.error(`权限${action}失败:`, error);
        showAlert(`权限${action}失败`, 'danger');
    });
}

/**
 * 删除权限
 */
async function deletePermission(permissionId) {
    const confirmed = await showConfirmDialog('确定要删除这个权限吗？删除后不可恢复！\n注意：删除父权限会同时删除所有子权限。', '删除确认');
    if (!confirmed) {
        return;
    }

    fetch(`/api/v1/permissions/${permissionId}`, {
        method: 'DELETE',
        headers: {
            'Authorization': 'Bearer ' + getToken(),
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(result => {
        if (result.code === 200) {
            showAlert('权限删除成功', 'success');
            loadPermissionTree();
        } else {
            showAlert('权限删除失败: ' + result.message, 'danger');
        }
    })
    .catch(error => {
        console.error('权限删除失败:', error);
        showAlert('权限删除失败', 'danger');
    });
}

/**
 * 显示Alert消息
 */
function showAlert(message, type = 'info') {
    // 使用showToast函数，如果不存在则创建一个简单的实现
    if (typeof showToast === 'function') {
        showToast(message, type === 'danger' ? 'error' : type);
    } else {
        // 创建toast容器（如果不存在）
        let toastContainer = document.getElementById('toast-container');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.id = 'toast-container';
            toastContainer.className = 'position-fixed top-0 end-0 p-3';
            toastContainer.style.zIndex = '9999';
            document.body.appendChild(toastContainer);
        }

        // 创建toast元素
        const toastId = 'toast-' + Date.now();
        const bgClass = type === 'danger' ? 'bg-danger' : type === 'success' ? 'bg-success' : type === 'warning' ? 'bg-warning' : 'bg-primary';
        const toastHtml = `
            <div id="${toastId}" class="toast align-items-center text-white ${bgClass} border-0" role="alert">
                <div class="d-flex">
                    <div class="toast-body">
                        ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            </div>
        `;

        toastContainer.insertAdjacentHTML('beforeend', toastHtml);

        // 显示toast
        const toastElement = document.getElementById(toastId);
        const toast = new bootstrap.Toast(toastElement, {
            autohide: true,
            delay: 3000
        });
        toast.show();

        // 自动移除DOM元素
        toastElement.addEventListener('hidden.bs.toast', () => {
            toastElement.remove();
        });
    }
}

/**
 * 显示确认对话框
 */
function showConfirmDialog(message, title = '确认操作') {
    return new Promise((resolve) => {
        // 创建确认模态框
        const modalHtml = `
            <div class="modal fade" id="permissionConfirmModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="bi bi-question-circle me-2"></i>${title}
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <p style="white-space: pre-line;">${message}</p>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" id="permissionCancelBtn">取消</button>
                            <button type="button" class="btn btn-danger" id="permissionConfirmBtn">确定</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 移除已存在的确认对话框
        const existingModal = document.getElementById('permissionConfirmModal');
        if (existingModal) {
            existingModal.remove();
        }

        // 添加模态框到页面
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // 获取模态框元素
        const modalElement = document.getElementById('permissionConfirmModal');
        const modal = new bootstrap.Modal(modalElement);

        // 绑定按钮事件
        const confirmBtn = document.getElementById('permissionConfirmBtn');
        const cancelBtn = document.getElementById('permissionCancelBtn');

        confirmBtn.addEventListener('click', () => {
            modal.hide();
            resolve(true);
        });

        cancelBtn.addEventListener('click', () => {
            modal.hide();
            resolve(false);
        });

        // 模态框关闭时也返回false
        modalElement.addEventListener('hidden.bs.modal', () => {
            modalElement.remove();
        });

        // 显示模态框
        modal.show();
    });
}
