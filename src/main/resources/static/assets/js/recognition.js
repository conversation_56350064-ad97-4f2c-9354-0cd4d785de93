// 文档识别页面JavaScript

// 页面初始化函数
function initRecognitionPage() {
    console.log('文档识别页面初始化');
    loadRecognitionAgents();
    initFileUpload();

    // 检查是否有从其他页面传递的agentCode
    setTimeout(() => {
        const selectedAgentCode = sessionStorage.getItem('selectedAgentCode');
        if (selectedAgentCode) {
            const select = document.getElementById('recognitionAgentSelect');
            if (select) {
                // 查找对应的Agent选项
                for (let option of select.options) {
                    if (option.value === selectedAgentCode) {
                        select.value = selectedAgentCode;
                        // 触发change事件
                        const event = new Event('change');
                        select.dispatchEvent(event);
                        break;
                    }
                }
            }
            // 清除sessionStorage中的值
            sessionStorage.removeItem('selectedAgentCode');
        }
    }, 1000);
}

// 加载Agent列表
async function loadRecognitionAgents() {
    const select = document.getElementById('recognitionAgentSelect');
    if (!select) return;

    try {
        // 调用真实API - 获取已发布的Agent列表
        const response = await apiCall('/api/v1/agents?current=1&size=100&status=3');
        if (response.code === 200) {
            // 检查返回的数据结构
            let agents = [];
            if (response.data && response.data.records) {
                agents = response.data.records; // 分页结构
            } else if (response.data && Array.isArray(response.data)) {
                agents = response.data; // 直接数组
            } else if (response.data && response.data.list) {
                agents = response.data.list; // list结构
            }

            select.innerHTML = '<option value="">请选择Agent...</option>';
            agents.forEach(agent => {
                // 只显示已发布的Agent
                if (agent.status === 3) {
                    select.innerHTML += `<option value="${agent.agentCode}" data-id="${agent.id}">${agent.agentName} (${agent.agentCode})</option>`;
                }
            });

            if (agents.length === 0) {
                select.innerHTML += '<option value="" disabled>暂无可用的Agent</option>';
            }
        } else {
            // 如果API失败，使用模拟数据
            const agents = [
                { agentCode: 'FINANCE_REPORT', agentName: '财务报表识别Agent' },
                { agentCode: 'ID_CARD', agentName: '身份证识别Agent' },
                { agentCode: 'INVOICE', agentName: '发票识别Agent' },
                { agentCode: 'CONTRACT', agentName: '合同识别Agent' }
            ];

            select.innerHTML = '<option value="">请选择Agent...</option>' +
                agents.map(agent => `<option value="${agent.agentCode}">${agent.agentName}</option>`).join('');
        }

        // 监听选择变化
        select.addEventListener('change', checkRecognitionReady);

    } catch (error) {
        console.error('加载Agent列表失败:', error);
        // 使用模拟数据作为后备
        const agents = [
            { agentCode: 'FINANCE_REPORT', agentName: '财务报表识别Agent' },
            { agentCode: 'ID_CARD', agentName: '身份证识别Agent' },
            { agentCode: 'INVOICE', agentName: '发票识别Agent' },
            { agentCode: 'CONTRACT', agentName: '合同识别Agent' }
        ];

        select.innerHTML = '<option value="">请选择Agent...</option>' +
            agents.map(agent => `<option value="${agent.agentCode}">${agent.agentName}</option>`).join('');
        select.addEventListener('change', checkRecognitionReady);
    }
}

// 初始化文件上传
function initFileUpload() {
    const fileZone = document.getElementById('fileDropZone');
    const fileInput = document.getElementById('fileInput');
    
    if (!fileZone || !fileInput) return;
    
    // 文件选择
    fileInput.addEventListener('change', handleFileSelect);
    
    // 拖拽上传
    fileZone.addEventListener('dragover', (e) => {
        e.preventDefault();
        fileZone.classList.add('dragover');
    });
    
    fileZone.addEventListener('dragleave', () => {
        fileZone.classList.remove('dragover');
    });
    
    fileZone.addEventListener('drop', (e) => {
        e.preventDefault();
        fileZone.classList.remove('dragover');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            handleFile(files[0]);
        }
    });
}

// 处理文件选择
function handleFileSelect(e) {
    const file = e.target.files[0];
    if (file) {
        handleFile(file);
    }
}

// 处理文件
function handleFile(file) {
    // 验证文件类型
    const allowedTypes = ['image/jpeg', 'image/png', 'application/pdf'];
    if (!allowedTypes.includes(file.type)) {
        alert('不支持的文件格式，请上传 JPG、PNG 或 PDF 文件');
        return;
    }
    
    // 验证文件大小（10MB限制）
    if (file.size > 10 * 1024 * 1024) {
        alert('文件大小不能超过 10MB');
        return;
    }
    
    // 显示文件信息
    const fileZone = document.getElementById('fileDropZone');
    fileZone.innerHTML = `
        <div class="text-center">
            <i class="bi bi-file-earmark-check fs-4 text-success"></i>
            <p class="mt-2 mb-0">${file.name}</p>
            <small class="text-muted">${(file.size / 1024).toFixed(1)} KB</small>
        </div>
    `;
    
    // 存储文件引用
    window.selectedFile = file;
    
    // 检查是否可以开始识别
    checkRecognitionReady();
}

// 检查识别准备状态
function checkRecognitionReady() {
    const agentSelect = document.getElementById('recognitionAgentSelect');
    const recognizeBtn = document.getElementById('recognizeBtn');
    
    if (agentSelect && recognizeBtn) {
        if (agentSelect.value && window.selectedFile) {
            recognizeBtn.disabled = false;
        } else {
            recognizeBtn.disabled = true;
        }
    }
}

// 开始识别
async function startRecognition() {
    const agentSelect = document.getElementById('recognitionAgentSelect');
    const resultContainer = document.getElementById('recognitionResult');

    if (!agentSelect.value || !window.selectedFile) {
        alert('请选择Agent并上传文件');
        return;
    }

    try {
        // 显示加载状态
        resultContainer.innerHTML = `
            <div class="text-center py-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">识别中...</span>
                </div>
                <p class="mt-2 text-muted">正在识别文档，请稍候...</p>
                <small class="text-muted d-block mt-2">Agent: ${agentSelect.options[agentSelect.selectedIndex].text}</small>
            </div>
        `;

        // 禁用识别按钮
        const recognizeBtn = document.getElementById('recognizeBtn');
        recognizeBtn.disabled = true;
        recognizeBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 识别中...';

        // 构建FormData
        const formData = new FormData();
        formData.append('file', window.selectedFile);
        formData.append('agentCode', agentSelect.value);

        // 调用真实API
        const response = await uploadFile('/api/v1/recognition/analyzeByCode', formData);

        const result = await response;
        console.log('识别API响应:', result);

        // 处理响应
        if (result.code === 200 && result.data) {
            const taskData = result.data;

            // 如果任务已完成，直接显示结果
            if (taskData.success  && taskData.result) {
                displayRecognitionResult(taskData, true);
            } else if (taskData.status === '失败') {
                displayRecognitionResult(taskData, false);
            } else {
                // 任务还在处理中，轮询查询结果
                pollRecognitionResult(taskData.recordId);
            }
        } else {
            throw new Error(result.message || '识别请求失败');
        }

    } catch (error) {
        console.error('识别失败:', error);
        resultContainer.innerHTML = `
            <div class="alert alert-danger">
                <h6><i class="bi bi-exclamation-triangle me-2"></i>识别失败</h6>
                <p class="mb-0">${error.message || '网络错误，请稍后重试'}</p>
            </div>
        `;

        // 重新启用识别按钮
        resetRecognitionButton();
    }
}

// 轮询查询识别结果
async function pollRecognitionResult(recordId) {
    const resultContainer = document.getElementById('recognitionResult');
    let pollCount = 0;
    const maxPolls = 60; // 最多轮询60次（5分钟）

    const poll = async () => {
        try {
            pollCount++;

            // 更新加载状态
            resultContainer.innerHTML = `
                <div class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">识别中...</span>
                    </div>
                    <p class="mt-2 text-muted">正在识别文档，请稍候...</p>
                    <small class="text-muted d-block mt-2">轮询次数: ${pollCount}/${maxPolls}</small>
                </div>
            `;

            const response = await fetch(`/api/v1/recognition/records/${recordId}`, {
                headers: {
                    'Authorization': 'Bearer ' + localStorage.getItem('token')
                }
            });

            const result = await response.json();
            console.log('轮询结果:', result);

            if (result.code === 200 && result.data) {
                const record = result.data;

                // 检查状态
                if (record.status === 1) { // 成功
                    displayRecognitionResult({
                        result: record.recognitionResult,
                        status: '成功',
                        recordId: record.id,
                        confidence: record.confidenceScore,
                        processingTime: record.processingTime
                    }, true);
                    return;
                } else if (record.status === 2) { // 失败
                    displayRecognitionResult({
                        result: record.errorMessage,
                        status: '失败',
                        recordId: record.id
                    }, false);
                    return;
                } else if (record.status === 0) { // 处理中
                    if (pollCount < maxPolls) {
                        setTimeout(poll, 5000); // 5秒后再次轮询
                    } else {
                        throw new Error('识别超时，请稍后查看结果');
                    }
                } else {
                    throw new Error('未知的识别状态');
                }
            } else {
                throw new Error(result.message || '查询识别结果失败');
            }

        } catch (error) {
            console.error('轮询识别结果失败:', error);
            resultContainer.innerHTML = `
                <div class="alert alert-danger">
                    <h6><i class="bi bi-exclamation-triangle me-2"></i>查询识别结果失败</h6>
                    <p class="mb-0">${error.message}</p>
                </div>
            `;
            resetRecognitionButton();
        }
    };

    // 开始轮询
    setTimeout(poll, 2000); // 2秒后开始第一次轮询
}

// 显示识别结果
function displayRecognitionResult(taskData, isSuccess) {
    const resultContainer = document.getElementById('recognitionResult');

    if (isSuccess) {
        let resultContent = '';
        let parsedResult = null;

        try {
            // 尝试解析JSON结果
            if (taskData.result && typeof taskData.result === 'string' && taskData.result.trim() !== '') {
                parsedResult = JSON.parse(taskData.result);
                resultContent = JSON.stringify(parsedResult, null, 2);
            } else if (taskData.result && typeof taskData.result === 'object') {
                parsedResult = taskData.result;
                resultContent = JSON.stringify(parsedResult, null, 2);
            } else if (taskData.result && typeof taskData.result === 'string') {
                // 非JSON字符串，直接显示
                resultContent = taskData.result;
            } else {
                resultContent = '无识别结果';
            }
        } catch (e) {
            console.warn('JSON解析失败:', e.message);
            // 如果不是JSON，直接显示文本
            resultContent = taskData.result || '无识别结果';
        }

        resultContainer.innerHTML = `
            <div class="alert alert-success">
                <h6><i class="bi bi-check-circle me-2"></i>识别成功</h6>
                <div class="mt-3">
                    ${taskData.confidence ? `<strong>置信度:</strong> ${(taskData.confidence * 100).toFixed(1)}%<br>` : ''}
                    ${taskData.processingTime ? `<strong>处理时间:</strong> ${(taskData.processingTime / 1000).toFixed(2)}秒<br>` : ''}
                    <strong>记录ID:</strong> ${taskData.recordId}
                </div>
            </div>
            <div class="mt-3">
                <h6>识别结果</h6>
                <pre class="bg-light p-3 rounded" style="max-height: 400px; overflow-y: auto;">${resultContent}</pre>
            </div>
            <div class="mt-3">
                <button class="btn btn-outline-primary btn-sm" onclick="copyResult()">
                    <i class="bi bi-clipboard"></i> 复制结果
                </button>
                <button class="btn btn-outline-success btn-sm ms-2" onclick="exportResult()">
                    <i class="bi bi-download"></i> 导出结果
                </button>
                <button class="btn btn-outline-info btn-sm ms-2" onclick="viewRecordDetail(${taskData.recordId})">
                    <i class="bi bi-eye"></i> 查看详情
                </button>
            </div>
        `;

        // 存储结果用于复制和导出
        window.recognitionResult = parsedResult || resultContent;
        window.currentRecordId = taskData.recordId;
    } else {
        resultContainer.innerHTML = `
            <div class="alert alert-danger">
                <h6><i class="bi bi-exclamation-triangle me-2"></i>识别失败</h6>
                <p class="mb-0">${taskData.result || '识别过程中发生错误'}</p>
                ${taskData.recordId ? `<small class="text-muted">记录ID: ${taskData.recordId}</small>` : ''}
            </div>
        `;
    }

    // 重新启用识别按钮
    resetRecognitionButton();
}

// 重置识别按钮状态
function resetRecognitionButton() {
    const recognizeBtn = document.getElementById('recognizeBtn');
    if (recognizeBtn) {
        recognizeBtn.disabled = false;
        recognizeBtn.innerHTML = '<i class="bi bi-play"></i> 开始识别';
    }
}

// 查看记录详情
function viewRecordDetail(recordId) {
    if (recordId) {
        // 可以跳转到记录详情页面或显示模态框
        window.open(`/pages/recognition-records.html?recordId=${recordId}`, '_blank');
    }
}

// 清空识别
function clearRecognition() {
    // 重置文件上传区域
    const fileZone = document.getElementById('fileDropZone');
    fileZone.innerHTML = `
        <i class="bi bi-cloud-upload fs-1 text-muted"></i>
        <p class="mt-2 mb-0">点击选择文件或拖拽文件到此处</p>
        <small class="text-muted">支持 JPG, PNG, PDF 格式</small>
    `;

    // 重置结果区域
    const resultContainer = document.getElementById('recognitionResult');
    resultContainer.innerHTML = `
        <div class="text-muted text-center py-4">
            <i class="bi bi-file-earmark-text fs-1"></i>
            <p class="mt-2">请上传文件并开始识别</p>
        </div>
    `;

    // 重置表单
    document.getElementById('fileInput').value = '';
    document.getElementById('recognitionAgentSelect').value = '';

    // 重置识别按钮
    resetRecognitionButton();
    document.getElementById('recognizeBtn').disabled = true;

    // 清除文件引用和结果
    window.selectedFile = null;
    window.recognitionResult = null;
    window.currentRecordId = null;
}

// 复制结果
function copyResult() {
    if (window.recognitionResult) {
        let text;
        if (typeof window.recognitionResult === 'string') {
            text = window.recognitionResult;
        } else {
            text = JSON.stringify(window.recognitionResult, null, 2);
        }

        navigator.clipboard.writeText(text).then(() => {
            // 显示成功提示
            showToast('结果已复制到剪贴板', 'success');
        }).catch(() => {
            // 降级方案：使用传统方法
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            try {
                document.execCommand('copy');
                showToast('结果已复制到剪贴板', 'success');
            } catch (err) {
                showToast('复制失败，请手动复制', 'error');
            }
            document.body.removeChild(textArea);
        });
    } else {
        showToast('没有可复制的结果', 'warning');
    }
}

// 导出结果
function exportResult() {
    if (window.recognitionResult) {
        let dataStr;
        let fileName;
        let mimeType;

        if (typeof window.recognitionResult === 'string') {
            dataStr = window.recognitionResult;
            fileName = `recognition_result_${new Date().getTime()}.txt`;
            mimeType = 'text/plain';
        } else {
            dataStr = JSON.stringify(window.recognitionResult, null, 2);
            fileName = `recognition_result_${new Date().getTime()}.json`;
            mimeType = 'application/json';
        }

        const dataBlob = new Blob([dataStr], {type: mimeType});
        const url = URL.createObjectURL(dataBlob);

        const link = document.createElement('a');
        link.href = url;
        link.download = fileName;
        link.style.display = 'none';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        URL.revokeObjectURL(url);
        showToast('结果导出成功！', 'success');
    } else {
        showToast('没有可导出的结果', 'warning');
    }
}

// 显示提示消息
function showToast(message, type = 'info') {
    // 创建提示元素
    const toast = document.createElement('div');
    toast.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(toast);

    // 3秒后自动移除
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 3000);
}
