// 智能体矩阵 公共JavaScript工具库

// API基础配置
const API_BASE_URL = '';
const API_TIMEOUT = 30000;

// 通用API调用函数
async function apiCall(url, options = {}) {
    const defaultOptions = {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
        },
        timeout: API_TIMEOUT
    };

    // 合并选项
    const finalOptions = { ...defaultOptions, ...options };

    // 添加认证token
    // 尝试从多个可能的存储位置获取token
    const token = localStorage.getItem('token') || localStorage.getItem('authToken') || sessionStorage.getItem('token');
    if (token) {
        finalOptions.headers['Authorization'] = `Bearer ${token}`;
        console.log('添加认证头:', `Bearer ${token.substring(0, 10)}...`);
    } else {
        console.warn('未找到认证token');
    }

    try {
        const response = await fetch(API_BASE_URL + url, finalOptions);
        
        // 检查HTTP状态
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();
        
        // 检查业务状态码
        if (result.code === 401) {
            // 未授权，清除所有可能的token并跳转到登录页
            localStorage.removeItem('token');
            localStorage.removeItem('authToken');
            sessionStorage.removeItem('token');
            console.warn('认证失败，跳转到登录页');
            window.location.href = '/login.html';
            return;
        }

        return result;
    } catch (error) {
        console.error('API调用失败:', error);
        throw error;
    }
}

// GET请求
async function apiGet(url, params = {}) {
    const queryString = new URLSearchParams(params).toString();
    const fullUrl = queryString ? `${url}?${queryString}` : url;
    return await apiCall(fullUrl);
}

// POST请求
async function apiPost(url, data = {}) {
    return await apiCall(url, {
        method: 'POST',
        body: JSON.stringify(data)
    });
}

// PUT请求
async function apiPut(url, data = {}) {
    return await apiCall(url, {
        method: 'PUT',
        body: JSON.stringify(data)
    });
}

// DELETE请求
async function apiDelete(url) {
    return await apiCall(url, {
        method: 'DELETE'
    });
}

// 格式化日期时间
function formatDateTime(dateTime, format = 'YYYY-MM-DD HH:mm:ss') {
    if (!dateTime) return '-';
    
    const date = new Date(dateTime);
    if (isNaN(date.getTime())) return '-';
    
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    
    switch (format) {
        case 'YYYY-MM-DD':
            return `${year}-${month}-${day}`;
        case 'HH:mm:ss':
            return `${hours}:${minutes}:${seconds}`;
        case 'YYYY-MM-DD HH:mm':
            return `${year}-${month}-${day} ${hours}:${minutes}`;
        default:
            return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    }
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 防抖函数
function debounce(func, wait, immediate) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            timeout = null;
            if (!immediate) func(...args);
        };
        const callNow = immediate && !timeout;
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
        if (callNow) func(...args);
    };
}

// 节流函数
function throttle(func, limit) {
    let inThrottle;
    return function(...args) {
        if (!inThrottle) {
            func.apply(this, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// 显示Toast消息
function showToast(message, type = 'info', duration = 3000) {
    // 创建toast容器（如果不存在）
    let toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.className = 'position-fixed top-0 end-0 p-3';
        toastContainer.style.zIndex = '9999';
        document.body.appendChild(toastContainer);
    }

    // 创建toast元素
    const toastId = 'toast-' + Date.now();
    const bgColor = type === 'error' ? 'danger' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : 'primary';
    const toastHtml = `
        <div id="${toastId}" class="toast align-items-center text-white bg-${bgColor} border-0" role="alert" style="transition: var(--theme-transition, all 0.3s ease);">
            <div class="d-flex">
                <div class="toast-body">
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        </div>
    `;

    toastContainer.insertAdjacentHTML('beforeend', toastHtml);

    // 显示toast
    const toastElement = document.getElementById(toastId);
    const toast = new bootstrap.Toast(toastElement, {
        autohide: true,
        delay: duration
    });
    toast.show();

    // 自动移除DOM元素
    toastElement.addEventListener('hidden.bs.toast', () => {
        toastElement.remove();
    });
}

// 显示确认对话框
function showConfirm(message, title = '确认操作') {
    return new Promise((resolve) => {
        // 创建确认模态框
        const modalHtml = `
            <div class="modal fade" id="confirmModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content" style="background-color: var(--card-background); color: var(--dark-text); border: 1px solid var(--border-color);">
                        <div class="modal-header" style="background-color: var(--background-color); border-bottom: 1px solid var(--border-color); color: var(--dark-text);">
                            <h5 class="modal-title" style="color: var(--dark-text);">
                                <i class="bi bi-question-circle me-2"></i>${title}
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body" style="background-color: var(--card-background); color: var(--dark-text);">
                            <p style="color: var(--dark-text);">${message}</p>
                        </div>
                        <div class="modal-footer" style="background-color: var(--background-color); border-top: 1px solid var(--border-color);">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" id="cancelConfirmBtn">取消</button>
                            <button type="button" class="btn btn-primary" id="confirmConfirmBtn">确定</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 移除已存在的确认对话框
        const existingModal = document.getElementById('confirmModal');
        if (existingModal) {
            existingModal.remove();
        }

        // 添加模态框到页面
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // 获取模态框元素
        const modalElement = document.getElementById('confirmModal');
        const modal = new bootstrap.Modal(modalElement);

        // 绑定按钮事件
        const confirmBtn = document.getElementById('confirmConfirmBtn');
        const cancelBtn = document.getElementById('cancelConfirmBtn');

        confirmBtn.addEventListener('click', () => {
            modal.hide();
            resolve(true);
        });

        cancelBtn.addEventListener('click', () => {
            modal.hide();
            resolve(false);
        });

        // 模态框关闭时也返回false
        modalElement.addEventListener('hidden.bs.modal', () => {
            modalElement.remove();
        });

        // 显示模态框
        modal.show();
    });
}

// 复制文本到剪贴板
async function copyToClipboard(text) {
    try {
        await navigator.clipboard.writeText(text);
        showToast('已复制到剪贴板', 'success');
        return true;
    } catch (err) {
        console.error('复制失败:', err);
        showToast('复制失败', 'error');
        return false;
    }
}

// 下载文件
function downloadFile(url, filename) {
    const link = document.createElement('a');
    link.href = url;
    link.download = filename || 'download';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// 获取URL参数
function getUrlParam(name) {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get(name);
}

// 设置URL参数
function setUrlParam(name, value) {
    const url = new URL(window.location);
    url.searchParams.set(name, value);
    window.history.replaceState({}, '', url);
}

// 移除URL参数
function removeUrlParam(name) {
    const url = new URL(window.location);
    url.searchParams.delete(name);
    window.history.replaceState({}, '', url);
}

// 验证邮箱格式
function validateEmail(email) {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(email);
}

// 验证手机号格式
function validatePhone(phone) {
    const re = /^1[3-9]\d{9}$/;
    return re.test(phone);
}

// 生成随机字符串
function generateRandomString(length = 8) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
}

// 设置当前Agent ID
function setCurrentAgentId(agentId) {
    window.currentAgentId = agentId;
    localStorage.setItem('currentAgentId', agentId);
}

// 获取当前Agent ID
function getCurrentAgentId() {
    return window.currentAgentId || localStorage.getItem('currentAgentId');
}

// 清除当前Agent ID
function clearCurrentAgentId() {
    window.currentAgentId = null;
    localStorage.removeItem('currentAgentId');
}

// 页面加载完成后的通用初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化所有tooltip
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // 初始化所有popover
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });

    // 侧边栏切换功能
    const sidebarToggle = document.getElementById('sidebarToggle');
    const sidebar = document.getElementById('sidebar');
    
    if (sidebarToggle && sidebar) {
        sidebarToggle.addEventListener('click', function() {
            sidebar.classList.toggle('collapsed');
            
            // 保存状态到localStorage
            const isCollapsed = sidebar.classList.contains('collapsed');
            localStorage.setItem('sidebar-collapsed', isCollapsed);
        });

        // 恢复侧边栏状态
        const isCollapsed = localStorage.getItem('sidebar-collapsed') === 'true';
        if (isCollapsed) {
            sidebar.classList.add('collapsed');
        }
    }

    // 设置当前用户信息
    const currentUserElement = document.getElementById('currentUser');
    if (currentUserElement) {
        const userInfo = localStorage.getItem('userInfo');
        if (userInfo) {
            try {
                const user = JSON.parse(userInfo);
                currentUserElement.textContent = user.username || user.realName || '用户';
            } catch (e) {
                console.error('解析用户信息失败:', e);
            }
        }
    }
});

// 全局错误处理
window.addEventListener('error', function(e) {
    console.error('全局错误:', e.error);
});

window.addEventListener('unhandledrejection', function(e) {
    console.error('未处理的Promise拒绝:', e.reason);
});

// 导出常用函数到全局
window.apiCall = apiCall;
window.apiGet = apiGet;
window.apiPost = apiPost;
window.apiPut = apiPut;
window.apiDelete = apiDelete;
window.formatDateTime = formatDateTime;
window.formatFileSize = formatFileSize;
window.showToast = showToast;
window.showConfirm = showConfirm;
window.copyToClipboard = copyToClipboard;
window.downloadFile = downloadFile;
window.setCurrentAgentId = setCurrentAgentId;
window.getCurrentAgentId = getCurrentAgentId;
window.clearCurrentAgentId = clearCurrentAgentId;
