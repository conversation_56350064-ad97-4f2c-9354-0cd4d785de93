// 用户管理页面JavaScript

// 页面初始化函数
function initUsersPage() {
    console.log('用户管理页面初始化');

    // 初始化Tab事件监听
    initTabEvents();

    // 加载系统用户（默认Tab）
    loadSystemUsers();
    loadSystemUserStatistics();

    // 加载待审核数量
    loadPendingApprovalCount();

    // 加载角色列表
    loadRoles();
}

// 初始化Tab事件监听
function initTabEvents() {
    const systemTab = document.getElementById('system-users-tab');
    const platformTab = document.getElementById('platform-users-tab');

    systemTab.addEventListener('shown.bs.tab', function() {
        loadSystemUsers();
        loadSystemUserStatistics();
    });

    platformTab.addEventListener('shown.bs.tab', function() {
        loadPlatformUsers();
        loadPlatformUserStatistics();
    });
}

// 加载系统用户统计数据
async function loadSystemUserStatistics() {
    try {
        const response = await apiCall('/api/v1/users/by-forward?forward=MANAGEMENT');
        if (response.code === 200) {
            const users = response.data || [];
            const totalCount = users.length;
            const activeCount = users.filter(u => u.status === 1).length;
            const adminCount = users.filter(u => u.roleName === '管理员').length;

            // 更新统计卡片
            document.getElementById('totalSystemUsers').textContent = totalCount;
            document.getElementById('activeSystemUsers').textContent = activeCount;
            document.getElementById('adminSystemUsers').textContent = adminCount;
            document.getElementById('todaySystemLogins').textContent = 0; // TODO: 实现今日登录统计
        }
    } catch (error) {
        console.error('加载系统用户统计失败:', error);
        // 设置默认值
        document.getElementById('totalSystemUsers').textContent = 0;
        document.getElementById('activeSystemUsers').textContent = 0;
        document.getElementById('adminSystemUsers').textContent = 0;
        document.getElementById('todaySystemLogins').textContent = 0;
    }
}

// 加载平台用户统计数据
async function loadPlatformUserStatistics() {
    try {
        const response = await apiCall('/api/v1/users/by-forward?forward=PORTAL');
        if (response.code === 200) {
            const users = response.data || [];
            const totalCount = users.length;
            const pendingCount = users.filter(u => u.approvalStatus === 0).length;
            const approvedCount = users.filter(u => u.approvalStatus === 1).length;
            const rejectedCount = users.filter(u => u.approvalStatus === 2).length;

            // 更新统计卡片
            document.getElementById('totalPlatformUsers').textContent = totalCount;
            document.getElementById('pendingPlatformUsers').textContent = pendingCount;
            document.getElementById('approvedPlatformUsers').textContent = approvedCount;
            document.getElementById('rejectedPlatformUsers').textContent = rejectedCount;
        }
    } catch (error) {
        console.error('加载平台用户统计失败:', error);
        // 设置默认值
        document.getElementById('totalPlatformUsers').textContent = 0;
        document.getElementById('pendingPlatformUsers').textContent = 0;
        document.getElementById('approvedPlatformUsers').textContent = 0;
        document.getElementById('rejectedPlatformUsers').textContent = 0;
    }
}

// 加载系统用户列表
async function loadSystemUsers() {
    const container = document.getElementById('systemUsersTable');
    if (!container) return;

    try {
        // 显示加载状态
        container.innerHTML = `
            <div class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <div class="mt-2 text-muted">正在加载系统用户列表...</div>
            </div>
        `;

        // 构建查询参数
        const params = new URLSearchParams();
        params.append('forward', 'MANAGEMENT');

        if (currentSystemFilter && currentSystemFilter !== 'all') {
            if (currentSystemFilter === 'active') {
                params.append('status', '1');
            } else if (currentSystemFilter === 'disabled') {
                params.append('status', '0');
            }
        }
        if (currentSystemKeyword) {
            params.append('keyword', currentSystemKeyword);
        }

        // 调用API
        const url = '/api/v1/users/by-forward?' + params.toString();
        const response = await apiCall(url);

        if (response.code !== 200) {
            throw new Error(response.message || '获取系统用户列表失败');
        }

        const users = response.data || [];

        // 渲染用户表格
        container.innerHTML = `
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead>
                        <tr>
                            <th width="25%">用户信息</th>
                            <th width="12%">角色权限</th>
                            <th width="10%">账号状态</th>
                            <th width="15%">创建时间</th>
                            <th width="15%">最后登录</th>
                            <th width="23%">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${users.map(user => `
                            <tr>
                                <td>
                                    <div>
                                        <div class="fw-medium">${user.realName || user.username}</div>
                                        <small class="text-muted">@${user.username} • ${user.email || '未设置邮箱'}</small>
                                        ${user.department ? `<small class="text-info d-block">${user.department}</small>` : ''}
                                    </div>
                                </td>
                                <td>${renderUserRoles(user)}</td>
                                <td><span class="badge bg-${getUserStatusColor(user.status)}">${getUserStatusText(user.status)}</span></td>
                                <td>${formatDateTime(user.createdTime)}</td>
                                <td>${user.lastLoginTime ? formatDateTime(user.lastLoginTime) : '从未登录'}</td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary" onclick="editUser(${user.id})" title="编辑" data-permission="USER_EDIT">
                                            <i class="bi bi-pencil"></i>
                                        </button>
                                        <button class="btn btn-outline-success" onclick="assignUserRoles(${user.id})" title="分配角色" data-permission="USER_EDIT">
                                            <i class="bi bi-person-badge"></i>
                                        </button>
                                        <button class="btn btn-outline-info" onclick="resetPassword(${user.id})" title="重置密码" data-permission="USER_RESET_PASSWORD">
                                            <i class="bi bi-key"></i>
                                        </button>
                                        <button class="btn btn-outline-${user.status === 1 ? 'warning' : 'success'}"
                                                onclick="toggleUserStatus(${user.id}, ${user.status})"
                                                title="${user.status === 1 ? '禁用' : '启用'}" data-permission="USER_EDIT">
                                            <i class="bi bi-${user.status === 1 ? 'pause' : 'play'}"></i>
                                        </button>
                                        <button class="btn btn-outline-danger" onclick="deleteUser(${user.id})" title="删除" data-permission="USER_DELETE">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;

    } catch (error) {
        console.error('加载系统用户列表失败:', error);
        container.innerHTML = '<div class="alert alert-danger">加载系统用户列表失败</div>';
    }
}

// 加载平台用户列表
async function loadPlatformUsers() {
    const container = document.getElementById('platformUsersTable');
    if (!container) return;

    try {
        // 显示加载状态
        container.innerHTML = `
            <div class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <div class="mt-2 text-muted">正在加载平台用户列表...</div>
            </div>
        `;

        // 构建查询参数
        const params = new URLSearchParams();
        params.append('forward', 'PORTAL');

        if (currentPlatformApprovalFilter !== undefined && currentPlatformApprovalFilter !== '') {
            params.append('approvalStatus', currentPlatformApprovalFilter);
        }
        if (currentPlatformStatusFilter && currentPlatformStatusFilter !== 'all') {
            if (currentPlatformStatusFilter === 'active') {
                params.append('status', '1');
            } else if (currentPlatformStatusFilter === 'disabled') {
                params.append('status', '0');
            }
        }
        if (currentPlatformKeyword) {
            params.append('keyword', currentPlatformKeyword);
        }

        // 调用API
        const url = '/api/v1/users/by-forward?' + params.toString();
        const response = await apiCall(url);

        if (response.code !== 200) {
            throw new Error(response.message || '获取平台用户列表失败');
        }

        const users = response.data || [];

        // 渲染用户表格
        container.innerHTML = `
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead>
                        <tr>
                            <th width="25%">用户信息</th>
                            <th width="12%">审核状态</th>
                            <th width="10%">账号状态</th>
                            <th width="15%">注册时间</th>
                            <th width="15%">审核信息</th>
                            <th width="23%">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${users.map(user => `
                            <tr>
                                <td>
                                    <div>
                                        <div class="fw-medium">${user.realName || user.username}</div>
                                        <small class="text-muted">@${user.username} • ${user.email || '未设置邮箱'}</small>
                                        ${user.department ? `<small class="text-info d-block">${user.department}</small>` : ''}
                                    </div>
                                </td>
                                <td><span class="badge bg-${getApprovalStatusColor(user.approvalStatus)}">${getApprovalStatusText(user.approvalStatus)}</span></td>
                                <td><span class="badge bg-${getUserStatusColor(user.status)}">${getUserStatusText(user.status)}</span></td>
                                <td>${formatDateTime(user.createdTime)}</td>
                                <td>
                                    ${user.approvedTime ? formatDateTime(user.approvedTime) : '-'}
                                    ${user.approvedByName ? `<br><small class="text-muted">审核人: ${user.approvedByName}</small>` : ''}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        ${user.approvalStatus === 0 ? `
                                            <button class="btn btn-outline-success" onclick="approveUser(${user.id}, 1)" title="通过审核" data-permission="USER_APPROVE">
                                                <i class="bi bi-check-lg"></i>
                                            </button>
                                            <button class="btn btn-outline-danger" onclick="approveUser(${user.id}, 2)" title="拒绝审核" data-permission="USER_APPROVE">
                                                <i class="bi bi-x-lg"></i>
                                            </button>
                                        ` : ''}
                                        <button class="btn btn-outline-primary" onclick="showUserApprovalModal(${user.id})" title="审核详情" data-permission="USER_VIEW">
                                            <i class="bi bi-eye"></i>
                                        </button>
                                        <button class="btn btn-outline-success" onclick="assignUserRoles(${user.id})" title="分配角色" data-permission="USER_EDIT">
                                            <i class="bi bi-person-badge"></i>
                                        </button>
                                        <button class="btn btn-outline-${user.status === 1 ? 'warning' : 'success'}"
                                                onclick="toggleUserStatus(${user.id}, ${user.status})"
                                                title="${user.status === 1 ? '禁用' : '启用'}" data-permission="USER_EDIT">
                                            <i class="bi bi-${user.status === 1 ? 'pause' : 'play'}"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;

    } catch (error) {
        console.error('加载平台用户列表失败:', error);
        container.innerHTML = '<div class="alert alert-danger">加载平台用户列表失败</div>';
    }
}

// 显示创建用户模态框
function showCreateUserModal() {
    const modal = new bootstrap.Modal(document.getElementById('createUserModal'));
    modal.show();
}

// 编辑用户
async function editUser(id) {
    try {
        // 获取用户详情
        const response = await apiCall(`/api/v1/users/${id}`);
        if (response.code === 200) {
            const user = response.data;

            // 填充编辑表单
            document.getElementById('editUserId').value = user.id;
            document.getElementById('editUserEmail').value = user.email || '';
            document.getElementById('editUserPhone').value = user.phone || '';
            document.getElementById('editUserRealName').value = user.realName || '';
            document.getElementById('editUserDepartment').value = user.department || '';
            document.getElementById('editUserStatus').value = user.status;

            // 角色管理通过"分配角色"功能进行

            // 显示模态框
            const modal = new bootstrap.Modal(document.getElementById('editUserModal'));
            modal.show();
        } else {
            showToast('获取用户信息失败: ' + response.message, 'error');
        }
    } catch (error) {
        console.error('编辑用户失败:', error);
        showToast('获取用户信息失败', 'error');
    }
}

// 切换用户状态
async function toggleUserStatus(id, currentStatus) {
    const action = currentStatus === 1 ? '禁用' : '启用';
    showConfirmDialog(`确定要${action}这个用户吗？`, async () => {
        try {
            const response = await apiCall(`/api/v1/users/${id}/toggle-status`, 'POST');
            if (response.code === 200) {
                showToast(`${action}成功`, 'success');
                refreshCurrentTab(); // 重新加载当前Tab的用户列表
            } else {
                showToast(`${action}失败: ` + response.message, 'error');
            }
        } catch (error) {
            console.error(`${action}用户失败:`, error);
            showToast(`${action}失败`, 'error');
        }
    });
}

// 删除用户
async function deleteUser(id) {
    showConfirmDialog('确定要删除这个用户吗？此操作不可恢复！', async () => {
        try {
            const response = await apiCall(`/api/v1/users/${id}`, 'DELETE');
            if (response.code === 200) {
                showToast('删除成功', 'success');
                refreshCurrentTab(); // 重新加载当前Tab的用户列表
            } else {
                showToast('删除失败: ' + response.message, 'error');
            }
        } catch (error) {
            console.error('删除用户失败:', error);
            showToast('删除失败', 'error');
        }
    });
}

// 导出用户功能已移除

// 筛选系统用户
function filterSystemUsers() {
    const roleFilter = document.getElementById('systemUserRoleFilter').value;
    const statusFilter = document.getElementById('systemUserStatusFilter').value;
    currentSystemFilter = statusFilter;
    loadSystemUsers();
}

// 搜索系统用户
function searchSystemUsers() {
    const query = document.getElementById('systemUserSearchInput').value;
    currentSystemKeyword = query;
    loadSystemUsers();
}

// 筛选平台用户
function filterPlatformUsers() {
    const approvalFilter = document.getElementById('platformUserApprovalFilter').value;
    const statusFilter = document.getElementById('platformUserStatusFilter').value;
    currentPlatformApprovalFilter = approvalFilter;
    currentPlatformStatusFilter = statusFilter;
    loadPlatformUsers();
}

// 搜索平台用户
function searchPlatformUsers() {
    const query = document.getElementById('platformUserSearchInput').value;
    currentPlatformKeyword = query;
    loadPlatformUsers();
}

// 创建用户
async function createUser() {
    const form = document.getElementById('createUserForm');
    const formData = new FormData(form);

    // 前端验证
    const username = formData.get('username');
    const password = formData.get('password');

    if (!username || username.trim().length < 3) {
        showToast('用户名长度必须在3个字符以上', 'warning');
        return;
    }

    if (!password || password.length < 6 || password.length > 20) {
        showToast('密码长度必须在6-20个字符之间', 'warning');
        return;
    }

    const userData = {
        username: username.trim(),
        password: password,
        email: formData.get('email'),
        phone: formData.get('phone'),
        realName: formData.get('realName'),
        department: formData.get('department'),
        status: parseInt(formData.get('status')),
        forward: 'MANAGEMENT' // 系统创建的用户
    };

    try {
        const response = await apiCall('/api/v1/users', 'POST', userData);
        if (response.code === 200) {
            showToast('用户创建成功', 'success');
            bootstrap.Modal.getInstance(document.getElementById('createUserModal')).hide();
            form.reset();
            loadSystemUsers(); // 重新加载系统用户列表
            loadSystemUserStatistics(); // 重新加载统计数据
        } else {
            // 处理详细的验证错误信息
            if (response.message && response.message.includes('参数验证失败')) {
                // 解析验证错误详情
                const errorMatch = response.message.match(/message=([^,\]]+)/);
                if (errorMatch) {
                    showToast(errorMatch[1], 'error');
                } else {
                    showToast('输入信息不符合要求，请检查后重试', 'error');
                }
            } else {
                showToast('创建失败: ' + response.message, 'error');
            }
        }
    } catch (error) {
        console.error('创建用户失败:', error);
        if (error.message && error.message.includes('400')) {
            showToast('输入信息格式不正确，请检查后重试', 'error');
        } else {
            showToast('创建失败，请重试', 'error');
        }
    }
}

// 更新用户
async function updateUser() {
    const form = document.getElementById('editUserForm');
    const formData = new FormData(form);
    const userId = document.getElementById('editUserId').value;

    const userData = {
        email: formData.get('email'),
        phone: formData.get('phone'),
        realName: formData.get('realName'),
        department: formData.get('department'),
        status: parseInt(formData.get('status'))
    };

    try {
        const response = await apiCall(`/api/v1/users/${userId}`, 'PUT', userData);
        if (response.code === 200) {
            showToast('用户更新成功', 'success');
            bootstrap.Modal.getInstance(document.getElementById('editUserModal')).hide();
            loadSystemUsers(); // 重新加载系统用户列表
            loadSystemUserStatistics(); // 重新加载统计数据
        } else {
            showToast('更新失败: ' + response.message, 'error');
        }
    } catch (error) {
        console.error('更新用户失败:', error);
        showToast('更新失败', 'error');
    }
}

// 重置密码
async function resetPassword(id) {
    showConfirmDialog('确定要重置这个用户的密码吗？', async () => {
        try {
            const response = await apiCall(`/api/v1/users/${id}/reset-password`, 'POST');
            if (response.code === 200) {
                const data = response.data;
                showPasswordResetResult(data.username, data.newPassword);
            } else {
                showToast('重置密码失败: ' + response.message, 'error');
            }
        } catch (error) {
            console.error('重置密码失败:', error);
            showToast('重置密码失败', 'error');
        }
    });
}

// 加载角色列表（用于过滤）
async function loadRoles() {
    try {
        const response = await apiCall('/api/v1/roles');
        if (response.code === 200) {
            const roles = response.data || [];
            const systemRoleSelect = document.getElementById('systemUserRoleFilter');

            if (systemRoleSelect) {
                systemRoleSelect.innerHTML = '<option value="">全部角色</option>';
                if (Array.isArray(roles)) {
                    roles.forEach(role => {
                        const option = document.createElement('option');
                        option.value = role.id;
                        option.textContent = role.roleName;
                        systemRoleSelect.appendChild(option);
                    });
                }
            }
        }
    } catch (error) {
        console.error('加载角色列表失败:', error);
        // 设置默认选项
        const systemRoleSelect = document.getElementById('systemUserRoleFilter');
        if (systemRoleSelect) {
            systemRoleSelect.innerHTML = '<option value="">全部角色</option>';
        }
    }
}

// 全局变量存储当前筛选条件
let currentSystemFilter = null;
let currentSystemKeyword = '';
let currentPlatformApprovalFilter = '';
let currentPlatformStatusFilter = null;
let currentPlatformKeyword = '';

// 辅助函数
function getRoleColor(role) {
    const colors = {
        '管理员': 'danger',
        '普通用户': 'primary',
        '只读用户': 'secondary'
    };
    return colors[role] || 'secondary';
}

// 用户状态函数已在admin-common.js中定义，这里不需要重复定义

function formatDateTime(dateTimeStr) {
    if (!dateTimeStr) return '-';
    const date = new Date(dateTimeStr);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
}

// 角色分配相关功能
let currentUserId = null;

/**
 * 分配用户角色
 */
async function assignUserRoles(userId) {
    currentUserId = userId;

    try {
        // 获取用户信息
        const userResponse = await apiCall(`/api/v1/users/${userId}`);
        if (userResponse.code !== 200) {
            throw new Error(userResponse.message);
        }

        const user = userResponse.data;

        // 填充用户信息
        document.getElementById('roleUserUsername').textContent = user.username;
        document.getElementById('roleUserRealName').textContent = user.realName || '-';
        document.getElementById('roleUserEmail').textContent = user.email || '-';

        // 获取所有角色
        const rolesResponse = await apiCall('/api/v1/roles/active');
        if (rolesResponse.code !== 200) {
            throw new Error(rolesResponse.message);
        }

        const allRoles = rolesResponse.data;

        // 获取用户当前角色
        const userRolesResponse = await apiCall(`/api/v1/users/${userId}/roles`);
        const userRoleIds = userRolesResponse.code === 200 ? userRolesResponse.data : [];

        // 渲染角色列表
        renderUserRoleList(allRoles, userRoleIds);

        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('userRoleModal'));
        modal.show();

    } catch (error) {
        console.error('获取用户角色信息失败:', error);
        showToast('获取用户角色信息失败: ' + error.message, 'error');
    }
}

/**
 * 渲染用户角色列表
 */
function renderUserRoleList(allRoles, userRoleIds) {
    const container = document.getElementById('userRoleList');
    container.innerHTML = '';

    if (!allRoles || allRoles.length === 0) {
        container.innerHTML = '<p class="text-muted">暂无可用角色</p>';
        return;
    }

    allRoles.forEach(role => {
        const isChecked = userRoleIds.includes(role.id);

        const roleItem = document.createElement('div');
        roleItem.className = 'form-check mb-2';
        roleItem.innerHTML = `
            <input class="form-check-input" type="checkbox"
                   id="role_${role.id}" value="${role.id}"
                   ${isChecked ? 'checked' : ''}>
            <label class="form-check-label" for="role_${role.id}">
                <strong>${role.roleName}</strong>
                <small class="text-muted d-block">${role.description || '暂无描述'}</small>
                <span class="badge ${role.status === 1 ? 'bg-success' : 'bg-secondary'} me-1">
                    ${role.status === 1 ? '启用' : '禁用'}
                </span>
                <code class="small">${role.roleCode}</code>
            </label>
        `;

        container.appendChild(roleItem);
    });
}

/**
 * 保存用户角色
 */
async function saveUserRoles() {
    if (!currentUserId) {
        showToast('用户ID不存在', 'error');
        return;
    }

    // 获取选中的角色ID
    const checkboxes = document.querySelectorAll('#userRoleList input[type="checkbox"]:checked');
    const roleIds = Array.from(checkboxes).map(cb => Number(cb.value));

    try {
        const response = await apiCall(`/api/v1/users/${currentUserId}/roles`, 'POST', {
            roleIds: roleIds
        });

        if (response.code === 200) {
            showToast('角色分配成功', 'success');
            bootstrap.Modal.getInstance(document.getElementById('userRoleModal')).hide();
            refreshCurrentTab(); // 重新加载当前Tab的用户列表
        } else {
            showToast('角色分配失败: ' + response.message, 'error');
        }
    } catch (error) {
        console.error('保存用户角色失败:', error);
        showToast('角色分配失败', 'error');
    }
}

// 加载待审核用户数量
async function loadPendingApprovalCount() {
    try {
        const response = await apiCall('/api/v1/users/pending-approval-count');
        if (response.code === 200) {
            const count = response.data;
            const badge = document.getElementById('pendingApprovalBadge');
            if (count > 0) {
                badge.textContent = count;
                badge.style.display = 'inline';
            } else {
                badge.style.display = 'none';
            }
        }
    } catch (error) {
        console.error('加载待审核用户数量失败:', error);
    }
}

// 显示用户审核模态框
async function showUserApprovalModal(userId) {
    try {
        // 获取用户详情
        const response = await apiCall(`/api/v1/users/${userId}`);
        if (response.code === 200) {
            const user = response.data;

            // 填充用户信息
            document.getElementById('approvalUserId').value = user.id;
            document.getElementById('approvalUsername').textContent = user.username;
            document.getElementById('approvalRealName').textContent = user.realName || '-';
            document.getElementById('approvalEmail').textContent = user.email || '-';
            document.getElementById('approvalCreatedTime').textContent = formatDateTime(user.createdTime);

            // 重置表单
            document.getElementById('userApprovalForm').reset();
            document.getElementById('approvalUserId').value = user.id;

            // 显示模态框
            const modal = new bootstrap.Modal(document.getElementById('userApprovalModal'));
            modal.show();
        } else {
            showToast('获取用户信息失败: ' + response.message, 'error');
        }
    } catch (error) {
        console.error('获取用户信息失败:', error);
        showToast('获取用户信息失败', 'error');
    }
}

// 快速审核用户
async function approveUser(userId, approvalStatus) {
    const action = approvalStatus === 1 ? '通过' : '拒绝';
    showConfirmDialog(`确定要${action}这个用户的审核吗？`, async () => {
        await performUserApproval(userId, approvalStatus, action);
    });
}

// 执行用户审核
async function performUserApproval(userId, approvalStatus, action) {

    try {
        const response = await apiCall('/api/v1/users/approve', 'POST', {
            userId: userId,
            approvalStatus: approvalStatus,
            approvalRemark: approvalStatus === 1 ? '快速通过' : '快速拒绝'
        });

        if (response.code === 200) {
            showToast(`用户审核${action}成功`, 'success');
            loadPlatformUsers(); // 重新加载平台用户列表
            loadPlatformUserStatistics(); // 重新加载统计数据
            loadPendingApprovalCount(); // 更新待审核数量
        } else {
            showToast(`审核失败: ${response.message}`, 'error');
        }
    } catch (error) {
        console.error('审核用户失败:', error);
        showToast('审核失败', 'error');
    }
}

// 提交用户审核
async function submitUserApproval() {
    const form = document.getElementById('userApprovalForm');
    const formData = new FormData(form);

    const approvalData = {
        userId: parseInt(document.getElementById('approvalUserId').value),
        approvalStatus: parseInt(formData.get('approvalStatus')),
        approvalRemark: formData.get('approvalRemark')
    };

    if (!approvalData.approvalStatus) {
        showToast('请选择审核结果', 'warning');
        return;
    }

    try {
        const response = await apiCall('/api/v1/users/approve', 'POST', approvalData);
        if (response.code === 200) {
            showToast('审核提交成功', 'success');
            bootstrap.Modal.getInstance(document.getElementById('userApprovalModal')).hide();
            loadPlatformUsers(); // 重新加载平台用户列表
            loadPlatformUserStatistics(); // 重新加载统计数据
            loadPendingApprovalCount(); // 更新待审核数量
        } else {
            showToast('审核失败: ' + response.message, 'error');
        }
    } catch (error) {
        console.error('提交审核失败:', error);
        showToast('审核失败', 'error');
    }
}

// 获取审核状态颜色
function getApprovalStatusColor(status) {
    const colors = {
        0: 'warning',  // 待审核
        1: 'success',  // 已通过
        2: 'danger'    // 已拒绝
    };
    return colors[status] || 'secondary';
}

// 获取审核状态文本
function getApprovalStatusText(status) {
    const texts = {
        0: '待审核',
        1: '已通过',
        2: '已拒绝'
    };
    return texts[status] || '未知';
}

// 显示Toast消息
function showToast(message, type = 'info') {
    // 创建toast容器（如果不存在）
    let toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
        toastContainer.style.zIndex = '9999';
        document.body.appendChild(toastContainer);
    }

    // 创建toast元素
    const toastId = 'toast-' + Date.now();
    const toastHtml = `
        <div id="${toastId}" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header">
                <i class="bi bi-${getToastIcon(type)} text-${type} me-2"></i>
                <strong class="me-auto">系统消息</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body">
                ${message}
            </div>
        </div>
    `;

    toastContainer.insertAdjacentHTML('beforeend', toastHtml);

    // 显示toast
    const toastElement = document.getElementById(toastId);
    const toast = new bootstrap.Toast(toastElement, {
        autohide: true,
        delay: 3000
    });
    toast.show();

    // 自动清理
    toastElement.addEventListener('hidden.bs.toast', () => {
        toastElement.remove();
    });
}

// 获取Toast图标
function getToastIcon(type) {
    const icons = {
        'success': 'check-circle',
        'error': 'exclamation-triangle',
        'warning': 'exclamation-triangle',
        'info': 'info-circle'
    };
    return icons[type] || 'info-circle';
}

// 显示确认对话框
function showConfirmDialog(message, onConfirm, onCancel = null) {
    // 移除现有的确认对话框
    const existingModal = document.getElementById('confirmModal');
    if (existingModal) {
        existingModal.remove();
    }

    // 创建确认对话框
    const modalHtml = `
        <div class="modal fade" id="confirmModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="bi bi-question-circle text-warning me-2"></i>确认操作
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <p class="mb-0">${message}</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" id="confirmBtn">确定</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);

    const modal = new bootstrap.Modal(document.getElementById('confirmModal'));
    const confirmBtn = document.getElementById('confirmBtn');

    // 绑定确认按钮事件
    confirmBtn.addEventListener('click', () => {
        modal.hide();
        if (onConfirm) {
            onConfirm();
        }
    });

    // 绑定取消事件
    document.getElementById('confirmModal').addEventListener('hidden.bs.modal', () => {
        document.getElementById('confirmModal').remove();
        if (onCancel) {
            onCancel();
        }
    });

    modal.show();
}

// 显示密码重置结果
function showPasswordResetResult(username, newPassword) {
    // 移除现有的结果对话框
    const existingModal = document.getElementById('passwordResetModal');
    if (existingModal) {
        existingModal.remove();
    }

    // 创建密码重置结果对话框
    const modalHtml = `
        <div class="modal fade" id="passwordResetModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="bi bi-check-circle text-success me-2"></i>密码重置成功
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-success">
                            <h6 class="alert-heading">密码重置成功！</h6>
                            <hr>
                            <p class="mb-2"><strong>用户名：</strong>${username}</p>
                            <p class="mb-2"><strong>新密码：</strong><code>${newPassword}</code></p>
                            <hr class="my-2">
                            <p class="mb-0 text-muted">请及时通知用户修改密码。</p>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" onclick="copyPassword('${newPassword}')">
                            <i class="bi bi-clipboard me-1"></i>复制密码
                        </button>
                        <button type="button" class="btn btn-primary" data-bs-dismiss="modal">确定</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHtml);

    const modal = new bootstrap.Modal(document.getElementById('passwordResetModal'));

    // 绑定关闭事件
    document.getElementById('passwordResetModal').addEventListener('hidden.bs.modal', () => {
        document.getElementById('passwordResetModal').remove();
    });

    modal.show();
}

// 复制密码到剪贴板
function copyPassword(password) {
    navigator.clipboard.writeText(password).then(() => {
        showToast('密码已复制到剪贴板', 'success');
    }).catch(() => {
        // 降级方案
        const textArea = document.createElement('textarea');
        textArea.value = password;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showToast('密码已复制到剪贴板', 'success');
    });
}

// 渲染用户角色
function renderUserRoles(user) {
    // 如果有多角色信息，优先使用
    if (user.roles && Array.isArray(user.roles) && user.roles.length > 0) {
        if (user.roles.length === 1) {
            // 只有一个角色，直接显示
            const role = user.roles[0];
            return `<span class="badge bg-${getRoleColor(role.roleName)}">${role.roleName}</span>`;
        } else {
            // 多个角色，显示数量和下拉
            const primaryRole = user.roles[0];
            const otherCount = user.roles.length - 1;
            return `
                <div class="d-flex align-items-center gap-1">
                    <span class="badge bg-${getRoleColor(primaryRole.roleName)}">${primaryRole.roleName}</span>
                    ${otherCount > 0 ? `
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle p-1" type="button"
                                    data-bs-toggle="dropdown" title="查看所有角色">
                                +${otherCount}
                            </button>
                            <ul class="dropdown-menu">
                                ${user.roles.slice(1).map(role => `
                                    <li><span class="dropdown-item-text">
                                        <span class="badge bg-${getRoleColor(role.roleName)}">${role.roleName}</span>
                                    </span></li>
                                `).join('')}
                            </ul>
                        </div>
                    ` : ''}
                </div>
            `;
        }
    } else {
        // 降级到单角色显示（兼容旧数据）
        if (user.roleName) {
            return `<span class="badge bg-${getRoleColor(user.roleName)}">${user.roleName}</span>`;
        } else {
            return `<span class="badge bg-secondary">未分配角色</span>`;
        }
    }
}

// 刷新当前Tab的用户列表
function refreshCurrentTab() {
    const activeTab = document.querySelector('#userTabs .nav-link.active');
    if (activeTab) {
        const tabId = activeTab.getAttribute('aria-controls');
        if (tabId === 'system-users') {
            loadSystemUsers();
            loadSystemUserStatistics();
        } else if (tabId === 'platform-users') {
            loadPlatformUsers();
            loadPlatformUserStatistics();
            loadPendingApprovalCount();
        }
    }
}
