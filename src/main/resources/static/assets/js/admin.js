/**
 * 智能体矩阵 管理后台主框架 JavaScript
 * 负责页面路由、导航管理和公共功能
 */

// 当前活动页面
let currentPage = 'dashboard';

// 侧边栏状态
let sidebarCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';

// 当前用户信息
let currentUserInfo = null;

// 主题管理
let currentTheme = localStorage.getItem('theme') || 'light';

// 消息侧边栏状态
let messageSidebarOpen = false;

// 页面配置
const pageConfig = {
    dashboard: {
        title: '仪表板',
        file: 'pages/dashboard.html',
        script: 'assets/js/dashboard.js',
        permission: 'DASHBOARD_VIEW'
    },
    agents: {
        title: 'Agent管理',
        file: 'pages/agents.html',
        script: 'assets/js/agents.js',
        permission: 'AGENT_VIEW'
    },
    playground: {
        title: '智能调试台',
        file: 'pages/playground.html',
        script: 'assets/js/playground.js',
        permission: 'PLAYGROUND_VIEW'
    },
    templates: {
        title: '业务模板',
        file: 'pages/templates.html',
        script: 'assets/js/templates.js',
        permission: 'TEMPLATE_VIEW'
    },
    'smart-form-config': {
        title: '智能表单配置',
        file: 'pages/smart-form-config.html',
        script: 'assets/js/smart-form-config.js',
        permission: 'business:smart-form'
    },
    recognition: {
        title: '文档识别',
        file: 'pages/recognition.html',
        script: 'assets/js/recognition.js',
        permission: 'RECOGNITION_VIEW'
    },
    users: {
        title: '用户管理',
        file: 'pages/users.html',
        script: 'assets/js/users.js',
        permission: 'USER_VIEW'
    },
    roles: {
        title: '角色管理',
        file: 'pages/roles.html',
        script: 'assets/js/roles.js',
        permission: 'ROLE_VIEW'
    },
    permissions: {
        title: '权限管理',
        file: 'pages/permissions.html',
        script: 'assets/js/permissions.js',
        permission: 'PERMISSION_VIEW'
    },
    analytics: {
        title: '数据分析',
        file: 'pages/analytics.html',
        script: 'assets/js/analytics.js',
        permission: 'ANALYTICS_VIEW'
    },
    // Agent审核页面配置已注释
    /*
    approval: {
        title: 'Agent审核',
        file: 'pages/approval.html',
        script: 'assets/js/approval.js',
        permission: 'AGENT_APPROVAL_VIEW'
    },
    */
    'agent-approval-list': {
        title: 'Agent审批列表',
        file: 'pages/agent-approval-list.html',
        script: 'assets/js/agent-approval-list.js',
        permission: 'AGENT_APPROVAL_VIEW'
    },
    'agent-approval-detail': {
        title: 'Agent审批详情',
        file: 'pages/agent-unified-detail.html',
        script: 'assets/js/agent-unified-detail.js',
        permission: 'AGENT_APPROVAL_VIEW',
        mode: 'approval'
    },
    'agent-detail': {
        title: 'Agent详情',
        file: 'pages/agent-unified-detail.html',
        script: 'assets/js/agent-unified-detail.js',
        permission: 'AGENT_APPROVAL_VIEW',
        mode: 'view'
    },
    'agent-supplement': {
        title: 'Agent补充资料',
        file: 'pages/agent-unified-detail.html',
        script: 'assets/js/agent-unified-detail.js',
        permission: 'AGENT_MANAGE',
        mode: 'supplement'
    },
    'operation-logs': {
        title: '操作日志',
        file: 'pages/operation-logs.html',
        script: 'assets/js/operation-logs.js',
        permission: 'LOG_VIEW'
    }
};

// 已加载的脚本缓存
const loadedScripts = new Set();

/**
 * 页面初始化
 */
document.addEventListener('DOMContentLoaded', function() {
    // 初始化侧边栏状态
    initSidebar();

    // 初始化主题
    initTheme();

    // 初始化用户信息
    initCurrentUserInfo();

    // 初始化消息系统
    initMessageSystem();

    // 检查URL hash，如果有则加载对应页面
    const hash = window.location.hash.substring(1); // 去掉#号
    let initialPage = 'dashboard'; // 默认页面

    if (hash && pageConfig[hash]) {
        initialPage = hash;
    } else if (hash) {
        // 如果hash存在但不在pageConfig中，清除hash并使用默认页面
        console.warn('无效的页面hash:', hash);
        history.replaceState(null, null, window.location.pathname);
    }

    // 加载初始页面
    loadPage(initialPage);

    // 监听hash变化
    window.addEventListener('hashchange', function() {
        const newHash = window.location.hash.substring(1);
        if (newHash && pageConfig[newHash]) {
            loadPage(newHash);
        } else if (newHash) {
            // 如果hash不在pageConfig中，显示页面不存在
            showAccessDenied('页面不存在');
        }
    });

    // 监听来自子页面的消息
    window.addEventListener('message', function(event) {
        if (event.data.type === 'switchToPlayground') {
            // 存储agentCode
            if (event.data.agentCode) {
                sessionStorage.setItem('selectedAgentCode', event.data.agentCode);
            }
            // 切换到智能调试台
            loadPage('playground');
        } else if (event.data.type === 'switchToRecognition') {
            // 存储agentCode
            if (event.data.agentCode) {
                sessionStorage.setItem('selectedAgentCode', event.data.agentCode);
            }
            // 切换到文档识别
            loadPage('recognition');
        }
    });
});

/**
 * 加载页面
 * @param {string} pageName 页面名称
 */
async function loadPage(pageName) {
    if (!pageConfig[pageName]) {
        console.error('页面不存在:', pageName);
        showAccessDenied('页面不存在');
        return;
    }

    const config = pageConfig[pageName];

    // 检查页面访问权限
    if (config.permission && !hasPagePermission(config.permission)) {
        console.warn('用户无权限访问页面:', pageName, '需要权限:', config.permission);
        showAccessDenied('您没有权限访问此页面');
        return;
    }

    try {
        // 显示加载状态
        showLoading();

        // 清理之前页面的资源
        cleanupPreviousPage();

        // 更新导航状态
        updateNavigation(pageName);

        // 更新页面标题
        updatePageTitle(config.title);

        // 如果页面有模式参数，存储到sessionStorage
        if (config.mode) {
            sessionStorage.setItem('agentDetailMode', config.mode);
        }

        // 加载页面内容
        const html = await loadHTML(config.file);
        document.getElementById('content-container').innerHTML = html;

        // 加载页面脚本
        if (config.script && !loadedScripts.has(config.script)) {
            await loadScript(config.script);
            loadedScripts.add(config.script);
        }

        // 调用页面初始化函数（如果存在）
        // 注意：即使脚本已经加载过，也要调用初始化函数，因为页面内容已经重新加载
        if (config.file === 'pages/agent-unified-detail.html') {
            if (typeof window.initUnifiedDetailPage === 'function') {
                window.initUnifiedDetailPage();
            }
        } else {
            // 其他页面使用原有的命名规则
            const camelCaseName = pageName.replace(/-([a-z])/g, (match, letter) => letter.toUpperCase());
            const initFunctionName = `init${camelCaseName.charAt(0).toUpperCase() + camelCaseName.slice(1)}Page`;

            console.log(`尝试调用页面初始化函数: ${initFunctionName}`);

            if (typeof window[initFunctionName] === 'function') {
                try {
                    window[initFunctionName]();
                    console.log(`页面初始化函数 ${initFunctionName} 调用成功`);
                } catch (error) {
                    console.error(`页面初始化函数 ${initFunctionName} 调用失败:`, error);
                }
            } else {
                console.warn(`页面初始化函数 ${initFunctionName} 不存在`);
            }
        }

        // 应用权限控制（仅对页面内容，不影响菜单）
        setTimeout(() => {
            if (window.permissionManager) {
                // 只控制页面内容的权限，不重复控制菜单
                window.permissionManager.controlButtons();
                window.permissionManager.controlTableActions();
            }
        }, 100);

        // 更新当前页面
        currentPage = pageName;

        // 更新URL hash（避免触发hashchange事件的无限循环）
        if (window.location.hash.substring(1) !== pageName) {
            history.replaceState(null, null, '#' + pageName);
        }

    } catch (error) {
        console.error('加载页面失败:', error);
        showError('页面加载失败，请刷新重试');
    }
}

/**
 * 加载HTML文件
 * @param {string} url HTML文件路径
 * @returns {Promise<string>} HTML内容
 */
async function loadHTML(url) {
    const response = await fetch(url);
    if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    return await response.text();
}

/**
 * 动态加载JavaScript文件
 * @param {string} url 脚本文件路径
 * @returns {Promise<void>}
 */
function loadScript(url) {
    return new Promise((resolve, reject) => {
        const script = document.createElement('script');
        script.src = url;
        script.onload = resolve;
        script.onerror = reject;
        document.head.appendChild(script);
    });
}

/**
 * 更新导航状态
 * @param {string} activePage 当前活动页面
 */
function updateNavigation(activePage) {
    // 移除所有活动状态
    document.querySelectorAll('.sidebar .nav-link').forEach(link => {
        link.classList.remove('active');
    });

    // 添加当前页面的活动状态
    const activeLink = document.querySelector(`.sidebar .nav-link[onclick="loadPage('${activePage}')"]`);
    if (activeLink) {
        activeLink.classList.add('active');
    }
}

/**
 * 跳转到Agent详情页面（查看模式）
 * @param {string|number} agentId Agent ID
 * @param {string} sourcePage 来源页面，用于返回
 */
function showAgentDetail(agentId, sourcePage = 'agents') {
    if (!agentId) {
        showToast('Agent ID不能为空', 'error');
        return;
    }

    // 存储参数到sessionStorage
    sessionStorage.setItem('currentAgentId', agentId);
    sessionStorage.setItem('agentDetailSource', sourcePage);
    sessionStorage.setItem('agentDetailMode', 'view');

    // 跳转到统一详情页面
    loadPage('agent-detail');
}

/**
 * 跳转到Agent审批详情页面（审批模式）
 * @param {string|number} agentId Agent ID
 * @param {string} sourcePage 来源页面，用于返回
 */
function showAgentApprovalDetail(agentId, sourcePage = 'agent-approval-list') {
    if (!agentId) {
        showToast('Agent ID不能为空', 'error');
        return;
    }

    // 存储参数到sessionStorage
    sessionStorage.setItem('currentAgentId', agentId);
    sessionStorage.setItem('agentDetailSource', sourcePage);
    sessionStorage.setItem('agentDetailMode', 'approval');

    // 跳转到统一详情页面
    loadPage('agent-approval-detail');
}

/**
 * 跳转到Agent补充资料页面（补充资料模式）
 * @param {string|number} agentId Agent ID
 * @param {string} sourcePage 来源页面，用于返回
 */
function showAgentSupplement(agentId, sourcePage = 'agent-approval-list') {
    if (!agentId) {
        showToast('Agent ID不能为空', 'error');
        return;
    }

    // 存储参数到sessionStorage
    sessionStorage.setItem('currentAgentId', agentId);
    sessionStorage.setItem('agentDetailSource', sourcePage);
    sessionStorage.setItem('agentDetailMode', 'supplement');

    // 跳转到统一详情页面
    loadPage('agent-supplement');
}

/**
 * 更新页面标题
 * @param {string} title 页面标题
 */
function updatePageTitle(title) {
    document.getElementById('page-title').textContent = title;
    document.title = `${title} - 智能体矩阵 管理后台`;
}

/**
 * 检查页面访问权限
 * @param {string} permission 权限代码
 * @returns {boolean} 是否有权限
 */
function hasPagePermission(permission) {
    // 如果没有权限要求，允许访问
    if (!permission) {
        return true;
    }

    // 检查菜单权限控制器中的权限
    if (window.menuPermissionController && window.menuPermissionController.permissionsLoaded) {
        return window.menuPermissionController.hasPermission(permission);
    }

    // 检查权限管理器中的权限
    if (window.permissionManager) {
        return window.permissionManager.hasPermission(permission);
    }

    // 如果权限系统未加载，暂时允许访问（避免阻塞）
    console.warn('权限系统未完全加载，暂时允许访问:', permission);
    return true;
}

/**
 * 显示访问拒绝页面
 * @param {string} message 拒绝原因
 */
function showAccessDenied(message = '您没有权限访问此页面') {
    document.getElementById('content-container').innerHTML = `
        <div class="d-flex justify-content-center align-items-center" style="height: 400px;">
            <div class="text-center">
                <div class="mb-4">
                    <i class="bi bi-shield-exclamation text-warning" style="font-size: 4rem;"></i>
                </div>
                <h3 class="text-muted mb-3">访问被拒绝</h3>
                <p class="text-muted mb-4">${message}</p>
                <div class="d-flex gap-2 justify-content-center">
                    <button class="btn btn-primary" onclick="loadPage('dashboard')">
                        <i class="bi bi-house"></i> 返回首页
                    </button>
                    <button class="btn btn-outline-secondary" onclick="history.back()">
                        <i class="bi bi-arrow-left"></i> 返回上页
                    </button>
                </div>
            </div>
        </div>
    `;

    // 更新页面标题
    updatePageTitle('访问被拒绝');

    // 清除URL hash
    if (window.location.hash) {
        history.replaceState(null, null, window.location.pathname);
    }
}

/**
 * 显示加载状态
 */
function showLoading() {
    document.getElementById('content-container').innerHTML = `
        <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-3 text-muted">正在加载页面...</p>
        </div>
    `;
}

/**
 * 显示错误信息
 * @param {string} message 错误信息
 */
function showError(message) {
    document.getElementById('content-container').innerHTML = `
        <div class="text-center py-5">
            <div class="alert alert-danger" role="alert">
                <i class="bi bi-exclamation-triangle me-2"></i>
                ${message}
            </div>
            <button class="btn btn-primary" onclick="location.reload()">
                <i class="bi bi-arrow-clockwise me-2"></i>刷新页面
            </button>
        </div>
    `;
}

/**
 * 切换侧边栏（移动端）
 */
function toggleSidebar() {
    const sidebar = document.querySelector('.sidebar');
    sidebar.classList.toggle('show');
}

/**
 * 退出登录
 */
async function logout() {
    const confirmed = await showConfirm('确定要退出登录吗？', '退出确认');
    if (confirmed) {
        // 清除本地存储
        localStorage.removeItem('authToken');
        localStorage.removeItem('currentUser');
        sessionStorage.clear();

        // 跳转到登录页
        window.location.href = '/login.html';
    }
}

/**
 * 获取当前页面名称
 * @returns {string} 当前页面名称
 */
function getCurrentPage() {
    return currentPage;
}

/**
 * 页面间通信事件系统
 */
const pageEvents = {
    listeners: {},
    
    // 监听事件
    on(event, callback) {
        if (!this.listeners[event]) {
            this.listeners[event] = [];
        }
        this.listeners[event].push(callback);
    },
    
    // 触发事件
    emit(event, data) {
        if (this.listeners[event]) {
            this.listeners[event].forEach(callback => callback(data));
        }
    },
    
    // 移除事件监听
    off(event, callback) {
        if (this.listeners[event]) {
            this.listeners[event] = this.listeners[event].filter(cb => cb !== callback);
        }
    }
};

// 将事件系统暴露到全局
window.pageEvents = pageEvents;

/**
 * 初始化当前用户信息
 */
async function initCurrentUserInfo() {
    try {
        console.log('🔄 开始获取当前用户信息...');

        // 获取认证token
        const token = localStorage.getItem('authToken') || sessionStorage.getItem('token');
        if (!token) {
            console.warn('⚠️ 未找到认证token，使用默认显示');
            updateUserDisplay('用户', null);
            return;
        }

        // 调用API获取用户信息
        const response = await fetch('/api/v1/user/info', {
            method: 'GET',
            headers: {
                'Authorization': 'Bearer ' + token,
                'Content-Type': 'application/json'
            }
        });

        if (response.ok) {
            const result = await response.json();
            if (result.code === 200 && result.data) {
                currentUserInfo = result.data;
                console.log('✅ 用户信息获取成功:', currentUserInfo);

                // 更新显示
                const displayName = currentUserInfo.realName || currentUserInfo.username || '用户';
                updateUserDisplay(displayName, currentUserInfo);

                // 保存到本地存储
                localStorage.setItem('currentUserInfo', JSON.stringify(currentUserInfo));
            } else {
                console.warn('⚠️ 用户信息API返回异常:', result);
                updateUserDisplay('用户', null);
            }
        } else {
            console.warn('⚠️ 用户信息API调用失败，状态码:', response.status);
            updateUserDisplay('用户', null);
        }
    } catch (error) {
        console.error('❌ 获取用户信息失败:', error);
        updateUserDisplay('用户', null);
    }
}

/**
 * 更新用户显示信息
 */
function updateUserDisplay(displayName, userInfo) {
    const userDisplayElement = document.getElementById('currentUserDisplay');
    if (userDisplayElement) {
        userDisplayElement.textContent = displayName;
    }

    // 如果有用户信息，可以在这里添加更多的UI更新逻辑
    if (userInfo) {
        // 例如：更新头像、状态等
        console.log('用户角色:', userInfo.roleCode);
        console.log('用户权限:', userInfo.permissions);
    }
}

/**
 * 初始化主题
 */
function initTheme() {
    console.log('🎨 初始化主题系统...');

    // 应用当前主题
    applyTheme(currentTheme);

    // 更新主题图标
    updateThemeIcon();

    console.log('✅ 主题系统初始化完成，当前主题:', currentTheme);
}

/**
 * 切换主题
 */
function toggleTheme() {
    console.log('🎨 切换主题被调用，当前主题:', currentTheme);

    currentTheme = currentTheme === 'light' ? 'dark' : 'light';
    console.log('🎨 新主题:', currentTheme);

    applyTheme(currentTheme);
    updateThemeIcon();

    // 保存到本地存储
    localStorage.setItem('theme', currentTheme);

    console.log('🎨 主题已切换为:', currentTheme);

    // 触发主题变更事件
    window.dispatchEvent(new CustomEvent('themeChanged', {
        detail: { theme: currentTheme }
    }));
}

/**
 * 应用主题
 */
function applyTheme(theme) {
    console.log('🎨 应用主题:', theme);

    const body = document.body;
    const html = document.documentElement;

    if (theme === 'dark') {
        body.classList.add('theme-dark');
        html.classList.add('theme-dark');
        console.log('🌙 已应用深色主题');
    } else {
        body.classList.remove('theme-dark');
        html.classList.remove('theme-dark');
        console.log('☀️ 已应用浅色主题');
    }

    // 强制重新渲染
    body.style.display = 'none';
    body.offsetHeight; // 触发重排
    body.style.display = '';
}

/**
 * 更新主题图标
 */
function updateThemeIcon() {
    const themeIcon = document.getElementById('themeIcon');
    if (themeIcon) {
        if (currentTheme === 'dark') {
            themeIcon.className = 'bi bi-moon-fill';
        } else {
            themeIcon.className = 'bi bi-sun-fill';
        }
    }
}

/**
 * 初始化侧边栏状态
 */
function initSidebar() {
    const sidebar = document.getElementById('sidebar');
    const toggleIcon = document.getElementById('sidebarToggleIcon');

    if (sidebarCollapsed) {
        sidebar.classList.add('collapsed');
        if (toggleIcon) {
            toggleIcon.className = 'bi bi-layout-sidebar';
        }
    }
}

/**
 * 初始化消息系统
 */
async function initMessageSystem() {
    console.log('📨 初始化消息系统...');

    try {
        // 加载未读消息数量
        await loadUnreadMessageCount();

        // 设置定时刷新未读数量（每30秒）
        setInterval(loadUnreadMessageCount, 30000);

        console.log('✅ 消息系统初始化完成');
    } catch (error) {
        console.error('❌ 消息系统初始化失败:', error);
    }
}

/**
 * 加载未读消息数量
 */
async function loadUnreadMessageCount() {
    try {
        const token = localStorage.getItem('authToken') || sessionStorage.getItem('token');
        if (!token) return;

        const response = await fetch('/api/v1/messages/unread-count', {
            method: 'GET',
            headers: {
                'Authorization': 'Bearer ' + token,
                'Content-Type': 'application/json'
            }
        });

        if (response.ok) {
            const result = await response.json();
            if (result.code === 200) {
                updateUnreadBadge(result.data);
            }
        }
    } catch (error) {
        console.error('获取未读消息数量失败:', error);
    }
}

/**
 * 更新未读消息徽章
 */
function updateUnreadBadge(count) {
    const badge = document.getElementById('unreadBadge');
    if (badge) {
        if (count > 0) {
            badge.textContent = count > 99 ? '99+' : count;
            badge.style.display = 'block';
        } else {
            badge.style.display = 'none';
        }
    }
}

/**
 * 切换消息侧边栏
 */
function toggleMessageSidebar() {
    const sidebar = document.getElementById('messageSidebar');
    const overlay = document.getElementById('messageSidebarOverlay');

    messageSidebarOpen = !messageSidebarOpen;

    if (messageSidebarOpen) {
        sidebar.classList.add('show');
        overlay.classList.add('show');
        // 加载消息列表
        loadMessageList();
    } else {
        sidebar.classList.remove('show');
        overlay.classList.remove('show');
    }
}

/**
 * 加载消息列表
 */
async function loadMessageList() {
    const messageList = document.getElementById('messageList');

    try {
        // 显示加载状态
        messageList.innerHTML = `
            <div class="text-center py-4">
                <div class="spinner-border spinner-border-sm text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <p class="mt-2 text-muted">正在加载消息...</p>
            </div>
        `;

        const token = localStorage.getItem('authToken') || sessionStorage.getItem('token');
        if (!token) {
            throw new Error('未找到认证token');
        }

        const response = await fetch('/api/v1/messages/list?limit=50', {
            method: 'GET',
            headers: {
                'Authorization': 'Bearer ' + token,
                'Content-Type': 'application/json'
            }
        });

        if (response.ok) {
            const result = await response.json();
            if (result.code === 200) {
                renderMessageList(result.data);
            } else {
                throw new Error(result.message);
            }
        } else {
            throw new Error('API调用失败');
        }
    } catch (error) {
        console.error('加载消息列表失败:', error);
        messageList.innerHTML = `
            <div class="empty-messages">
                <i class="bi bi-exclamation-triangle"></i>
                <p>加载消息失败</p>
                <button class="btn btn-sm btn-primary" onclick="loadMessageList()">重试</button>
            </div>
        `;
    }
}

/**
 * 切换侧边栏展开/收起状态
 */
function toggleSidebarCollapse() {
    const sidebar = document.getElementById('sidebar');
    const toggleIcon = document.getElementById('sidebarToggleIcon');

    // 防止快速点击导致的动画冲突
    if (sidebar.classList.contains('transitioning')) {
        return;
    }

    sidebar.classList.add('transitioning');
    sidebarCollapsed = !sidebarCollapsed;

    if (sidebarCollapsed) {
        sidebar.classList.add('collapsed');
        if (toggleIcon) {
            toggleIcon.className = 'bi bi-layout-sidebar';
        }
    } else {
        sidebar.classList.remove('collapsed');
        if (toggleIcon) {
            toggleIcon.className = 'bi bi-list';
        }
    }

    // 保存状态到本地存储
    localStorage.setItem('sidebarCollapsed', sidebarCollapsed.toString());

    // 动画完成后移除transitioning类
    setTimeout(() => {
        sidebar.classList.remove('transitioning');
    }, 300);
}

/**
 * 显示用户资料模态框
 */
function showUserProfile() {
    // 获取用户信息
    const userInfo = currentUserInfo || JSON.parse(localStorage.getItem('currentUserInfo') || '{}');

    // 用户基本信息
    const username = userInfo.username || '未知用户';
    const realName = userInfo.realName || username;
    const email = userInfo.email || '未设置';
    const roleCode = userInfo.roleCode || 'USER';

    // 角色显示名称和样式
    const roleDisplay = getRoleDisplay(roleCode);

    // 权限标签
    const permissionBadges = getPermissionBadges(userInfo.permissions || []);

    const modalHtml = `
        <div class="modal fade" id="userProfileModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="bi bi-person me-2"></i>个人资料
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row g-3">
                            <div class="col-12 text-center">
                                <div class="mb-3">
                                    <i class="bi bi-person-circle" style="font-size: 4rem; color: var(--primary-color);"></i>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold">用户名</label>
                                <p class="form-control-plaintext">${username}</p>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold">真实姓名</label>
                                <p class="form-control-plaintext">${realName}</p>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold">角色</label>
                                <p class="form-control-plaintext">
                                    <span class="badge ${roleDisplay.class}">${roleDisplay.name}</span>
                                </p>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold">邮箱</label>
                                <p class="form-control-plaintext">${email}</p>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold">状态</label>
                                <p class="form-control-plaintext">
                                    <span class="badge bg-success">
                                        <i class="bi bi-check-circle me-1"></i>正常
                                    </span>
                                </p>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold">最后登录</label>
                                <p class="form-control-plaintext">${new Date().toLocaleString('zh-CN')}</p>
                            </div>
                            <div class="col-12">
                                <label class="form-label fw-bold">权限</label>
                                <div class="d-flex flex-wrap gap-2">
                                    ${permissionBadges}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                        <button type="button" class="btn btn-primary" onclick="editUserProfile()">
                            <i class="bi bi-pencil me-1"></i>编辑资料
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 移除已存在的模态框
    const existingModal = document.getElementById('userProfileModal');
    if (existingModal) {
        existingModal.remove();
    }

    // 添加新模态框
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('userProfileModal'));
    modal.show();
}

/**
 * 获取角色显示信息
 */
function getRoleDisplay(roleCode) {
    const roleMap = {
        'SUPER_ADMIN': { name: '超级管理员', class: 'bg-danger' },
        'ADMIN': { name: '管理员', class: 'bg-warning' },
        'MANAGER': { name: '经理', class: 'bg-info' },
        'USER': { name: '普通用户', class: 'bg-secondary' },
        'GUEST': { name: '访客', class: 'bg-light text-dark' }
    };

    return roleMap[roleCode] || { name: roleCode || '未知角色', class: 'bg-secondary' };
}

/**
 * 获取权限标签HTML
 */
function getPermissionBadges(permissions) {
    if (!permissions || permissions.length === 0) {
        return '<span class="text-muted">暂无权限</span>';
    }

    // 权限名称映射
    const permissionMap = {
        'DASHBOARD_VIEW': 'Dashboard查看',
        'AGENT_VIEW': 'Agent管理',
        'AGENT_CREATE': 'Agent创建',
        'AGENT_EDIT': 'Agent编辑',
        'AGENT_DELETE': 'Agent删除',
        'PLAYGROUND_VIEW': '智能调试台',
        'TEMPLATE_VIEW': '模板管理',
        'RECOGNITION_VIEW': '文档识别',
        'AGENT_APPROVAL_VIEW': 'Agent审批',
        'USER_VIEW': '用户查看',
        'USER_CREATE': '用户创建',
        'USER_EDIT': '用户编辑',
        'USER_DELETE': '用户删除',
        'ROLE_VIEW': '角色查看',
        'ROLE_CREATE': '角色创建',
        'ROLE_EDIT': '角色编辑',
        'ROLE_DELETE': '角色删除',
        'PERMISSION_VIEW': '权限查看',
        'LOG_VIEW': '日志查看',
        'business:smart-form': '智能表单'
    };

    return permissions.map(permission => {
        const displayName = permissionMap[permission] || permission;
        return `<span class="badge bg-primary">${displayName}</span>`;
    }).join('');
}

/**
 * 渲染消息列表
 */
function renderMessageList(messages) {
    const messageList = document.getElementById('messageList');

    if (!messages || messages.length === 0) {
        messageList.innerHTML = `
            <div class="empty-messages">
                <i class="bi bi-bell-slash"></i>
                <p>暂无消息</p>
            </div>
        `;
        return;
    }

    const messagesHtml = messages.map(message => {
        const isUnread = message.isRead === 0;
        const messageTypeClass = getMessageTypeClass(message.messageType);
        const timeAgo = formatTimeAgo(message.createdTime);

        return `
            <div class="message-item ${isUnread ? 'unread' : ''}" onclick="handleMessageClick(${message.id}, ${isUnread})">
                <div class="message-title">${message.title}</div>
                <div class="message-content">${message.content}</div>
                <div class="message-meta">
                    <span class="message-type-badge badge ${messageTypeClass}">${message.messageTypeName}</span>
                    <span class="message-time">${timeAgo}</span>
                </div>
            </div>
        `;
    }).join('');

    messageList.innerHTML = messagesHtml;
}

/**
 * 获取消息类型样式类
 */
function getMessageTypeClass(messageType) {
    switch (messageType) {
        case 1: return 'bg-info';      // 系统通知
        case 2: return 'bg-warning';   // 审批通知
        case 3: return 'bg-secondary'; // 其他
        default: return 'bg-secondary';
    }
}

/**
 * 格式化时间为相对时间
 */
function formatTimeAgo(timeString) {
    const now = new Date();
    const time = new Date(timeString);
    const diffMs = now - time;
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMs / 3600000);
    const diffDays = Math.floor(diffMs / 86400000);

    if (diffMins < 1) return '刚刚';
    if (diffMins < 60) return `${diffMins}分钟前`;
    if (diffHours < 24) return `${diffHours}小时前`;
    if (diffDays < 7) return `${diffDays}天前`;

    return time.toLocaleDateString('zh-CN');
}

/**
 * 处理消息点击
 */
async function handleMessageClick(messageId, isUnread) {
    if (isUnread) {
        // 标记为已读
        await markMessageAsRead([messageId]);
        // 刷新未读数量
        await loadUnreadMessageCount();
        // 重新加载消息列表
        await loadMessageList();
    }
}

/**
 * 标记消息为已读
 */
async function markMessageAsRead(messageIds) {
    try {
        const token = localStorage.getItem('authToken') || sessionStorage.getItem('token');
        if (!token) return;

        const response = await fetch('/api/v1/messages/mark-read', {
            method: 'POST',
            headers: {
                'Authorization': 'Bearer ' + token,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(messageIds)
        });

        if (response.ok) {
            const result = await response.json();
            return result.code === 200;
        }
    } catch (error) {
        console.error('标记消息已读失败:', error);
    }
    return false;
}

/**
 * 标记所有消息为已读
 */
async function markAllMessagesAsRead() {
    try {
        const token = localStorage.getItem('authToken') || sessionStorage.getItem('token');
        if (!token) return;

        const response = await fetch('/api/v1/messages/mark-all-read', {
            method: 'POST',
            headers: {
                'Authorization': 'Bearer ' + token,
                'Content-Type': 'application/json'
            }
        });

        if (response.ok) {
            const result = await response.json();
            if (result.code === 200) {
                // 刷新未读数量
                await loadUnreadMessageCount();
                // 重新加载消息列表
                await loadMessageList();
                showToast('所有消息已标记为已读', 'success');
            }
        }
    } catch (error) {
        console.error('标记所有消息已读失败:', error);
        showToast('操作失败，请重试', 'error');
    }
}

/**
 * 编辑用户资料
 */
function editUserProfile() {
    // 获取用户信息
    const userInfo = currentUserInfo || JSON.parse(localStorage.getItem('currentUserInfo') || '{}');

    // 关闭当前的个人资料模态框
    const profileModal = bootstrap.Modal.getInstance(document.getElementById('userProfileModal'));
    if (profileModal) {
        profileModal.hide();
    }

    // 创建编辑资料模态框
    const editModalHtml = `
        <div class="modal fade" id="editProfileModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="bi bi-pencil me-2"></i>编辑个人资料
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <!-- 导航标签 -->
                        <ul class="nav nav-tabs" id="editProfileTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="basic-info-tab" data-bs-toggle="tab"
                                        data-bs-target="#basic-info" type="button" role="tab">
                                    <i class="bi bi-person me-1"></i>基本信息
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="change-password-tab" data-bs-toggle="tab"
                                        data-bs-target="#change-password" type="button" role="tab">
                                    <i class="bi bi-lock me-1"></i>修改密码
                                </button>
                            </li>
                        </ul>

                        <!-- 标签内容 -->
                        <div class="tab-content mt-3" id="editProfileTabContent">
                            <!-- 基本信息标签 -->
                            <div class="tab-pane fade show active" id="basic-info" role="tabpanel">
                                <form id="updateProfileForm">
                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <label class="form-label fw-bold">用户名</label>
                                            <input type="text" class="form-control" value="${userInfo.username || ''}" disabled>
                                            <div class="form-text">用户名不可修改</div>
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label fw-bold">真实姓名</label>
                                            <input type="text" class="form-control" name="realName" id="editRealName"
                                                   value="${userInfo.realName || ''}" placeholder="请输入真实姓名">
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label fw-bold">邮箱</label>
                                            <input type="email" class="form-control" name="email" id="editEmail"
                                                   value="${userInfo.email || ''}" placeholder="请输入邮箱地址">
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label fw-bold">手机号</label>
                                            <input type="tel" class="form-control" name="phone" id="editPhone"
                                                   value="${userInfo.phone || ''}" placeholder="请输入手机号">
                                        </div>
                                        <div class="col-12">
                                            <label class="form-label fw-bold">部门</label>
                                            <input type="text" class="form-control" name="department" id="editDepartment"
                                                   value="${userInfo.department || ''}" placeholder="请输入部门">
                                        </div>
                                    </div>
                                </form>
                            </div>

                            <!-- 修改密码标签 -->
                            <div class="tab-pane fade" id="change-password" role="tabpanel">
                                <form id="changePasswordForm">
                                    <div class="row g-3">
                                        <div class="col-12">
                                            <label class="form-label fw-bold">原密码</label>
                                            <input type="password" class="form-control" name="oldPassword" id="oldPassword"
                                                   placeholder="请输入原密码" required>
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label fw-bold">新密码</label>
                                            <input type="password" class="form-control" name="newPassword" id="newPassword"
                                                   placeholder="请输入新密码" required minlength="6" maxlength="20">
                                            <div class="form-text">密码长度6-20个字符</div>
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label fw-bold">确认密码</label>
                                            <input type="password" class="form-control" name="confirmPassword" id="confirmPassword"
                                                   placeholder="请再次输入新密码" required>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" onclick="saveProfile()">
                            <i class="bi bi-check me-1"></i>保存
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 移除已存在的编辑模态框
    const existingEditModal = document.getElementById('editProfileModal');
    if (existingEditModal) {
        existingEditModal.remove();
    }

    // 添加新的编辑模态框
    document.body.insertAdjacentHTML('beforeend', editModalHtml);

    // 显示编辑模态框
    const editModal = new bootstrap.Modal(document.getElementById('editProfileModal'));
    editModal.show();
}

/**
 * 保存个人资料
 */
async function saveProfile() {
    // 获取当前激活的标签
    const activeTab = document.querySelector('#editProfileTabs .nav-link.active');
    const activeTabId = activeTab.getAttribute('data-bs-target');

    if (activeTabId === '#basic-info') {
        await updateProfile();
    } else if (activeTabId === '#change-password') {
        await changePassword();
    }
}

/**
 * 更新个人资料
 */
async function updateProfile() {
    try {
        const form = document.getElementById('updateProfileForm');
        const formData = new FormData(form);

        const requestData = {
            realName: formData.get('realName'),
            email: formData.get('email'),
            phone: formData.get('phone'),
            department: formData.get('department')
        };

        const response = await apiCall('/api/v1/users/update-profile', 'POST', requestData);

        if (response.code === 200) {
            showToast('个人资料更新成功', 'success');

            // 更新本地存储的用户信息
            const currentUserInfo = JSON.parse(localStorage.getItem('currentUserInfo') || '{}');
            Object.assign(currentUserInfo, requestData);
            localStorage.setItem('currentUserInfo', JSON.stringify(currentUserInfo));

            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('editProfileModal'));
            modal.hide();

            // 刷新页面用户信息显示
            updateUserInfoDisplay();
        } else {
            showToast('更新失败: ' + response.message, 'error');
        }
    } catch (error) {
        console.error('更新个人资料失败:', error);
        showToast('更新失败，请重试', 'error');
    }
}

/**
 * 修改密码
 */
async function changePassword() {
    try {
        const form = document.getElementById('changePasswordForm');
        const formData = new FormData(form);

        const oldPassword = formData.get('oldPassword');
        const newPassword = formData.get('newPassword');
        const confirmPassword = formData.get('confirmPassword');

        // 验证表单
        if (!oldPassword || !newPassword || !confirmPassword) {
            showToast('请填写完整的密码信息', 'warning');
            return;
        }

        if (newPassword !== confirmPassword) {
            showToast('新密码和确认密码不一致', 'warning');
            return;
        }

        if (newPassword.length < 6 || newPassword.length > 20) {
            showToast('新密码长度必须在6-20个字符之间', 'warning');
            return;
        }

        const requestData = {
            oldPassword: oldPassword,
            newPassword: newPassword,
            confirmPassword: confirmPassword
        };

        const response = await apiCall('/api/v1/users/change-password', 'POST', requestData);

        if (response.code === 200) {
            showToast('密码修改成功，请重新登录', 'success');

            // 清除本地存储
            localStorage.removeItem('authToken');
            localStorage.removeItem('currentUserInfo');
            sessionStorage.removeItem('token');

            // 延迟跳转到登录页
            setTimeout(() => {
                window.location.href = '/login.html';
            }, 2000);
        } else {
            showToast('修改失败: ' + response.message, 'error');
        }
    } catch (error) {
        console.error('修改密码失败:', error);
        showToast('修改失败，请重试', 'error');
    }
}

/**
 * 更新页面用户信息显示
 */
function updateUserInfoDisplay() {
    const userInfo = JSON.parse(localStorage.getItem('currentUserInfo') || '{}');

    // 更新顶部用户名显示
    const userNameElement = document.querySelector('.user-name');
    if (userNameElement && userInfo.realName) {
        userNameElement.textContent = userInfo.realName;
    }

    // 更新其他可能的用户信息显示位置
    const userEmailElement = document.querySelector('.user-email');
    if (userEmailElement && userInfo.email) {
        userEmailElement.textContent = userInfo.email;
    }
}

/**
 * 清理之前页面的资源
 */
function cleanupPreviousPage() {
    // 清理可能存在的定时器
    if (window.pageTimers) {
        window.pageTimers.forEach(timer => {
            clearTimeout(timer);
            clearInterval(timer);
        });
        window.pageTimers = [];
    }

    // 清理可能存在的事件监听器
    if (window.pageEventListeners) {
        window.pageEventListeners.forEach(({ element, event, handler }) => {
            if (element && element.removeEventListener) {
                element.removeEventListener(event, handler);
            }
        });
        window.pageEventListeners = [];
    }

    // 清理模态框
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        const modalInstance = bootstrap.Modal.getInstance(modal);
        if (modalInstance) {
            modalInstance.hide();
        }
    });

    // 清理工具提示
    const tooltips = document.querySelectorAll('[data-bs-toggle="tooltip"]');
    tooltips.forEach(tooltip => {
        const tooltipInstance = bootstrap.Tooltip.getInstance(tooltip);
        if (tooltipInstance) {
            tooltipInstance.dispose();
        }
    });

    console.log('页面资源清理完成');
}
