// 操作日志管理页面JavaScript

// 全局变量
let currentPageNum = 1;
let currentPageSize = 20;
let totalRecords = 0;
let totalPages = 0;

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('操作日志页面初始化');
    initPage();
    loadLogs();
});

// 供admin.js调用的初始化函数
function initOperationLogsPage() {
    console.log('通过admin.js初始化操作日志页面');
    initPage();
    loadLogs();
}

// 初始化页面
function initPage() {
    // 设置默认时间范围（最近7天）
    const endTime = new Date();
    const startTime = new Date();
    startTime.setDate(startTime.getDate() - 7);
    
    document.getElementById('startTime').value = formatDateTimeLocal(startTime);
    document.getElementById('endTime').value = formatDateTimeLocal(endTime);
    
    // 绑定回车键搜索
    const searchInputs = ['userId', 'username'];
    searchInputs.forEach(id => {
        const input = document.getElementById(id);
        if (input) {
            input.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    searchLogs();
                }
            });
        }
    });
}

// 格式化日期时间为本地格式
function formatDateTimeLocal(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    return `${year}-${month}-${day}T${hours}:${minutes}`;
}

// 加载日志列表
async function loadLogs(page = 1) {
    try {
        showTableLoading();
        currentPageNum = page;

        // 构建查询参数
        const params = new URLSearchParams();
        params.append('page', page - 1); // 后端从0开始
        params.append('size', currentPageSize);

        // 添加查询条件
        const userId = document.getElementById('userId').value.trim();
        const username = document.getElementById('username').value.trim();
        const module = document.getElementById('module').value;
        const operationType = document.getElementById('operationType').value;
        const status = document.getElementById('status').value;
        const startTime = document.getElementById('startTime').value;
        const endTime = document.getElementById('endTime').value;

        if (userId) params.append('userId', userId);
        if (username) params.append('username', username);
        if (module) params.append('module', module);
        if (operationType) params.append('operationType', operationType);
        if (status) params.append('status', status);
        if (startTime) params.append('startTime', startTime.replace('T', ' ') + ':00');
        if (endTime) params.append('endTime', endTime.replace('T', ' ') + ':00');

        const result = await apiCall(`/api/v1/logs?${params.toString()}`);

        if (result.code === 200) {
            const pageData = result.data;
            totalRecords = pageData.totalElements;
            totalPages = pageData.totalPages;

            renderLogsTable(pageData.content);
            updatePagination();
        } else {
            showTableError('加载日志失败：' + result.message);
        }
    } catch (error) {
        console.error('加载日志失败:', error);
        showTableError('加载日志失败，请检查网络连接');
    }
}

// 渲染日志表格
function renderLogsTable(logs) {
    const tbody = document.getElementById('logsTableBody');
    
    if (!logs || logs.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="10" class="text-center text-muted py-4">
                    <i class="bi bi-inbox display-4 mb-3"></i>
                    <div>暂无日志数据</div>
                </td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = logs.map(log => `
        <tr>
            <td>
                <small class="text-muted">${log.id.substring(0, 8)}...</small>
            </td>
            <td>
                <div class="d-flex align-items-center">
                    <i class="bi bi-person-circle me-2 text-muted"></i>
                    <div>
                        <div class="fw-medium">${log.username || '-'}</div>
                        ${log.realName ? `<small class="text-muted">${log.realName}</small>` : ''}
                    </div>
                </div>
            </td>
            <td>
                <span class="badge bg-light text-dark">${log.module || '-'}</span>
            </td>
            <td>
                <span class="badge ${getOperationTypeBadgeClass(log.operationType)}">${getOperationTypeText(log.operationType)}</span>
            </td>
            <td>
                <div class="text-truncate" style="max-width: 200px;" title="${log.operationDesc || '-'}">
                    ${log.operationDesc || '-'}
                </div>
            </td>
            <td>
                <span class="badge ${getMethodBadgeClass(log.requestMethod)}">${log.requestMethod || '-'}</span>
            </td>
            <td>
                <span class="badge ${getStatusBadgeClass(log.status)}">${getStatusText(log.status)}</span>
            </td>
            <td>
                <span class="text-${log.executionTime > 1000 ? 'warning' : 'muted'}">
                    ${log.executionTime || 0}
                </span>
            </td>
            <td>
                <small class="text-muted">${formatDateTime(log.operationTime)}</small>
            </td>
            <td>
                <button class="btn btn-outline-info btn-sm" onclick="showLogDetail('${log.id}')" title="查看详情">
                    <i class="bi bi-eye"></i>
                </button>
            </td>
        </tr>
    `).join('');
}

// 获取操作类型徽章样式
function getOperationTypeBadgeClass(type) {
    const classMap = {
        'CREATE': 'bg-success',
        'UPDATE': 'bg-warning',
        'DELETE': 'bg-danger',
        'QUERY': 'bg-info',
        'LOGIN': 'bg-primary',
        'LOGOUT': 'bg-secondary',
        'UPLOAD': 'bg-info',
        'DOWNLOAD': 'bg-info',
        'EXPORT': 'bg-warning',
        'IMPORT': 'bg-warning',
        'RECOGNITION': 'bg-primary',
        'AGENT_TEST': 'bg-info',
        'AGENT_PUBLISH': 'bg-success'
    };
    return classMap[type] || 'bg-secondary';
}

// 获取操作类型文本
function getOperationTypeText(type) {
    const textMap = {
        'CREATE': '创建',
        'UPDATE': '更新',
        'DELETE': '删除',
        'QUERY': '查询',
        'LOGIN': '登录',
        'LOGOUT': '登出',
        'UPLOAD': '上传',
        'DOWNLOAD': '下载',
        'EXPORT': '导出',
        'IMPORT': '导入',
        'RECOGNITION': '识别',
        'AGENT_TEST': 'Agent测试',
        'AGENT_PUBLISH': 'Agent发布'
    };
    return textMap[type] || type;
}

// 获取请求方法徽章样式
function getMethodBadgeClass(method) {
    const classMap = {
        'GET': 'bg-info',
        'POST': 'bg-success',
        'PUT': 'bg-warning',
        'DELETE': 'bg-danger',
        'PATCH': 'bg-warning'
    };
    return classMap[method] || 'bg-secondary';
}

// 获取状态徽章样式
function getStatusBadgeClass(status) {
    const classMap = {
        'SUCCESS': 'bg-success',
        'FAILED': 'bg-warning',
        'ERROR': 'bg-danger'
    };
    return classMap[status] || 'bg-secondary';
}

// 获取状态文本
function getStatusText(status) {
    const textMap = {
        'SUCCESS': '成功',
        'FAILED': '失败',
        'ERROR': '错误'
    };
    return textMap[status] || status;
}

// 格式化日期时间
function formatDateTime(dateTimeStr) {
    if (!dateTimeStr) return '-';
    const date = new Date(dateTimeStr);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
}

// 更新分页信息
function updatePagination() {
    document.getElementById('totalCount').textContent = totalRecords;
    document.getElementById('currentPage').textContent = currentPageNum;
    document.getElementById('totalPages').textContent = totalPages;
    
    renderPagination();
}

// 渲染分页控件
function renderPagination() {
    const pagination = document.getElementById('pagination');
    let html = '';
    
    // 上一页
    html += `
        <li class="page-item ${currentPageNum <= 1 ? 'disabled' : ''}">
            <a class="page-link" href="javascript:void(0);" onclick="loadLogs(${currentPageNum - 1})">
                <i class="bi bi-chevron-left"></i>
            </a>
        </li>
    `;
    
    // 页码
    const startPage = Math.max(1, currentPageNum - 2);
    const endPage = Math.min(totalPages, currentPageNum + 2);
    
    if (startPage > 1) {
        html += `<li class="page-item"><a class="page-link" href="javascript:void(0);" onclick="loadLogs(1)">1</a></li>`;
        if (startPage > 2) {
            html += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
        }
    }
    
    for (let i = startPage; i <= endPage; i++) {
        html += `
            <li class="page-item ${i === currentPageNum ? 'active' : ''}">
                <a class="page-link" href="javascript:void(0);" onclick="loadLogs(${i})">${i}</a>
            </li>
        `;
    }
    
    if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
            html += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
        }
        html += `<li class="page-item"><a class="page-link" href="javascript:void(0);" onclick="loadLogs(${totalPages})">${totalPages}</a></li>`;
    }
    
    // 下一页
    html += `
        <li class="page-item ${currentPageNum >= totalPages ? 'disabled' : ''}">
            <a class="page-link" href="javascript:void(0);" onclick="loadLogs(${currentPageNum + 1})">
                <i class="bi bi-chevron-right"></i>
            </a>
        </li>
    `;
    
    pagination.innerHTML = html;
}

// 搜索日志
function searchLogs() {
    currentPageNum = 1;
    loadLogs(1);
}

// 重置搜索条件
function resetSearch() {
    document.getElementById('searchForm').reset();
    
    // 重新设置默认时间范围
    const endTime = new Date();
    const startTime = new Date();
    startTime.setDate(startTime.getDate() - 7);
    
    document.getElementById('startTime').value = formatDateTimeLocal(startTime);
    document.getElementById('endTime').value = formatDateTimeLocal(endTime);
    
    // 重新加载数据
    searchLogs();
}

// 改变每页显示条数
function changePageSize() {
    currentPageSize = parseInt(document.getElementById('pageSize').value);
    currentPageNum = 1;
    loadLogs(1);
}

// 显示表格加载状态
function showTableLoading() {
    const tbody = document.getElementById('logsTableBody');
    tbody.innerHTML = `
        <tr>
            <td colspan="10" class="text-center py-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <div class="mt-2 text-muted">正在加载日志数据...</div>
            </td>
        </tr>
    `;
}

// 显示表格错误信息
function showTableError(message) {
    const tbody = document.getElementById('logsTableBody');
    tbody.innerHTML = `
        <tr>
            <td colspan="10" class="text-center text-danger py-4">
                <i class="bi bi-exclamation-triangle display-4 mb-3"></i>
                <div>${message}</div>
            </td>
        </tr>
    `;
}

// 显示日志详情
async function showLogDetail(logId) {
    try {
        const result = await apiCall(`/api/v1/logs/${logId}`);

        if (result.code === 200) {
            const log = result.data;
            renderLogDetail(log);

            // 显示模态框
            const modal = new bootstrap.Modal(document.getElementById('logDetailModal'));
            modal.show();
        } else {
            if (typeof showToast === 'function') {
                showToast('获取日志详情失败：' + result.message, 'error');
            } else {
                alert('获取日志详情失败：' + result.message);
            }
        }
    } catch (error) {
        console.error('获取日志详情失败:', error);
        if (typeof showToast === 'function') {
            showToast('获取日志详情失败，请检查网络连接', 'error');
        } else {
            alert('获取日志详情失败，请检查网络连接');
        }
    }
}

// 渲染日志详情
function renderLogDetail(log) {
    const content = document.getElementById('logDetailContent');

    content.innerHTML = `
        <div class="row">
            <!-- 基本信息 -->
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-header">
                        <h6 class="card-title mb-0">
                            <i class="bi bi-info-circle me-2"></i>基本信息
                        </h6>
                    </div>
                    <div class="card-body">
                        <table class="table table-sm">
                            <tr>
                                <td class="text-muted" width="30%">日志ID:</td>
                                <td><code>${log.id}</code></td>
                            </tr>
                            <tr>
                                <td class="text-muted">用户ID:</td>
                                <td>${log.userId || '-'}</td>
                            </tr>
                            <tr>
                                <td class="text-muted">用户名:</td>
                                <td>${log.username || '-'}</td>
                            </tr>
                            <tr>
                                <td class="text-muted">真实姓名:</td>
                                <td>${log.realName || '-'}</td>
                            </tr>
                            <tr>
                                <td class="text-muted">模块:</td>
                                <td><span class="badge bg-light text-dark">${log.module || '-'}</span></td>
                            </tr>
                            <tr>
                                <td class="text-muted">操作类型:</td>
                                <td><span class="badge ${getOperationTypeBadgeClass(log.operationType)}">${getOperationTypeText(log.operationType)}</span></td>
                            </tr>
                            <tr>
                                <td class="text-muted">操作描述:</td>
                                <td>${log.operationDesc || '-'}</td>
                            </tr>
                            <tr>
                                <td class="text-muted">状态:</td>
                                <td><span class="badge ${getStatusBadgeClass(log.status)}">${getStatusText(log.status)}</span></td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 请求信息 -->
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-header">
                        <h6 class="card-title mb-0">
                            <i class="bi bi-arrow-up-circle me-2"></i>请求信息
                        </h6>
                    </div>
                    <div class="card-body">
                        <table class="table table-sm">
                            <tr>
                                <td class="text-muted" width="30%">请求方法:</td>
                                <td><span class="badge ${getMethodBadgeClass(log.requestMethod)}">${log.requestMethod || '-'}</span></td>
                            </tr>
                            <tr>
                                <td class="text-muted">请求URL:</td>
                                <td><code class="text-break">${log.requestUrl || '-'}</code></td>
                            </tr>
                            <tr>
                                <td class="text-muted">客户端IP:</td>
                                <td>${log.clientIp || '-'}</td>
                            </tr>
                            <tr>
                                <td class="text-muted">用户代理:</td>
                                <td class="text-break small">${log.userAgent || '-'}</td>
                            </tr>
                            <tr>
                                <td class="text-muted">执行时间:</td>
                                <td>
                                    <span class="text-${log.executionTime > 1000 ? 'warning' : 'muted'}">
                                        ${log.executionTime || 0} ms
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td class="text-muted">操作时间:</td>
                                <td>${formatDateTime(log.operationTime)}</td>
                            </tr>
                            <tr>
                                <td class="text-muted">创建时间:</td>
                                <td>${formatDateTime(log.createdTime)}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- 响应信息 -->
        <div class="row mt-3">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="card-title mb-0">
                            <i class="bi bi-arrow-down-circle me-2"></i>响应信息
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-sm">
                                    <tr>
                                        <td class="text-muted" width="30%">响应状态码:</td>
                                        <td>
                                            <span class="badge ${log.responseCode >= 200 && log.responseCode < 300 ? 'bg-success' : 'bg-danger'}">
                                                ${log.responseCode || '-'}
                                            </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="text-muted">响应消息:</td>
                                        <td>${log.responseMessage || '-'}</td>
                                    </tr>
                                    <tr>
                                        <td class="text-muted">错误信息:</td>
                                        <td class="text-danger">${log.errorMessage || '-'}</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 详细数据 -->
        ${renderDetailData(log)}
    `;
}

// 渲染详细数据
function renderDetailData(log) {
    let html = '';

    // 请求参数
    if (log.requestParams && Object.keys(log.requestParams).length > 0) {
        html += `
            <div class="row mt-3">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="bi bi-code-square me-2"></i>请求参数
                            </h6>
                        </div>
                        <div class="card-body">
                            <pre class="bg-light p-3 rounded"><code>${JSON.stringify(log.requestParams, null, 2)}</code></pre>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // 请求体
    if (log.requestBody && Object.keys(log.requestBody).length > 0) {
        html += `
            <div class="row mt-3">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="bi bi-file-text me-2"></i>请求体数据
                            </h6>
                        </div>
                        <div class="card-body">
                            <pre class="bg-light p-3 rounded"><code>${JSON.stringify(log.requestBody, null, 2)}</code></pre>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // 响应结果
    if (log.responseResult && Object.keys(log.responseResult).length > 0) {
        html += `
            <div class="row mt-3">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="bi bi-check-circle me-2"></i>响应结果
                            </h6>
                        </div>
                        <div class="card-body">
                            <pre class="bg-light p-3 rounded"><code>${JSON.stringify(log.responseResult, null, 2)}</code></pre>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // 业务数据
    if (log.businessData && Object.keys(log.businessData).length > 0) {
        html += `
            <div class="row mt-3">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="bi bi-briefcase me-2"></i>业务数据
                            </h6>
                        </div>
                        <div class="card-body">
                            <pre class="bg-light p-3 rounded"><code>${JSON.stringify(log.businessData, null, 2)}</code></pre>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    return html;
}

// 加载最近日志
async function loadRecentLogs() {
    try {
        const result = await apiCall('/api/v1/logs/recent?limit=20');

        if (result.code === 200) {
            // 清空查询条件
            document.getElementById('searchForm').reset();

            // 渲染数据
            renderLogsTable(result.data);

            // 更新分页信息
            totalRecords = result.data.length;
            totalPages = 1;
            currentPageNum = 1;
            updatePagination();

            showOperationSuccess('已加载最近20条日志');
        } else {
            showTableError('加载最近日志失败：' + result.message);
        }
    } catch (error) {
        console.error('加载最近日志失败:', error);
        showTableError('加载最近日志失败，请检查网络连接');
    }
}

// 加载慢操作日志
async function loadSlowLogs() {
    try {
        const result = await apiCall('/api/v1/logs/slow?page=0&size=20&thresholdMs=1000');

        if (result.code === 200) {
            const pageData = result.data;

            // 清空查询条件
            document.getElementById('searchForm').reset();

            // 渲染数据
            renderLogsTable(pageData.content);

            // 更新分页信息
            totalRecords = pageData.totalElements;
            totalPages = pageData.totalPages;
            currentPageNum = 1;
            updatePagination();

            showOperationSuccess(`已加载慢操作日志，共${totalRecords}条`);
        } else {
            showTableError('加载慢操作日志失败：' + result.message);
        }
    } catch (error) {
        console.error('加载慢操作日志失败:', error);
        showTableError('加载慢操作日志失败，请检查网络连接');
    }
}

// 导出日志
async function exportLogs() {
    try {
        // 构建查询参数
        const params = new URLSearchParams();

        const userId = document.getElementById('userId').value.trim();
        const username = document.getElementById('username').value.trim();
        const module = document.getElementById('module').value;
        const operationType = document.getElementById('operationType').value;
        const status = document.getElementById('status').value;
        const startTime = document.getElementById('startTime').value;
        const endTime = document.getElementById('endTime').value;

        if (userId) params.append('userId', userId);
        if (username) params.append('username', username);
        if (module) params.append('module', module);
        if (operationType) params.append('operationType', operationType);
        if (status) params.append('status', status);
        if (startTime) params.append('startTime', startTime.replace('T', ' ') + ':00');
        if (endTime) params.append('endTime', endTime.replace('T', ' ') + ':00');

        // 使用apiCall下载文件
        const url = `/api/v1/logs/export?${params.toString()}`;

        // 获取认证token
        const token = localStorage.getItem('token');
        const headers = {
            'Content-Type': 'application/json'
        };
        if (token) {
            headers['Authorization'] = `Bearer ${token}`;
        }

        // 发送请求获取文件内容
        const response = await fetch(url, {
            method: 'GET',
            headers: headers
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        // 获取文件内容并创建下载
        const csvContent = await response.text();
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const downloadUrl = window.URL.createObjectURL(blob);

        const link = document.createElement('a');
        link.href = downloadUrl;
        link.download = `operation_logs_${new Date().getTime()}.csv`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // 清理URL对象
        window.URL.revokeObjectURL(downloadUrl);

        showOperationSuccess('日志导出成功！');
    } catch (error) {
        console.error('导出日志失败:', error);
        showTableError('导出日志失败，请检查网络连接');
    }
}

// 显示操作成功信息
function showOperationSuccess(message) {
    console.log('Success:', message);
    // 使用common.js中的showToast函数
    if (typeof showToast === 'function') {
        showToast(message, 'success');
    } else {
        console.log('showToast function not available:', message);
    }
}

// 页面嵌套在admin.html中运行，不需要侧边栏和登录相关功能
