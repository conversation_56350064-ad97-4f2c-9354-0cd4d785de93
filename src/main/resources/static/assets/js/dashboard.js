/**
 * 仪表板页面 JavaScript
 */

/**
 * 初始化仪表板页面
 */
function initDashboardPage() {
    loadDashboardData();
    initUsageChart();
}

/**
 * 加载仪表板数据
 */
async function loadDashboardData() {
    try {
        // 显示加载动画
        showStatsLoading();

        // 加载统计数据
        const agentsResponse = await apiCall('/api/v1/agents?page=1&size=1');
        if (agentsResponse.code === 200) {
            updateStatsValue('totalAgents', agentsResponse.data.pagination.total);
            updateStatsValue('publishedAgents',
                agentsResponse.data.list.filter(agent => agent.status === 3).length);
        }
        
        // 模拟其他统计数据
        updateStatsValue('todayCalls', Math.floor(Math.random() * 5000) + 1000);
        updateStatsValue('successRate', (95 + Math.random() * 5).toFixed(1) + '%');
        
        // 加载系统状态
        const healthResponse = await apiCall('/api/v1/system/health');
        if (healthResponse.code === 200) {
            const statusHtml = Object.entries(healthResponse.data.components)
                .map(([key, value]) => `
                    <div class="d-flex justify-content-between mb-2">
                        <span>${key}:</span>
                        <span class="badge bg-${value === 'UP' ? 'success' : 'danger'}">${value}</span>
                    </div>
                `).join('');
            document.getElementById('systemStatus').innerHTML = statusHtml;
        }
        
        // 加载最近的Agent
        if (agentsResponse.code === 200 && agentsResponse.data.list.length > 0) {
            const recentHtml = agentsResponse.data.list.slice(0, 5).map(agent => `
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <div>
                        <strong>${agent.agentName}</strong>
                        <br><small class="text-muted">${agent.agentCode}</small>
                    </div>
                    <span class="badge bg-${getAgentStatusColor(agent.status)}">${getAgentStatusText(agent.status)}</span>
                </div>
            `).join('');
            document.getElementById('recentAgents').innerHTML = recentHtml;
        }
        
    } catch (error) {
        console.error('Failed to load dashboard:', error);
        showError('仪表板数据加载失败');
    }
}

/**
 * 显示统计数据加载状态
 */
function showStatsLoading() {
    const statsElements = ['totalAgents', 'publishedAgents', 'todayCalls', 'successRate'];
    statsElements.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.innerHTML = '<div class="spinner-border spinner-border-sm" role="status"></div>';
        }
    });
}

/**
 * 更新统计数值
 * @param {string} elementId 元素ID
 * @param {string|number} value 数值
 */
function updateStatsValue(elementId, value) {
    const element = document.getElementById(elementId);
    if (element) {
        // 数字动画效果
        if (typeof value === 'number') {
            animateNumber(element, 0, value, 1000);
        } else {
            element.textContent = value;
        }
    }
}

/**
 * 数字动画效果
 * @param {HTMLElement} element 目标元素
 * @param {number} start 起始值
 * @param {number} end 结束值
 * @param {number} duration 动画时长
 */
function animateNumber(element, start, end, duration) {
    const startTime = performance.now();
    
    function update(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);
        
        // 使用缓动函数
        const easeOutQuart = 1 - Math.pow(1 - progress, 4);
        const current = Math.floor(start + (end - start) * easeOutQuart);
        
        element.textContent = current.toLocaleString();
        
        if (progress < 1) {
            requestAnimationFrame(update);
        }
    }
    
    requestAnimationFrame(update);
}

/**
 * 初始化使用统计图表
 */
function initUsageChart() {
    const canvas = document.getElementById('usageChart');
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    
    // 模拟数据
    const data = {
        labels: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00', '24:00'],
        datasets: [{
            label: 'API调用量',
            data: [120, 80, 200, 350, 280, 180, 90],
            borderColor: '#2563eb',
            backgroundColor: 'rgba(37, 99, 235, 0.1)',
            tension: 0.4,
            fill: true
        }]
    };
    
    // 简单的图表绘制（如果需要更复杂的图表，建议使用Chart.js）
    drawSimpleChart(ctx, data, canvas.width, canvas.height);
}

/**
 * 绘制简单图表
 * @param {CanvasRenderingContext2D} ctx 画布上下文
 * @param {object} data 图表数据
 * @param {number} width 画布宽度
 * @param {number} height 画布高度
 */
function drawSimpleChart(ctx, data, width, height) {
    const padding = 40;
    const chartWidth = width - padding * 2;
    const chartHeight = height - padding * 2;
    
    // 清空画布
    ctx.clearRect(0, 0, width, height);
    
    // 设置样式
    ctx.strokeStyle = '#2563eb';
    ctx.fillStyle = 'rgba(37, 99, 235, 0.1)';
    ctx.lineWidth = 2;
    
    // 计算数据点位置
    const maxValue = Math.max(...data.datasets[0].data);
    const points = data.datasets[0].data.map((value, index) => ({
        x: padding + (index / (data.labels.length - 1)) * chartWidth,
        y: padding + chartHeight - (value / maxValue) * chartHeight
    }));
    
    // 绘制填充区域
    ctx.beginPath();
    ctx.moveTo(points[0].x, padding + chartHeight);
    points.forEach(point => ctx.lineTo(point.x, point.y));
    ctx.lineTo(points[points.length - 1].x, padding + chartHeight);
    ctx.closePath();
    ctx.fill();
    
    // 绘制线条
    ctx.beginPath();
    ctx.moveTo(points[0].x, points[0].y);
    points.forEach(point => ctx.lineTo(point.x, point.y));
    ctx.stroke();
    
    // 绘制数据点
    ctx.fillStyle = '#2563eb';
    points.forEach(point => {
        ctx.beginPath();
        ctx.arc(point.x, point.y, 4, 0, Math.PI * 2);
        ctx.fill();
    });
    
    // 绘制坐标轴标签
    ctx.fillStyle = '#64748b';
    ctx.font = '12px Inter';
    ctx.textAlign = 'center';
    
    data.labels.forEach((label, index) => {
        const x = padding + (index / (data.labels.length - 1)) * chartWidth;
        ctx.fillText(label, x, height - 10);
    });
}

/**
 * 刷新仪表板数据
 */
function refreshDashboard() {
    loadDashboardData();
    showSuccess('仪表板数据已刷新');
}

// 定时刷新数据（每5分钟）
setInterval(() => {
    if (getCurrentPage() === 'dashboard') {
        loadDashboardData();
    }
}, 5 * 60 * 1000);
