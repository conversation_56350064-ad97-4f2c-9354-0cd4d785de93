/**
 * Keen主题增强JavaScript
 * 提供现代化的交互效果和动画
 */

class KeenThemeEnhancer {
    constructor() {
        this.init();
    }

    init() {
        this.initScrollEffects();
        this.initHoverEffects();
        this.initLoadingStates();
        this.initTooltips();
        this.initAnimations();
        this.initSearchEnhancements();
        this.initCardInteractions();
    }

    // 滚动效果
    initScrollEffects() {
        // 滚动时添加阴影效果到顶部导航栏
        const navbar = document.querySelector('.top-navbar');
        if (navbar) {
            window.addEventListener('scroll', () => {
                if (window.scrollY > 10) {
                    navbar.style.boxShadow = 'var(--kt-box-shadow)';
                } else {
                    navbar.style.boxShadow = 'var(--kt-box-shadow-xs)';
                }
            });
        }

        // 滚动到顶部按钮
        this.createScrollToTopButton();
    }

    // 创建滚动到顶部按钮
    createScrollToTopButton() {
        const scrollBtn = document.createElement('button');
        scrollBtn.innerHTML = '<i class="bi bi-arrow-up"></i>';
        scrollBtn.className = 'btn btn-primary position-fixed';
        scrollBtn.style.cssText = `
            bottom: 30px;
            right: 30px;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: var(--kt-transition);
            box-shadow: var(--kt-box-shadow);
        `;

        document.body.appendChild(scrollBtn);

        // 显示/隐藏按钮
        window.addEventListener('scroll', () => {
            if (window.scrollY > 300) {
                scrollBtn.style.opacity = '1';
                scrollBtn.style.visibility = 'visible';
            } else {
                scrollBtn.style.opacity = '0';
                scrollBtn.style.visibility = 'hidden';
            }
        });

        // 点击滚动到顶部
        scrollBtn.addEventListener('click', () => {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    }

    // 悬停效果
    initHoverEffects() {
        // 为卡片添加悬停效果
        document.addEventListener('mouseenter', (e) => {
            if (e.target.closest('.card')) {
                const card = e.target.closest('.card');
                card.style.transform = 'translateY(-2px)';
            }
        }, true);

        document.addEventListener('mouseleave', (e) => {
            if (e.target.closest('.card')) {
                const card = e.target.closest('.card');
                card.style.transform = 'translateY(0)';
            }
        }, true);

        // 为按钮添加波纹效果
        this.initRippleEffect();
    }

    // 波纹效果
    initRippleEffect() {
        document.addEventListener('click', (e) => {
            if (e.target.closest('.btn')) {
                const button = e.target.closest('.btn');
                const ripple = document.createElement('span');
                const rect = button.getBoundingClientRect();
                const size = Math.max(rect.width, rect.height);
                const x = e.clientX - rect.left - size / 2;
                const y = e.clientY - rect.top - size / 2;

                ripple.style.cssText = `
                    position: absolute;
                    width: ${size}px;
                    height: ${size}px;
                    left: ${x}px;
                    top: ${y}px;
                    background: rgba(255, 255, 255, 0.3);
                    border-radius: 50%;
                    transform: scale(0);
                    animation: ripple 0.6s linear;
                    pointer-events: none;
                `;

                button.style.position = 'relative';
                button.style.overflow = 'hidden';
                button.appendChild(ripple);

                setTimeout(() => {
                    ripple.remove();
                }, 600);
            }
        });

        // 添加波纹动画CSS
        if (!document.querySelector('#ripple-style')) {
            const style = document.createElement('style');
            style.id = 'ripple-style';
            style.textContent = `
                @keyframes ripple {
                    to {
                        transform: scale(4);
                        opacity: 0;
                    }
                }
            `;
            document.head.appendChild(style);
        }
    }

    // 加载状态
    initLoadingStates() {
        // 为表单提交添加加载状态
        document.addEventListener('submit', (e) => {
            const form = e.target;
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                this.showButtonLoading(submitBtn);
            }
        });
    }

    // 显示按钮加载状态
    showButtonLoading(button, text = '处理中...') {
        const originalText = button.innerHTML;
        button.innerHTML = `
            <span class="spinner-border spinner-border-sm me-2" role="status"></span>
            ${text}
        `;
        button.disabled = true;

        // 存储原始文本以便恢复
        button.dataset.originalText = originalText;
    }

    // 隐藏按钮加载状态
    hideButtonLoading(button) {
        if (button.dataset.originalText) {
            button.innerHTML = button.dataset.originalText;
            button.disabled = false;
            delete button.dataset.originalText;
        }
    }

    // 工具提示增强
    initTooltips() {
        // 为所有带title属性的元素添加Bootstrap tooltip
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[title]'));
        tooltipTriggerList.forEach(function (tooltipTriggerEl) {
            if (!tooltipTriggerEl.hasAttribute('data-bs-toggle')) {
                tooltipTriggerEl.setAttribute('data-bs-toggle', 'tooltip');
                new bootstrap.Tooltip(tooltipTriggerEl);
            }
        });
    }

    // 动画效果
    initAnimations() {
        // 页面加载时的动画
        this.initPageLoadAnimations();

        // 滚动时的动画效果
        this.initScrollAnimations();
    }

    // 页面加载动画
    initPageLoadAnimations() {
        // 统计卡片动画
        const statsCards = document.querySelectorAll('.stats-card');
        statsCards.forEach((card, index) => {
            card.classList.add('animate-slide-in-bottom', `animate-delay-${index + 1}`);
        });

        // 信息卡片动画
        const infoCards = document.querySelectorAll('.card:not(.stats-card)');
        infoCards.forEach((card, index) => {
            card.classList.add('animate-slide-in-bottom', `animate-delay-${index + 2}`);
        });

        // 页面标题动画
        const pageTitle = document.querySelector('.page-title-wrapper');
        if (pageTitle) {
            pageTitle.classList.add('animate-slide-in-left');
        }

        // 页面操作按钮动画
        const pageActions = document.querySelector('.page-actions');
        if (pageActions) {
            pageActions.classList.add('animate-slide-in-right');
        }
    }

    // 滚动动画
    initScrollAnimations() {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('fade-in');
                }
            });
        }, {
            threshold: 0.1
        });

        // 观察所有卡片元素
        document.querySelectorAll('.card:not(.stats-card)').forEach(card => {
            observer.observe(card);
        });
    }

    // 搜索增强
    initSearchEnhancements() {
        const searchInput = document.querySelector('.search-box input');
        if (searchInput) {
            // 添加搜索建议功能
            this.initSearchSuggestions(searchInput);
            
            // 添加快捷键支持
            document.addEventListener('keydown', (e) => {
                if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                    e.preventDefault();
                    searchInput.focus();
                }
            });
        }
    }

    // 搜索建议
    initSearchSuggestions(input) {
        const suggestions = [
            'Agent管理', '用户管理', '角色管理', '权限管理',
            '仪表板', '智能调试台', '业务模板', '文档识别'
        ];

        let suggestionBox = null;

        input.addEventListener('input', (e) => {
            const value = e.target.value.toLowerCase();
            
            if (value.length > 0) {
                const matches = suggestions.filter(s => 
                    s.toLowerCase().includes(value)
                );

                if (matches.length > 0) {
                    this.showSearchSuggestions(input, matches);
                } else {
                    this.hideSearchSuggestions();
                }
            } else {
                this.hideSearchSuggestions();
            }
        });

        input.addEventListener('blur', () => {
            setTimeout(() => this.hideSearchSuggestions(), 200);
        });
    }

    // 显示搜索建议
    showSearchSuggestions(input, suggestions) {
        this.hideSearchSuggestions();

        const suggestionBox = document.createElement('div');
        suggestionBox.className = 'search-suggestions';
        suggestionBox.style.cssText = `
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: var(--kt-card-bg);
            border: 1px solid var(--kt-border-color);
            border-radius: var(--kt-border-radius);
            box-shadow: var(--kt-box-shadow);
            z-index: 1000;
            max-height: 200px;
            overflow-y: auto;
        `;

        suggestions.forEach(suggestion => {
            const item = document.createElement('div');
            item.className = 'search-suggestion-item';
            item.textContent = suggestion;
            item.style.cssText = `
                padding: var(--kt-space-2) var(--kt-space-4);
                cursor: pointer;
                transition: var(--kt-transition);
                font-size: var(--kt-font-size-sm);
            `;

            item.addEventListener('mouseenter', () => {
                item.style.backgroundColor = 'var(--kt-gray-100)';
            });

            item.addEventListener('mouseleave', () => {
                item.style.backgroundColor = 'transparent';
            });

            item.addEventListener('click', () => {
                input.value = suggestion;
                this.hideSearchSuggestions();
                // 触发搜索
                input.dispatchEvent(new Event('search'));
            });

            suggestionBox.appendChild(item);
        });

        input.parentElement.style.position = 'relative';
        input.parentElement.appendChild(suggestionBox);
    }

    // 隐藏搜索建议
    hideSearchSuggestions() {
        const existing = document.querySelector('.search-suggestions');
        if (existing) {
            existing.remove();
        }
    }

    // 卡片交互
    initCardInteractions() {
        // 为统计卡片添加点击效果
        document.querySelectorAll('.stats-card').forEach(card => {
            card.addEventListener('click', () => {
                card.style.transform = 'scale(0.98)';
                setTimeout(() => {
                    card.style.transform = 'scale(1)';
                }, 150);
            });
        });
    }

    // 公共方法：显示通知
    showNotification(message, type = 'info', duration = 3000) {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} position-fixed`;
        notification.style.cssText = `
            top: 20px;
            right: 20px;
            z-index: 1050;
            min-width: 300px;
            box-shadow: var(--kt-box-shadow);
            animation: slideInRight 0.3s ease;
        `;
        notification.textContent = message;

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.style.animation = 'slideOutRight 0.3s ease';
            setTimeout(() => {
                notification.remove();
            }, 300);
        }, duration);
    }
}

// 初始化Keen主题增强器
document.addEventListener('DOMContentLoaded', () => {
    window.keenTheme = new KeenThemeEnhancer();
});

// 添加滑动动画CSS
const animationStyle = document.createElement('style');
animationStyle.textContent = `
    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    @keyframes slideOutRight {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
`;
document.head.appendChild(animationStyle);
