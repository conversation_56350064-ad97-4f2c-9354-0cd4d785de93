/**
 * 智能体矩阵 管理后台公共 JavaScript 函数
 * 包含API调用、工具函数、通用组件等
 */

// 全局变量
let authToken = localStorage.getItem('authToken') || '';
let currentUser = null;

/**
 * 获取认证Token
 */
function getToken() {
    return authToken || localStorage.getItem('authToken') || sessionStorage.getItem('token') || '';
}

/**
 * 设置认证Token
 */
function setToken(token, remember = false) {
    authToken = token;
    if (remember) {
        localStorage.setItem('authToken', token);
    } else {
        sessionStorage.setItem('token', token);
    }
}

/**
 * 显示提示消息
 */
function showAlert(message, type = 'info', duration = 3000) {
    // 移除现有的提示
    const existingAlert = document.querySelector('.custom-alert');
    if (existingAlert) {
        existingAlert.remove();
    }

    // 创建新的提示
    const alert = document.createElement('div');
    alert.className = `alert alert-${type} custom-alert position-fixed`;
    alert.style.cssText = `
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        border: none;
        border-radius: 8px;
    `;
    alert.innerHTML = `
        <div class="d-flex align-items-center">
            <i class="bi bi-${getAlertIcon(type)} me-2"></i>
            <span>${message}</span>
            <button type="button" class="btn-close ms-auto" onclick="this.parentElement.parentElement.remove()"></button>
        </div>
    `;

    document.body.appendChild(alert);

    // 自动移除
    if (duration > 0) {
        setTimeout(() => {
            if (alert.parentNode) {
                alert.remove();
            }
        }, duration);
    }
}

/**
 * 获取提示图标
 */
function getAlertIcon(type) {
    switch (type) {
        case 'success': return 'check-circle';
        case 'danger': return 'exclamation-triangle';
        case 'warning': return 'exclamation-triangle';
        case 'info': return 'info-circle';
        default: return 'info-circle';
    }
}

/**
 * 检查认证状态
 */
function checkAuth() {
    if (!authToken) {
        console.log('No auth token found, redirecting to login');
        window.location.href = '/login.html';
        return false;
    }

    console.log('Auth token found:', authToken.substring(0, 10) + '...');

    // 简化认证检查 - 如果有token就认为已认证
    // 实际的token验证会在API调用时进行
    return true;
}

/**
 * API调用封装
 * @param {string} url API地址
 * @param {string} method HTTP方法
 * @param {object} data 请求数据
 * @returns {Promise<object>} API响应
 */
async function apiCall(url, method = 'GET', data = null) {
    const options = {
        method: method,
        headers: {
            'Authorization': 'Bearer ' + authToken,
            'Content-Type': 'application/json'
        }
    };
    
    if (data) {
        options.body = JSON.stringify(data);
    }
    
    try {
        const response = await fetch(url, options);
        
        // 处理认证失败
        if (response.status === 401) {
            localStorage.removeItem('authToken');
            window.location.href = '/login.html';
            return;
        }
        
        return await response.json();
    } catch (error) {
        console.error('API call failed:', error);
        throw error;
    }
}

/**
 * 文件上传API调用
 * @param {string} url API地址
 * @param {FormData} formData 文件数据
 * @returns {Promise<object>} API响应
 */
async function uploadFile(url, formData) {
    try {
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Authorization': 'Bearer ' + authToken
            },
            body: formData
        });

        if (response.status === 401) {
            localStorage.removeItem('authToken');
            window.location.href = '/login.html';
            return { code: 401, message: '认证失败，请重新登录' };
        }

        if (!response.ok) {
            return { code: response.status, message: `HTTP错误: ${response.status}` };
        }

        const result = await response.json();
        return result;
    } catch (error) {
        console.error('File upload failed:', error);
        return { code: 500, message: '网络错误: ' + error.message };
    }
}

/**
 * 获取通用状态颜色（已弃用，请使用具体模块的状态函数）
 * @param {number} status 状态值
 * @returns {string} Bootstrap颜色类
 * @deprecated 请使用 getAgentStatusColor 或 getUserStatusColor
 */
function getCommonStatusColor(status) {
    const colors = {
        1: 'secondary', // 草稿
        2: 'warning',   // 测试中
        3: 'success',   // 已发布
        4: 'danger'     // 已停用
    };
    return colors[status] || 'secondary';
}

/**
 * 获取通用状态文本（已弃用，请使用具体模块的状态函数）
 * @param {number} status 状态值
 * @returns {string} 状态文本
 * @deprecated 请使用 getAgentStatusText 或 getUserStatusText
 */
function getCommonStatusText(status) {
    const texts = {
        1: '草稿',
        2: '测试中',
        3: '已发布',
        4: '已停用'
    };
    return texts[status] || '未知';
}

/**
 * Agent状态颜色函数（全局可用）
 * @param {number} status 状态值
 * @returns {string} Bootstrap颜色类
 */
function getAgentStatusColor(status) {
    const colors = {
        0: 'dark',       // 已删除
        1: 'secondary',  // 草稿
        2: 'warning',    // 测试中
        3: 'success',    // 已发布
        4: 'danger'      // 已下线
    };
    return colors[status] || 'secondary';
}

/**
 * Agent状态文本函数（全局可用）
 * @param {number} status 状态值
 * @returns {string} 状态文本
 */
function getAgentStatusText(status) {
    const statusMap = {
        0: '已删除',
        1: '草稿',
        2: '测试中',
        3: '已发布',
        4: '已下线'
    };
    return statusMap[status] || '未知';
}

/**
 * 用户状态颜色函数（全局可用）
 * @param {number} status 状态值
 * @returns {string} Bootstrap颜色类
 */
function getUserStatusColor(status) {
    return status === 1 ? 'success' : 'secondary';
}

/**
 * 用户状态文本函数（全局可用）
 * @param {number} status 状态值
 * @returns {string} 状态文本
 */
function getUserStatusText(status) {
    return status === 1 ? '正常' : '禁用';
}

/**
 * 格式化日期
 * @param {string|Date} date 日期
 * @returns {string} 格式化后的日期字符串
 */
function formatDate(date) {
    if (!date) return '-';

    const d = new Date(date);
    if (isNaN(d.getTime())) return '-';

    return d.toLocaleDateString('zh-CN');
}

/**
 * 格式化日期时间
 * @param {string|Date} dateTime 日期时间
 * @returns {string} 格式化后的日期时间字符串
 */
function formatDateTime(dateTime) {
    if (!dateTime) return '-';

    const d = new Date(dateTime);
    if (isNaN(d.getTime())) return '-';

    return d.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
}

/**
 * 格式化文件大小
 * @param {number} bytes 字节数
 * @returns {string} 格式化后的文件大小
 */
function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 显示成功消息
 * @param {string} message 消息内容
 */
function showSuccess(message) {
    showToast(message, 'success');
}

/**
 * 显示错误消息
 * @param {string} message 消息内容
 */
function showError(message) {
    showToast(message, 'danger');
}

/**
 * 显示警告消息
 * @param {string} message 消息内容
 */
function showWarning(message) {
    showToast(message, 'warning');
}

/**
 * 显示信息消息
 * @param {string} message 消息内容
 */
function showInfo(message) {
    showToast(message, 'info');
}

/**
 * 显示Toast消息
 * @param {string} message 消息内容
 * @param {string} type 消息类型
 */
function showToast(message, type = 'info') {
    // 创建toast容器（如果不存在）
    let toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
        toastContainer.style.zIndex = '9999';
        document.body.appendChild(toastContainer);
    }
    
    // 创建toast元素
    const toastId = 'toast-' + Date.now();
    const toastHtml = `
        <div id="${toastId}" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header">
                <i class="bi bi-${getToastIcon(type)} text-${type} me-2"></i>
                <strong class="me-auto">系统消息</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body">
                ${message}
            </div>
        </div>
    `;
    
    toastContainer.insertAdjacentHTML('beforeend', toastHtml);
    
    // 显示toast
    const toastElement = document.getElementById(toastId);
    const toast = new bootstrap.Toast(toastElement, {
        autohide: true,
        delay: 3000
    });
    toast.show();
    
    // 自动清理
    toastElement.addEventListener('hidden.bs.toast', () => {
        toastElement.remove();
    });
}

/**
 * 获取Toast图标
 * @param {string} type 消息类型
 * @returns {string} 图标类名
 */
function getToastIcon(type) {
    const icons = {
        success: 'check-circle-fill',
        danger: 'exclamation-triangle-fill',
        warning: 'exclamation-triangle-fill',
        info: 'info-circle-fill'
    };
    return icons[type] || 'info-circle-fill';
}

/**
 * 确认对话框
 * @param {string} message 确认消息
 * @param {string} title 对话框标题
 * @returns {Promise<boolean>} 用户选择结果
 */
function confirmDialog(message, title = '确认操作') {
    return new Promise((resolve) => {
        const modalId = 'confirm-modal-' + Date.now();
        const modalHtml = `
            <div class="modal fade" id="${modalId}" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">${title}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <p>${message}</p>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-primary" id="${modalId}-confirm">确认</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        document.body.insertAdjacentHTML('beforeend', modalHtml);
        
        const modalElement = document.getElementById(modalId);
        const modal = new bootstrap.Modal(modalElement);
        
        // 绑定事件
        document.getElementById(modalId + '-confirm').addEventListener('click', () => {
            modal.hide();
            resolve(true);
        });
        
        modalElement.addEventListener('hidden.bs.modal', () => {
            modalElement.remove();
            resolve(false);
        });
        
        modal.show();
    });
}

/**
 * 防抖函数
 * @param {Function} func 要防抖的函数
 * @param {number} wait 等待时间
 * @returns {Function} 防抖后的函数
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * 节流函数
 * @param {Function} func 要节流的函数
 * @param {number} limit 时间限制
 * @returns {Function} 节流后的函数
 */
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

/**
 * 深拷贝对象
 * @param {object} obj 要拷贝的对象
 * @returns {object} 拷贝后的对象
 */
function deepClone(obj) {
    if (obj === null || typeof obj !== 'object') return obj;
    if (obj instanceof Date) return new Date(obj.getTime());
    if (obj instanceof Array) return obj.map(item => deepClone(item));
    if (typeof obj === 'object') {
        const clonedObj = {};
        for (const key in obj) {
            if (obj.hasOwnProperty(key)) {
                clonedObj[key] = deepClone(obj[key]);
            }
        }
        return clonedObj;
    }
}

/**
 * 显示确认对话框 (showConfirm别名)
 * @param {string} message 确认消息
 * @param {string} title 对话框标题
 * @returns {Promise<boolean>} 用户选择结果
 */
function showConfirm(message, title = '确认操作') {
    return confirmDialog(message, title);
}

// logout函数已移至admin.js中，避免重复定义

// 页面加载完成后检查认证
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, checking auth...');

    // 如果是管理页面才检查认证
    if (window.location.pathname.includes('admin')) {
        checkAuth();
    }
});
