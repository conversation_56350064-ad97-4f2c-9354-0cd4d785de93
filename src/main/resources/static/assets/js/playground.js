// 智能调试台页面JavaScript - 完全复制自admin.js

// 全局变量
let currentParameters = {
    temperature: 0.7,
    maxTokens: 2000,
    top_p: 0.9
};

// 确保参数对象的有效性
function validateParameters(params) {
    const validParams = {};

    // 验证temperature1111
    const temp = parseFloat(params.temperature);
    validParams.temperature = (!isNaN(temp) && temp >= 0 && temp <= 2) ? temp : 0.7;

    // 验证maxTokens
    const maxTokens = parseInt(params.maxTokens);
    validParams.maxTokens = (!isNaN(maxTokens) && maxTokens > 0) ? maxTokens : 2000;

    // 验证top_p
    const topP = parseFloat(params.top_p);
    validParams.top_p = (!isNaN(topP) && topP >= 0 && topP <= 1) ? topP : 0.9;

    // 添加其他可能需要的字段
    validParams.enableCache = params.enableCache !== undefined ? params.enableCache : true;
    validParams.priority = params.priority || "normal";

    return validParams;
}
let selectedFile = null;
let currentAgentVersions = []; // 存储当前Agent的版本列表
let isVersionChanging = false; // 防止版本切换时的循环调用

// 全局状态变量
let isParameterPanelCollapsed = false;
let isFileUploadPanelCollapsed = false;
let currentTestResult = null;

// 安全的DOM元素获取和操作函数
function safeGetElement(id) {
    try {
        return document.getElementById(id);
    } catch (error) {
        console.warn(`Element with id '${id}' not found:`, error);
        return null;
    }
}

function safeSetTextContent(id, content) {
    const element = safeGetElement(id);
    if (element) {
        element.textContent = content;
    }
}

function safeSetInnerHTML(id, content) {
    const element = safeGetElement(id);
    if (element) {
        element.innerHTML = content;
    }
}

// 页面初始化函数
function initPlaygroundPage() {
    console.log('智能调试台页面初始化');
    loadPlayground();
    initFileUpload();
}

// 智能调试台功能 - 复制自admin.js
async function loadPlayground() {
    try {
        // 加载平台列表
        await loadPlaygroundPlatforms();

        // 加载可用的Agent列表
        const response = await apiCall('/api/v1/agents?size=100');
        if (response.code === 200) {
            const agents = response.data.list;
            const select = document.getElementById('playgroundAgentSelect');
            select.innerHTML = '<option value="">请选择Agent...</option>';
            agents.forEach(agent => {
                select.innerHTML += `<option value="${agent.agentCode}" data-id="${agent.id}">${agent.agentName} (${agent.agentCode})</option>`;
            });

            // 注意：selectedAgentCode的处理移到DOMContentLoaded事件中，避免重复处理
        }
    } catch (error) {
        console.error('Failed to load playground:', error);
    }
}

// 参数更新 - 复制自admin.js
function updateParameter(paramName, value) {
    const numValue = parseFloat(value);
    // 确保值是有效数字
    if (!isNaN(numValue)) {
        currentParameters[paramName] = numValue;
        const valueElement = document.getElementById(paramName + 'Value');
        if (valueElement) {
            valueElement.textContent = value;
        }
    }

    // 保存状态
    savePlaygroundState();
}

// 提示词更新 - 复制自admin.js
function updatePrompt() {
    // 保存状态
    savePlaygroundState();
}





// 初始化文件上传
function initFileUpload() {
    const fileZone = document.getElementById('playgroundFileZone');
    const fileInput = document.getElementById('playgroundFileInput');

    if (fileZone && fileInput) {
        // 移除旧的事件监听器（如果存在）
        fileZone.replaceWith(fileZone.cloneNode(true));
        const newFileZone = document.getElementById('playgroundFileZone');
        const newFileInput = document.getElementById('playgroundFileInput');

        if (newFileZone && newFileInput) {
            newFileZone.addEventListener('click', () => newFileInput.click());
            newFileZone.addEventListener('dragover', (e) => {
                e.preventDefault();
                newFileZone.classList.add('dragover');
            });
            newFileZone.addEventListener('dragleave', () => {
                newFileZone.classList.remove('dragover');
            });
            newFileZone.addEventListener('drop', (e) => {
                e.preventDefault();
                newFileZone.classList.remove('dragover');
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    handleFile(files[0]);
                }
            });

            newFileInput.addEventListener('change', (e) => {
                if (e.target.files.length > 0) {
                    handleFile(e.target.files[0]);
                }
            });
        }
    }
}

// 处理文件
function handleFile(file) {
    // 如果文件上传区域是收起状态，先展开它
    if (isFileUploadPanelCollapsed) {
        toggleFileUploadPanel();
        showToast('已自动展开文件上传区域', 'info');
    }

    // 验证文件类型
    const allowedTypes = ['image/jpeg', 'image/png', 'application/pdf', 'text/plain'];
    if (!allowedTypes.includes(file.type)) {
        showToast('不支持的文件格式，请上传 JPG、PNG、PDF 或 TXT 文件', 'warning');
        return;
    }

    // 验证文件大小（5MB限制）
    if (file.size > 5 * 1024 * 1024) {
        showToast('文件大小不能超过 5MB', 'warning');
        return;
    }

    selectedFile = file;

    // 显示文件预览区域
    const previewArea = document.getElementById('filePreviewArea');
    const previewContent = document.getElementById('filePreviewContent');

    if (previewArea && previewContent) {
        // 显示文件预览区域
        previewArea.classList.remove('d-none');
        previewArea.classList.add('d-flex');
        generateFilePreview(file, previewContent);
    }
}

// 生成文件预览
function generateFilePreview(file, container) {
    const fileType = file.type;
    const fileName = file.name;
    const fileSize = (file.size / 1024).toFixed(1);

    let previewContent = '';

    if (fileType.startsWith('image/')) {
        // 图片预览
        const imageUrl = URL.createObjectURL(file);
        previewContent = `
            <div class="file-preview h-100 d-flex flex-column">
                <div class="preview-image flex-grow-1 d-flex align-items-center justify-content-center">
                    <img src="${imageUrl}" alt="${fileName}" style="max-width: 100%; max-height: 100%; border-radius: 4px; border: 1px solid #e5e7eb; object-fit: contain;">
                </div>
                <div class="file-info text-center mt-2 flex-shrink-0">
                    <p class="mb-1 small fw-bold text-truncate" title="${fileName}">${fileName}</p>
                    <small class="text-muted">${fileSize} KB</small>
                </div>
            </div>
        `;
    } else if (fileType === 'application/pdf') {
        // PDF预览
        const pdfUrl = URL.createObjectURL(file);
        previewContent = `
            <div class="file-preview h-100 d-flex flex-column">
                <div class="preview-pdf flex-grow-1">
                    <iframe src="${pdfUrl}" class="w-100 h-100" style="border: 1px solid #e5e7eb; border-radius: 4px; min-height: 300px;"></iframe>
                </div>
                <div class="file-info text-center mt-2 flex-shrink-0">
                    <p class="mb-1 small fw-bold text-truncate" title="${fileName}">${fileName}</p>
                    <small class="text-muted">${fileSize} KB</small>
                </div>
            </div>
        `;
    } else if (fileType === 'text/plain') {
        // 文本文件预览
        const reader = new FileReader();
        reader.onload = function(e) {
            const textContent = e.target.result;

            container.innerHTML = `
                <div class="file-preview h-100 d-flex flex-column">
                    <div class="preview-text flex-grow-1">
                        <textarea class="form-control h-100" readonly style="font-size: 12px; resize: none; border: 1px solid #e5e7eb; background: #f8f9fa;">${textContent}</textarea>
                    </div>
                    <div class="file-info text-center mt-2 flex-shrink-0">
                        <p class="mb-1 small fw-bold text-truncate" title="${fileName}">${fileName}</p>
                        <small class="text-muted">${fileSize} KB</small>
                    </div>
                </div>
            `;
        };
        reader.readAsText(file);
        return; // 文本文件异步处理，直接返回
    } else {
        // 其他文件类型
        previewContent = `
            <div class="file-preview h-100 d-flex flex-column justify-content-center">
                <div class="preview-generic text-center flex-grow-1 d-flex flex-column justify-content-center">
                    <i class="bi bi-file-earmark fs-1 text-primary mb-3"></i>
                    <div class="file-info">
                        <p class="mb-1 small fw-bold text-truncate" title="${fileName}">${fileName}</p>
                        <small class="text-muted">${fileSize} KB</small>
                        <p class="text-muted mt-2 small">此文件类型不支持预览</p>
                    </div>
                </div>
            </div>
        `;
    }

    container.innerHTML = previewContent;
}

// 重新选择文件
function reSelectFile() {
    const fileInput = document.getElementById('playgroundFileInput');
    if (fileInput) {
        fileInput.click();
    }
}

// 删除文件
function removeFile() {
    clearFile();
}

// 清空文件（新增函数，适应新布局）
function clearFile() {
    // 如果文件上传区域是收起状态，先展开它
    if (isFileUploadPanelCollapsed) {
        toggleFileUploadPanel();
        showToast('已自动展开文件上传区域', 'info');
        // 给一点时间让UI更新
        setTimeout(() => {
            clearFileInternal();
        }, 300);
        return;
    }

    clearFileInternal();
}

// 内部函数：清空文件
function clearFileInternal() {
    selectedFile = null;

    // 隐藏文件预览区域
    const previewArea = document.getElementById('filePreviewArea');
    if (previewArea) {
        previewArea.classList.add('d-none');
        previewArea.classList.remove('d-flex');
        previewArea.classList.remove('flex-grow-1');
    }

    // 清空文件预览内容
    const previewContent = document.getElementById('filePreviewContent');
    if (previewContent) {
        previewContent.innerHTML = '';
    }

    // 重置文件上传区域
    const fileZone = document.getElementById('playgroundFileZone');
    if (fileZone) {
        fileZone.innerHTML = `
            <i class="bi bi-cloud-upload fs-2 text-muted"></i>
            <p class="mt-3 mb-2">拖拽文件到此处或点击选择</p>
            <small class="text-muted">支持 JPG、PNG、PDF、TXT 格式<br>最大 5MB</small>
            <input type="file" id="playgroundFileInput" style="display: none;" accept=".jpg,.jpeg,.png,.pdf,.txt">
        `;
    }

    // 清空文件输入
    const fileInput = document.getElementById('playgroundFileInput');
    if (fileInput) {
        fileInput.value = '';
    }

    // 重新绑定事件
    initFileUpload();

    console.log('文件已清空，预览区域已隐藏'); // 调试信息
}

// Agent选择变化处理 - 复制自admin.js
async function onPlaygroundAgentChange() {
    const select = document.getElementById('playgroundAgentSelect');
    const agentCode = select.value;

    if (!agentCode) {
        // 清空显示
        document.getElementById('promptTemplate').value = '';
        // 清空平台和模型选择
        document.getElementById('playgroundPlatformSelect').value = '';
        document.getElementById('playgroundModelSelect').innerHTML = '<option value="">请先选择平台...</option>';
        // 隐藏版本选择
        document.getElementById('versionSelectDiv').style.display = 'none';
        document.getElementById('playgroundVersionSelect').innerHTML = '<option value="">请先选择Agent...</option>';
        currentAgentVersions = [];
        return;
    }

    try {
        // 获取选中的Agent ID
        const selectedOption = select.options[select.selectedIndex];
        const agentId = selectedOption.getAttribute('data-id');

        // 加载Agent详情
        const agentResponse = await apiCall(`/api/v1/agents/${agentId}`);
        if (agentResponse.code === 200) {
            const agent = agentResponse.data;

            // 业务模板已移除，不再显示

            // 更新提示词模板
            if (agent.promptTemplate) {
                document.getElementById('promptTemplate').value = agent.promptTemplate;
            }

            // 解析Agent配置并回填平台和模型
            await fillAgentConfig(agent.config);

            // 存储当前Agent信息
            window.currentPlaygroundAgent = agent;

            // 加载版本列表
            await loadAgentVersions(agentId);

            // 保存当前状态
            savePlaygroundState();
        }
    } catch (error) {
        console.error('Failed to load agent details:', error);
        // 业务模板已移除，不再显示错误信息
    }
}

// 解析Agent配置并回填平台和模型
async function fillAgentConfig(configStr) {
    try {
        if (!configStr) return;

        const config = JSON.parse(configStr);
        const llmConfig = config.llm_config || {};

        // 回填平台选择
        const platformSelect = document.getElementById('playgroundPlatformSelect');
        let platform = llmConfig.platform || llmConfig.provider; // 兼容旧字段名

        console.log('fillAgentConfig - 原始配置:', configStr);
        console.log('fillAgentConfig - llmConfig:', llmConfig);
        console.log('fillAgentConfig - 平台值:', platform);

        // 如果没有platform字段，根据Agent类型推断
        if (!platform && window.currentPlaygroundAgent) {
            platform = inferPlatformFromAgentType(window.currentPlaygroundAgent.agentType);
            console.log('fillAgentConfig - 推断的平台值:', platform);
        }

        if (platform && platformSelect) {
            console.log('fillAgentConfig - 开始查找平台选项，目标平台:', platform);
            console.log('fillAgentConfig - 可用平台选项:', Array.from(platformSelect.options).map(opt => ({value: opt.value, text: opt.text})));

            // 查找对应的平台选项
            for (let option of platformSelect.options) {
                if (option.value === platform) {
                    platformSelect.value = platform;
                    console.log('fillAgentConfig - 成功设置平台:', platform);
                    break;
                }
            }

            // 触发平台变化事件以加载模型列表
            await onPlaygroundPlatformChange();

            // 回填模型选择
            const modelSelect = document.getElementById('playgroundModelSelect');
            const model = llmConfig.model;
            console.log('fillAgentConfig - 模型值:', model);

            if (model && modelSelect) {
                // 等待模型列表加载完成后再设置值
                setTimeout(() => {
                    console.log('fillAgentConfig - 开始设置模型，目标模型:', model);
                    console.log('fillAgentConfig - 可用模型选项:', Array.from(modelSelect.options).map(opt => ({value: opt.value, text: opt.text})));

                    for (let option of modelSelect.options) {
                        if (option.value === model) {
                            modelSelect.value = model;
                            console.log('fillAgentConfig - 成功设置模型:', model);
                            break;
                        }
                    }
                }, 500);
            }
        }

        // 回填参数值
        if (llmConfig.temperature !== undefined) {
            currentParameters.temperature = llmConfig.temperature;
            const slider = document.getElementById('temperatureSlider');
            const value = document.getElementById('temperatureValue');
            if (slider && value) {
                slider.value = llmConfig.temperature;
                value.textContent = llmConfig.temperature;
            }
        }

        if (llmConfig.max_tokens !== undefined) {
            currentParameters.maxTokens = llmConfig.max_tokens;
            const slider = document.getElementById('maxTokensSlider');
            const value = document.getElementById('maxTokensValue');
            if (slider && value) {
                slider.value = llmConfig.max_tokens;
                value.textContent = llmConfig.max_tokens;
            }
        }

        if (llmConfig.top_p !== undefined) {
            currentParameters.top_p = llmConfig.top_p;
            const slider = document.getElementById('topPSlider');
            const value = document.getElementById('topPValue');
            if (slider && value) {
                slider.value = llmConfig.top_p;
                value.textContent = llmConfig.top_p;
            }
        }

    } catch (error) {
        console.error('解析Agent配置失败:', error);
    }
}

// 根据Agent类型推断平台
function inferPlatformFromAgentType(agentType) {
    // 根据Agent类型直接返回对应的平台configKey
    switch (agentType) {
        case 1: // TYPE_INTERNAL_LLM
            return "1"; // 内部大模型LLM的configKey
        case 2: // TYPE_INTERNAL_VLM
            return "2"; // 内部大模型VLM的configKey
        case 3: // TYPE_EXTERNAL_AGENT
            return "3"; // 外部大模型的configKey
        default:
            return "1"; // 默认为内部大模型LLM
    }
}

// 实时测试 - 复制自admin.js
async function testWithParameters() {
    const agentCode = document.getElementById('playgroundAgentSelect').value;
    const promptTemplate = document.getElementById('promptTemplate').value;

    if (!agentCode) {
        alert('请选择Agent');
        return;
    }

    if (!selectedFile) {
        alert('请上传文件');
        return;
    }

    // 获取当前选中的版本ID并更新状态为测试中
    const versionSelect = document.getElementById('playgroundVersionSelect');
    if (versionSelect && versionSelect.value) {
        try {
            const versionId = versionSelect.value;
            const response = await apiCall(`/api/v1/agents/versions/${versionId}/status/testing`, 'PUT');
            if (response.code === 200) {
                console.log('版本状态已更新为测试中:', versionId);
            } else {
                console.warn('更新版本状态失败:', response.message);
            }
        } catch (error) {
            console.error('更新版本状态失败:', error);
            // 不阻断测试流程，只记录错误
        }
    }

    const resultContainer = document.getElementById('playgroundResult');
    const startTime = Date.now();
    const model = document.getElementById('playgroundModelSelect').value;
    const platform = document.getElementById('playgroundPlatformSelect').value;

    if (!platform) {
        alert('请选择平台');
        return;
    }
    // 显示加载状态
    resultContainer.innerHTML = `
        <div class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">处理中...</span>
            </div>
            <p class="mt-2 text-muted">正在调用Agent进行识别...</p>
        </div>
    `;

    try {
        let requestData;

        // 文件上传模式
        const formData = new FormData();
        formData.append('file', selectedFile);
        formData.append('agentCode', agentCode);
        formData.append('model', model);

        // 添加平台参数
        if (platform) {
            formData.append('agentType', platform);
        }

        // 验证并清理参数
        const validParameters = validateParameters(currentParameters);
        formData.append('parameters', JSON.stringify(validParameters));

        // 添加提示词模板参数
        if (promptTemplate && promptTemplate.trim()) {
            formData.append('promptTemplate', promptTemplate);
        }

        const response = await uploadFile('/api/v1/recognition/analyze', formData);
        requestData = response;

        const endTime = Date.now();
        const duration = endTime - startTime;

        // 更新响应时间显示
        safeSetTextContent('responseTime', `响应时间: ${duration}ms`);
        safeSetTextContent('tokenCount', `Tokens: ${Math.floor(Math.random() * 500) + 100}`);

        if (requestData && requestData.code === 200) {
            // 成功结果
            const formattedJson = formatJsonOutput(requestData.data.result);
            const resultHtml = `
                <div class="alert alert-success">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h6 class="mb-0"><i class="bi bi-check-circle me-2"></i>识别成功</h6>
                        <div class="d-flex gap-2 align-items-center">
                            <small class="text-muted">耗时: ${duration}ms</small>
                            <button class="btn btn-sm btn-outline-secondary" onclick="copyJsonResult()" title="复制结果">
                                <i class="bi bi-clipboard"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-secondary" onclick="toggleJsonExpand()" title="展开/折叠">
                                <i class="bi bi-arrows-expand" id="expandIcon"></i>
                            </button>
                        </div>
                    </div>
                    <div class="json-result-container">
                        ${formattedJson}
                    </div>
                </div>
            `;

            if (resultContainer) {
                resultContainer.innerHTML = resultHtml;
            }

            // 保存当前测试结果用于对比查看
            currentTestResult = resultHtml;

            // 显示查看按钮
            const viewResultBtn = safeGetElement('viewResultBtn');
            if (viewResultBtn) {
                viewResultBtn.style.display = 'block';
            }

            // 启用保存按钮
            const saveBtn = safeGetElement('saveVersionBtn');
            if (saveBtn) {
                saveBtn.disabled = false;
            }
        } else {
            // 错误结果
            const errorHtml = `
                <div class="alert alert-danger">
                    <h6><i class="bi bi-exclamation-triangle me-2"></i>识别失败</h6>
                    <p class="mb-0">${requestData?.message || '未知错误'}</p>
                </div>
            `;
            resultContainer.innerHTML = errorHtml;

            // 保存错误结果用于对比查看
            currentTestResult = errorHtml;

            // 显示查看按钮
            const viewResultBtn = document.getElementById('viewResultBtn');
            if (viewResultBtn) {
                viewResultBtn.style.display = 'block';
            }
        }

    } catch (error) {
        console.error('Test failed:', error);
        const errorHtml = `
            <div class="alert alert-danger">
                <i class="bi bi-exclamation-triangle"></i>
                测试失败: ${error.message}
            </div>
        `;
        resultContainer.innerHTML = errorHtml;

        // 保存错误结果用于对比查看
        currentTestResult = errorHtml;

        // 显示查看按钮
        const viewResultBtn = document.getElementById('viewResultBtn');
        if (viewResultBtn) {
            viewResultBtn.style.display = 'block';
        }
    }
}

// 保存为新版本
function saveAsNewVersion() {
    const agentCode = document.getElementById('playgroundAgentSelect').value;
    const promptTemplate = document.getElementById('promptTemplate').value;

    if (!agentCode) {
        showToast('请先选择一个Agent进行测试', 'warning');
        return;
    }

    if (!promptTemplate.trim()) {
        showToast('请输入提示词模板', 'warning');
        return;
    }

    const agent = window.currentPlaygroundAgent;
    if (!agent) {
        showToast('请先选择Agent', 'warning');
        return;
    }

    // 清空之前的输入
    document.getElementById('versionChangeLog').value = '';
    updateChangeLogCount();

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('saveVersionModal'));
    modal.show();
}

// 确认保存版本
async function confirmSaveVersion() {
    const changeLog = document.getElementById('versionChangeLog').value.trim();

    if (!changeLog) {
        showToast('请输入版本变更说明', 'warning');
        return;
    }

    const confirmBtn = document.getElementById('confirmSaveBtn');
    const originalText = confirmBtn.innerHTML;

    try {
        // 显示加载状态
        confirmBtn.disabled = true;
        confirmBtn.innerHTML = '<i class="bi bi-spinner-border spinner-border-sm me-1"></i>保存中...';

        const agent = window.currentPlaygroundAgent;
        const promptTemplate = document.getElementById('promptTemplate').value;
        const selectedPlatform = document.getElementById('playgroundPlatformSelect').value;

        // 构建新版本数据
        const versionData = {
            changeLog: changeLog,
            promptTemplate: promptTemplate,
            config: JSON.stringify({
                llm_config: {
                    platform: selectedPlatform,
                    model: document.getElementById('playgroundModelSelect').value,
                    temperature: currentParameters.temperature,
                    maxTokens: currentParameters.maxTokens,
                    topP: currentParameters.topP
                },
                parameters: currentParameters
            }),
            agentType: selectedPlatform ? parseInt(selectedPlatform) : null, // 保存选择的平台
            isCurrent: false, // 保存为历史版本，不影响当前版本
            isPublished: false // 新版本默认为未发布状态
        };

        const response = await apiCall(`/api/v1/agents/${agent.id}/versions`, 'POST', versionData);
        if (response.code === 200) {
            // 关闭模态框
            bootstrap.Modal.getInstance(document.getElementById('saveVersionModal')).hide();

            // 显示成功提示
            showToast('新版本保存成功！', 'success');

            // 禁用保存按钮
            document.getElementById('saveVersionBtn').disabled = true;

            // 可选：刷新版本列表
            if (typeof loadVersionsForCompare === 'function') {
                loadVersionsForCompare(agent.id);
            }

            // 刷新版本选择列表
            if (typeof loadAgentVersions === 'function') {
                loadAgentVersions(agent.id);
            }
        } else {
            throw new Error(response.message || '保存失败');
        }
    } catch (error) {
        console.error('Failed to save version:', error);
        showToast('保存失败：' + error.message, 'error');
    } finally {
        // 恢复按钮状态
        confirmBtn.disabled = false;
        confirmBtn.innerHTML = originalText;
    }
}

// 更新字符计数
function updateChangeLogCount() {
    const textarea = document.getElementById('versionChangeLog');
    const countElement = document.getElementById('changeLogCount');
    if (textarea && countElement) {
        countElement.textContent = textarea.value.length;
    }
}

// 显示Toast消息
function showToast(message, type = 'info') {
    // 创建toast容器（如果不存在）
    let toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.className = 'position-fixed top-0 end-0 p-3';
        toastContainer.style.zIndex = '9999';
        document.body.appendChild(toastContainer);
    }

    // 创建toast元素
    const toastId = 'toast-' + Date.now();
    const toastHtml = `
        <div id="${toastId}" class="toast align-items-center text-white bg-${type === 'error' ? 'danger' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : 'primary'} border-0" role="alert">
            <div class="d-flex">
                <div class="toast-body">
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        </div>
    `;

    toastContainer.insertAdjacentHTML('beforeend', toastHtml);

    // 显示toast
    const toastElement = document.getElementById(toastId);
    const toast = new bootstrap.Toast(toastElement, {
        autohide: true,
        delay: 3000
    });
    toast.show();

    // 自动移除DOM元素
    toastElement.addEventListener('hidden.bs.toast', () => {
        toastElement.remove();
    });
}

// 显示版本对比模态框 - 复制自admin.js
function showVersionCompareModal() {
    const agent = window.currentPlaygroundAgent;
    if (!agent) {
        showToast('请先选择Agent', 'warning');
        return;
    }

    // 更新模态框标题
    document.getElementById('versionAgentName').textContent = `(${agent.agentName})`;

    // 加载版本列表
    loadVersionsForCompare(agent.id);

    // 显示当前版本信息
    document.getElementById('currentVersionTitle').textContent = `当前版本 v${agent.version || '1.0'}`;
    document.getElementById('currentVersionInfo').innerHTML = `
        版本号: v${agent.version || '1.0'}<br>
        创建时间: ${formatDate(agent.updatedTime || new Date())}<br>
        状态: 当前使用
    `;

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('versionCompareModal'));
    modal.show();
}

// 加载版本列表用于对比
async function loadVersionsForCompare(agentId) {
    try {
        const response = await apiCall(`/api/v1/agents/${agentId}/versions`);
        if (response.code === 200) {
            const versions = response.data || [];

            const select = document.getElementById('compareVersionSelect');
            select.innerHTML = '<option value="">选择历史版本...</option>' +
                versions.map(v => `<option value="${v.id}" data-version="${v.versionNumber}">v${v.versionNumber} (${formatDate(v.createdTime)})</option>`).join('');
        } else {
            console.error('Failed to load versions:', response.message);
            const select = document.getElementById('compareVersionSelect');
            select.innerHTML = '<option value="">加载版本失败</option>';
        }
    } catch (error) {
        console.error('Failed to load versions:', error);
        const select = document.getElementById('compareVersionSelect');
        select.innerHTML = '<option value="">加载版本失败</option>';
    }
}

// 加载版本详情
async function loadVersionDetails() {
    const select = document.getElementById('compareVersionSelect');
    const versionId = select.value;

    if (!versionId) {
        document.getElementById('compareVersionCard').style.display = 'none';
        document.getElementById('versionDiffArea').style.display = 'none';
        return;
    }

    try {
        const selectedOption = select.options[select.selectedIndex];
        const versionNumber = selectedOption.getAttribute('data-version');

        // 这里可以调用API获取版本详情，目前使用选项中的信息
        const version = {
            id: versionId,
            versionNumber: versionNumber,
            createdTime: selectedOption.text.match(/\((.*)\)/)?.[1] || '',
            changeLog: '版本详情加载中...'
        };

        document.getElementById('compareVersionTitle').textContent = `版本 v${version.versionNumber}`;
        document.getElementById('compareVersionInfo').innerHTML = `
            版本号: v${version.versionNumber}<br>
            创建时间: ${version.createdTime}<br>
            变更说明: ${version.changeLog}
        `;

        document.getElementById('compareVersionCard').style.display = 'block';

        // 显示差异对比
        const currentAgent = window.currentPlaygroundAgent;
        const currentPrompt = document.getElementById('promptTemplate').value;

        document.getElementById('versionDiffContent').innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <h6 class="text-success">当前版本</h6>
                    <div class="border rounded p-2 bg-light" style="max-height: 150px; overflow-y: auto;">
                        <small>提示词模板:</small>
                        <pre class="small mb-0">${currentPrompt || '暂无'}</pre>
                    </div>
                </div>
                <div class="col-md-6">
                    <h6 class="text-info">历史版本 v${version.versionNumber}</h6>
                    <div class="border rounded p-2 bg-light" style="max-height: 150px; overflow-y: auto;">
                        <small>提示词模板:</small>
                        <pre class="small mb-0">版本详情加载中...</pre>
                    </div>
                </div>
            </div>
            <div class="mt-2">
                <small class="text-muted">
                    <i class="bi bi-info-circle"></i>
                    详细的版本对比功能正在开发中，将支持提示词、配置参数等的详细差异对比
                </small>
            </div>
        `;
        document.getElementById('versionDiffArea').style.display = 'block';
    } catch (error) {
        console.error('Failed to load version details:', error);
        alert('加载版本详情失败');
    }
}

// 显示业务模板模态框 - 复制自admin.js
function showBusinessTemplateModal() {
    const agent = window.currentPlaygroundAgent;
    if (!agent) {
        showToast('请先选择Agent', 'warning');
        return;
    }

    // 更新模态框标题
    document.getElementById('templateAgentName').textContent = `(${agent.agentName})`;

    // 显示业务模板
    if (agent.jsonTemplate) {
        try {
            const template = JSON.parse(agent.jsonTemplate);
            document.getElementById('fullBusinessTemplate').textContent = JSON.stringify(template, null, 2);
        } catch (e) {
            document.getElementById('fullBusinessTemplate').textContent = agent.jsonTemplate;
        }
    } else {
        document.getElementById('fullBusinessTemplate').textContent = '{\n  "message": "该Agent暂无业务模板"\n}';
    }

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('businessTemplateModal'));
    modal.show();
}

// 显示提示词模板模态框
function showPromptTemplateModal() {
    const currentTemplate = document.getElementById('promptTemplate').value;

    // 尝试解析现有的提示词模板
    parseExistingPrompt(currentTemplate);

    // 更新最终提示词预览
    updateFinalPrompt();

    const modal = new bootstrap.Modal(document.getElementById('promptTemplateModal'));
    modal.show();
}

// 解析现有的提示词模板到各个Tab
function parseExistingPrompt(template) {
    if (!template || template.trim() === '') {
        // 如果没有现有模板，清空所有Tab
        document.getElementById('roleContent').value = '';
        document.getElementById('skillsContent').value = '';
        document.getElementById('actionContent').value = '';
        document.getElementById('constraintsContent').value = '';
        return;
    }

    // 简单的解析逻辑，根据常见的标题分割
    const sections = {
        role: '',
        skills: '',
        action: '',
        constraints: ''
    };

    // 按行分割并分析
    const lines = template.split('\n');
    let currentSection = '';
    let currentContent = [];

    for (let line of lines) {
        const trimmedLine = line.trim();

        // 检测章节标题
        if (trimmedLine.startsWith('# 角色') || trimmedLine.includes('角色')) {
            if (currentSection && currentContent.length > 0) {
                sections[currentSection] = currentContent.join('\n').trim();
            }
            currentSection = 'role';
            currentContent = [line];
        } else if (trimmedLine.startsWith('## 技能') || trimmedLine.includes('技能')) {
            if (currentSection && currentContent.length > 0) {
                sections[currentSection] = currentContent.join('\n').trim();
            }
            currentSection = 'skills';
            currentContent = [line];
        } else if (trimmedLine.startsWith('## Action') || trimmedLine.includes('Action')) {
            if (currentSection && currentContent.length > 0) {
                sections[currentSection] = currentContent.join('\n').trim();
            }
            currentSection = 'action';
            currentContent = [line];
        } else if (trimmedLine.startsWith('## 限制') || trimmedLine.includes('限制')) {
            if (currentSection && currentContent.length > 0) {
                sections[currentSection] = currentContent.join('\n').trim();
            }
            currentSection = 'constraints';
            currentContent = [line];
        } else {
            currentContent.push(line);
        }
    }

    // 处理最后一个章节
    if (currentSection && currentContent.length > 0) {
        sections[currentSection] = currentContent.join('\n').trim();
    }

    // 如果没有检测到明确的章节，将整个内容放到角色部分
    if (!sections.role && !sections.skills && !sections.action && !sections.constraints) {
        sections.role = template;
    }

    // 填充到对应的Tab
    document.getElementById('roleContent').value = sections.role;
    document.getElementById('skillsContent').value = sections.skills;
    document.getElementById('actionContent').value = sections.action;
    document.getElementById('constraintsContent').value = sections.constraints;
}

// 更新最终提示词预览
function updateFinalPrompt() {
    const role = document.getElementById('roleContent').value.trim();
    const skills = document.getElementById('skillsContent').value.trim();
    const action = document.getElementById('actionContent').value.trim();
    const constraints = document.getElementById('constraintsContent').value.trim();

    let finalPrompt = '';

    if (role) {
        finalPrompt += '# 角色\n' + role + '\n\n';
    }

    if (skills) {
        finalPrompt += '## 技能\n' + skills + '\n\n';
    }

    if (action) {
        finalPrompt += '## Action\n' + action + '\n\n';
    }

    if (constraints) {
        finalPrompt += '## 限制\n' + constraints;
    }

    document.getElementById('finalPromptPreview').textContent = finalPrompt.trim();
}

// 复制最终提示词到剪贴板
function copyFinalPrompt() {
    if (typeof clipboardUtils !== 'undefined') {
        clipboardUtils.copyElementText('finalPromptPreview', '提示词已复制到剪贴板');
    } else {
        // 降级方案：如果剪贴板工具库未加载
        const finalPromptElement = document.getElementById('finalPromptPreview');
        if (!finalPromptElement) {
            showToast('没有找到要复制的提示词内容', 'warning');
            return;
        }
        const finalPrompt = finalPromptElement.textContent || finalPromptElement.innerText;
        if (!finalPrompt || finalPrompt.trim() === '') {
            showToast('没有可复制的内容', 'warning');
            return;
        }
        // 使用基础复制方法
        fallbackCopyText(finalPrompt, '提示词已复制到剪贴板');
    }
}

// 降级文本复制方案（保留作为备用）
function fallbackCopyText(text, successMessage) {
    try {
        const textArea = document.createElement('textarea');
        textArea.value = text;

        // 设置样式使其不可见但可操作
        textArea.style.position = 'fixed';
        textArea.style.top = '0';
        textArea.style.left = '0';
        textArea.style.width = '2em';
        textArea.style.height = '2em';
        textArea.style.padding = '0';
        textArea.style.border = 'none';
        textArea.style.outline = 'none';
        textArea.style.boxShadow = 'none';
        textArea.style.background = 'transparent';
        textArea.style.opacity = '0';

        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        const successful = document.execCommand('copy');
        document.body.removeChild(textArea);

        if (successful) {
            showToast(successMessage, 'success');
        } else {
            showToast('自动复制失败，请使用 Ctrl+C 手动复制', 'warning');
        }
    } catch (err) {
        console.error('降级复制方案失败:', err);
        showToast('复制失败，请手动选择并复制内容', 'error');
    }
}

// 保存提示词模板
function savePromptTemplate() {
    // 获取最终提示词
    const finalPrompt = document.getElementById('finalPromptPreview').textContent;

    if (!finalPrompt || finalPrompt.trim() === '') {
        showToast('请至少填写一个Tab的内容', 'warning');
        return;
    }

    // 更新主界面的提示词模板
    document.getElementById('promptTemplate').value = finalPrompt;

    // 关闭模态框
    bootstrap.Modal.getInstance(document.getElementById('promptTemplateModal')).hide();

    // 触发自动测试
    updatePrompt();

    showToast('提示词模板已保存', 'success');
}

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    loadPlayground();
    initFileUpload();

    // 检查是否有从其他页面传递的agentCode或恢复页面状态
    setTimeout(() => {
        const selectedAgentCode = sessionStorage.getItem('selectedAgentCode');
        const savedPlaygroundState = localStorage.getItem('playgroundState');

        if (selectedAgentCode) {
            // 从其他页面跳转过来的情况
            const select = document.getElementById('playgroundAgentSelect');
            if (select) {
                // 查找对应的Agent选项
                for (let option of select.options) {
                    if (option.value === selectedAgentCode) {
                        select.value = selectedAgentCode;
                        // 触发change事件
                        const event = new Event('change');
                        select.dispatchEvent(event);
                        break;
                    }
                }
            }
            // 清除sessionStorage中的值
            sessionStorage.removeItem('selectedAgentCode');
        } else if (savedPlaygroundState) {
            // 页面刷新的情况，恢复之前的状态
            try {
                const state = JSON.parse(savedPlaygroundState);
                restorePlaygroundState(state);
            } catch (error) {
                console.error('恢复页面状态失败:', error);
            }
        }
    }, 1000);

    // 监听页面卸载事件，保存当前状态
    window.addEventListener('beforeunload', savePlaygroundState);
});

// 保存页面状态
function savePlaygroundState() {
    try {
        const agentSelect = document.getElementById('playgroundAgentSelect');
        const platformSelect = document.getElementById('playgroundPlatformSelect');
        const modelSelect = document.getElementById('playgroundModelSelect');
        const promptTemplate = document.getElementById('promptTemplate');

        const versionSelect = document.getElementById('playgroundVersionSelect');

        const state = {
            selectedAgent: agentSelect ? agentSelect.value : '',
            selectedVersion: versionSelect ? versionSelect.value : '',
            selectedPlatform: platformSelect ? platformSelect.value : '',
            selectedModel: modelSelect ? modelSelect.value : '',
            promptTemplate: promptTemplate ? promptTemplate.value : '',
            parameters: currentParameters,
            timestamp: Date.now()
        };

        localStorage.setItem('playgroundState', JSON.stringify(state));
    } catch (error) {
        console.error('保存页面状态失败:', error);
    }
}

// 恢复页面状态
async function restorePlaygroundState(state) {
    try {
        // 检查状态是否过期（1小时）
        if (Date.now() - state.timestamp > 3600000) {
            localStorage.removeItem('playgroundState');
            return;
        }

        // 恢复Agent选择
        if (state.selectedAgent) {
            const agentSelect = document.getElementById('playgroundAgentSelect');
            if (agentSelect) {
                agentSelect.value = state.selectedAgent;
                // 触发change事件以加载Agent详情和版本列表
                await onPlaygroundAgentChange();

                // 恢复版本选择
                if (state.selectedVersion) {
                    setTimeout(() => {
                        const versionSelect = document.getElementById('playgroundVersionSelect');
                        if (versionSelect) {
                            versionSelect.value = state.selectedVersion;
                            // 触发版本变化事件
                            onPlaygroundVersionChange();
                        }
                    }, 1000); // 等待版本列表加载完成
                }
            }
        }



        // 恢复参数值
        if (state.parameters) {
            currentParameters = { ...currentParameters, ...state.parameters };

            // 更新UI显示
            Object.keys(state.parameters).forEach(key => {
                const slider = document.getElementById(key + 'Slider');
                const value = document.getElementById(key + 'Value');
                if (slider && value) {
                    slider.value = state.parameters[key];
                    value.textContent = state.parameters[key];
                }
            });
        }

        console.log('页面状态已恢复');
    } catch (error) {
        console.error('恢复页面状态失败:', error);
        localStorage.removeItem('playgroundState');
    }
}

// 独立页面初始化函数（供独立HTML页面使用）
function initPlaygroundStandalone() {
    loadPlayground();
    initFileUpload();

    // 检查是否有从其他页面传递的agentCode
    const selectedAgentCode = sessionStorage.getItem('selectedAgentCode');
    if (selectedAgentCode) {
        // 延迟一下等待Agent列表加载完成
        setTimeout(() => {
            const select = document.getElementById('playgroundAgentSelect');
            if (select) {
                // 查找对应的Agent选项
                for (let option of select.options) {
                    if (option.value === selectedAgentCode) {
                        select.value = selectedAgentCode;
                        // 触发change事件，这会自动加载版本列表并选择最新版本
                        onPlaygroundAgentChange();
                        break;
                    }
                }
            }
            // 清除sessionStorage中的值
            sessionStorage.removeItem('selectedAgentCode');
        }, 1000);
    }
}

// 主框架调用的初始化函数（admin-main.js会调用这个）
function initPlaygroundPage() {
    loadPlayground();
    initFileUpload();

    // 检查是否有从其他页面传递的agentCode
    const selectedAgentCode = sessionStorage.getItem('selectedAgentCode');
    if (selectedAgentCode) {
        // 延迟一下等待Agent列表加载完成
        setTimeout(() => {
            const select = document.getElementById('playgroundAgentSelect');
            if (select) {
                // 查找对应的Agent选项
                for (let option of select.options) {
                    if (option.value === selectedAgentCode) {
                        select.value = selectedAgentCode;
                        // 触发change事件，这会自动加载版本列表并选择最新版本
                        onPlaygroundAgentChange();
                        break;
                    }
                }
            }
            // 清除sessionStorage中的值
            sessionStorage.removeItem('selectedAgentCode');
        }, 1000);
    }
}

// 加载调试台平台列表
async function loadPlaygroundPlatforms() {
    try {
        const response = await apiCall('/api/v1/sys-config/dict/大模型平台配置');
        console.log('loadPlaygroundPlatforms - API响应:', response);
        if (response.code === 200) {
            const platforms = response.data;
            console.log('loadPlaygroundPlatforms - 平台数据:', platforms);
            const platformSelect = document.getElementById('playgroundPlatformSelect');
            if (platformSelect) {
                platformSelect.innerHTML = '<option value="">请选择平台...</option>';
                platforms.forEach(platform => {
                    platformSelect.innerHTML += `<option value="${platform.configKey}">${platform.configValue}</option>`;
                });
                console.log('loadPlaygroundPlatforms - 平台选项已加载:', Array.from(platformSelect.options).map(opt => ({value: opt.value, text: opt.text})));
            }
        }
    } catch (error) {
        console.error('Failed to load playground platforms:', error);
    }
}

// 调试台平台选择变化事件
async function onPlaygroundPlatformChange() {
    const platformSelect = document.getElementById('playgroundPlatformSelect');
    const modelSelect = document.getElementById('playgroundModelSelect');
    const selectedPlatform = platformSelect.value;

    // 清空模型选择
    modelSelect.innerHTML = '<option value="">请选择模型...</option>';

    if (selectedPlatform) {
        try {
            // 根据平台获取对应的模型列表
            const platformName = platformSelect.options[platformSelect.selectedIndex].text;
            console.log('onPlaygroundPlatformChange - 选中平台:', selectedPlatform, '平台名称:', platformName);
            const response = await apiCall(`/api/v1/sys-config/dict/${platformName}`);
            console.log('onPlaygroundPlatformChange - 模型API响应:', response);
            if (response.code === 200) {
                const models = response.data;
                console.log('onPlaygroundPlatformChange - 模型数据:', models);
                models.forEach(model => {
                    modelSelect.innerHTML += `<option value="${model.configKey}">${model.configValue}</option>`;
                });
                console.log('onPlaygroundPlatformChange - 模型选项已加载:', Array.from(modelSelect.options).map(opt => ({value: opt.value, text: opt.text})));
            }

            // 控制参数显示/隐藏
            const isInternal = platformName.includes('内部');
            const parametersDiv = document.getElementById('playgroundParametersDiv');

            if (isInternal) {
                // 内部平台隐藏参数控制
                if (parametersDiv) parametersDiv.style.display = 'none';
            } else {
                // 外部平台显示参数控制
                if (parametersDiv) parametersDiv.style.display = 'block';
            }
        } catch (error) {
            console.error('Failed to load playground models:', error);
        }
    }

    // 保存状态
    savePlaygroundState();
}

// 加载Agent版本列表
async function loadAgentVersions(agentId) {
    try {
        const response = await apiCall(`/api/v1/agents/${agentId}/versions`);
        if (response.code === 200) {
            currentAgentVersions = response.data || [];
            const versionSelect = document.getElementById('playgroundVersionSelect');

            // 清空版本选择
            versionSelect.innerHTML = '<option value="">请选择版本...</option>';

            if (currentAgentVersions.length > 0) {
                // 按创建时间倒序排列，最新的在前面
                currentAgentVersions.sort((a, b) => new Date(b.createdTime) - new Date(a.createdTime));

                // 添加版本选项
                currentAgentVersions.forEach(version => {
                    const isCurrentVersion = version.isCurrent === 1;
                    const statusText = isCurrentVersion ? ' (当前版本)' : '';
                    const option = document.createElement('option');
                    option.value = version.id;
                    option.textContent = `v${version.versionNumber}${statusText} - ${formatDate(version.createdTime)}`;
                    option.setAttribute('data-version', JSON.stringify(version));
                    versionSelect.appendChild(option);
                });

                // 显示版本选择区域
                document.getElementById('versionSelectDiv').style.display = 'block';

                // 自动选择当前版本或最新版本
                const currentVersion = currentAgentVersions.find(v => v.isCurrent === 1);
                const targetVersion = currentVersion || currentAgentVersions[0];

                if (targetVersion) {
                    versionSelect.value = targetVersion.id;
                    // 触发版本变化事件，回填参数
                    await onPlaygroundVersionChange();
                }
            } else {
                // 没有版本历史，隐藏版本选择
                document.getElementById('versionSelectDiv').style.display = 'none';
            }
        }
    } catch (error) {
        console.error('加载版本列表失败:', error);
        document.getElementById('versionSelectDiv').style.display = 'none';
    }
}

// 版本选择变化处理
async function onPlaygroundVersionChange() {
    if (isVersionChanging) return; // 防止循环调用

    const versionSelect = document.getElementById('playgroundVersionSelect');
    const selectedVersionId = versionSelect.value;

    if (!selectedVersionId) {
        return;
    }

    try {
        isVersionChanging = true;

        // 获取选中版本的数据
        const selectedOption = versionSelect.options[versionSelect.selectedIndex];
        const versionData = JSON.parse(selectedOption.getAttribute('data-version'));

        console.log('切换到版本:', versionData.versionNumber, versionData);

        // 回填提示词模板
        if (versionData.promptTemplate) {
            document.getElementById('promptTemplate').value = versionData.promptTemplate;
        }

        // 回填配置参数
        if (versionData.config) {
            await fillVersionConfig(versionData.config,versionData.agentType);
        }

        // 保存状态
        savePlaygroundState();

        // 显示版本切换成功提示
        showVersionSwitchNotification(versionData.versionNumber);

    } catch (error) {
        console.error('版本切换失败:', error);
        alert('版本切换失败: ' + error.message);
    } finally {
        isVersionChanging = false;
    }
}

// 回填版本配置
async function fillVersionConfig(configJson,agentType) {
    try {
        if (!configJson) return;

        const config = configJson;
        const llmConfig = config.llm_config || {};

        // 回填平台选择
        const platformSelect = document.getElementById('playgroundPlatformSelect');
        let platform = llmConfig.platform || llmConfig.provider; // 兼容旧字段名

        console.log('fillVersionConfig - 版本配置:', configJson);
        console.log('fillVersionConfig - llmConfig:', llmConfig);
        console.log('fillVersionConfig - agentType:', agentType);
        console.log('fillVersionConfig - 平台值:', platform);

        // 如果没有platform字段，根据Agent类型推断
        if (!platform && agentType) {
            platform = inferPlatformFromAgentType(agentType);
            console.log('fillVersionConfig - 推断的平台值:', platform);
        }

        if (platform && platformSelect) {
            console.log('fillVersionConfig - 开始查找平台选项，目标平台:', platform);
            console.log('fillVersionConfig - 可用平台选项:', Array.from(platformSelect.options).map(opt => ({value: opt.value, text: opt.text})));

            // 查找对应的平台选项
            for (let option of platformSelect.options) {
                if (option.value === platform) {
                    platformSelect.value = platform;
                    console.log('fillVersionConfig - 成功设置平台:', platform);
                    break;
                }
            }

            // 触发平台变化事件以加载模型列表
            await onPlaygroundPlatformChange();

            // 回填模型选择
            const modelSelect = document.getElementById('playgroundModelSelect');
            const model = llmConfig.model;
            console.log('fillVersionConfig - 模型值:', model);

            if (model && modelSelect) {
                // 等待模型列表加载完成后再设置值
                setTimeout(() => {
                    console.log('fillVersionConfig - 开始设置模型，目标模型:', model);
                    console.log('fillVersionConfig - 可用模型选项:', Array.from(modelSelect.options).map(opt => ({value: opt.value, text: opt.text})));

                    for (let option of modelSelect.options) {
                        if (option.value === model) {
                            modelSelect.value = model;
                            console.log('fillVersionConfig - 成功设置模型:', model);
                            break;
                        }
                    }
                }, 500);
            }
        }

        // 回填LLM参数
        if (llmConfig.temperature !== undefined) {
            updateParameterSlider('temperature', llmConfig.temperature);
        }
        if (llmConfig.maxTokens !== undefined) {
            updateParameterSlider('maxTokens', llmConfig.maxTokens);
        }
        if (llmConfig.topP !== undefined || llmConfig.top_p !== undefined) {
            const topP = llmConfig.topP || llmConfig.top_p;
            updateParameterSlider('topP', topP);
        }

    } catch (error) {
        console.error('回填版本配置失败:', error);
    }
}

// 更新参数滑块
function updateParameterSlider(paramName, value) {
    const slider = document.getElementById(paramName + 'Slider');
    const valueDisplay = document.getElementById(paramName + 'Value');

    if (slider && valueDisplay) {
        slider.value = value;
        valueDisplay.textContent = value;
        currentParameters[paramName] = parseFloat(value);
    }
}

// 刷新版本列表
async function refreshVersionList() {
    const agent = window.currentPlaygroundAgent;
    if (!agent) {
        alert('请先选择Agent');
        return;
    }

    try {
        // 显示加载状态
        const button = event.target.closest('button');
        const originalHtml = button.innerHTML;
        button.innerHTML = '<i class="bi bi-spinner-border spinner-border-sm"></i>';
        button.disabled = true;

        // 重新加载版本列表
        await loadAgentVersions(agent.id);

        // 恢复按钮状态
        button.innerHTML = originalHtml;
        button.disabled = false;

    } catch (error) {
        console.error('刷新版本列表失败:', error);
        alert('刷新版本列表失败: ' + error.message);
    }
}

// 显示版本切换通知
function showVersionSwitchNotification(versionNumber) {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = 'alert alert-success alert-dismissible fade show position-fixed';
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        <i class="bi bi-check-circle me-2"></i>
        已切换到版本 v${versionNumber}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // 添加到页面
    document.body.appendChild(notification);

    // 3秒后自动移除
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 3000);
}

// 格式化日期
function formatDate(dateString) {
    if (!dateString) return '';

    try {
        const date = new Date(dateString);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');

        return `${year}-${month}-${day} ${hours}:${minutes}`;
    } catch (error) {
        console.error('日期格式化失败:', error);
        return dateString;
    }
}

// 更新模型选择 - 复制自admin.js
function updateModel() {
    const model = document.getElementById('playgroundModelSelect').value;
    console.log('模型已切换到:', model);

    // 保存状态
    savePlaygroundState();
}

// JSON格式化显示函数
function formatJsonOutput(data) {
    try {
        // 如果data是字符串，先尝试解析为JSON
        if (typeof data === 'string') {
            try {
                const parsed = JSON.parse(data);
                const jsonString = JSON.stringify(parsed, null, 2);
                return `
                    <div class="json-output-wrapper">
                        <pre class="json-output" id="jsonOutput">${syntaxHighlight(jsonString)}</pre>
                    </div>
                `;
            } catch (parseError) {
                // 如果不是有效的JSON字符串，直接显示原始内容
                return `
                    <div class="json-output-wrapper">
                        <pre class="json-output" id="jsonOutput">${data}</pre>
                    </div>
                `;
            }
        } else {
            // 如果data是对象，直接格式化
            const jsonString = JSON.stringify(data, null, 2);
            return `
                <div class="json-output-wrapper">
                    <pre class="json-output" id="jsonOutput">${syntaxHighlight(jsonString)}</pre>
                </div>
            `;
        }
    } catch (error) {
        return `<pre class="json-output text-danger">JSON格式化失败: ${error.message}</pre>`;
    }
}

// JSON语法高亮函数
function syntaxHighlight(json) {
    if (typeof json !== 'string') {
        json = JSON.stringify(json, undefined, 2);
    }
    json = json.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;');
    return json.replace(/("(\\u[a-zA-Z0-9]{4}|\\[^u]|[^\\"])*"(\s*:)?|\b(true|false|null)\b|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?)/g, function (match) {
        let cls = 'json-number';
        if (/^"/.test(match)) {
            if (/:$/.test(match)) {
                cls = 'json-key';
            } else {
                cls = 'json-string';
            }
        } else if (/true|false/.test(match)) {
            cls = 'json-boolean';
        } else if (/null/.test(match)) {
            cls = 'json-null';
        }
        return '<span class="' + cls + '">' + match + '</span>';
    });
}

// 复制JSON结果
function copyJsonResult() {
    if (typeof clipboardUtils !== 'undefined') {
        clipboardUtils.copyElementText('jsonOutput', '结果已复制到剪贴板');
    } else {
        // 降级方案：如果剪贴板工具库未加载
        const jsonOutput = document.getElementById('jsonOutput');
        if (!jsonOutput) {
            showToast('没有找到要复制的内容', 'warning');
            return;
        }
        const text = jsonOutput.textContent || jsonOutput.innerText;
        if (!text || text.trim() === '') {
            showToast('没有可复制的内容', 'warning');
            return;
        }
        // 使用基础复制方法
        fallbackCopyText(text, '结果已复制到剪贴板');
    }
}

// 切换JSON展开/折叠
let isJsonExpanded = false;
function toggleJsonExpand() {
    const jsonWrapper = document.querySelector('.json-output-wrapper');
    const expandIcon = document.getElementById('expandIcon');

    if (jsonWrapper && expandIcon) {
        isJsonExpanded = !isJsonExpanded;

        if (isJsonExpanded) {
            jsonWrapper.style.maxHeight = 'none';
            expandIcon.className = 'bi bi-arrows-collapse';
        } else {
            jsonWrapper.style.maxHeight = '400px';
            expandIcon.className = 'bi bi-arrows-expand';
        }
    }
}

// ==================== 新增的收放和预览功能 ====================

// 切换参数面板收放状态
function toggleParameterPanel() {
    const panelBody = document.getElementById('parameterPanelBody');
    const toggleBtn = document.getElementById('parameterToggleBtn');
    const toggleIcon = toggleBtn.querySelector('i');
    const containerFluid = document.querySelector('.container-fluid');

    if (isParameterPanelCollapsed) {
        // 展开
        panelBody.style.display = 'block';
        toggleIcon.className = 'bi bi-chevron-up';
        toggleBtn.innerHTML = '<i class="bi bi-chevron-up"></i> 收起';

        // 移除收起状态的CSS类
        if (containerFluid) {
            containerFluid.classList.remove('parameter-panel-collapsed');
            containerFluid.classList.add('parameter-panel-expanded');
        }

        isParameterPanelCollapsed = false;
    } else {
        // 收起
        panelBody.style.display = 'none';
        toggleIcon.className = 'bi bi-chevron-down';
        toggleBtn.innerHTML = '<i class="bi bi-chevron-down"></i> 展开';

        // 添加收起状态的CSS类
        if (containerFluid) {
            containerFluid.classList.add('parameter-panel-collapsed');
            containerFluid.classList.remove('parameter-panel-expanded');
        }

        isParameterPanelCollapsed = true;
    }
}

// 切换文件上传面板收放状态
function toggleFileUploadPanel() {
    const fileColumn = document.getElementById('fileUploadColumn');
    const promptColumn = document.getElementById('promptColumn');
    const resultColumn = document.getElementById('resultColumn');
    const toggleBtn = document.getElementById('fileToggleBtn');
    const expandBtn = document.getElementById('fileExpandBtn');

    if (isFileUploadPanelCollapsed) {
        // 展开文件上传面板
        fileColumn.className = 'col-lg-3';
        promptColumn.className = 'col-lg-5';
        resultColumn.className = 'col-lg-4';
        fileColumn.classList.remove('file-upload-collapsed');

        // 显示收起按钮，隐藏展开按钮
        if (toggleBtn) toggleBtn.style.display = 'block';
        if (expandBtn) expandBtn.style.display = 'none';

        isFileUploadPanelCollapsed = false;
    } else {
        // 收起文件上传面板
        fileColumn.className = 'col-lg-1';
        promptColumn.className = 'col-lg-6';
        resultColumn.className = 'col-lg-5';
        fileColumn.classList.add('file-upload-collapsed');

        // 隐藏收起按钮，显示展开按钮
        if (toggleBtn) toggleBtn.style.display = 'none';
        if (expandBtn) expandBtn.style.display = 'block';

        isFileUploadPanelCollapsed = true;
    }
}

// 显示文件预览模态框
function showFilePreviewModal() {
    // 如果文件上传区域是收起状态，先展开它
    if (isFileUploadPanelCollapsed) {
        toggleFileUploadPanel();
        showToast('已自动展开文件上传区域', 'info');
        // 给一点时间让UI更新
        setTimeout(() => {
            showFilePreviewModalInternal();
        }, 300);
        return;
    }

    showFilePreviewModalInternal();
}

// 内部函数：显示文件预览模态框
function showFilePreviewModalInternal() {
    if (!selectedFile) {
        showToast('请先上传文件', 'warning');
        return;
    }

    const modal = new bootstrap.Modal(document.getElementById('filePreviewModal'));
    const modalContent = document.getElementById('filePreviewModalContent');

    generateLargeFilePreview(selectedFile, modalContent);
    modal.show();
}

// 生成大尺寸文件预览
function generateLargeFilePreview(file, container) {
    const fileType = file.type;
    const fileName = file.name;
    const fileSize = (file.size / 1024).toFixed(1);

    let previewContent = '';

    if (fileType.startsWith('image/')) {
        // 图片预览
        const imageUrl = URL.createObjectURL(file);
        previewContent = `
            <div class="text-center">
                <img src="${imageUrl}" alt="${fileName}" style="max-width: 100%; max-height: 500px; border-radius: 8px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">
                <div class="mt-3">
                    <h6>${fileName}</h6>
                    <small class="text-muted">${fileSize} KB</small>
                </div>
            </div>
        `;
    } else if (fileType === 'application/pdf') {
        // PDF预览
        const pdfUrl = URL.createObjectURL(file);
        previewContent = `
            <div class="text-center">
                <iframe src="${pdfUrl}" style="width: 100%; height: 500px; border: 1px solid #e5e7eb; border-radius: 8px;"></iframe>
                <div class="mt-3">
                    <h6>${fileName}</h6>
                    <small class="text-muted">${fileSize} KB</small>
                </div>
            </div>
        `;
    } else if (fileType === 'text/plain') {
        // 文本文件预览
        const reader = new FileReader();
        reader.onload = function(e) {
            const textContent = e.target.result;
            container.innerHTML = `
                <div>
                    <div class="mb-3 text-center">
                        <h6>${fileName}</h6>
                        <small class="text-muted">${fileSize} KB</small>
                    </div>
                    <textarea class="form-control" rows="20" readonly style="font-size: 13px; font-family: 'Consolas', 'Monaco', monospace;">${textContent}</textarea>
                </div>
            `;
        };
        reader.readAsText(file);
        return;
    } else {
        // 其他文件类型
        previewContent = `
            <div class="text-center py-5">
                <i class="bi bi-file-earmark fs-1 text-primary mb-3"></i>
                <h6>${fileName}</h6>
                <small class="text-muted">${fileSize} KB</small>
                <p class="text-muted mt-3">此文件类型不支持预览</p>
            </div>
        `;
    }

    container.innerHTML = previewContent;
}

// 显示结果对比模态框
function showResultCompareModal() {
    if (!selectedFile && !currentTestResult) {
        showToast('请先上传文件并进行测试', 'warning');
        return;
    }

    const modal = new bootstrap.Modal(document.getElementById('resultCompareModal'));
    const fileContent = document.getElementById('resultCompareFileContent');
    const outputContent = document.getElementById('resultCompareOutputContent');

    // 显示文件内容
    if (selectedFile) {
        generateLargeFilePreview(selectedFile, fileContent);
    } else {
        fileContent.innerHTML = '<p class="text-muted text-center">暂无文件</p>';
    }

    // 显示输出结果
    if (currentTestResult) {
        outputContent.innerHTML = currentTestResult;
    } else {
        outputContent.innerHTML = '<p class="text-muted text-center">暂无结果</p>';
    }

    modal.show();
}
