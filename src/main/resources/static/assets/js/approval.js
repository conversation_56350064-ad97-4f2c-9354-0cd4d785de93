// Agent审批管理页面JavaScript

// 审批页面命名空间，避免变量冲突
const ApprovalPage = {
    currentApprovalId: null,
    currentPage: 1,
    pageSize: 10
};

// 页面初始化函数
function initApprovalPage() {
    console.log('Agent审批页面初始化');

    // 确保DOM完全加载后再初始化
    setTimeout(() => {
        // 检查关键DOM元素是否存在
        const categorySelect = document.getElementById('categorySelect');
        if (!categorySelect) {
            console.warn('分类选择框元素未找到，延迟重试...');
            setTimeout(initApprovalPage, 50);
            return;
        }

        loadCategories();
        loadApprovalList();
    }, 100); // 延迟100ms确保DOM完全渲染
}

// 加载分类列表（带重试机制）
async function loadCategories(retryCount = 3) {
    const categorySelect = document.getElementById('categorySelect');

    if (!categorySelect) {
        console.warn('找不到分类选择框元素');
        return;
    }

    // 显示加载状态
    categorySelect.innerHTML = '<option value="">加载中...</option>';

    for (let attempt = 1; attempt <= retryCount; attempt++) {
        try {
            console.log(`尝试加载Agent分类 (第${attempt}次)`);
            const response = await apiCall('/api/v1/agent-categories/active');

            if (response.code === 200 && response.data) {
                categorySelect.innerHTML = '<option value="">全部分类</option>';

                response.data.forEach(category => {
                    const option = document.createElement('option');
                    option.value = category.id;
                    option.textContent = category.categoryName;
                    categorySelect.appendChild(option);
                });

                console.log(`Agent分类加载成功，共${response.data.length}个分类`);
                return; // 成功加载，退出重试循环
            } else {
                console.warn(`加载分类失败 (第${attempt}次):`, response.message);
                if (attempt === retryCount) {
                    // 最后一次尝试失败，设置默认选项
                    categorySelect.innerHTML = '<option value="">全部分类</option>';
                }
            }
        } catch (error) {
            console.error(`加载分类失败 (第${attempt}次):`, error);

            if (attempt === retryCount) {
                // 最后一次尝试失败，设置默认选项
                categorySelect.innerHTML = '<option value="">全部分类</option>';
                // 显示用户友好的错误提示
                if (typeof showToast === 'function') {
                    showToast('分类加载失败，请刷新页面重试', 'warning');
                }
            } else {
                // 等待一段时间后重试
                await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
            }
        }
    }
}

// 加载审批列表
async function loadApprovalList(page = 1) {
    try {
        ApprovalPage.currentPage = page;
        const formData = new FormData(document.getElementById('approvalQueryForm'));
        const queryParams = new URLSearchParams();

        // 添加查询参数
        for (let [key, value] of formData.entries()) {
            if (value) {
                queryParams.append(key, value);
            }
        }

        queryParams.append('current', page);
        queryParams.append('size', ApprovalPage.pageSize);
        
        const response = await apiCall(`/api/v1/agent-approval/list?${queryParams.toString()}`);
        
        if (response.code === 200) {
            renderApprovalTable(response.data);
        } else {
            showError('加载审批列表失败: ' + response.message);
        }
    } catch (error) {
        console.error('加载审批列表失败:', error);
        showError('加载审批列表失败');
    }
}

// 渲染审批表格
function renderApprovalTable(data) {
    const tableContainer = document.getElementById('approvalTable');
    
    if (!data.records || data.records.length === 0) {
        tableContainer.innerHTML = `
            <div class="text-center py-5">
                <i class="bi bi-inbox text-muted" style="font-size: 3rem;"></i>
                <div class="mt-3 text-muted">暂无审批数据</div>
            </div>
        `;
        return;
    }
    
    let tableHtml = `
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>Agent名称</th>
                        <th>编码</th>
                        <th>状态</th>
                        <th>分类</th>
                        <th>模型</th>
                        <th>订阅数</th>
                        <th>提交时间</th>
                        <th>创建者</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
    `;
    
    data.records.forEach(item => {
        const statusBadge = getApprovalStatusBadge(item.approvalStatus);
        const submitTime = item.submitTime ? formatDateTime(item.submitTime) : '-';
        
        tableHtml += `
            <tr>
                <td>
                    <div class="fw-bold">${item.agentName}</div>
                    <small class="text-muted">${item.description || ''}</small>
                </td>
                <td><code>${item.agentCode}</code></td>
                <td>${statusBadge}</td>
                <td>${item.categoryName || '-'}</td>
                <td>${item.modelInfo || '-'}</td>
                <td>${item.subscriptionCount || 0}</td>
                <td>${submitTime}</td>
                <td>${item.creatorName || '-'}</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="showApprovalDetail(${item.id})" title="查看详情">
                            <i class="bi bi-eye"></i>
                        </button>
                        ${item.approvalStatus === 1 ? `
                            <button class="btn btn-outline-success" onclick="showApprovalAction(${item.id})" title="审批">
                                <i class="bi bi-check-circle"></i>
                            </button>
                        ` : ''}
                        <button class="btn btn-outline-info" onclick="showApprovalHistory(${item.id})" title="审批历史">
                            <i class="bi bi-clock-history"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    });
    
    tableHtml += `
                </tbody>
            </table>
        </div>
    `;
    
    // 添加分页
    if (data.pages > 1) {
        tableHtml += renderPagination(data.current, data.pages, data.total);
    }
    
    tableContainer.innerHTML = tableHtml;
}

// 获取审批状态徽章
function getApprovalStatusBadge(status) {
    const statusMap = {
        1: { text: '审批中', class: 'warning' },
        2: { text: '审批通过', class: 'success' },
        3: { text: '审批不通过', class: 'danger' }
    };
    
    const statusInfo = statusMap[status] || { text: '未知', class: 'secondary' };
    return `<span class="badge bg-${statusInfo.class}">${statusInfo.text}</span>`;
}

// 渲染分页
function renderPagination(current, pages, total) {
    let paginationHtml = `
        <div class="d-flex justify-content-between align-items-center p-3 border-top">
            <div class="text-muted">
                共 ${total} 条记录，第 ${current} / ${pages} 页
            </div>
            <nav>
                <ul class="pagination pagination-sm mb-0">
    `;
    
    // 上一页
    paginationHtml += `
        <li class="page-item ${current <= 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="loadApprovalList(${current - 1})">上一页</a>
        </li>
    `;
    
    // 页码
    const startPage = Math.max(1, current - 2);
    const endPage = Math.min(pages, current + 2);
    
    for (let i = startPage; i <= endPage; i++) {
        paginationHtml += `
            <li class="page-item ${i === current ? 'active' : ''}">
                <a class="page-link" href="#" onclick="loadApprovalList(${i})">${i}</a>
            </li>
        `;
    }
    
    // 下一页
    paginationHtml += `
        <li class="page-item ${current >= pages ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="loadApprovalList(${current + 1})">下一页</a>
        </li>
    `;
    
    paginationHtml += `
                </ul>
            </nav>
        </div>
    `;
    
    return paginationHtml;
}

// 查询审批
function searchApprovals() {
    loadApprovalList(1);
}

// 重置查询
function resetApprovalQuery() {
    document.getElementById('approvalQueryForm').reset();
    loadApprovalList(1);
}

// 刷新列表
function refreshApprovalList() {
    loadApprovalList(ApprovalPage.currentPage);
}

// 显示审批详情
async function showApprovalDetail(agentId) {
    try {
        const response = await apiCall(`/api/v1/agent-approval/detail/${agentId}`);
        
        if (response.code === 200) {
            renderApprovalDetail(response.data);
            new bootstrap.Modal(document.getElementById('approvalDetailModal')).show();
        } else {
            showError('获取审批详情失败: ' + response.message);
        }
    } catch (error) {
        console.error('获取审批详情失败:', error);
        showError('获取审批详情失败');
    }
}

// 渲染审批详情
function renderApprovalDetail(data) {
    const content = document.getElementById('approvalDetailContent');
    
    let detailHtml = `
        <!-- 基本信息 -->
        <div class="row mb-4">
            <div class="col-12">
                <h6 class="border-bottom pb-2 mb-3">基本信息</h6>
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>Agent名称：</strong>${data.basicInfo?.agentName || '-'}</p>
                        <p><strong>Agent编码：</strong><code>${data.basicInfo?.agentCode || '-'}</code></p>
                        <p><strong>分类：</strong>${data.basicInfo?.categoryName || '-'}</p>
                        <p><strong>版本：</strong>${data.basicInfo?.version || '-'}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>创建者：</strong>${data.basicInfo?.creatorName || '-'}</p>
                        <p><strong>创建时间：</strong>${data.basicInfo?.createTime || '-'}</p>
                        <p><strong>描述：</strong>${data.basicInfo?.description || '-'}</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 使用统计 -->
        <div class="row mb-4">
            <div class="col-12">
                <h6 class="border-bottom pb-2 mb-3">使用统计</h6>
                <div class="row">
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-primary">${data.usageStats?.totalCalls || 0}</h5>
                                <p class="card-text">总调用次数</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-success">${data.usageStats?.successRate?.toFixed(1) || 0}%</h5>
                                <p class="card-text">成功率</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-info">${data.usageStats?.avgResponseTime?.toFixed(1) || 0}s</h5>
                                <p class="card-text">平均响应时间</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-warning">${data.usageStats?.subscriptionCount || 0}</h5>
                                <p class="card-text">订阅用户数</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 配置信息 -->
        <div class="row mb-4">
            <div class="col-12">
                <h6 class="border-bottom pb-2 mb-3">配置信息</h6>
                <div class="mb-3">
                    <label class="form-label fw-bold">模型配置</label>
                    <pre class="bg-light p-3 rounded"><code>${data.configInfo?.modelConfig || '{}'}</code></pre>
                </div>
                <div class="mb-3">
                    <label class="form-label fw-bold">提示词模板</label>
                    <textarea class="form-control" rows="4" readonly>${data.configInfo?.promptTemplate || ''}</textarea>
                </div>
            </div>
        </div>
    `;
    
    content.innerHTML = detailHtml;
}

// 显示审批操作
async function showApprovalAction(agentId) {
    ApprovalPage.currentApprovalId = agentId;
    
    try {
        const response = await apiCall(`/api/v1/agent-approval/detail/${agentId}`);
        
        if (response.code === 200) {
            renderApprovalAction(response.data);
            new bootstrap.Modal(document.getElementById('approvalActionModal')).show();
        } else {
            showError('获取Agent信息失败: ' + response.message);
        }
    } catch (error) {
        console.error('获取Agent信息失败:', error);
        showError('获取Agent信息失败');
    }
}

// 渲染审批操作
function renderApprovalAction(data) {
    const agentInfo = document.getElementById('approvalAgentInfo');
    
    agentInfo.innerHTML = `
        <div class="card">
            <div class="card-body">
                <h6 class="card-title">${data.basicInfo?.agentName || '-'}</h6>
                <p class="card-text">
                    <strong>编码：</strong><code>${data.basicInfo?.agentCode || '-'}</code><br>
                    <strong>分类：</strong>${data.basicInfo?.categoryName || '-'}<br>
                    <strong>创建者：</strong>${data.basicInfo?.creatorName || '-'}<br>
                    <strong>描述：</strong>${data.basicInfo?.description || '-'}
                </p>
            </div>
        </div>
    `;
    
    // 清空审批意见
    document.getElementById('approvalOpinion').value = '';
}

// 提交审批
async function submitApproval(approved) {
    if (!ApprovalPage.currentApprovalId) {
        showError('请选择要审批的Agent');
        return;
    }
    
    const opinion = document.getElementById('approvalOpinion').value.trim();
    
    if (!opinion) {
        showError('请输入审批意见');
        return;
    }
    
    const confirmMessage = approved ? '确定要审批通过吗？' : '确定要审批不通过吗？';
    if (!confirm(confirmMessage)) {
        return;
    }
    
    try {
        const response = await apiCall('/api/v1/agent-approval/approve', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                agentId: ApprovalPage.currentApprovalId,
                approved: approved,
                opinion: opinion
            })
        });
        
        if (response.code === 200) {
            showSuccess('审批成功');
            bootstrap.Modal.getInstance(document.getElementById('approvalActionModal')).hide();
            loadApprovalList(ApprovalPage.currentPage);
        } else {
            showError('审批失败: ' + response.message);
        }
    } catch (error) {
        console.error('审批失败:', error);
        showError('审批失败');
    }
}

// 显示审批历史
async function showApprovalHistory(agentId) {
    try {
        const response = await apiCall(`/api/v1/agent-approval/history/${agentId}`);
        
        if (response.code === 200) {
            renderApprovalHistory(response.data);
            new bootstrap.Modal(document.getElementById('approvalHistoryModal')).show();
        } else {
            showError('获取审批历史失败: ' + response.message);
        }
    } catch (error) {
        console.error('获取审批历史失败:', error);
        showError('获取审批历史失败');
    }
}

// 渲染审批历史
function renderApprovalHistory(data) {
    const content = document.getElementById('approvalHistoryContent');
    
    if (!data || data.length === 0) {
        content.innerHTML = `
            <div class="text-center py-4">
                <i class="bi bi-clock-history text-muted" style="font-size: 2rem;"></i>
                <div class="mt-2 text-muted">暂无审批历史</div>
            </div>
        `;
        return;
    }
    
    let historyHtml = '<div class="timeline">';
    
    data.forEach((record, index) => {
        const statusBadge = getApprovalStatusBadge(record.approvalStatus);
        const approvalTime = record.approvalTime ? formatDateTime(record.approvalTime) : '-';
        
        historyHtml += `
            <div class="timeline-item ${index === 0 ? 'timeline-item-current' : ''}">
                <div class="timeline-marker"></div>
                <div class="timeline-content">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <div>
                            <h6 class="mb-1">${record.approverName || '-'}</h6>
                            <small class="text-muted">${approvalTime}</small>
                        </div>
                        ${statusBadge}
                    </div>
                    ${record.approvalOpinion ? `
                        <div class="mt-2">
                            <strong>审批意见：</strong>
                            <p class="mb-0">${record.approvalOpinion}</p>
                        </div>
                    ` : ''}
                </div>
            </div>
        `;
    });
    
    historyHtml += '</div>';
    
    content.innerHTML = historyHtml;
}

// 导出审批列表
function exportApprovalList() {
    showInfo('导出功能开发中...');
}

// 格式化日期时间
function formatDateTime(dateTimeStr) {
    if (!dateTimeStr) return '-';
    const date = new Date(dateTimeStr);
    return date.toLocaleString('zh-CN');
}
