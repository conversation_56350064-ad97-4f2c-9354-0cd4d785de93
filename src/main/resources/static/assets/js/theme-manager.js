/**
 * SinoairAgent 主题管理系统
 * 企业级多主题切换功能
 */

class ThemeManager {
    constructor() {
        this.currentTheme = localStorage.getItem('theme') || 'default';
        this.themes = {
            'default': {
                name: '默认主题',
                description: '经典深色主题，专业稳重',
                class: '',
                icon: '🌙'
            },
            'light': {
                name: '浅色主题',
                description: '清新明亮，护眼舒适',
                class: 'theme-light',
                icon: '☀️'
            },
            'dark': {
                name: '深色主题',
                description: '深邃优雅，专注工作',
                class: 'theme-dark',
                icon: '🌚'
            },
            'tech-blue': {
                name: '科技蓝',
                description: '科技感十足，现代简约',
                class: 'theme-tech-blue',
                icon: '💙'
            },
            'business-green': {
                name: '商务绿',
                description: '商务专业，稳重可靠',
                class: 'theme-business-green',
                icon: '💚'
            }
        };
        this.init();
    }

    /**
     * 初始化主题管理器
     */
    init() {
        this.applyTheme(this.currentTheme);
        this.createThemeSwitcher();
        this.bindEvents();
        
        // 监听系统主题变化
        if (window.matchMedia) {
            window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
                if (this.currentTheme === 'auto') {
                    this.applySystemTheme();
                }
            });
        }
    }

    /**
     * 应用主题
     * @param {string} themeId 主题ID
     */
    applyTheme(themeId) {
        const theme = this.themes[themeId];
        if (!theme) return;

        const body = document.body;
        const html = document.documentElement;

        // 移除所有主题类
        Object.values(this.themes).forEach(t => {
            if (t.class) {
                body.classList.remove(t.class);
                html.classList.remove(t.class);
            }
        });

        // 应用新主题
        if (theme.class) {
            body.classList.add(theme.class);
            html.classList.add(theme.class);
        }

        this.currentTheme = themeId;
        localStorage.setItem('theme', themeId);

        // 触发主题变更事件
        window.dispatchEvent(new CustomEvent('themeChanged', {
            detail: { theme: themeId, themeData: theme }
        }));

        console.log(`🎨 主题已切换为: ${theme.name}`);
    }

    /**
     * 创建主题切换器UI
     */
    createThemeSwitcher() {
        // 检查是否已存在主题切换器
        if (document.getElementById('themeSwitcher')) return;

        const switcher = document.createElement('div');
        switcher.id = 'themeSwitcher';
        switcher.className = 'theme-switcher';
        switcher.innerHTML = this.generateSwitcherHTML();

        // 添加到页面
        document.body.appendChild(switcher);
    }

    /**
     * 生成主题切换器HTML
     */
    generateSwitcherHTML() {
        const currentTheme = this.themes[this.currentTheme];
        
        return `
            <div class="theme-switcher-toggle" id="themeSwitcherToggle" title="切换主题">
                <span class="theme-icon">${currentTheme.icon}</span>
                <span class="theme-text">主题</span>
            </div>
            <div class="theme-switcher-dropdown" id="themeSwitcherDropdown">
                <div class="theme-switcher-header">
                    <h6>选择主题</h6>
                </div>
                <div class="theme-options">
                    ${Object.entries(this.themes).map(([id, theme]) => `
                        <div class="theme-option ${id === this.currentTheme ? 'active' : ''}" 
                             data-theme="${id}" title="${theme.description}">
                            <div class="theme-preview">
                                <span class="theme-icon">${theme.icon}</span>
                            </div>
                            <div class="theme-info">
                                <div class="theme-name">${theme.name}</div>
                                <div class="theme-desc">${theme.description}</div>
                            </div>
                            ${id === this.currentTheme ? '<i class="bi bi-check-circle-fill theme-check"></i>' : ''}
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 主题切换器切换
        document.addEventListener('click', (e) => {
            const toggle = e.target.closest('#themeSwitcherToggle');
            const dropdown = document.getElementById('themeSwitcherDropdown');
            
            if (toggle) {
                dropdown.classList.toggle('show');
                return;
            }

            // 点击主题选项
            const option = e.target.closest('.theme-option');
            if (option) {
                const themeId = option.dataset.theme;
                this.switchTheme(themeId);
                dropdown.classList.remove('show');
                return;
            }

            // 点击其他地方关闭下拉菜单
            if (!e.target.closest('.theme-switcher')) {
                dropdown?.classList.remove('show');
            }
        });

        // ESC键关闭下拉菜单
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                document.getElementById('themeSwitcherDropdown')?.classList.remove('show');
            }
        });
    }

    /**
     * 切换主题
     * @param {string} themeId 主题ID
     */
    switchTheme(themeId) {
        if (themeId === this.currentTheme) return;

        this.applyTheme(themeId);
        this.updateSwitcherUI();
        
        // 显示切换成功提示
        this.showThemeChangeNotification(this.themes[themeId]);
    }

    /**
     * 更新切换器UI
     */
    updateSwitcherUI() {
        const currentTheme = this.themes[this.currentTheme];
        const toggle = document.getElementById('themeSwitcherToggle');
        const options = document.querySelectorAll('.theme-option');

        if (toggle) {
            const icon = toggle.querySelector('.theme-icon');
            if (icon) icon.textContent = currentTheme.icon;
        }

        options.forEach(option => {
            const themeId = option.dataset.theme;
            const check = option.querySelector('.theme-check');
            
            if (themeId === this.currentTheme) {
                option.classList.add('active');
                if (!check) {
                    option.insertAdjacentHTML('beforeend', '<i class="bi bi-check-circle-fill theme-check"></i>');
                }
            } else {
                option.classList.remove('active');
                if (check) check.remove();
            }
        });
    }

    /**
     * 显示主题切换通知
     * @param {object} theme 主题对象
     */
    showThemeChangeNotification(theme) {
        // 如果存在showAlert函数，使用它
        if (typeof showAlert === 'function') {
            showAlert(`主题已切换为：${theme.name}`, 'success', 2000);
            return;
        }

        // 否则创建简单的通知
        const notification = document.createElement('div');
        notification.className = 'theme-notification';
        notification.innerHTML = `
            <div class="theme-notification-content">
                <span class="theme-notification-icon">${theme.icon}</span>
                <span class="theme-notification-text">已切换为 ${theme.name}</span>
            </div>
        `;

        document.body.appendChild(notification);

        // 显示动画
        setTimeout(() => notification.classList.add('show'), 100);

        // 自动移除
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => notification.remove(), 300);
        }, 2000);
    }

    /**
     * 获取当前主题
     */
    getCurrentTheme() {
        return {
            id: this.currentTheme,
            ...this.themes[this.currentTheme]
        };
    }

    /**
     * 获取所有主题
     */
    getAllThemes() {
        return this.themes;
    }
}

// 全局主题管理器实例
window.themeManager = new ThemeManager();

// 导出供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ThemeManager;
}
