<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能体矩阵 管理后台</title>
    <link href="assets/css/bootstrap.min.css" rel="stylesheet">
    <link href="assets/css/bootstrap-icons.css" rel="stylesheet">
    <link href="assets/css/summernote-bs5.min.css" rel="stylesheet">
    <link href="assets/css/admin-common.css" rel="stylesheet">
</head>
<body>
    <div class="admin-container">
        <!-- 侧边栏 -->
        <nav class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <!-- Logo区域 -->
                <div class="sidebar-item logo-section">
                    <div class="sidebar-item-icon logo-icon">
                        <i class="bi bi-robot"></i>
                    </div>
                    <div class="sidebar-item-text logo-text sidebar-text">
                        <h5 class="text-light mb-0">智能体矩阵</h5>
                        <small class="text-light">管理后台</small>
                    </div>
                </div>

<!--                &lt;!&ndash; 状态指示器 &ndash;&gt;-->
<!--                <div class="sidebar-item status-section">-->
<!--                    <div class="sidebar-item-icon">-->
<!--                        <div class="status-indicator bg-success"></div>-->
<!--                    </div>-->
<!--                    <div class="sidebar-item-text status-text sidebar-text">-->
<!--                        <small class="text-light">系统运行中</small>-->
<!--                    </div>-->
<!--                </div>-->
            </div>

            <!-- 导航菜单 -->
            <div class="sidebar-nav">
                <!-- 权限加载提示 -->
                <div class="permission-loading-indicator">
                    <i class="bi bi-hourglass-split"></i> 正在加载菜单权限...
                </div>

                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link active sidebar-item" href="#" onclick="loadPage('dashboard')" title="仪表板" data-permission="DASHBOARD_VIEW">
                            <div class="sidebar-item-icon">
                                <i class="bi bi-grid-1x2"></i>
                            </div>
                            <div class="sidebar-item-text">
                                <span class="sidebar-text">仪表板</span>
                            </div>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link sidebar-item" href="#" onclick="loadPage('agents')" title="Agent管理" data-permission="AGENT_VIEW">
                            <div class="sidebar-item-icon">
                                <i class="bi bi-robot"></i>
                            </div>
                            <div class="sidebar-item-text">
                                <span class="sidebar-text">Agent管理</span>
                            </div>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link sidebar-item" href="#" onclick="loadPage('playground')" title="智能调试台" data-permission="PLAYGROUND_VIEW">
                            <div class="sidebar-item-icon">
                                <i class="bi bi-play-circle"></i>
                            </div>
                            <div class="sidebar-item-text">
                                <span class="sidebar-text">智能调试台</span>
                            </div>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link sidebar-item" href="#" onclick="loadPage('templates')" title="业务模板" data-permission="TEMPLATE_VIEW">
                            <div class="sidebar-item-icon">
                                <i class="bi bi-file-code"></i>
                            </div>
                            <div class="sidebar-item-text">
                                <span class="sidebar-text">业务模板</span>
                            </div>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link sidebar-item" href="#" onclick="loadPage('smart-form-config')" title="智能表单配置" data-permission="business:smart-form">
                            <div class="sidebar-item-icon">
                                <i class="bi bi-magic"></i>
                            </div>
                            <div class="sidebar-item-text">
                                <span class="sidebar-text">智能表单配置</span>
                            </div>
                        </a>
                    </li>
                    <!-- 文档识别菜单已隐藏 -->

                    <li class="nav-item">
                        <a class="nav-link sidebar-item" href="#" onclick="loadPage('recognition')" title="文档识别" data-permission="RECOGNITION_VIEW">
                            <div class="sidebar-item-icon">
                                <i class="bi bi-file-earmark-text"></i>
                            </div>
                            <div class="sidebar-item-text">
                                <span class="sidebar-text">文档识别</span>
                            </div>
                        </a>
                    </li>

                    <!-- Agent审核菜单已隐藏 -->
                    <!--
                    <li class="nav-item">
                        <a class="nav-link sidebar-item" href="#" onclick="loadPage('approval')" title="Agent审核" data-permission="AGENT_APPROVAL_VIEW">
                            <div class="sidebar-item-icon">
                                <i class="bi bi-check-circle"></i>
                            </div>
                            <div class="sidebar-item-text">
                                <span class="sidebar-text">Agent审核</span>
                            </div>
                        </a>
                    </li>
                    -->
                    <li class="nav-item">
                        <a class="nav-link sidebar-item" href="#" onclick="loadPage('agent-approval-list')" title="Agent审批列表" data-permission="AGENT_APPROVAL_VIEW">
                            <div class="sidebar-item-icon">
                                <i class="bi bi-list-check"></i>
                            </div>
                            <div class="sidebar-item-text">
                                <span class="sidebar-text">Agent审批列表</span>
                            </div>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link sidebar-item" href="#" onclick="loadPage('users')" title="用户管理" data-permission="USER_VIEW">
                            <div class="sidebar-item-icon">
                                <i class="bi bi-people"></i>
                            </div>
                            <div class="sidebar-item-text">
                                <span class="sidebar-text">用户管理</span>
                            </div>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link sidebar-item" href="#" onclick="loadPage('roles')" title="角色管理" data-permission="ROLE_VIEW">
                            <div class="sidebar-item-icon">
                                <i class="bi bi-person-badge"></i>
                            </div>
                            <div class="sidebar-item-text">
                                <span class="sidebar-text">角色管理</span>
                            </div>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link sidebar-item" href="#" onclick="loadPage('permissions')" title="权限管理" data-permission="PERMISSION_VIEW">
                            <div class="sidebar-item-icon">
                                <i class="bi bi-shield-check"></i>
                            </div>
                            <div class="sidebar-item-text">
                                <span class="sidebar-text">权限管理</span>
                            </div>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link sidebar-item" href="#" onclick="loadPage('operation-logs')" title="操作日志" data-permission="LOG_VIEW">
                            <div class="sidebar-item-icon">
                                <i class="bi bi-journal-text"></i>
                            </div>
                            <div class="sidebar-item-text">
                                <span class="sidebar-text">操作日志</span>
                            </div>
                        </a>
                    </li>
                </ul>
            </div>
        </nav>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 顶部导航栏 -->
            <header class="top-navbar">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="d-flex align-items-center">
                        <!-- 侧边栏切换按钮 -->
                        <button class="btn btn-link sidebar-toggle" onclick="toggleSidebarCollapse()" title="收起/展开侧边栏">
                            <i class="bi bi-list" id="sidebarToggleIcon"></i>
                        </button>
                        <h4 class="mb-0" id="page-title">仪表板</h4>
                    </div>
                    <div class="d-flex align-items-center gap-3">
                        <!-- 主题切换按钮 -->
                        <div class="theme-toggle">
                            <button class="btn btn-link p-2" onclick="toggleTheme()" title="切换主题" id="themeToggleBtn">
                                <i class="bi bi-sun-fill" id="themeIcon"></i>
                            </button>
                        </div>

                        <!-- 消息通知按钮 -->
                        <div class="message-notification">
                            <button class="btn btn-link p-2 position-relative" onclick="toggleMessageSidebar()" title="消息通知" id="messageToggleBtn">
                                <i class="bi bi-bell"></i>
                                <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" id="unreadBadge" style="display: none;">
                                    0
                                </span>
                            </button>
                        </div>

                        <!-- 用户下拉菜单 -->
                        <div class="dropdown">
                            <button class="btn btn-link dropdown-toggle" type="button" data-bs-toggle="dropdown" id="userDropdownButton">
                                <i class="bi bi-person-circle"></i> <span id="currentUserDisplay">加载中...</span>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" onclick="showUserProfile()"><i class="bi bi-person"></i> 个人资料</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#" onclick="logout()"><i class="bi bi-box-arrow-right"></i> 退出登录</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </header>

            <!-- 动态内容区域 -->
            <div id="content-container" class="content-container">
                <!-- 页面内容将在这里动态加载 -->
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-3 text-muted">正在加载页面...</p>
                </div>
            </div>
        </main>
    </div>

    <!-- 消息侧边栏 -->
    <div class="message-sidebar" id="messageSidebar">
        <div class="message-sidebar-header">
            <h5 class="mb-0">
                <i class="bi bi-bell me-2"></i>系统消息
            </h5>
            <div class="d-flex gap-2">
                <button class="btn btn-sm btn-outline-primary" onclick="markAllMessagesAsRead()" title="全部已读">
                    <i class="bi bi-check-all"></i>
                </button>
                <button class="btn btn-sm btn-outline-secondary" onclick="toggleMessageSidebar()" title="关闭">
                    <i class="bi bi-x"></i>
                </button>
            </div>
        </div>
        <div class="message-sidebar-body" id="messageList">
            <div class="text-center py-4">
                <div class="spinner-border spinner-border-sm text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <p class="mt-2 text-muted">正在加载消息...</p>
            </div>
        </div>
    </div>

    <!-- 消息侧边栏遮罩 -->
    <div class="message-sidebar-overlay" id="messageSidebarOverlay" onclick="toggleMessageSidebar()"></div>

    <!-- 公共模态框容器 -->
    <div id="modal-container"></div>

    <!-- jQuery -->
    <script src="assets/js/jquery-3.7.1.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="assets/js/bootstrap.bundle.min.js"></script>
    <!-- Summernote JS -->
    <script src="assets/js/summernote-bs5.min.js"></script>
    <!-- Summernote 中文语言包 -->
    <script src="assets/js/summernote-zh-CN.min.js"></script>
    <!-- 公共JS -->
    <script src="assets/js/admin-common.js"></script>
    <!-- 剪贴板工具库 -->
    <script src="assets/js/clipboard-utils.js"></script>
    <!-- 权限管理JS -->
    <script src="assets/js/permission-manager.js"></script>
    <!-- 主框架JS -->
    <script src="assets/js/admin.js"></script>

    <!-- 权限控制样式 -->
    <style>
        /* 主题变量定义 */
        :root {
            /* 浅色主题变量 */
            --dark-text: #181C32;
            --medium-text: #5E6278;
            --light-text: #A1A5B7;
            --border-color: #E4E6EA;
            --background-color: #F9F9F9;
            --card-background: #FFFFFF;
            --primary-color: #009EF7;
            --success-color: #50CD89;
            --warning-color: #FFC700;
            --danger-color: #F1416C;
            --info-color: #7239EA;

            /* 阴影系统 */
            --light-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            --normal-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            --deep-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
            --hover-shadow: 0 0.5rem 2rem rgba(0, 0, 0, 0.1);

            /* 主题过渡 */
            --theme-transition: all 0.3s ease;
        }

        /* 深色主题变量 */
        .theme-dark {
            --dark-text: #FFFFFF;
            --medium-text: #A1A5B7;
            --light-text: #5E6278;
            --border-color: #2B2B40;
            --background-color: #1B1B29;
            --card-background: #1E1E2D;

            /* 深色主题侧边栏 */
            --sidebar-background: #1A1A27;
            --sidebar-text: #FFFFFF;
            --sidebar-text-secondary: #A1A5B7;
            --sidebar-hover: #2B2B40;

            /* Bootstrap表格变量 - 深色主题 */
            --bs-table-bg: #1E1E2D;
            --bs-table-accent-bg: #2B2B40;
            --bs-table-striped-bg: #252532;
            --bs-table-active-bg: #2B2B40;
            --bs-table-hover-bg: #252532;
            --bs-table-border-color: #2B2B40;
            --bs-table-color: #FFFFFF;

            /* Bootstrap按钮变量 - 深色主题 */
            --bs-btn-bg: #1E1E2D;
            --bs-btn-border-color: #2B2B40;
            --bs-btn-color: #FFFFFF;
            --bs-btn-hover-bg: #2B2B40;
            --bs-btn-hover-border-color: #3B3B52;
            --bs-btn-hover-color: #FFFFFF;

            /* Bootstrap下拉菜单变量 - 深色主题 */
            --bs-dropdown-bg: #1E1E2D;
            --bs-dropdown-border-color: #2B2B40;
            --bs-dropdown-color: #FFFFFF;
            --bs-dropdown-link-color: #FFFFFF;
            --bs-dropdown-link-hover-bg: #2B2B40;
            --bs-dropdown-link-hover-color: #FFFFFF;

            /* 深色主题阴影 */
            --light-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.3);
            --normal-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.4);
            --deep-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.5);
            --hover-shadow: 0 0.5rem 2rem rgba(0, 0, 0, 0.3);
        }

        /* 浅色主题侧边栏变量 */
        :root {
            --sidebar-background: #FFFFFF;
            --sidebar-text: #181C32;
            --sidebar-text-secondary: #5E6278;
            --sidebar-hover: #F9F9F9;

            /* Bootstrap表格变量 - 浅色主题 */
            --bs-table-bg: #FFFFFF;
            --bs-table-accent-bg: #F9F9F9;
            --bs-table-striped-bg: #F9F9F9;
            --bs-table-active-bg: #E9ECEF;
            --bs-table-hover-bg: #F5F5F5;
            --bs-table-border-color: #E4E6EA;
            --bs-table-color: #181C32;

            /* Bootstrap按钮变量 - 浅色主题 */
            --bs-btn-bg: #FFFFFF;
            --bs-btn-border-color: #E4E6EA;
            --bs-btn-color: #181C32;
            --bs-btn-hover-bg: #F9F9F9;
            --bs-btn-hover-border-color: #D1D5DB;
            --bs-btn-hover-color: #181C32;

            /* Bootstrap下拉菜单变量 - 浅色主题 */
            --bs-dropdown-bg: #FFFFFF;
            --bs-dropdown-border-color: #E4E6EA;
            --bs-dropdown-color: #181C32;
            --bs-dropdown-link-color: #181C32;
            --bs-dropdown-link-hover-bg: #F9F9F9;
            --bs-dropdown-link-hover-color: #181C32;
        }

        /* 权限控制相关样式 */
        .nav-item.permission-hidden {
            display: none !important;
        }

        .nav-item.permission-loading {
            opacity: 0.5;
        }

        /* 初始状态：隐藏所有菜单项，等待权限加载完成 */
        .sidebar .nav-item {
            display: none !important;
        }

        /* 权限加载完成后显示有权限的菜单项 */
        .sidebar.permissions-loaded .nav-item {
            display: block !important;
        }

        /* 无权限的菜单项始终隐藏 */
        .sidebar.permissions-loaded .nav-item.permission-hidden {
            display: none !important;
        }

        /* 权限加载中的提示 */
        .sidebar .permission-loading-indicator {
            display: block;
            text-align: center;
            padding: 20px;
            color: #6c757d;
            font-size: 14px;
        }

        .sidebar.permissions-loaded .permission-loading-indicator {
            display: none;
        }

        /* 确保侧边栏本身可见 */
        .sidebar {
            opacity: 1 !important;
            visibility: visible !important;
        }

        /* 主题过渡效果 */
        body, .main-content, .top-navbar, .content-container, .card, .modal-content, .dropdown-menu {
            transition: var(--theme-transition);
        }

        /* 主内容区域主题适配 */
        .main-content {
            background-color: var(--background-color) !important;
            color: var(--dark-text) !important;
        }

        .top-navbar {
            background-color: var(--card-background) !important;
            border-bottom: 1px solid var(--border-color) !important;
            color: var(--dark-text) !important;
        }

        .content-container {
            background-color: var(--background-color) !important;
            color: var(--dark-text) !important;
        }

        /* 卡片和模态框主题适配 */
        .card, .modal-content {
            background-color: var(--card-background) !important;
            border-color: var(--border-color) !important;
            color: var(--dark-text) !important;
        }

        /* 下拉菜单主题适配 */
        .dropdown-menu {
            background-color: var(--card-background) !important;
            border-color: var(--border-color) !important;
            color: var(--dark-text) !important;
        }

        .dropdown-item {
            color: var(--dark-text) !important;
        }

        .dropdown-item:hover {
            background-color: var(--background-color) !important;
            color: var(--dark-text) !important;
        }

        /* 表单元素主题适配 */
        .form-control, .form-select {
            background-color: var(--card-background) !important;
            border-color: var(--border-color) !important;
            color: var(--dark-text) !important;
        }

        .form-control:focus, .form-select:focus {
            background-color: var(--card-background) !important;
            border-color: var(--primary-color) !important;
            color: var(--dark-text) !important;
            box-shadow: 0 0 0 0.2rem rgba(0, 158, 247, 0.25) !important;
        }

        /* 按钮主题适配 */
        .btn-link {
            color: var(--dark-text) !important;
        }

        /* 文本颜色适配 */
        .text-muted {
            color: var(--medium-text) !important;
        }

        /* 边框颜色适配 */
        .border, .border-top, .border-bottom, .border-left, .border-right {
            border-color: var(--border-color) !important;
        }

        /* 标题元素主题适配 */
        h1, h2, h3, h4, h5, h6,
        .h1, .h2, .h3, .h4, .h5, .h6 {
            color: var(--dark-text) !important;
        }

        /* 段落和文本元素主题适配 */
        p, span, div, label, small {
            color: var(--dark-text) !important;
        }

        /* 卡片标题和内容主题适配 */
        .card-header, .card-title, .card-subtitle {
            background-color: var(--card-background) !important;
            border-color: var(--border-color) !important;
            color: var(--dark-text) !important;
        }

        .card-body, .card-footer {
            background-color: var(--card-background) !important;
            color: var(--dark-text) !important;
        }

        /* 统计卡片主题适配 */
        .stats-card {
            background-color: var(--card-background) !important;
            border: 1px solid var(--border-color) !important;
            color: var(--dark-text) !important;
        }

        .stats-card h3, .stats-card p {
            color: var(--dark-text) !important;
        }

        /* 列表和列表项主题适配 */
        .list-group, .list-group-item {
            background-color: var(--card-background) !important;
            border-color: var(--border-color) !important;
            color: var(--dark-text) !important;
        }

        /* 表格主题适配 */
        .table {
            --bs-table-bg: var(--card-background) !important;
            --bs-table-color: var(--dark-text) !important;
            --bs-table-border-color: var(--border-color) !important;
            --bs-table-striped-bg: var(--background-color) !important;
            --bs-table-hover-bg: var(--background-color) !important;
            --bs-table-active-bg: var(--background-color) !important;
            background-color: var(--card-background) !important;
            color: var(--dark-text) !important;
        }

        .table th, .table td {
            border-color: var(--border-color) !important;
            color: var(--dark-text) !important;
            background-color: inherit !important;
        }

        .table thead th {
            background-color: var(--background-color) !important;
            color: var(--dark-text) !important;
            border-color: var(--border-color) !important;
        }

        .table-striped > tbody > tr:nth-of-type(odd) > td,
        .table-striped > tbody > tr:nth-of-type(odd) > th {
            --bs-table-accent-bg: var(--background-color) !important;
            background-color: var(--background-color) !important;
        }

        .table-hover > tbody > tr:hover > td,
        .table-hover > tbody > tr:hover > th {
            --bs-table-hover-bg: var(--background-color) !important;
            background-color: var(--background-color) !important;
        }

        /* 表格容器适配 */
        .table-responsive {
            background-color: var(--card-background) !important;
        }

        /* 导航和菜单主题适配 */
        .nav-link {
            color: var(--dark-text) !important;
        }

        .nav-link:hover, .nav-link:focus {
            color: var(--primary-color) !important;
        }

        .nav-link.active {
            color: var(--primary-color) !important;
        }

        /* 徽章和标签主题适配 */
        .badge {
            color: #FFFFFF !important;
        }

        /* 进度条主题适配 */
        .progress {
            background-color: var(--border-color) !important;
        }

        /* 警告框主题适配 */
        .alert {
            border-color: var(--border-color) !important;
        }

        /* 工具提示主题适配 */
        .tooltip-inner {
            background-color: var(--card-background) !important;
            color: var(--dark-text) !important;
            border: 1px solid var(--border-color) !important;
        }

        /* 弹出框主题适配 */
        .popover {
            background-color: var(--card-background) !important;
            border-color: var(--border-color) !important;
        }

        .popover-header {
            background-color: var(--background-color) !important;
            border-color: var(--border-color) !important;
            color: var(--dark-text) !important;
        }

        .popover-body {
            color: var(--dark-text) !important;
        }

        /* 侧边栏主题适配 */
        .sidebar {
            background: var(--sidebar-background, var(--card-background)) !important;
            border-right: 1px solid var(--border-color) !important;
            box-shadow: var(--normal-shadow) !important;
        }

        /* 移除侧边栏顶部装饰条，保持简洁 */
        .sidebar::before {
            display: none !important;
        }

        /* 侧边栏导航链接 */
        .sidebar .nav-link {
            color: var(--sidebar-text, var(--dark-text)) !important;
        }

        .sidebar .nav-link:hover {
            background-color: var(--sidebar-hover, var(--background-color)) !important;
            color: var(--primary-color) !important;
            transform: translateX(4px) !important;
        }

        .sidebar .nav-link.active {
            background: linear-gradient(135deg, var(--primary-color), #3b82f6) !important;
            color: #FFFFFF !important;
            box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3) !important;
        }

        .sidebar .nav-link i {
            color: var(--sidebar-text-secondary, var(--medium-text)) !important;
        }

        .sidebar .nav-link.active i {
            color: #FFFFFF !important;
        }

        .sidebar .nav-link:hover i {
            color: var(--primary-color) !important;
        }

        /* 侧边栏品牌区域 */
        .sidebar .brand,
        .sidebar-header,
        .logo-section,
        .status-section {
            color: var(--sidebar-text, var(--dark-text)) !important;
        }

        /* 侧边栏文本元素 */
        .sidebar-text,
        .sidebar-item-text,
        .logo-text,
        .status-text {
            color: var(--sidebar-text, var(--dark-text)) !important;
        }

        /* 侧边栏图标 */
        .sidebar-item-icon {
            color: var(--sidebar-text-secondary, var(--medium-text)) !important;
        }

        .logo-section .sidebar-item-icon {
            background: linear-gradient(135deg, var(--primary-color), #3b82f6) !important;
            color: #FFFFFF !important;
        }

        /* 状态指示器 */
        .status-indicator {
            background-color: var(--success-color) !important;
        }

        /* 收起状态下的悬停效果 */
        .sidebar.collapsed .nav-link:hover {
            background-color: var(--sidebar-hover, rgba(255, 255, 255, 0.15)) !important;
            transform: none !important;
        }

        /* 收起状态下的激活状态 */
        .sidebar.collapsed .nav-link.active {
            background: linear-gradient(135deg, var(--primary-color), #3b82f6) !important;
            box-shadow: 0 2px 8px rgba(37, 99, 235, 0.3) !important;
        }

        /* 面包屑导航主题适配 */
        .breadcrumb {
            background-color: transparent !important;
        }

        .breadcrumb-item {
            color: var(--medium-text) !important;
        }

        .breadcrumb-item.active {
            color: var(--dark-text) !important;
        }

        .breadcrumb-item a {
            color: var(--primary-color) !important;
        }

        /* 分页主题适配 */
        .page-link {
            background-color: var(--card-background) !important;
            border-color: var(--border-color) !important;
            color: var(--dark-text) !important;
        }

        .page-link:hover {
            background-color: var(--background-color) !important;
            border-color: var(--border-color) !important;
            color: var(--primary-color) !important;
        }

        .page-item.active .page-link {
            background-color: var(--primary-color) !important;
            border-color: var(--primary-color) !important;
            color: #FFFFFF !important;
        }

        /* 输入组主题适配 */
        .input-group-text {
            background-color: var(--background-color) !important;
            border-color: var(--border-color) !important;
            color: var(--dark-text) !important;
        }

        /* 选项卡主题适配 */
        .nav-tabs {
            border-color: var(--border-color) !important;
        }

        .nav-tabs .nav-link {
            border-color: transparent !important;
            color: var(--medium-text) !important;
        }

        .nav-tabs .nav-link:hover {
            border-color: var(--border-color) !important;
            color: var(--dark-text) !important;
        }

        .nav-tabs .nav-link.active {
            background-color: var(--card-background) !important;
            border-color: var(--border-color) var(--border-color) var(--card-background) !important;
            color: var(--dark-text) !important;
        }

        .tab-content {
            background-color: var(--card-background) !important;
            border-color: var(--border-color) !important;
            color: var(--dark-text) !important;
        }

        /* 图标颜色主题适配 */
        .bi, i[class*="bi-"] {
            color: inherit !important;
        }

        /* 文本颜色覆盖 */
        .text-dark {
            color: var(--dark-text) !important;
        }

        .text-secondary {
            color: var(--medium-text) !important;
        }

        .text-muted {
            color: var(--light-text) !important;
        }

        /* 强制所有文本元素使用主题颜色 */
        * {
            color: inherit;
        }

        /* 确保所有容器使用主题背景 */
        .container, .container-fluid, .row, .col, [class*="col-"] {
            color: var(--dark-text);
        }

        /* 特殊元素强制主题适配 */
        .dashboard-content,
        .dashboard-content *,
        .content-container,
        .content-container * {
            color: var(--dark-text) !important;
        }

        /* 统计数字和标题强制适配 */
        .stats-card h1, .stats-card h2, .stats-card h3, .stats-card h4, .stats-card h5, .stats-card h6,
        .card h1, .card h2, .card h3, .card h4, .card h5, .card h6,
        .card p, .card span, .card div:not(.spinner-border) {
            color: var(--dark-text) !important;
        }

        /* 加载动画保持原色 */
        .spinner-border {
            color: var(--primary-color) !important;
        }

        /* 按钮主题适配 */
        .btn {
            color: inherit !important;
        }

        .btn-outline-secondary,
        .btn-outline-light {
            --bs-btn-bg: var(--card-background) !important;
            --bs-btn-border-color: var(--border-color) !important;
            --bs-btn-color: var(--dark-text) !important;
            --bs-btn-hover-bg: var(--background-color) !important;
            --bs-btn-hover-border-color: var(--border-color) !important;
            --bs-btn-hover-color: var(--dark-text) !important;
            background-color: var(--card-background) !important;
            border-color: var(--border-color) !important;
            color: var(--dark-text) !important;
        }

        .btn-outline-secondary:hover,
        .btn-outline-light:hover {
            background-color: var(--background-color) !important;
            border-color: var(--border-color) !important;
            color: var(--dark-text) !important;
        }

        .btn-primary {
            color: #FFFFFF !important;
        }

        .btn-secondary {
            color: #FFFFFF !important;
        }

        .btn-success {
            color: #FFFFFF !important;
        }

        .btn-danger {
            color: #FFFFFF !important;
        }

        .btn-warning {
            color: #000000 !important;
        }

        .btn-info {
            color: #FFFFFF !important;
        }

        .btn-light {
            color: #000000 !important;
        }

        .btn-dark {
            color: #FFFFFF !important;
        }

        /* 下拉菜单主题适配 */
        .dropdown-menu {
            --bs-dropdown-bg: var(--card-background) !important;
            --bs-dropdown-border-color: var(--border-color) !important;
            --bs-dropdown-color: var(--dark-text) !important;
            --bs-dropdown-link-color: var(--dark-text) !important;
            --bs-dropdown-link-hover-bg: var(--background-color) !important;
            --bs-dropdown-link-hover-color: var(--dark-text) !important;
            background-color: var(--card-background) !important;
            border-color: var(--border-color) !important;
            color: var(--dark-text) !important;
        }

        .dropdown-item {
            color: var(--dark-text) !important;
        }

        .dropdown-item:hover,
        .dropdown-item:focus {
            background-color: var(--background-color) !important;
            color: var(--dark-text) !important;
        }

        .dropdown-divider {
            border-color: var(--border-color) !important;
        }

        /* 操作栏按钮组主题适配 */
        .btn-group .btn {
            background-color: var(--card-background) !important;
            border-color: var(--border-color) !important;
            color: var(--dark-text) !important;
        }

        .btn-group .btn:hover {
            background-color: var(--background-color) !important;
            border-color: var(--border-color) !important;
            color: var(--dark-text) !important;
        }

        .btn-group .btn.active {
            background-color: var(--primary-color) !important;
            border-color: var(--primary-color) !important;
            color: #FFFFFF !important;
        }

        /* 分页组件主题适配 */
        .pagination .page-link {
            --bs-pagination-bg: var(--card-background) !important;
            --bs-pagination-border-color: var(--border-color) !important;
            --bs-pagination-color: var(--dark-text) !important;
            --bs-pagination-hover-bg: var(--background-color) !important;
            --bs-pagination-hover-border-color: var(--border-color) !important;
            --bs-pagination-hover-color: var(--dark-text) !important;
            --bs-pagination-active-bg: var(--primary-color) !important;
            --bs-pagination-active-border-color: var(--primary-color) !important;
            --bs-pagination-active-color: #FFFFFF !important;
            background-color: var(--card-background) !important;
            border-color: var(--border-color) !important;
            color: var(--dark-text) !important;
        }

        .pagination .page-link:hover {
            background-color: var(--background-color) !important;
            border-color: var(--border-color) !important;
            color: var(--dark-text) !important;
        }

        .pagination .page-item.active .page-link {
            background-color: var(--primary-color) !important;
            border-color: var(--primary-color) !important;
            color: #FFFFFF !important;
        }

        /* 输入组主题适配 */
        .input-group-text {
            --bs-input-group-addon-bg: var(--background-color) !important;
            --bs-input-group-addon-border-color: var(--border-color) !important;
            --bs-input-group-addon-color: var(--dark-text) !important;
            background-color: var(--background-color) !important;
            border-color: var(--border-color) !important;
            color: var(--dark-text) !important;
        }

        /* 警告框主题适配 */
        .alert {
            border-color: var(--border-color) !important;
        }

        .alert-light {
            --bs-alert-bg: var(--background-color) !important;
            --bs-alert-color: var(--dark-text) !important;
            --bs-alert-border-color: var(--border-color) !important;
            background-color: var(--background-color) !important;
            color: var(--dark-text) !important;
            border-color: var(--border-color) !important;
        }

        /* 权限树主题适配 */
        .permission-tree {
            background-color: var(--card-background) !important;
        }

        .permission-node {
            background-color: var(--background-color) !important;
            border-color: var(--border-color) !important;
            color: var(--dark-text) !important;
        }

        .permission-node:hover {
            background-color: var(--card-background) !important;
        }

        .permission-node-header,
        .permission-node-content,
        .permission-info {
            color: var(--dark-text) !important;
        }

        .permission-toggle {
            color: var(--medium-text) !important;
        }

        .permission-toggle:hover {
            color: var(--primary-color) !important;
        }

        /* 文件上传和预览区域主题适配 */
        .file-drop-zone {
            background-color: var(--background-color) !important;
            border-color: var(--border-color) !important;
            color: var(--dark-text) !important;
        }

        .file-drop-zone:hover {
            background-color: var(--card-background) !important;
        }

        .file-preview {
            background-color: var(--card-background) !important;
            color: var(--dark-text) !important;
        }

        .file-preview textarea {
            background-color: var(--background-color) !important;
            border-color: var(--border-color) !important;
            color: var(--dark-text) !important;
        }

        /* 主题切换和消息通知按钮样式 */
        .theme-toggle .btn,
        .message-notification .btn {
            color: var(--dark-text);
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .theme-toggle .btn:hover,
        .message-notification .btn:hover {
            background-color: var(--border-color);
            transform: scale(1.1);
        }

        /* 消息侧边栏样式 */
        .message-sidebar {
            position: fixed;
            top: 0;
            right: -400px;
            width: 400px;
            height: 100vh;
            background: var(--card-background);
            border-left: 1px solid var(--border-color);
            box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
            z-index: 1050;
            transition: right 0.3s ease;
            display: flex;
            flex-direction: column;
        }

        .message-sidebar.show {
            right: 0;
        }

        .message-sidebar-header {
            padding: 1rem;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: between;
            align-items: center;
            background: var(--background-color);
        }

        .message-sidebar-body {
            flex: 1;
            overflow-y: auto;
            padding: 0;
        }

        .message-sidebar-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1040;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .message-sidebar-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        /* 消息项样式 */
        .message-item {
            padding: 1rem;
            border-bottom: 1px solid var(--border-color);
            cursor: pointer;
            transition: background-color 0.2s ease;
        }

        .message-item:hover {
            background-color: var(--background-color);
        }

        .message-item.unread {
            background-color: rgba(0, 158, 247, 0.05);
            border-left: 3px solid var(--primary-color);
        }

        .message-item .message-title {
            font-weight: 600;
            color: var(--dark-text);
            margin-bottom: 0.25rem;
        }

        .message-item .message-content {
            color: var(--medium-text);
            font-size: 0.875rem;
            margin-bottom: 0.5rem;
            line-height: 1.4;
        }

        .message-item .message-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.75rem;
            color: var(--light-text);
        }

        .message-type-badge {
            font-size: 0.7rem;
            padding: 0.2rem 0.5rem;
        }

        /* 空状态样式 */
        .empty-messages {
            text-align: center;
            padding: 3rem 1rem;
            color: var(--light-text);
        }

        .empty-messages i {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .message-sidebar {
                width: 100%;
                right: -100%;
            }
        }

        /* 修复Bootstrap .text-light 类在浅色主题下的显示问题 */
        .text-light {
            color: var(--sidebar-text, var(--dark-text)) !important;
        }

        /* 确保Logo文字在所有情况下都可见 */
        .logo-text,
        .sidebar .text-light {
            color: var(--sidebar-text, var(--dark-text)) !important;
        }

        /* 修复侧边栏中所有使用.text-light的元素 */
        .sidebar .text-light,
        .sidebar-header .text-light,
        .logo-section .text-light {
            color: var(--sidebar-text, var(--dark-text)) !important;
        }
    </style>

    <!-- 权限控制脚本 -->
    <script>
        // 权限控制管理器
        class MenuPermissionController {
            constructor() {
                this.userPermissions = [];
                this.permissionsLoaded = false;
                // 不在构造函数中调用init，由外部调用
            }

            async init() {
                console.log('🔧 初始化菜单权限控制器...');

                try {
                    // 立即加载权限
                    await this.loadPermissions();

                    // 应用权限控制
                    this.applyPermissions();

                    console.log('✅ 菜单权限控制器初始化完成');
                } catch (error) {
                    console.error('❌ 菜单权限控制器初始化失败:', error);

                    // 即使失败也要显示默认权限的菜单
                    this.userPermissions = this.getDefaultPermissions();
                    this.permissionsLoaded = true;
                    this.applyPermissions();
                }
            }

            async loadPermissions() {
                try {
                    console.log('🔄 开始加载用户权限...');

                    // 获取认证token
                    const token = localStorage.getItem('authToken') || sessionStorage.getItem('token');
                    if (!token) {
                        console.warn('⚠️ 未找到认证token，使用默认权限');
                        this.userPermissions = this.getDefaultPermissions();
                        this.permissionsLoaded = true;
                        return;
                    }

                    // 设置较短的超时时间，避免长时间等待
                    const controller = new AbortController();
                    const timeoutId = setTimeout(() => controller.abort(), 5000); // 5秒超时

                    try {
                        const response = await fetch('/api/v1/user/permissions', {
                            method: 'GET',
                            headers: {
                                'Authorization': 'Bearer ' + token,
                                'Content-Type': 'application/json'
                            },
                            signal: controller.signal
                        });

                        clearTimeout(timeoutId);

                        if (response.ok) {
                            const result = await response.json();
                            if (result.code === 200 && result.data) {
                                this.userPermissions = result.data;
                                console.log('✅ 用户权限加载成功:', this.userPermissions);
                            } else {
                                console.warn('⚠️ 权限API返回异常，使用默认权限:', result);
                                this.userPermissions = this.getDefaultPermissions();
                            }
                        } else {
                            console.warn('⚠️ 权限API调用失败，状态码:', response.status);
                            this.userPermissions = this.getDefaultPermissions();
                        }
                    } catch (fetchError) {
                        clearTimeout(timeoutId);
                        if (fetchError.name === 'AbortError') {
                            console.warn('⚠️ 权限API请求超时，使用默认权限');
                        } else {
                            console.error('❌ 权限API请求失败:', fetchError);
                        }
                        this.userPermissions = this.getDefaultPermissions();
                    }
                } catch (error) {
                    console.error('❌ 加载权限过程中发生异常:', error);
                    this.userPermissions = this.getDefaultPermissions();
                }

                this.permissionsLoaded = true;
                console.log('🏁 权限加载完成，最终权限:', this.userPermissions);
            }

            applyPermissions() {
                console.log('🔧 应用菜单权限控制...');

                const sidebar = document.querySelector('.sidebar');
                if (!sidebar) {
                    console.error('❌ 找不到侧边栏元素');
                    return;
                }

                let visibleMenuCount = 0;
                let hiddenMenuCount = 0;

                // 遍历所有菜单项，根据权限显示/隐藏
                document.querySelectorAll('.sidebar .nav-link[data-permission]').forEach(link => {
                    const permission = link.getAttribute('data-permission');
                    const navItem = link.closest('.nav-item');

                    if (!this.hasPermission(permission)) {
                        navItem.classList.add('permission-hidden');
                        hiddenMenuCount++;
                        console.log(`❌ 隐藏菜单: ${permission}`);
                    } else {
                        navItem.classList.remove('permission-hidden');
                        visibleMenuCount++;
                        console.log(`✅ 显示菜单: ${permission}`);
                    }
                });

                // 标记权限加载完成，显示菜单
                sidebar.classList.add('permissions-loaded');

                console.log(`✅ 菜单权限控制应用完成 - 显示: ${visibleMenuCount}, 隐藏: ${hiddenMenuCount}`);

                // 如果没有任何可见菜单，显示提示
                if (visibleMenuCount === 0) {
                    console.warn('⚠️ 用户没有任何菜单权限');
                    this.showNoPermissionMessage();
                }
            }

            showNoPermissionMessage() {
                const sidebar = document.querySelector('.sidebar');
                const indicator = sidebar.querySelector('.permission-loading-indicator');
                if (indicator) {
                    indicator.innerHTML = '<i class="bi bi-exclamation-triangle"></i> 暂无菜单权限';
                    indicator.style.display = 'block';
                }
            }

            hasPermission(permissionCode) {
                if (!permissionCode) return true;
                return this.userPermissions.includes(permissionCode);
            }

            getAllPermissions() {
                return [
                    'DASHBOARD_VIEW', 'AGENT_VIEW', 'PLAYGROUND_VIEW', 'TEMPLATE_VIEW',
                    'business:smart-form', 'RECOGNITION_VIEW', 'AGENT_APPROVAL_VIEW',
                    'USER_VIEW', 'ROLE_VIEW', 'PERMISSION_VIEW'
                ];
            }

            getDefaultPermissions() {
                // 为了测试，暂时给予所有权限
                return this.getAllPermissions();
            }
        }

        // 立即执行，不等待DOMContentLoaded
        (async function() {
            console.log('📄 立即初始化菜单权限控制器...');

            // 等待DOM加载完成
            if (document.readyState === 'loading') {
                await new Promise(resolve => {
                    document.addEventListener('DOMContentLoaded', resolve, { once: true });
                });
            }

            try {
                window.menuPermissionController = new MenuPermissionController();
                // 等待权限控制器初始化完成
                await window.menuPermissionController.init();
                console.log('🎉 菜单权限控制器初始化完成');
            } catch (error) {
                console.error('💥 菜单权限控制器初始化失败:', error);

                // 失败时显示所有菜单，避免用户无法使用
                const sidebar = document.querySelector('.sidebar');
                if (sidebar) {
                    sidebar.classList.add('permissions-loaded');
                    console.log('🔄 权限控制失败，显示所有菜单');
                }
            }
        })();
    </script>
</body>
</html>
