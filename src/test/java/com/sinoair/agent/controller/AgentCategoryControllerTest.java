package com.sinoair.agent.controller;

import com.sinoair.agent.entity.AgentCategory;
import com.sinoair.agent.service.AgentCategoryService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureTestMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Agent分类控制器测试类
 * 
 * <AUTHOR> Team
 */
@SpringBootTest
// @AutoConfigureTestMvc
public class AgentCategoryControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private AgentCategoryService agentCategoryService;

    @Test
    public void testGetActiveCategories() throws Exception {
        // 准备测试数据
        AgentCategory category1 = new AgentCategory();
        category1.setId(1L);
        category1.setCategoryName("文档识别");
        category1.setCategoryCode("DOCUMENT_RECOGNITION");
        category1.setParentId(0L);
        category1.setLevel(1);
        category1.setSortOrder(1);
        category1.setDescription("各类文档识别Agent");
        category1.setStatus(1);
        category1.setCreatedTime(LocalDateTime.now());

        AgentCategory category2 = new AgentCategory();
        category2.setId(2L);
        category2.setCategoryName("数据提取");
        category2.setCategoryCode("DATA_EXTRACTION");
        category2.setParentId(0L);
        category2.setLevel(1);
        category2.setSortOrder(2);
        category2.setDescription("数据提取类Agent");
        category2.setStatus(1);
        category2.setCreatedTime(LocalDateTime.now());

        List<AgentCategory> categories = Arrays.asList(category1, category2);

        // Mock服务方法
        when(agentCategoryService.getAllActiveCategories()).thenReturn(categories);

        // 执行测试
        mockMvc.perform(get("/api/v1/agent-categories/active")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("获取可用分类成功"))
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(2))
                .andExpect(jsonPath("$.data[0].categoryName").value("文档识别"))
                .andExpect(jsonPath("$.data[0].categoryCode").value("DOCUMENT_RECOGNITION"))
                .andExpect(jsonPath("$.data[1].categoryName").value("数据提取"))
                .andExpect(jsonPath("$.data[1].categoryCode").value("DATA_EXTRACTION"));
    }

    @Test
    public void testGetCategoryTree() throws Exception {
        // 准备测试数据
        AgentCategory rootCategory = new AgentCategory();
        rootCategory.setId(1L);
        rootCategory.setCategoryName("文档识别");
        rootCategory.setCategoryCode("DOCUMENT_RECOGNITION");
        rootCategory.setParentId(0L);
        rootCategory.setLevel(1);
        rootCategory.setSortOrder(1);
        rootCategory.setDescription("各类文档识别Agent");
        rootCategory.setStatus(1);
        rootCategory.setCreatedTime(LocalDateTime.now());

        List<AgentCategory> categories = Arrays.asList(rootCategory);

        // Mock服务方法
        when(agentCategoryService.getCategoryTree()).thenReturn(categories);

        // 执行测试
        mockMvc.perform(get("/api/v1/agent-categories/tree")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("获取分类树形结构成功"))
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(1))
                .andExpect(jsonPath("$.data[0].categoryName").value("文档识别"));
    }

    @Test
    public void testGetRootCategories() throws Exception {
        // 准备测试数据
        AgentCategory rootCategory = new AgentCategory();
        rootCategory.setId(1L);
        rootCategory.setCategoryName("文档识别");
        rootCategory.setCategoryCode("DOCUMENT_RECOGNITION");
        rootCategory.setParentId(0L);
        rootCategory.setLevel(1);
        rootCategory.setSortOrder(1);
        rootCategory.setDescription("各类文档识别Agent");
        rootCategory.setStatus(1);
        rootCategory.setCreatedTime(LocalDateTime.now());

        List<AgentCategory> categories = Arrays.asList(rootCategory);

        // Mock服务方法
        when(agentCategoryService.getRootCategories()).thenReturn(categories);

        // 执行测试
        mockMvc.perform(get("/api/v1/agent-categories/root")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("获取根分类成功"))
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(1))
                .andExpect(jsonPath("$.data[0].categoryName").value("文档识别"));
    }
}
