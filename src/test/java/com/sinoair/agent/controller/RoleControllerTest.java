package com.sinoair.agent.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sinoair.agent.dto.request.RolePermissionAssignDTO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureTestMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * 角色控制器测试类
 */
@SpringBootTest
// @AutoConfigureTestMvc
public class RoleControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    public void testAssignPermissions() throws Exception {
        // 创建测试数据
        RolePermissionAssignDTO request = new RolePermissionAssignDTO();
        request.setPermissionIds(Arrays.asList(1L, 2L, 3L));

        // 执行请求
        mockMvc.perform(post("/api/v1/roles/1/permissions")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk());
    }
}
