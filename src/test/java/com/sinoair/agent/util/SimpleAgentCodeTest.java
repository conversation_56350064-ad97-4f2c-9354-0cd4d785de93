package com.sinoair.agent.util;

import java.util.HashSet;
import java.util.Set;
import java.util.regex.Pattern;

/**
 * 简单的Agent编码生成测试类（不依赖Spring Boot）
 * 
 * <AUTHOR> Team
 */
public class SimpleAgentCodeTest {
    
    /**
     * 生成6位随机编码（数字+大写字母）
     */
    public static String generateRandomCode() {
        String characters = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        StringBuilder code = new StringBuilder();
        java.util.Random random = new java.util.Random();
        
        for (int i = 0; i < 6; i++) {
            int index = random.nextInt(characters.length());
            code.append(characters.charAt(index));
        }
        
        return code.toString();
    }
    
    /**
     * 验证编码格式
     */
    public static boolean isValidCode(String code) {
        if (code == null || code.length() != 6) {
            return false;
        }
        
        Pattern pattern = Pattern.compile("^[0-9A-Z]{6}$");
        return pattern.matcher(code).matches();
    }
    
    /**
     * 测试编码生成
     */
    public static void testCodeGeneration() {
        System.out.println("=== Agent编码生成测试 ===");
        
        // 测试1: 生成示例编码
        System.out.println("\n1. 生成示例编码:");
        for (int i = 0; i < 10; i++) {
            String code = generateRandomCode();
            System.out.println("  " + (i + 1) + ": " + code + " (有效: " + isValidCode(code) + ")");
        }
        
        // 测试2: 验证编码格式
        System.out.println("\n2. 编码格式验证测试:");
        String[] testCodes = {"ABC123", "000000", "ZZZZZZ", "abc123", "ABC12", "ABC1234", "ABC12@"};
        for (String code : testCodes) {
            System.out.println("  " + code + " -> " + (isValidCode(code) ? "有效" : "无效"));
        }
        
        // 测试3: 唯一性测试
        System.out.println("\n3. 唯一性测试 (生成1000个编码):");
        Set<String> codes = new HashSet<>();
        int totalCount = 1000;
        
        for (int i = 0; i < totalCount; i++) {
            codes.add(generateRandomCode());
        }
        
        int uniqueCount = codes.size();
        double uniqueRate = (double) uniqueCount / totalCount * 100;
        System.out.println("  总数: " + totalCount);
        System.out.println("  唯一: " + uniqueCount);
        System.out.println("  唯一率: " + String.format("%.2f%%", uniqueRate));
        
        // 测试4: 性能测试
        System.out.println("\n4. 性能测试 (生成10000个编码):");
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < 10000; i++) {
            generateRandomCode();
        }
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        System.out.println("  耗时: " + duration + "ms");
        System.out.println("  平均: " + String.format("%.3f", (double) duration / 10000) + "ms/个");
        
        // 测试5: 理论容量
        System.out.println("\n5. 理论容量:");
        long capacity = (long) Math.pow(36, 6);
        System.out.println("  理论容量: " + capacity + " (约" + (capacity / 10000) + "万)");
        
        System.out.println("\n=== 测试完成 ===");
    }
    
    public static void main(String[] args) {
        testCodeGeneration();
    }
}
