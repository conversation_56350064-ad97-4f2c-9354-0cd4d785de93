package com.sinoair.agent.util;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;

import java.util.HashSet;
import java.util.Set;
import java.util.regex.Pattern;

/**
 * Agent编码生成器测试类
 * 
 * <AUTHOR> Team
 */
class AgentCodeGeneratorTest {

    @Test
    @DisplayName("测试生成的编码长度为6位")
    void testCodeLength() {
        String code = AgentCodeGenerator.generateRandomCode();
        assertEquals(6, code.length(), "生成的编码长度应该为6位");
    }

    @Test
    @DisplayName("测试生成的编码只包含数字和大写字母")
    void testCodeCharacters() {
        String code = AgentCodeGenerator.generateRandomCode();
        Pattern pattern = Pattern.compile("^[0-9A-Z]{6}$");
        assertTrue(pattern.matcher(code).matches(), 
                "生成的编码应该只包含数字0-9和大写字母A-Z，实际生成: " + code);
    }

    @Test
    @DisplayName("测试编码格式验证功能")
    void testCodeValidation() {
        // 测试有效编码
        assertTrue(AgentCodeGenerator.isValidCode("ABC123"), "ABC123应该是有效编码");
        assertTrue(AgentCodeGenerator.isValidCode("000000"), "000000应该是有效编码");
        assertTrue(AgentCodeGenerator.isValidCode("ZZZZZZ"), "ZZZZZZ应该是有效编码");
        
        // 测试无效编码
        assertFalse(AgentCodeGenerator.isValidCode(null), "null应该是无效编码");
        assertFalse(AgentCodeGenerator.isValidCode(""), "空字符串应该是无效编码");
        assertFalse(AgentCodeGenerator.isValidCode("ABC12"), "5位编码应该是无效的");
        assertFalse(AgentCodeGenerator.isValidCode("ABC1234"), "7位编码应该是无效的");
        assertFalse(AgentCodeGenerator.isValidCode("abc123"), "包含小写字母应该是无效的");
        assertFalse(AgentCodeGenerator.isValidCode("ABC12@"), "包含特殊字符应该是无效的");
    }

    @Test
    @DisplayName("测试批量生成编码的唯一性")
    void testCodeUniqueness() {
        int testCount = 1000;
        Set<String> codes = AgentCodeGenerator.generateBatchCodes(testCount);
        
        // 检查生成的编码数量
        assertTrue(codes.size() > testCount * 0.95, 
                "生成" + testCount + "个编码，唯一编码数量应该大于95%，实际: " + codes.size());
        
        // 检查每个编码的格式
        for (String code : codes) {
            assertTrue(AgentCodeGenerator.isValidCode(code), 
                    "生成的编码格式应该有效: " + code);
        }
    }

    @Test
    @DisplayName("测试理论编码容量计算")
    void testTheoreticalCapacity() {
        long capacity = AgentCodeGenerator.getTheoreticalCapacity();
        long expected = (long) Math.pow(36, 6); // 36^6
        assertEquals(expected, capacity, "理论编码容量应该为36^6");
        assertTrue(capacity > 2_000_000_000L, "理论编码容量应该超过20亿");
    }

    @Test
    @DisplayName("测试多次生成编码的随机性")
    void testCodeRandomness() {
        Set<String> codes = new HashSet<>();
        int generateCount = 100;
        
        // 生成100个编码
        for (int i = 0; i < generateCount; i++) {
            String code = AgentCodeGenerator.generateRandomCode();
            codes.add(code);
        }
        
        // 检查唯一性（应该大部分都是唯一的）
        assertTrue(codes.size() > generateCount * 0.9, 
                "生成" + generateCount + "个编码，唯一编码应该大于90%，实际: " + codes.size());
    }

    @Test
    @DisplayName("测试编码不包含容易混淆的字符")
    void testNoConfusingCharacters() {
        // 生成多个编码，检查是否包含容易混淆的字符
        for (int i = 0; i < 100; i++) {
            String code = AgentCodeGenerator.generateRandomCode();
            
            // 当前实现包含所有数字和字母，如果需要排除混淆字符，需要修改实现
            // 这里只是验证当前实现的行为
            assertFalse(code.contains("o"), "编码不应包含小写字母o");
            assertFalse(code.contains("l"), "编码不应包含小写字母l");
            assertFalse(code.contains("I"), "如果要避免混淆，可以考虑排除大写字母I");
        }
    }

    @Test
    @DisplayName("性能测试 - 生成大量编码")
    void testPerformance() {
        long startTime = System.currentTimeMillis();
        
        // 生成10000个编码
        for (int i = 0; i < 10000; i++) {
            AgentCodeGenerator.generateRandomCode();
        }
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        // 应该在合理时间内完成（比如1秒内）
        assertTrue(duration < 1000, 
                "生成10000个编码应该在1秒内完成，实际耗时: " + duration + "ms");
    }
}
