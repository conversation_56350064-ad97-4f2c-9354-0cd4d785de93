package com.sinoair.agent.service;

import com.sinoair.agent.common.Result;
import com.sinoair.agent.dto.response.FileUploadResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.ActiveProfiles;

import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

import static org.junit.jupiter.api.Assertions.*;

/**
 * MinIO上传测试
 * 
 * <AUTHOR> Team
 */
@Slf4j
@SpringBootTest
class MinioUploadTest {

    @Autowired
    private FileService fileService;

    private static final String TEST_FILE_PATH = "D:\\test\\LH提取自泽坤公司报价20240523.pdf";

    @Test
    @WithMockUser(username = "testuser", authorities = {"file:upload", "file:delete"})
    void testMinioUpload() throws Exception {
        // 检查测试文件是否存在
        Path testFilePath = Paths.get(TEST_FILE_PATH);
        if (!Files.exists(testFilePath)) {
            log.warn("测试文件不存在: {}", TEST_FILE_PATH);
            return;
        }

        // 读取测试文件
        byte[] fileContent = Files.readAllBytes(testFilePath);
        String fileName = testFilePath.getFileName().toString();
        
        log.info("开始测试MinIO上传: 文件名={}, 大小={} bytes", fileName, fileContent.length);

        // 创建MockMultipartFile
        MockMultipartFile testFile = new MockMultipartFile(
                "file",
                fileName,
                "text/plain",
                fileContent
        );

        // 执行上传
        Result<FileUploadResponse> uploadResult = fileService.uploadFile(testFile, "test");
        
        // 验证上传结果
        assertTrue(uploadResult.isSuccess(), "文件上传应该成功");
        assertNotNull(uploadResult.getData(), "上传响应数据不应为空");
        
        FileUploadResponse response = uploadResult.getData();
        assertNotNull(response.getFileId(), "文件ID不应为空");
        assertEquals(fileName, response.getOriginalName(), "原始文件名应该匹配");
        assertEquals(fileContent.length, response.getFileSize(), "文件大小应该匹配");
        
        log.info("MinIO上传测试成功: fileId={}, storedName={}", 
                response.getFileId(), response.getStoredName());

        // 清理测试文件
        // Result<String> deleteResult = fileService.deleteFile(response.getFileId());
        // assertTrue(deleteResult.isSuccess(), "文件删除应该成功");
        
        log.info("测试文件清理完成");
    }
}
