package com.sinoair.agent.service;

import com.sinoair.agent.config.MinioProperties;
import io.minio.BucketExistsArgs;
import io.minio.MinioClient;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.net.InetSocketAddress;
import java.net.Socket;
import java.util.concurrent.TimeUnit;

/**
 * MinIO连接测试
 * 
 * <AUTHOR> Team
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
class MinioConnectionTest {

    @Autowired
    private MinioProperties minioProperties;

    @Test
    void testNetworkConnection() {
        String host = extractHost(minioProperties.getEndpoint());
        int port = extractPort(minioProperties.getEndpoint());
        
        log.info("测试网络连接: {}:{}", host, port);
        
        try (Socket socket = new Socket()) {
            socket.connect(new InetSocketAddress(host, port), 5000);
            log.info("网络连接成功: {}:{}", host, port);
        } catch (Exception e) {
            log.error("网络连接失败: {}:{}", host, port, e);
        }
    }

    @Test
    void testMinioConnection() {
        log.info("测试MinIO连接配置:");
        log.info("Endpoint: {}", minioProperties.getEndpoint());
        log.info("Access Key: {}", minioProperties.getAccessKey());
        log.info("Bucket: {}", minioProperties.getBucketName());
        log.info("Connect Timeout: {}ms", minioProperties.getConnectTimeout());
        log.info("Write Timeout: {}ms", minioProperties.getWriteTimeout());
        log.info("Read Timeout: {}ms", minioProperties.getReadTimeout());

        try {
            // 创建自定义的OkHttpClient，设置更长的超时时间
            OkHttpClient httpClient = new OkHttpClient.Builder()
                    .connectTimeout(30, TimeUnit.SECONDS)
                    .writeTimeout(120, TimeUnit.SECONDS)
                    .readTimeout(30, TimeUnit.SECONDS)
                    .build();

            // 创建MinIO客户端
            MinioClient client = MinioClient.builder()
                    .endpoint(minioProperties.getEndpoint())
                    .credentials(minioProperties.getAccessKey(), minioProperties.getSecretKey())
                    .region(minioProperties.getRegion())
                    .httpClient(httpClient)
                    .build();

            log.info("MinIO客户端创建成功");

            // 测试连接
            log.info("测试列出存储桶...");
            var buckets = client.listBuckets();
            log.info("存储桶列表获取成功，共{}个存储桶", buckets.size());
            for (var bucket : buckets) {
                log.info("存储桶: {}, 创建时间: {}", bucket.name(), bucket.creationDate());
            }

            // 测试存储桶是否存在
            log.info("检查存储桶是否存在: {}", minioProperties.getBucketName());
            boolean exists = client.bucketExists(
                    BucketExistsArgs.builder()
                            .bucket(minioProperties.getBucketName())
                            .build()
            );
            log.info("存储桶存在: {}", exists);

        } catch (Exception e) {
            log.error("MinIO连接测试失败", e);
        }
    }

    @Test
    void testMinioWithDifferentTimeouts() {
        log.info("测试不同超时设置的MinIO连接");
        
        // 测试不同的超时配置
        int[][] timeoutConfigs = {
                {5000, 30000, 5000},    // 短超时
                {15000, 90000, 15000},  // 中等超时
                {30000, 180000, 30000}  // 长超时
        };
        
        for (int i = 0; i < timeoutConfigs.length; i++) {
            int[] config = timeoutConfigs[i];
            log.info("测试配置 {}: connect={}ms, write={}ms, read={}ms", 
                    i + 1, config[0], config[1], config[2]);
            
            try {
                OkHttpClient httpClient = new OkHttpClient.Builder()
                        .connectTimeout(config[0], TimeUnit.MILLISECONDS)
                        .writeTimeout(config[1], TimeUnit.MILLISECONDS)
                        .readTimeout(config[2], TimeUnit.MILLISECONDS)
                        .build();

                MinioClient client = MinioClient.builder()
                        .endpoint(minioProperties.getEndpoint())
                        .credentials(minioProperties.getAccessKey(), minioProperties.getSecretKey())
                        .region(minioProperties.getRegion())
                        .httpClient(httpClient)
                        .build();

                // 简单测试
                client.listBuckets();
                log.info("配置 {} 测试成功", i + 1);
                break; // 找到可用配置就停止
                
            } catch (Exception e) {
                log.warn("配置 {} 测试失败: {}", i + 1, e.getMessage());
            }
        }
    }

    private String extractHost(String endpoint) {
        try {
            return endpoint.replaceAll("^https?://", "").split(":")[0];
        } catch (Exception e) {
            return "localhost";
        }
    }

    private int extractPort(String endpoint) {
        try {
            String[] parts = endpoint.replaceAll("^https?://", "").split(":");
            return parts.length > 1 ? Integer.parseInt(parts[1]) : 9000;
        } catch (Exception e) {
            return 9000;
        }
    }
}
