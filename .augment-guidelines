# 智能体矩阵 项目工作区准则

## 项目概述
智能体矩阵是一个智能文档识别与自动填写平台，包含Spring Boot后端服务和Chrome插件前端。
- **项目名称**: 智能体矩阵 Platform
- **版本**: 1.0.0
- **描述**: 智能文档识别与自动填写平台
- **主要功能**: 文档识别、表单自动填写、Agent管理、用户权限管理

## 技术架构

### 后端技术栈
- **框架**: Spring Boot 3.2.1
- **Java版本**: JDK 17
- **数据库**: MySQL 8.0 + MyBatis-Plus 3.5.12
- **缓存**: Redis 6.0
- **日志存储**: MongoDB 4.11.1
- **连接池**: Druid 1.2.20
- **安全**: Spring Security + JWT
- **文档**: Knife4j 4.4.0 (Swagger)
- **工具库**: Hutool 5.8.24, Lombok 1.18.24
- **HTTP客户端**: Apache HttpClient5 + WebFlux
- **文件存储**: MinIO 8.5.7
- **构建工具**: Maven 3.x

### 前端技术栈
- **插件框架**: Chrome Extension Manifest V3
- **脚本语言**: JavaScript (ES6+)
- **样式**: CSS3,Bootstrap  v5.3.0
- **开发方式**: 原生开发（无构建工具）

### LLM集成
- **支持的提供商**: 千问(Qianwen)、OpenAI、DeepSeek
- **默认提供商**: qianwen
- **超时设置**: 30秒
- **重试次数**: 3次

## 项目结构

### 后端目录结构
```
src/main/java/com/sinoair/agent/
├── SinoairAgentApplication.java          # 主启动类
├── common/                               # 通用组件
│   ├── PageResult.java                   # 分页结果
│   ├── Result.java                       # 统一响应结果
│   └── ResultCode.java                   # 响应状态码
├── config/                               # 配置类
├── controller/                           # 控制器层
├── dto/                                  # 数据传输对象
├── entity/                               # 实体类
├── mapper/                               # MyBatis映射器
├── service/                              # 服务层
├── security/                             # 安全组件
└── exception/                            # 异常处理
```

### 前端目录结构
```
chrome-extension/
├── manifest.json                         # 插件配置文件
├── background.js                         # 后台脚本
├── content.js                           # 内容脚本
├── popup.html/js                        # 弹窗页面
├── sidebar.html/js/css                  # 侧边栏
├── icons/                               # 图标文件
└── 安装指南.md                           # 安装说明
```

### 核心实体模型
- **Agent**: Agent主实体，包含名称、编码、配置、提示词模板等
- **BusinessTemplate**: 业务JSON模板
- **RecognitionRecord**: 识别记录
- **User**: 用户实体
- **Role/Permission**: 角色权限体系
- **AgentVersion**: Agent版本管理
- **PageBinding**: 页面绑定配置

## 数据库设计

### 核心表结构
- **sys_user**: 用户表
- **sys_role**: 角色表
- **sys_permission**: 权限表
- **agents**: Agent主表
- **agent_categories**: Agent分类表
- **agent_versions**: Agent版本表
- **business_templates**: 业务模板表
- **recognition_records**: 识别记录表
- **page_bindings**: 页面绑定表
- **uploaded_files**: 上传文件表

### MongoDB集合结构
- **operation_logs**: 操作日志集合，存储用户操作记录、表单数据和返回结果

### 字段命名规范
- 主键统一使用 `id`
- 创建时间: `created_time`
- 更新时间: `updated_time`
- 删除标记: `deleted`
- 状态字段: `status`

## API设计规范

### RESTful API规范
- **基础路径**: `/api/v1/`
- **HTTP动词**: GET(查询)、POST(创建)、PUT(更新)、DELETE(删除)
- **URL命名**: 使用复数名词，如 `/api/v1/agents`、`/api/v1/users`

### 统一响应格式
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {},
  "timestamp": "2024-01-01T12:00:00"
}
```

### 分页响应格式
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "records": [],
    "total": 100,
    "size": 10,
    "current": 1,
    "pages": 10
  }
}
```

## 开发规范

### Java代码规范
- 使用阿里巴巴Java开发手册规范
- 类名使用大驼峰命名法
- 方法名和变量名使用小驼峰命名法
- 常量使用全大写加下划线
- 必须使用 `@Slf4j` 进行日志记录
- 必须使用 `@RequiredArgsConstructor` 进行依赖注入
- 实体类必须继承 `BaseEntity`
- 使用 `@Transactional` 进行事务管理

### 注释规范
- 类和接口必须有JavaDoc注释
- 公共方法必须有注释说明
- 复杂业务逻辑必须有行内注释
- 使用 `@author`、`@param`、`@return` 等标签

### 异常处理规范
- 使用统一的业务异常类 `BusinessException`
- 使用全局异常处理器 `GlobalExceptionHandler`
- 不要捕获并忽略异常
- 必须记录异常日志

## UI设计规范

### 设计理念
基于Keen主题的现代化企业级设计风格，结合Bootstrap 5.3.0框架，打造简洁、专业、易用的用户界面。

### 色彩系统

#### 主色调
- **主品牌色**: `#009EF7` (蓝色) - 用于主要按钮、链接、重要信息
- **成功色**: `#50CD89` (绿色) - 用于成功状态、确认操作
- **警告色**: `#FFC700` (黄色) - 用于警告信息、待处理状态
- **危险色**: `#F1416C` (红色) - 用于错误信息、删除操作
- **信息色**: `#7239EA` (紫色) - 用于提示信息、特殊标记

#### 中性色调
- **深色文本**: `#181C32` - 主要文本内容
- **中等文本**: `#5E6278` - 次要文本内容
- **浅色文本**: `#A1A5B7` - 辅助文本、占位符
- **边框色**: `#E4E6EA` - 分割线、边框
- **背景色**: `#F9F9F9` - 页面背景
- **卡片背景**: `#FFFFFF` - 卡片、表单背景

#### 渐变色
- **主渐变**: `linear-gradient(135deg, #667eea 0%, #764ba2 100%)`
- **成功渐变**: `linear-gradient(135deg, #667eea 0%, #764ba2 100%)`
- **警告渐变**: `linear-gradient(135deg, #f093fb 0%, #f5576c 100%)`

### 字体规范

#### 字体族
- **主字体**: `"Inter", "Helvetica Neue", Arial, sans-serif`
- **等宽字体**: `"JetBrains Mono", "Fira Code", Consolas, monospace`

#### 字体大小层级
- **超大标题**: `2.25rem` (36px) - h1, 页面主标题
- **大标题**: `1.875rem` (30px) - h2, 区块标题
- **中标题**: `1.5rem` (24px) - h3, 卡片标题
- **小标题**: `1.25rem` (20px) - h4, 子标题
- **正文**: `1rem` (16px) - 正文内容
- **小字**: `0.875rem` (14px) - 辅助信息
- **极小字**: `0.75rem` (12px) - 标签、状态

#### 字重规范
- **细体**: `300` - 辅助信息
- **常规**: `400` - 正文内容
- **中等**: `500` - 重要信息
- **粗体**: `600` - 标题、强调
- **超粗**: `700` - 主标题

### 间距系统

#### 基础间距单位
基于8px网格系统，使用Bootstrap的spacing utilities：

- **xs**: `0.25rem` (4px) - 极小间距
- **sm**: `0.5rem` (8px) - 小间距
- **md**: `1rem` (16px) - 中等间距
- **lg**: `1.5rem` (24px) - 大间距
- **xl**: `3rem` (48px) - 超大间距

#### 组件间距规范
- **卡片内边距**: `1.5rem` (24px)
- **表单元素间距**: `1rem` (16px)
- **按钮组间距**: `0.5rem` (8px)
- **列表项间距**: `0.75rem` (12px)
- **区块间距**: `2rem` (32px)

### 圆角规范
- **小圆角**: `0.375rem` (6px) - 按钮、输入框
- **中圆角**: `0.5rem` (8px) - 卡片、模态框
- **大圆角**: `0.75rem` (12px) - 大型容器
- **圆形**: `50%` - 头像、图标按钮

### 阴影系统
- **轻微阴影**: `0 0.125rem 0.25rem rgba(0, 0, 0, 0.075)`
- **常规阴影**: `0 0.5rem 1rem rgba(0, 0, 0, 0.15)`
- **深度阴影**: `0 1rem 3rem rgba(0, 0, 0, 0.175)`
- **悬浮阴影**: `0 0.5rem 2rem rgba(0, 0, 0, 0.1)`

### 组件规范

#### 按钮组件
- **主要按钮**: 使用主品牌色背景，白色文字，中圆角
- **次要按钮**: 透明背景，主品牌色边框和文字
- **危险按钮**: 使用危险色背景，白色文字
- **按钮高度**:
  - 大按钮: `48px`
  - 标准按钮: `40px`
  - 小按钮: `32px`
- **按钮内边距**: 水平 `1.5rem`，垂直 `0.75rem`
- **按钮状态**: hover时增加阴影效果，active时轻微缩放

#### 卡片组件
- **背景**: 白色背景 `#FFFFFF`
- **边框**: 1px实线边框 `#E4E6EA`
- **圆角**: 中圆角 `0.5rem`
- **阴影**: 常规阴影
- **内边距**: `1.5rem`
- **标题**: 使用中标题字体大小，粗体字重
- **间距**: 卡片之间保持 `1.5rem` 间距

#### 表单组件
- **输入框**:
  - 边框: 1px实线 `#E4E6EA`
  - 圆角: 小圆角 `0.375rem`
  - 高度: `40px`
  - 内边距: 水平 `0.75rem`
  - 聚焦状态: 主品牌色边框，轻微阴影
- **标签**: 使用小字字体大小，中等字重，深色文本
- **错误状态**: 危险色边框和文字
- **成功状态**: 成功色边框和文字

#### 表格组件
- **表头**: 浅灰背景 `#F9F9F9`，粗体文字
- **行间距**: `0.75rem` 垂直内边距
- **边框**: 轻微边框分隔
- **斑马纹**: 奇偶行不同背景色
- **悬停效果**: 行悬停时浅色背景高亮

#### 导航组件
- **侧边栏**:
  - 宽度: 展开 `280px`，收起 `80px`
  - 背景: 深色背景或白色背景
  - 菜单项高度: `48px`
  - 图标大小: `20px`
  - 激活状态: 主品牌色背景或左侧边框
- **顶部导航**:
  - 高度: `72px`
  - 背景: 白色背景，底部边框
  - 用户头像: 圆形，`40px` 直径

#### 模态框组件
- **背景遮罩**: 半透明黑色 `rgba(0, 0, 0, 0.5)`
- **内容区**: 白色背景，中圆角，深度阴影
- **最大宽度**: `600px`
- **内边距**: `2rem`
- **标题**: 大标题字体大小，粗体
- **关闭按钮**: 右上角，图标按钮

#### 状态标签
- **成功**: 成功色背景，白色文字，小圆角
- **警告**: 警告色背景，深色文字，小圆角
- **错误**: 危险色背景，白色文字，小圆角
- **信息**: 信息色背景，白色文字，小圆角
- **默认**: 中性灰背景，深色文字，小圆角

### 布局规范

#### 网格系统
- 使用Bootstrap 5.3.0的12列网格系统
- 容器最大宽度: `1320px`
- 响应式断点:
  - xs: `<576px`
  - sm: `≥576px`
  - md: `≥768px`
  - lg: `≥992px`
  - xl: `≥1200px`
  - xxl: `≥1400px`

#### 页面布局
- **主容器**: 使用 `.container-fluid` 或 `.container`
- **内容区域**: 左侧边栏 + 主内容区
- **页面标题**: 页面顶部，包含面包屑导航
- **内容卡片**: 使用卡片组件包装主要内容
- **操作按钮**: 通常位于页面右上角或卡片标题栏

#### 响应式设计
- **移动优先**: 从小屏幕开始设计，逐步增强
- **断点适配**: 在不同断点下调整布局和组件大小
- **触摸友好**: 移动端按钮和链接最小点击区域 `44px`
- **内容优先**: 确保核心内容在所有设备上都能良好显示

### 图标规范

#### 图标系统
- **图标库**: 使用Bootstrap Icons 1.11.0 + 自定义SVG图标
- **图标大小**:
  - 小图标: `16px` - 表格、列表中的操作图标
  - 标准图标: `20px` - 按钮、菜单图标
  - 大图标: `24px` - 页面标题、重要功能图标
  - 超大图标: `32px` - 空状态、引导页图标

#### 图标使用规范
- **语义化**: 图标应具有明确的语义含义
- **一致性**: 同类功能使用相同图标
- **可访问性**: 图标必须配合文字说明或aria-label
- **颜色**: 图标颜色应与文本颜色保持一致

### 动画效果

#### 过渡动画
- **持续时间**:
  - 快速: `150ms` - 按钮hover、focus状态
  - 标准: `300ms` - 模态框、下拉菜单
  - 慢速: `500ms` - 页面切换、大型组件
- **缓动函数**:
  - 标准: `ease-in-out` - 大多数过渡效果
  - 进入: `ease-out` - 元素出现
  - 退出: `ease-in` - 元素消失

#### 常用动画效果
- **淡入淡出**: `opacity` 变化，用于模态框、提示信息
- **滑动**: `transform: translateX/Y`，用于侧边栏、抽屉
- **缩放**: `transform: scale`，用于按钮点击反馈
- **旋转**: `transform: rotate`，用于加载图标

### JavaScript工具函数

#### 通用工具函数
```javascript
// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 节流函数
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    }
}

// 格式化日期
function formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
    // 实现日期格式化逻辑
}

// 深拷贝对象
function deepClone(obj) {
    return JSON.parse(JSON.stringify(obj));
}
```

#### UI交互函数
```javascript
// 显示加载状态
function showLoading(element, text = '加载中...') {
    element.innerHTML = `<i class="spinner-border spinner-border-sm me-2"></i>${text}`;
    element.disabled = true;
}

// 隐藏加载状态
function hideLoading(element, originalText) {
    element.innerHTML = originalText;
    element.disabled = false;
}

// 显示提示消息
function showToast(message, type = 'info') {
    // 实现Toast提示逻辑
}

// 确认对话框
function confirmDialog(message, callback) {
    // 实现确认对话框逻辑
}
```

### 开发最佳实践

#### CSS最佳实践
- **BEM命名法**: 使用Block-Element-Modifier命名规范
- **CSS变量**: 使用CSS自定义属性定义主题色彩和尺寸
- **移动优先**: 使用min-width媒体查询
- **性能优化**: 避免深层嵌套，合理使用CSS选择器

#### JavaScript最佳实践
- **模块化**: 使用ES6模块或立即执行函数表达式(IIFE)
- **事件委托**: 对动态生成的元素使用事件委托
- **错误处理**: 使用try-catch处理可能的错误
- **性能优化**: 使用防抖节流优化高频事件

#### 可访问性规范
- **语义化HTML**: 使用正确的HTML标签
- **键盘导航**: 确保所有交互元素可通过键盘访问
- **屏幕阅读器**: 提供适当的aria-label和alt属性
- **颜色对比**: 确保文字与背景有足够的对比度

#### 浏览器兼容性
- **目标浏览器**: Chrome 90+, Firefox 88+, Safari 14+, Edge 90+
- **Polyfill**: 为不支持的特性提供polyfill
- **渐进增强**: 确保基础功能在所有浏览器中可用
- **测试**: 在目标浏览器中进行充分测试

### 主题系统规范

#### 主题切换功能
智能体矩阵项目支持深色/浅色主题切换，提供更好的用户体验和可访问性。

#### 主题变量系统
- **浅色主题变量**: 定义在`:root`中的默认变量
- **深色主题变量**: 定义在`.theme-dark`类中的覆盖变量
- **主题过渡**: 使用`--theme-transition: all 0.3s ease`实现平滑切换

#### 色彩变量定义

##### 浅色主题
```css
:root {
    /* 中性色调 */
    --dark-text: #181C32;
    --medium-text: #5E6278;
    --light-text: #A1A5B7;
    --border-color: #E4E6EA;
    --background-color: #F9F9F9;
    --card-background: #FFFFFF;

    /* 阴影系统 */
    --light-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --normal-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --deep-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
    --hover-shadow: 0 0.5rem 2rem rgba(0, 0, 0, 0.1);
}
```

##### 深色主题
```css
.theme-dark {
    /* 中性色调 */
    --dark-text: #FFFFFF;
    --medium-text: #A1A5B7;
    --light-text: #5E6278;
    --border-color: #2B2B40;
    --background-color: #1B1B29;
    --card-background: #1E1E2D;

    /* 阴影系统 */
    --light-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.3);
    --normal-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.4);
    --deep-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.5);
    --hover-shadow: 0 0.5rem 2rem rgba(0, 0, 0, 0.3);
}
```

#### 主题控制器
```javascript
class ThemeController {
    constructor() {
        this.currentTheme = localStorage.getItem('theme') || 'light';
        this.themeToggle = document.getElementById('themeToggle');
        this.themeIcon = document.getElementById('themeIcon');
        this.init();
    }

    toggleTheme() {
        this.currentTheme = this.currentTheme === 'light' ? 'dark' : 'light';
        this.applyTheme(this.currentTheme);
        localStorage.setItem('theme', this.currentTheme);

        // 触发主题变更事件
        window.dispatchEvent(new CustomEvent('themeChanged', {
            detail: { theme: this.currentTheme }
        }));
    }

    applyTheme(theme) {
        const body = document.body;
        const html = document.documentElement;

        if (theme === 'dark') {
            body.classList.add('theme-dark');
            html.classList.add('theme-dark');
        } else {
            body.classList.remove('theme-dark');
            html.classList.remove('theme-dark');
        }
    }
}
```

#### 主题适配规范

##### CSS编写规范
1. **使用CSS变量**: 所有颜色值必须使用CSS变量，不得硬编码
2. **过渡效果**: 为主要元素添加`transition: var(--theme-transition)`
3. **深色主题样式**: 使用`.theme-dark`前缀覆盖特殊样式
4. **阴影适配**: 深色主题下使用更深的阴影值

##### 组件适配要求
- **侧边栏**: 背景、边框、链接颜色适配
- **顶部导航**: 背景、文字、下拉菜单适配
- **卡片组件**: 背景、边框、标题颜色适配
- **表格组件**: 背景、边框、文字颜色适配
- **表单控件**: 背景、边框、占位符颜色适配
- **模态框**: 背景、边框、标题颜色适配

##### JavaScript事件处理
```javascript
// 监听主题变更事件
window.addEventListener('themeChanged', (event) => {
    const theme = event.detail.theme;
    console.log('主题已切换为:', theme);

    // 在这里处理主题变更后的逻辑
    // 例如：重新渲染图表、更新第三方组件主题等
});
```

#### 本地存储
- **存储键**: `theme`
- **存储值**: `'light'` 或 `'dark'`
- **默认主题**: `'light'`
- **持久化**: 用户选择的主题会保存到localStorage

#### 可访问性考虑
- **对比度**: 确保深色主题下文字与背景有足够对比度
- **色盲友好**: 不仅依赖颜色传达信息
- **系统偏好**: 可扩展支持`prefers-color-scheme`媒体查询
- **键盘导航**: 主题切换按钮支持键盘操作

#### 开发最佳实践
1. **新组件开发**: 必须同时考虑深色和浅色主题
2. **测试要求**: 每个页面都要在两种主题下测试
3. **图标适配**: 使用合适的图标表示当前主题状态
4. **动画效果**: 主题切换时提供平滑的过渡动画
5. **第三方组件**: 确保第三方组件也能适配主题变化

## Keen主题组件规范

### 数据展示组件

#### 统计卡片组件
- **基础结构**: 卡片容器 + 图标/数字 + 标题 + 描述
- **样式特点**:
  - 白色背景，轻微阴影
  - 圆角: `0.5rem`
  - 内边距: `1.5rem`
  - 图标大小: `24px`，支持彩色图标
- **数据展示**: 大号数字 + 百分比变化 + 趋势图标
- **变体**: 简单统计、带图表统计、带进度条统计

#### 数据表格组件
- **表头设计**:
  - 背景色: `#F9F9F9`
  - 字体: 粗体，`0.875rem`
  - 内边距: `0.75rem 1rem`
  - 支持排序图标
- **表格行**:
  - 行高: `48px`
  - 斑马纹: 奇偶行不同背景
  - 悬停效果: 浅灰背景高亮
  - 边框: 轻微分割线
- **操作列**:
  - 下拉菜单按钮
  - 快捷操作图标
  - 批量操作复选框
- **分页组件**: Bootstrap分页样式，支持页码跳转

#### 用户头像组件
- **头像尺寸**:
  - 小: `32px`
  - 标准: `40px`
  - 大: `48px`
  - 超大: `64px`
- **头像样式**:
  - 圆形头像: `border-radius: 50%`
  - 默认头像: 彩色字母背景
  - 在线状态: 绿色圆点指示器
- **用户信息**: 头像 + 姓名 + 职位/邮箱

#### 状态徽章组件
- **状态类型**:
  - 成功: 绿色背景 `#50CD89`
  - 警告: 黄色背景 `#FFC700`
  - 危险: 红色背景 `#F1416C`
  - 信息: 蓝色背景 `#009EF7`
  - 默认: 灰色背景 `#A1A5B7`
- **样式规范**:
  - 圆角: `0.375rem`
  - 内边距: `0.25rem 0.5rem`
  - 字体大小: `0.75rem`
  - 字重: 500

#### 进度条组件
- **基础进度条**:
  - 高度: `8px`
  - 圆角: `4px`
  - 背景: `#E4E6EA`
  - 进度色: 主品牌色或状态色
- **带标签进度条**: 显示百分比数值
- **多段进度条**: 不同颜色段表示不同状态
- **环形进度条**: 用于仪表盘展示

### 交互组件

#### 搜索组件
- **搜索框设计**:
  - 圆角输入框 + 搜索图标
  - 占位符文本: 浅色
  - 聚焦状态: 主品牌色边框
- **高级搜索**:
  - 展开式过滤器
  - 多条件组合搜索
  - 搜索历史记录
- **搜索结果**:
  - 高亮匹配文本
  - 分类结果展示
  - 无结果状态页面

#### 过滤器组件
- **过滤按钮**:
  - 默认状态: 透明背景，边框
  - 激活状态: 主品牌色背景
  - 过滤数量徽章
- **下拉过滤器**:
  - 多选复选框
  - 单选单选框
  - 日期范围选择器
- **标签过滤**: 可删除的过滤标签

#### 操作按钮组
- **主要操作**:
  - 新增按钮: 主品牌色背景
  - 导出按钮: 次要样式
  - 批量操作: 危险色样式
- **按钮组合**:
  - 左对齐: 主要操作
  - 右对齐: 次要操作
  - 间距: `0.5rem`

#### 下拉菜单组件
- **触发器**: 三点图标或下拉箭头
- **菜单样式**:
  - 白色背景
  - 轻微阴影: `0 0.5rem 1rem rgba(0, 0, 0, 0.15)`
  - 圆角: `0.5rem`
  - 最小宽度: `160px`
- **菜单项**:
  - 高度: `40px`
  - 内边距: `0.5rem 1rem`
  - 悬停背景: `#F9F9F9`
  - 分割线: 1px实线

### 表单组件

#### 输入框组件
- **基础输入框**:
  - 边框: 1px实线 `#E4E6EA`
  - 圆角: `0.375rem`
  - 高度: `40px`
  - 内边距: `0.75rem`
- **输入框状态**:
  - 默认: 灰色边框
  - 聚焦: 主品牌色边框 + 阴影
  - 错误: 红色边框 + 错误信息
  - 成功: 绿色边框 + 成功图标
  - 禁用: 灰色背景 + 禁用样式

#### 选择器组件
- **下拉选择器**:
  - 与输入框相同样式
  - 下拉箭头图标
  - 选项列表: 白色背景 + 阴影
- **多选选择器**:
  - 选中项标签显示
  - 标签可删除
  - 搜索过滤功能
- **日期选择器**:
  - 日历弹窗
  - 日期范围选择
  - 快捷日期选项

#### 文件上传组件
- **拖拽上传区**:
  - 虚线边框
  - 上传图标 + 提示文字
  - 拖拽悬停状态
- **文件列表**:
  - 文件图标 + 文件名 + 大小
  - 上传进度条
  - 删除/重新上传按钮
- **上传限制**: 文件类型、大小限制提示

#### 表单验证组件
- **实时验证**: 失焦时验证
- **错误提示**:
  - 红色文字
  - 错误图标
  - 具体错误信息
- **成功提示**:
  - 绿色文字
  - 成功图标

### 导航组件

#### 面包屑导航
- **样式设计**:
  - 分隔符: `/` 或 `>`
  - 当前页面: 粗体显示
  - 链接样式: 主品牌色
- **响应式**: 移动端收缩显示

#### 标签页组件
- **标签样式**:
  - 默认: 透明背景
  - 激活: 底部边框 + 主品牌色
  - 悬停: 浅色背景
- **标签内容**:
  - 图标 + 文字
  - 关闭按钮（可选）
- **标签位置**: 顶部、左侧、右侧

#### 步骤条组件
- **步骤样式**:
  - 圆形步骤号
  - 连接线
  - 步骤标题 + 描述
- **步骤状态**:
  - 已完成: 绿色 + 对勾图标
  - 当前: 主品牌色 + 数字
  - 未完成: 灰色 + 数字
- **方向**: 水平、垂直布局

### 反馈组件

#### 通知组件
- **Toast通知**:
  - 位置: 右上角
  - 自动消失: 3-5秒
  - 类型: 成功、警告、错误、信息
- **通知中心**:
  - 通知列表
  - 未读数量徽章
  - 分类标签
- **系统通知**:
  - 重要通知置顶
  - 时间戳显示
  - 操作按钮

#### 确认对话框
- **对话框样式**:
  - 居中显示
  - 白色背景 + 阴影
  - 圆角: `0.5rem`
- **内容结构**:
  - 标题 + 图标
  - 描述文字
  - 确认/取消按钮
- **类型**: 删除确认、保存确认、离开确认

#### 加载状态组件
- **按钮加载**:
  - 旋转图标 + 加载文字
  - 禁用状态
- **页面加载**:
  - 骨架屏
  - 加载动画
  - 进度指示器
- **数据加载**:
  - 表格加载状态
  - 列表加载状态