// 智能体矩阵 Content Script - 独立生命周期版本
// 只负责DOM操作，不包含UI

class IntelligentMatrixContentScript {
    constructor() {
        this.serverUrl = '';
        this.authToken = '';
        this.init();
    }

    init() {
        console.log('🚀 智能体矩阵 Content Script 初始化...');

        try {
            // 监听来自Side Panel的消息
            this.bindMessageListener();

            // 加载设置
            this.loadSettings();

            console.log('✅ Content Script 初始化完成');
        } catch (error) {
            console.error('❌ Content Script 初始化失败:', error);
            console.error('❌ 错误堆栈:', error.stack);
        }
    }

    // 加载设置
    async loadSettings() {
        try {
            const result = await chrome.storage.local.get(['serverUrl', 'authToken']);
            this.serverUrl = result.serverUrl || 'http://localhost:8080';
            this.authToken = result.authToken || '';
        } catch (error) {
            console.error('❌ 加载设置失败:', error);
        }
    }

    // 监听消息
    bindMessageListener() {
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            console.log('🎯 Content Script收到消息:', message.action, message);
            
            switch (message.action) {
                case 'ping':
                    // 简单的连接测试
                    sendResponse({ success: true, message: 'Content script is alive' });
                    return true;

                case 'getCurrentPageInfo':
                    // 获取当前页面信息
                    this.handleGetCurrentPageInfo()
                        .then(result => sendResponse(result))
                        .catch(error => sendResponse({ success: false, message: error.message }));
                    return true;

                case 'captureScreen':
                    this.handleCaptureScreen(message.agent)
                        .then(result => sendResponse(result))
                        .catch(error => sendResponse({ success: false, message: error.message }));
                    return true;

                case 'checkPageBinding':
                    this.handleCheckPageBinding(message.agent)
                        .then(result => sendResponse(result))
                        .catch(error => sendResponse({ success: false, message: error.message }));
                    return true;
                    
                case 'autoFillForm':
                    this.handleAutoFillForm(message.data, message.binding, message.agent)
                        .then(result => sendResponse(result))
                        .catch(error => sendResponse({ success: false, message: error.message }));
                    return true;
                    
                case 'detectForms':
                    this.handleDetectForms()
                        .then(result => sendResponse(result))
                        .catch(error => sendResponse({ success: false, message: error.message }));
                    return true;
                    
                case 'extractData':
                    // 为了兼容性，让旧的extractData也使用新的方法
                    this.handleExtractFormData()
                        .then(result => sendResponse(result))
                        .catch(error => sendResponse({ success: false, message: error.message }));
                    return true;
                    
                case 'extractHTML':
                    this.handleExtractHTML()
                        .then(result => sendResponse(result))
                        .catch(error => sendResponse({ success: false, message: error.message }));
                    return true;

                case 'extractFormData':
                    this.handleExtractFormData()
                        .then(result => sendResponse(result))
                        .catch(error => sendResponse({ success: false, message: error.message }));
                    return true;

                default:
                    console.warn('⚠️ 未知的消息类型:', message.action);
                    sendResponse({ success: false, message: '未知的消息类型' });
            }
        });
    }

    // 获取当前页面信息
    async handleGetCurrentPageInfo() {
        try {
            console.log('📍 获取当前页面信息...');

            const pageInfo = {
                url: window.location.href,
                title: document.title,
                hostname: window.location.hostname,
                pathname: window.location.pathname,
                search: window.location.search,
                hash: window.location.hash,
                protocol: window.location.protocol,
                timestamp: new Date().toISOString()
            };

            console.log('✅ 页面信息获取成功:', pageInfo);

            return {
                success: true,
                data: pageInfo,
                message: '页面信息获取成功'
            };
        } catch (error) {
            console.error('❌ 获取页面信息失败:', error);
            return {
                success: false,
                message: '获取页面信息失败: ' + error.message
            };
        }
    }

    // 处理表单数据提取
    async handleExtractFormData() {
        try {
            console.log('📊 开始提取表单数据...');

            const formData = this.extractAllFormData();

            return {
                success: true,
                data: formData,
                message: `成功提取 ${formData.totalFields} 个表单字段`
            };
        } catch (error) {
            console.error('❌ 提取表单数据失败:', error);
            return {
                success: false,
                message: '提取表单数据失败: ' + error.message
            };
        }
    }

    // 提取所有表单数据
    extractAllFormData() {
        const fields = [];

        // 获取所有可能的表单元素
        const selectors = [
            'input[type="text"]',
            'input[type="email"]',
            'input[type="password"]',
            'input[type="number"]',
            'input[type="tel"]',
            'input[type="url"]',
            'input[type="search"]',
            'input[type="date"]',
            'input[type="datetime-local"]',
            'input[type="time"]',
            'input[type="month"]',
            'input[type="week"]',
            'input[type="color"]',
            'input[type="range"]',
            'input[type="file"]',
            'input[type="hidden"]',
            'input[type="radio"]:checked',
            'input[type="checkbox"]:checked',
            'select',
            'textarea'
        ];

        // 遍历所有iframe
        const allFrames = [window, ...Array.from(document.querySelectorAll('iframe')).map(iframe => {
            try {
                return iframe.contentWindow;
            } catch (e) {
                return null;
            }
        }).filter(w => w)];

        allFrames.forEach((frameWindow, frameIndex) => {
            try {
                const frameDoc = frameWindow.document;
                const framePrefix = frameIndex === 0 ? '' : `[iframe-${frameIndex}] `;

                selectors.forEach(selector => {
                    const elements = frameDoc.querySelectorAll(selector);
                    elements.forEach(element => {
                        const fieldData = this.extractFieldData(element, framePrefix);
                        if (fieldData) {
                            fields.push(fieldData);
                        }
                    });
                });
            } catch (error) {
                console.warn(`⚠️ 无法访问iframe ${frameIndex}:`, error);
            }
        });

        // 统计信息
        const filledFields = fields.filter(field => field.value && field.value.trim() !== '').length;
        const emptyFields = fields.length - filledFields;

        return {
            totalFields: fields.length,
            filledFields: filledFields,
            emptyFields: emptyFields,
            fields: fields,
            extractTime: new Date().toISOString()
        };
    }

    // 提取单个字段数据
    extractFieldData(element, framePrefix = '') {
        try {
            const tagName = element.tagName.toLowerCase();
            const type = element.type || '';

            // 获取字段值
            let value = '';
            if (tagName === 'select') {
                // 对于select，获取选中的选项文本和值
                const selectedOption = element.options[element.selectedIndex];
                if (selectedOption) {
                    value = `${selectedOption.text} (value: ${selectedOption.value})`;
                } else {
                    value = element.value;
                }
            } else if (type === 'radio' || type === 'checkbox') {
                value = element.checked ? element.value : '';
            } else {
                value = element.value || '';
            }

            // 获取字段标识信息
            const fieldName = element.name || element.id || element.getAttribute('placeholder') || '';
            const fieldId = element.id || '';

            // 生成选择器
            let selector = '';
            if (element.id) {
                selector = `#${element.id}`;
            } else if (element.name) {
                selector = `${tagName}[name="${element.name}"]`;
            } else if (element.className) {
                selector = `${tagName}.${element.className.split(' ')[0]}`;
            } else {
                selector = tagName;
            }

            // 获取关联的label
            let label = '';
            if (element.id) {
                const labelElement = document.querySelector(`label[for="${element.id}"]`);
                if (labelElement) {
                    label = labelElement.textContent.trim();
                }
            }

            return {
                name: framePrefix + (fieldName || fieldId || '未知字段'),
                id: fieldId,
                type: type || tagName,
                value: value,
                selector: selector,
                label: label,
                tagName: tagName,
                placeholder: element.placeholder || '',
                required: element.required || false,
                disabled: element.disabled || false,
                readonly: element.readOnly || false
            };
        } catch (error) {
            console.warn('⚠️ 提取字段数据失败:', error);
            return null;
        }
    }

    // 处理截图识别
    async handleCaptureScreen(agent) {
        try {
            console.log('📷 开始截图识别...');

            // 获取最新的token（重要：确保使用最新的认证信息）
            await this.loadSettings();

            // 通过background script截图
            const captureResult = await new Promise((resolve, reject) => {
                chrome.runtime.sendMessage({
                    action: 'captureScreen',
                    options: { format: 'png', quality: 90 }
                }, (response) => {
                    if (response && response.success) {
                        resolve(response);
                    } else {
                        reject(new Error(response?.error || '截图失败'));
                    }
                });
            });

            console.log('📷 截图完成，开始上传...');

            // 将dataURL转换为Blob
            const response = await fetch(captureResult.dataUrl);
            const blob = await response.blob();

            // 使用同步识别接口进行截图识别
            const formData = new FormData();
            formData.append('file', blob, 'screenshot.png');
            formData.append('agentCode', agent.agentCode);

            console.log('📤 开始截图识别...');

            // 使用同步识别接口
            const recognitionResponse = await fetch(`${this.serverUrl}/api/v1/recognition/analyzeByCode`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.authToken}`
                },
                body: formData
            });

            const recognitionResult = await recognitionResponse.json();

            if (recognitionResult.code === 200) {
                console.log('✅ 截图识别完成');

                // 提取并解析result字段中的JSON字符串
                let parsedResult = recognitionResult.data;
                if (parsedResult && parsedResult.result && typeof parsedResult.result === 'string') {
                    try {
                        // 解析JSON字符串，去掉转义
                        parsedResult.result = JSON.parse(parsedResult.result);
                    } catch (parseError) {
                        console.warn('解析result JSON失败，保持原始字符串:', parseError);
                    }
                }

                return {
                    success: true,
                    data: parsedResult,
                    message: '截图识别完成'
                };
            } else {
                throw new Error(recognitionResult.message || '识别失败');
            }
        } catch (error) {
            console.error('❌ 截图识别失败:', error);
            throw error;
        }
    }

    // 处理页面绑定检查（通过Side Panel避免混合内容问题）
    async handleCheckPageBinding(agent) {
        try {
            console.log('🔍 Content Script转发页面绑定检查...');
            console.log('🤖 使用Agent:', agent?.agentName);

            if (!agent || !agent.id) {
                throw new Error('Agent信息缺失，请先选择Agent');
            }

            const currentUrl = window.location.href;
            console.log('🌐 当前页面URL:', currentUrl);

            // 直接通过消息传递给Side Panel处理，避免混合内容问题
            const result = await new Promise((resolve, reject) => {
                chrome.runtime.sendMessage({
                    action: 'performPageBindingCheck',
                    currentUrl: currentUrl,
                    agent: agent
                }, (response) => {
                    if (chrome.runtime.lastError) {
                        reject(new Error(chrome.runtime.lastError.message));
                        return;
                    }

                    if (response && response.success === false) {
                        reject(new Error(response.message || '页面绑定检查失败'));
                    } else {
                        resolve(response);
                    }
                });
            });

            console.log('📋 页面绑定检查结果:', result);

            // 检查是否是有效的API响应
            if (result && result.code === 200) {
                const data = result.data;

                // 如果有多个绑定配置，返回供用户选择
                if (data.multipleBindings) {
                    // 确保每个绑定都包含完整的配置信息
                    const bindings = data.bindings.map(binding => {
                        // 如果binding中没有bindingConfig，但有单独的bindingConfig字段，则合并
                        if (!binding.bindingConfig && data.bindingConfig) {
                            return { ...binding, bindingConfig: data.bindingConfig };
                        }
                        return binding;
                    });

                    return {
                        success: true,
                        data: bindings,
                        message: data.message || '找到多个匹配的绑定配置'
                    };
                }

                // 如果只有一个绑定配置，直接返回
                if (data.binding) {
                    // 将bindingConfig合并到binding对象中
                    const binding = { ...data.binding };
                    if (data.bindingConfig && !binding.bindingConfig) {
                        binding.bindingConfig = data.bindingConfig;
                    }

                    return {
                        success: true,
                        data: [binding],
                        message: data.message || '找到匹配的绑定配置'
                    };
                }

                // 没有找到匹配的绑定
                return {
                    success: true,
                    data: [],
                    message: data.message || '没有找到匹配的绑定配置'
                };
            } else if (result && result.code !== undefined) {
                throw new Error(result.message || '页面绑定检查失败');
            } else {
                // 如果响应格式不正确，说明可能是Side Panel没有正确处理
                console.warn('⚠️ 收到意外的响应格式:', result);
                throw new Error('Side Panel响应格式错误，请检查Side Panel是否正常工作');
            }
        } catch (error) {
            console.error('❌ 页面绑定检查失败:', error);

            // 如果是页面连接问题，提供更友好的提示
            if (error.message.includes('页面连接异常')) {
                throw new Error('页面连接异常，请刷新当前页面后重试');
            }

            throw error;
        }
    }

    // 处理自动填充表单
    async handleAutoFillForm(data, binding, agent) {
        try {
            console.log('✏️ 开始自动填充表单...');
            console.log('📋 使用绑定配置:', binding?.bindingName);
            console.log('🤖 使用Agent:', agent?.agentName);
            console.log('📄 填充数据:', data);
            console.log('🔍 绑定类型检查:', {
                isMultiStep: binding?.isMultiStep,
                hasSubSteps: binding?.subSteps?.length > 0,
                subStepsCount: binding?.subSteps?.length,
                bindingConfig: binding?.bindingConfig,
                subStepsDetails: binding?.subSteps?.map(step => ({
                    stepOrder: step.stepOrder,
                    stepName: step.stepName,
                    hasConfig: !!step.bindingConfig
                }))
            });

            if (!binding) {
                throw new Error('未提供页面绑定配置');
            }

            let fillCount = 0;
            let fillResults = [];

            // 解析绑定配置
            let bindingConfig;
            try {
                bindingConfig = typeof binding.bindingConfig === 'string'
                    ? JSON.parse(binding.bindingConfig)
                    : binding.bindingConfig;
                console.log('🔧 解析后的绑定配置:', bindingConfig);
            } catch (error) {
                throw new Error('绑定配置格式错误: ' + error.message);
            }

            // 检查是否为多步骤绑定
            if (binding.isMultiStep === 1 && binding.subSteps && Array.isArray(binding.subSteps)) {
                console.log('🔄 处理多步骤绑定，总步骤数:', binding.subSteps.length + 1);
                console.log('🔍 主绑定配置检查:', {
                    hasBindingConfig: !!binding.bindingConfig,
                    bindingConfigType: typeof binding.bindingConfig,
                    bindingConfigContent: binding.bindingConfig
                });

                // 处理第一步（主绑定）
                // 兼容多种数据格式和存储方式
                let firstStepMappings = null;

                // 尝试从主绑定的bindingConfig中获取
                if (bindingConfig && bindingConfig.fieldMappings && Array.isArray(bindingConfig.fieldMappings)) {
                    firstStepMappings = bindingConfig.fieldMappings;
                    console.log('✅ 从主绑定bindingConfig.fieldMappings获取第1步配置');
                } else if (Array.isArray(bindingConfig)) {
                    firstStepMappings = bindingConfig;
                    console.log('✅ 从主绑定bindingConfig数组获取第1步配置');
                } else {
                    // 如果主绑定没有配置，尝试从第一个子步骤获取（兼容旧数据）
                    console.warn('⚠️ 主绑定没有配置，尝试从子步骤查找第1步配置');
                    const firstSubStep = binding.subSteps.find(step => step.stepOrder === 1);
                    if (firstSubStep && firstSubStep.bindingConfig) {
                        try {
                            const firstSubStepConfig = typeof firstSubStep.bindingConfig === 'string'
                                ? JSON.parse(firstSubStep.bindingConfig)
                                : firstSubStep.bindingConfig;
                            if (firstSubStepConfig && firstSubStepConfig.fieldMappings) {
                                firstStepMappings = firstSubStepConfig.fieldMappings;
                                console.log('✅ 从第一个子步骤获取第1步配置');
                            }
                        } catch (error) {
                            console.error('❌ 解析第一个子步骤配置失败:', error);
                        }
                    }
                }

                if (firstStepMappings && firstStepMappings.length > 0) {
                    console.log('📝 执行第1步填充，字段数:', firstStepMappings.length);

                    // 通知侧边栏更新进度
                    this.notifyProgress(1, binding.subSteps.length + 1, '正在填充第1步表单字段...');

                    for (const mapping of firstStepMappings) {
                        const result = await this.fillSingleField(mapping, data);
                        if (result.success) fillCount++;
                        fillResults.push(result);
                    }
                } else {
                    console.warn('⚠️ 第1步没有字段映射配置');
                }

                // 处理后续步骤（过滤掉stepOrder为1的步骤，因为第1步由主绑定处理）
                const actualSubSteps = binding.subSteps.filter(step => step.stepOrder > 1);
                console.log('🔍 实际子步骤数量:', actualSubSteps.length, '（已过滤stepOrder=1的步骤）');

                for (let i = 0; i < actualSubSteps.length; i++) {
                    const subStep = actualSubSteps[i];
                    const stepNumber = subStep.stepOrder || (i + 2);
                    console.log(`📝 执行第${stepNumber}步填充:`, subStep.stepName, `(stepOrder: ${subStep.stepOrder})`);

                    // 先执行上一步的下一步动作（如果有）
                    if (i === 0) {
                        // 第一步到第二步的动作在主绑定的nextAction中
                        await this.executeNextAction(binding.nextAction, `第1步到第2步`, binding.waitTime);
                    } else {
                        // 其他步骤的动作在前一个子步骤的nextAction中
                        const prevSubStep = actualSubSteps[i - 1];
                        await this.executeNextAction(prevSubStep.nextAction, `第${stepNumber - 1}步到第${stepNumber}步`, prevSubStep.waitTime);
                    }

                    // 通知侧边栏更新进度
                    this.notifyProgress(stepNumber, actualSubSteps.length + 1, `正在填充第${stepNumber}步表单字段...`);

                    // 解析子步骤的绑定配置
                    let subStepConfig;
                    try {
                        subStepConfig = typeof subStep.bindingConfig === 'string'
                            ? JSON.parse(subStep.bindingConfig)
                            : subStep.bindingConfig;
                    } catch (error) {
                        console.error(`❌ 子步骤${stepNumber}配置解析失败:`, error);
                        continue;
                    }

                    // 执行子步骤的字段填充
                    // 兼容两种数据格式：{fieldMappings: [...]} 或直接 [...]
                    let subStepMappings = null;
                    if (subStepConfig && subStepConfig.fieldMappings && Array.isArray(subStepConfig.fieldMappings)) {
                        subStepMappings = subStepConfig.fieldMappings;
                    } else if (Array.isArray(subStepConfig)) {
                        subStepMappings = subStepConfig;
                    }

                    if (subStepMappings && subStepMappings.length > 0) {
                        console.log(`📝 第${stepNumber}步字段数:`, subStepMappings.length);
                        for (const mapping of subStepMappings) {
                            const result = await this.fillSingleField(mapping, data);
                            if (result.success) fillCount++;
                            fillResults.push(result);
                        }
                    } else {
                        console.warn(`⚠️ 第${stepNumber}步没有字段映射配置`);
                    }
                }
            } else {
                // 单步骤绑定的原有逻辑
                console.log('📝 处理单步骤绑定');

                // 兼容两种数据格式：{fieldMappings: [...]} 或直接 [...]
                let singleStepMappings = null;
                if (bindingConfig && bindingConfig.fieldMappings && Array.isArray(bindingConfig.fieldMappings)) {
                    singleStepMappings = bindingConfig.fieldMappings;
                } else if (Array.isArray(bindingConfig)) {
                    singleStepMappings = bindingConfig;
                }

                if (singleStepMappings && singleStepMappings.length > 0) {
                    console.log('📝 单步骤字段数:', singleStepMappings.length);

                    // 并发填充所有字段
                    const fillPromises = singleStepMappings.map(mapping =>
                        this.fillSingleField(mapping, data, 1000) // 1秒超时，因为不支持异步加载
                    );

                    const results = await Promise.allSettled(fillPromises);

                    results.forEach((result, index) => {
                        if (result.status === 'fulfilled') {
                            const fillResult = result.value;
                            if (fillResult.success) fillCount++;
                            fillResults.push(fillResult);
                        } else {
                            // 处理Promise被拒绝的情况
                            const mapping = singleStepMappings[index];
                            const fieldKey = mapping.fieldKey || mapping.jsonField;
                            fillResults.push({
                                field: fieldKey,
                                selector: mapping.selector,
                                value: null,
                                success: false,
                                status: 'promise-rejected',
                                reason: result.reason?.message || '未知错误',
                                duration: 1000 // 超时时间
                            });
                        }
                    });
                } else {
                    console.warn('⚠️ 单步骤没有字段映射配置');
                }
            }

            // 如果没有填充任何字段，给出提示
            if (fillCount === 0) {
                console.warn('⚠️ 没有成功填充任何字段，请检查绑定配置和数据结构');
            }

            // 多步骤操作已在上面的逻辑中处理

            // 计算实际尝试填充的字段总数（只计算已执行的步骤）
            let totalFields = fillResults.length;

            // 统计详细结果
            const successResults = fillResults.filter(r => r.success);
            const failedResults = fillResults.filter(r => !r.success);
            const skippedResults = fillResults.filter(r => r.status === 'empty-data');
            const errorResults = fillResults.filter(r => r.status === 'error' || r.status === 'promise-rejected');

            // 计算总耗时
            const totalDuration = fillResults.reduce((sum, r) => sum + (r.duration || 0), 0);

            // 按控件类型分组统计
            const controlTypeStats = {};
            fillResults.forEach(result => {
                const type = result.controlType || 'unknown';
                if (!controlTypeStats[type]) {
                    controlTypeStats[type] = { total: 0, success: 0, failed: 0 };
                }
                controlTypeStats[type].total++;
                if (result.success) {
                    controlTypeStats[type].success++;
                } else {
                    controlTypeStats[type].failed++;
                }
            });

            return {
                success: true,
                data: {
                    fillCount,
                    totalFields,
                    bindingName: binding.bindingName,
                    isMultiStep: binding.isMultiStep === 1,
                    stepCount: binding.isMultiStep === 1 ? (binding.subSteps?.length || 0) + 1 : 1,
                    results: fillResults,
                    // 详细统计信息
                    statistics: {
                        success: successResults.length,
                        failed: failedResults.length,
                        skipped: skippedResults.length,
                        errors: errorResults.length,
                        totalDuration,
                        averageDuration: totalFields > 0 ? Math.round(totalDuration / totalFields) : 0,
                        controlTypeStats
                    },
                    // 分类结果
                    successResults,
                    failedResults,
                    skippedResults,
                    errorResults
                },
                message: `表单填充完成，成功 ${fillCount}/${totalFields} 个字段 (${totalDuration}ms)`
            };
        } catch (error) {
            console.error('❌ 自动填充表单失败:', error);
            throw error;
        }
    }

    // 获取嵌套对象的值
    getNestedValue(obj, path) {
        if (!obj || !path) return undefined;

        // 支持点号分隔的路径，如 "preconfigured_entry.master_airwaybill_number"
        const keys = path.split('.');
        let current = obj;

        for (const key of keys) {
            if (current === null || current === undefined) {
                return undefined;
            }
            current = current[key];
        }

        return current;
    }

    // 填充单个字段（增强版，支持详细结果）
    async fillSingleField(mapping, data, timeout = 1000) {
        const startTime = Date.now();
        const fieldKey = mapping.fieldKey || mapping.jsonField;

        try {
            // 首先尝试直接选择器
            let element = this.findElement(mapping.selector);

            // 如果直接选择器失败，且有存储的元素信息，使用智能匹配
            if (!element && mapping.elementInfo) {
                console.log('🔍 直接选择器失败，使用存储的元素信息进行智能匹配');
                element = this.findElementByIntelligentMatching(mapping.selector, mapping.elementInfo);
            }

            if (!element) {
                return {
                    field: fieldKey,
                    selector: mapping.selector,
                    value: null,
                    success: false,
                    status: 'element-not-found',
                    reason: '元素未找到',
                    duration: Date.now() - startTime,
                    elementInfo: mapping.elementInfo
                };
            }

            if (!fieldKey) {
                return {
                    field: fieldKey,
                    selector: mapping.selector,
                    value: null,
                    success: false,
                    status: 'no-field-key',
                    reason: '缺少字段键',
                    duration: Date.now() - startTime
                };
            }

            // 支持嵌套字段路径
            const value = this.getNestedValue(data, fieldKey);
            if (value === undefined || value === null) {
                return {
                    field: fieldKey,
                    selector: mapping.selector,
                    value: value,
                    success: false,
                    status: 'empty-data',
                    reason: '数据为空',
                    duration: Date.now() - startTime
                };
            }

            // 直接填充，不使用复杂的超时控制（因为已经移除异步加载）
            const fillResult = await this.fillElement(element, value, mapping.elementInfo);
            const duration = Date.now() - startTime;

            console.log(`✅ 填充字段: ${fieldKey} -> ${mapping.selector} = ${value} (${duration}ms)`);

            return {
                field: fieldKey,
                selector: mapping.selector,
                value: value,
                success: fillResult.success,
                status: fillResult.success ? 'success' : 'fill-failed',
                reason: fillResult.success ? fillResult.method : fillResult.reason,
                duration,
                fillDetails: fillResult,
                controlType: mapping.controlType || 'unknown'
            };

        } catch (error) {
            const duration = Date.now() - startTime;
            console.warn(`❌ 填充字段失败: ${fieldKey}`, error);

            return {
                field: fieldKey,
                selector: mapping.selector,
                value: null,
                success: false,
                status: 'error',
                reason: error.message,
                duration,
                error: error.message
            };
        }
    }

    // 查找元素（优先在可见iframe中查找）
    findElement(selector, elementInfo = null) {
        console.log(`🔍 查找元素: ${selector}`);

        // 如果没有传入elementInfo，尝试从全局变量获取
        if (!elementInfo && window.currentActionElement) {
            elementInfo = window.currentActionElement;
            console.log(`🔍 使用全局元素信息:`, elementInfo);
        }

        // 1. 首先在主文档中查找
        const mainElements = document.querySelectorAll(selector);
        if (mainElements.length > 0) {
            console.log(`✅ 在主文档中找到 ${mainElements.length} 个匹配元素`);
            if (mainElements.length === 1) {
                // 即使只有1个匹配，也要检查可见性！
                const element = mainElements[0];
                const isVisible = this.isElementVisible(element) && this.isElementAndParentsVisible(element);
                if (isVisible) {
                    console.log(`✅ 主文档中的唯一元素通过可见性检查`);
                    return element;
                } else {
                    console.log(`⚠️ 主文档中的唯一元素未通过可见性检查，继续查找其他位置`);
                    // 继续查找其他位置
                }
            }
            // 如果主文档中有多个匹配，先记录下来
        }

        // 2. 检测当前最上层/可见的iframe
        const topIframe = this.detectTopIframe();
        if (topIframe) {
            try {
                const iframeDoc = topIframe.contentDocument || topIframe.contentWindow.document;
                const topIframeElements = iframeDoc.querySelectorAll(selector);
                if (topIframeElements.length > 0) {
                    console.log(`🎯 在最上层iframe中找到 ${topIframeElements.length} 个匹配元素，优先使用`);
                    if (topIframeElements.length === 1) {
                        // 即使只有1个匹配，也要检查可见性！
                        const element = topIframeElements[0];
                        const isVisible = this.isElementVisible(element) && this.isElementAndParentsVisible(element);
                        if (isVisible) {
                            console.log(`✅ 最上层iframe中的唯一元素通过可见性检查`);
                            return element;
                        } else {
                            console.log(`⚠️ 最上层iframe中的唯一元素未通过可见性检查，继续查找其他位置`);
                            // 继续查找其他位置
                        }
                    }
                    // 如果最上层iframe中有多个匹配，尝试精确选择
                    const bestElement = this.selectBestElement(Array.from(topIframeElements), selector, elementInfo);
                    if (bestElement) {
                        console.log(`✅ 从最上层iframe中选择最佳元素`);
                        return bestElement;
                    } else {
                        console.warn(`⚠️ 最上层iframe中有${topIframeElements.length}个匹配元素，但无法精确确定选择哪个`);
                        return null; // 不继续查找其他位置
                    }
                }
            } catch (error) {
                console.warn('无法访问最上层iframe内容:', error);
            }
        }

        // 3. 如果主文档中有匹配元素，使用它们（这里已经在步骤1中检查过了）
        if (mainElements.length > 1) {
            console.log(`🔄 使用主文档中的多个匹配元素`);
            // 对于多个匹配元素，使用selectBestElement进行选择
            const bestElement = this.selectBestElement(Array.from(mainElements), selector, elementInfo);
            if (bestElement) {
                return bestElement;
            } else {
                console.warn(`⚠️ 主文档中有${mainElements.length}个匹配元素，但无法精确确定选择哪个`);
                return null; // 不继续查找其他位置
            }
        }

        // 4. 最后在其他iframe中查找
        console.log(`🔍 在其他iframe中查找...`);
        const allIframeElements = [];
        const iframes = document.querySelectorAll('iframe');
        for (const iframe of iframes) {
            // 跳过已经检查过的最上层iframe
            if (iframe === topIframe) continue;

            try {
                const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                const iframeElements = iframeDoc.querySelectorAll(selector);
                if (iframeElements.length > 0) {
                    allIframeElements.push(...Array.from(iframeElements));
                }
            } catch (error) {
                console.warn('无法访问iframe内容:', error);
            }
        }

        if (allIframeElements.length > 0) {
            console.log(`📋 在其他iframe中找到 ${allIframeElements.length} 个匹配元素`);
            const bestElement = this.selectBestElement(allIframeElements, selector, elementInfo);
            if (bestElement) {
                return bestElement;
            } else {
                console.warn(`⚠️ 其他iframe中有${allIframeElements.length}个匹配元素，但无法精确确定选择哪个`);
                return null; // 不使用智能匹配作为兜底
            }
        }

        // 5. 如果所有直接选择器都没找到元素，尝试智能匹配
        console.log(`🔍 直接选择器未找到元素，尝试智能匹配: ${selector}`);
        const intelligentElement = this.findElementByIntelligentMatching(selector);
        if (intelligentElement) {
            console.log(`✅ 智能匹配找到元素:`, intelligentElement.tagName, intelligentElement.textContent?.trim().substring(0, 20));
            return intelligentElement;
        }

        console.warn(`⚠️ 未找到匹配元素: ${selector}`);
        return null;
    }

    // 按优先级查找按钮（基于文本内容）
    findButtonByTextWithPriority(buttonText) {
        console.log(`🔍 按优先级查找按钮: "${buttonText}"`);

        // 辅助函数：检查按钮文本是否匹配
        const isTextMatch = (btn) => {
            const btnText = btn.textContent?.trim() || '';
            // 先尝试精确匹配，再尝试包含匹配
            return btnText === buttonText || btnText.includes(buttonText);
        };

        // 辅助函数：在指定文档中查找按钮（要求唯一匹配）
        const findInDocument = (doc, location) => {
            const buttons = doc.querySelectorAll('button');
            const allButtons = [];
            const matchingButtons = [];

            console.log(`🔍 在${location}中检查所有按钮...`);

            for (const btn of buttons) {
                const btnText = btn.textContent?.trim();
                const isVisible = this.isElementVisible(btn);
                const parentsVisible = this.isElementAndParentsVisible(btn);
                const textMatches = isTextMatch(btn);

                allButtons.push({
                    text: btnText,
                    className: btn.className,
                    isVisible: isVisible,
                    parentsVisible: parentsVisible,
                    textMatches: textMatches,
                    outerHTML: btn.outerHTML.substring(0, 150) + '...'
                });

                if (textMatches && isVisible && parentsVisible) {
                    matchingButtons.push(btn);
                }
            }

            console.log(`🔍 ${location}中所有按钮详情:`, allButtons);
            console.log(`🔍 ${location}中匹配的按钮数量: ${matchingButtons.length}`);

            if (matchingButtons.length === 1) {
                console.log(`✅ 在${location}中找到唯一匹配按钮: "${buttonText}" (实际文本: "${matchingButtons[0].textContent?.trim()}")`);
                return matchingButtons[0];
            } else if (matchingButtons.length > 1) {
                console.warn(`⚠️ 在${location}中找到${matchingButtons.length}个匹配按钮，文本都包含: "${buttonText}"`);
                console.warn(`⚠️ 匹配的按钮详情:`, matchingButtons.map(btn => ({
                    text: btn.textContent?.trim(),
                    className: btn.className,
                    outerHTML: btn.outerHTML.substring(0, 100) + '...'
                })));
                return null; // 不选择任何按钮
            }

            return null;
        };

        // 1. 首先在主文档中查找
        let foundButton = findInDocument(document, '主文档');
        if (foundButton) return foundButton;
        // 如果主文档中找到多个匹配，findInDocument会返回null，我们应该停止查找

        // 检查主文档中是否有多个匹配（这种情况下不应该继续查找其他位置）
        const mainButtons = document.querySelectorAll('button');
        const mainMatches = Array.from(mainButtons).filter(btn =>
            isTextMatch(btn) && this.isElementVisible(btn) && this.isElementAndParentsVisible(btn)
        );
        if (mainMatches.length > 1) {
            console.warn(`⚠️ 主文档中有多个匹配按钮，停止查找其他位置`);
            return null;
        }

        // 2. 然后在最上层iframe中查找
        const topIframe = this.detectTopIframe();
        if (topIframe) {
            try {
                const iframeDoc = topIframe.contentDocument || topIframe.contentWindow.document;
                foundButton = findInDocument(iframeDoc, '最上层iframe');
                if (foundButton) return foundButton;

                // 检查最上层iframe中是否有多个匹配
                const iframeButtons = iframeDoc.querySelectorAll('button');
                const iframeMatches = Array.from(iframeButtons).filter(btn =>
                    isTextMatch(btn) && this.isElementVisible(btn) && this.isElementAndParentsVisible(btn)
                );
                if (iframeMatches.length > 1) {
                    console.warn(`⚠️ 最上层iframe中有多个匹配按钮，停止查找其他位置`);
                    return null;
                }
            } catch (error) {
                console.warn('无法访问最上层iframe内容:', error);
            }
        }

        // 3. 最后在其他iframe中查找
        const iframes = document.querySelectorAll('iframe');
        for (const iframe of iframes) {
            // 跳过已经检查过的最上层iframe
            if (iframe === topIframe) continue;

            try {
                const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                foundButton = findInDocument(iframeDoc, `其他iframe`);
                if (foundButton) return foundButton;

                // 如果这个iframe中有多个匹配，也停止查找
                const iframeButtons = iframeDoc.querySelectorAll('button');
                const iframeMatches = Array.from(iframeButtons).filter(btn =>
                    isTextMatch(btn) && this.isElementVisible(btn) && this.isElementAndParentsVisible(btn)
                );
                if (iframeMatches.length > 1) {
                    console.warn(`⚠️ iframe中有多个匹配按钮，停止查找`);
                    return null;
                }
            } catch (error) {
                console.warn('无法访问iframe内容:', error);
            }
        }

        console.warn(`⚠️ 未找到包含文本"${buttonText}"的按钮`);
        return null;
    }

    // 从选择器中提取元素信息
    extractElementInfoFromSelector(selector) {
        // 这个方法用于从CSS选择器中提取可能的元素信息
        // 主要用于按钮文本匹配

        // 检查是否为按钮选择器
        if (selector.includes('button')) {
            // 尝试从全局存储的绑定信息中获取
            if (window.currentActionElement && window.currentActionElement.text) {
                return {
                    text: window.currentActionElement.text,
                    tagName: 'button'
                };
            }
        }

        return null;
    }

    // 智能匹配元素（当直接选择器失败时）
    findElementByIntelligentMatching(selector, elementInfo = null) {
        // 解析选择器，提取关键信息
        const selectorInfo = this.parseSelector(selector);

        // 根据元素类型进行智能匹配
        if (selectorInfo.tagName === 'button') {
            return this.findButtonByIntelligentMatching(selectorInfo, elementInfo);
        } else if (selectorInfo.tagName === 'input') {
            return this.findInputByIntelligentMatching(selectorInfo, elementInfo);
        }

        return null;
    }

    // 解析选择器，提取关键信息
    parseSelector(selector) {
        const info = {
            tagName: '',
            classes: [],
            attributes: {},
            text: ''
        };

        // 提取标签名
        const tagMatch = selector.match(/^(\w+)/);
        if (tagMatch) {
            info.tagName = tagMatch[1];
        }

        // 提取类名
        const classMatches = selector.match(/\.([^.\[\s>+~]+)/g);
        if (classMatches) {
            info.classes = classMatches.map(c => c.substring(1));
        }

        // 提取属性
        const attrMatches = selector.match(/\[([^=\]]+)="([^"]+)"\]/g);
        if (attrMatches) {
            attrMatches.forEach(attr => {
                const match = attr.match(/\[([^=\]]+)="([^"]+)"\]/);
                if (match) {
                    info.attributes[match[1]] = match[2];
                }
            });
        }

        return info;
    }

    // 通过智能匹配查找按钮（增强版，支持存储的元素信息）
    findButtonByIntelligentMatching(selectorInfo, elementInfo = null) {
        console.log('🔍 智能匹配按钮:', selectorInfo, elementInfo);

        // 获取所有按钮
        const allButtons = document.querySelectorAll('button');
        const candidates = [];

        for (const button of allButtons) {
            let score = 0;
            const buttonText = button.textContent?.trim() || '';

            // 如果有存储的元素信息，优先使用文本匹配
            if (elementInfo && elementInfo.text) {
                const storedText = elementInfo.text;
                console.log(`🔍 比较按钮文本: "${buttonText}" vs 存储文本: "${storedText}"`);

                // 精确文本匹配
                if (buttonText === storedText) {
                    score += 100; // 最高分
                    console.log(`✅ 精确文本匹配: +100分`);
                } else if (buttonText.includes(storedText)) {
                    score += 80; // 包含匹配
                    console.log(`✅ 包含匹配: +80分`);
                } else if (storedText.includes(buttonText)) {
                    score += 60; // 被包含匹配
                    console.log(`✅ 被包含匹配: +60分`);
                }

                // 检查特定关键词
                if (storedText.includes('创建') && buttonText.includes('创建')) {
                    score += 30;
                    console.log(`✅ 创建关键词匹配: +30分`);
                }
                if (storedText.includes('+') && buttonText.includes('+')) {
                    score += 25;
                    console.log(`✅ +号匹配: +25分`);
                }
            } else {
                // 回退到原有的文本匹配逻辑
                if (buttonText.includes('订单创建') || buttonText.includes('+订单创建')) {
                    score += 50;
                }
                if (buttonText.includes('创建')) {
                    score += 30;
                }
                if (buttonText.includes('+')) {
                    score += 20;
                }
            }

            // 类名匹配（降低权重，避免误匹配）
            if (selectorInfo.classes.length > 0) {
                const buttonClasses = Array.from(button.classList);
                const matchedClasses = selectorInfo.classes.filter(cls => buttonClasses.includes(cls));
                score += matchedClasses.length * 5; // 降低类名权重
                if (matchedClasses.length > 0) {
                    console.log(`✅ 类名匹配: +${matchedClasses.length * 5}分`);
                }
            }

            // 属性匹配
            for (const [attr, value] of Object.entries(selectorInfo.attributes)) {
                if (button.getAttribute(attr) === value) {
                    score += 10;
                    console.log(`✅ 属性匹配 ${attr}: +10分`);
                }
            }

            // 可见性和可用性检查
            const style = window.getComputedStyle(button);
            const isVisible = style.display !== 'none' && style.visibility !== 'hidden';
            const isEnabled = !button.disabled && !button.classList.contains('is-disabled');

            if (isVisible) {
                score += 5;
            }
            if (isEnabled) {
                score += 10; // 可用的按钮优先级更高
                console.log(`✅ 按钮可用: +10分`);
            }

            if (score > 0) {
                candidates.push({
                    element: button,
                    score,
                    text: buttonText,
                    isVisible,
                    isEnabled
                });
            }
        }

        // 按分数排序，返回最高分的
        candidates.sort((a, b) => b.score - a.score);

        if (candidates.length > 0) {
            console.log('🎯 按钮匹配结果:', candidates.map(c => ({
                text: c.text,
                score: c.score,
                isVisible: c.isVisible,
                isEnabled: c.isEnabled
            })));
            return candidates[0].element;
        }

        return null;
    }

    // 通过智能匹配查找输入框（增强版，支持存储的元素信息）
    findInputByIntelligentMatching(selectorInfo, elementInfo = null) {
        console.log('🔍 智能匹配输入框:', selectorInfo, elementInfo);

        // 获取所有输入框
        const allInputs = document.querySelectorAll('input');
        const candidates = [];

        for (const input of allInputs) {
            let score = 0;

            // 如果有存储的元素信息，优先使用
            if (elementInfo) {
                // label匹配（最重要）
                if (elementInfo.label) {
                    const matchingLabel = this.findMatchingLabel(input, elementInfo.label);
                    if (matchingLabel) {
                        score += 40; // label匹配是最准确的
                    }
                }

                // placeholder匹配
                if (elementInfo.attributes && elementInfo.attributes.cleanPlaceholder) {
                    const inputPlaceholder = input.getAttribute('placeholder') || '';
                    const storedPlaceholder = elementInfo.attributes.cleanPlaceholder;

                    if (inputPlaceholder === storedPlaceholder) {
                        score += 30; // 精确匹配
                    } else if (inputPlaceholder.includes(storedPlaceholder) ||
                               storedPlaceholder.includes(inputPlaceholder)) {
                        score += 20; // 部分匹配
                    }
                }

                // 容器匹配
                if (elementInfo.container) {
                    const container = this.findMatchingContainer(input, elementInfo.container);
                    if (container) {
                        score += 25;
                    }
                }
            }

            // 类名匹配
            if (selectorInfo.classes.length > 0) {
                const inputClasses = Array.from(input.classList);
                const matchedClasses = selectorInfo.classes.filter(cls => inputClasses.includes(cls));
                score += matchedClasses.length * 10;
            }

            // 属性匹配
            for (const [attr, value] of Object.entries(selectorInfo.attributes)) {
                if (attr === 'placeholder') {
                    // 对于placeholder，支持模糊匹配和清理字段名前缀
                    const inputPlaceholder = input.getAttribute('placeholder') || '';
                    const cleanValue = value.replace(/^\[[\w.]+\]\s*/, ''); // 清理字段名前缀

                    if (inputPlaceholder === cleanValue ||
                        inputPlaceholder.includes(cleanValue) ||
                        cleanValue.includes(inputPlaceholder)) {
                        score += 20; // placeholder匹配很重要
                    }
                } else if (input.getAttribute(attr) === value) {
                    score += 15;
                }
            }

            // 检查是否为可编辑的输入框
            if (!input.disabled && !input.readOnly) {
                score += 5;
            }

            // 可见性检查
            const style = window.getComputedStyle(input);
            if (style.display !== 'none' && style.visibility !== 'hidden') {
                score += 5;
            }

            if (score > 0) {
                candidates.push({
                    element: input,
                    score,
                    placeholder: input.getAttribute('placeholder') || '',
                    type: input.type || 'text'
                });
            }
        }

        // 按分数排序，返回最高分的
        candidates.sort((a, b) => b.score - a.score);

        if (candidates.length > 0) {
            console.log('🎯 输入框匹配结果:', candidates.map(c => ({
                placeholder: c.placeholder,
                type: c.type,
                score: c.score
            })));
            return candidates[0].element;
        }

        return null;
    }

    // 查找匹配的label
    findMatchingLabel(element, labelInfo) {
        if (!labelInfo) return null;

        // 根据存储的label类型查找
        if (labelInfo.type === 'for' && element.id) {
            const label = document.querySelector(`label[for="${element.id}"]`);
            if (label && label.textContent?.trim() === labelInfo.text) {
                return label;
            }
        }

        if (labelInfo.type === 'sibling') {
            const formItem = this.findFormItemContainer(element);
            if (formItem) {
                const label = formItem.querySelector('label');
                if (label && label.textContent?.trim() === labelInfo.text) {
                    return label;
                }
            }
        }

        if (labelInfo.type === 'parent') {
            let parent = element.parentElement;
            while (parent && parent.tagName.toLowerCase() !== 'body') {
                if (parent.tagName.toLowerCase() === 'label' &&
                    parent.textContent?.trim() === labelInfo.text) {
                    return parent;
                }
                parent = parent.parentElement;
            }
        }

        return null;
    }

    // 查找匹配的容器
    findMatchingContainer(element, containerInfo) {
        if (!containerInfo) return null;

        let parent = element.parentElement;
        while (parent && parent.tagName.toLowerCase() !== 'body') {
            // 检查标签名
            if (parent.tagName.toLowerCase() === containerInfo.tagName) {
                // 检查类名匹配
                const parentClasses = Array.from(parent.classList);
                const matchedClasses = containerInfo.classes.filter(cls => parentClasses.includes(cls));

                if (matchedClasses.length > 0) {
                    return parent;
                }
            }
            parent = parent.parentElement;
        }

        return null;
    }

    // 从多个匹配元素中选择最佳的
    selectBestElement(elements, selector, elementInfo = null) {
        console.log(`🔍 从${elements.length}个元素中选择最佳匹配`);

        // 过滤掉隐藏的元素（增强版，检查父容器）
        const visibleElements = elements.filter(el => {
            // 基础可见性检查
            const style = window.getComputedStyle(el);
            if (style.display === 'none' || style.visibility === 'hidden' || style.opacity === '0') {
                console.log(`🔍 元素基础可见性检查失败:`, el.textContent?.trim(), {
                    display: style.display,
                    visibility: style.visibility,
                    opacity: style.opacity
                });
                return false;
            }

            // 检查元素及其父容器是否真正可见（重要：过滤modal中的元素）
            const isVisible = this.isElementAndParentsVisible(el);
            if (!isVisible) {
                console.log(`🔍 元素父容器可见性检查失败:`, el.textContent?.trim(), el.outerHTML.substring(0, 100));
            } else {
                console.log(`✅ 元素通过所有可见性检查:`, el.textContent?.trim());
            }
            return isVisible;
        });

        console.log(`🔍 可见元素数量: ${visibleElements.length}`);

        if (visibleElements.length === 1) {
            return visibleElements[0];
        }

        if (visibleElements.length === 0) {
            console.warn('所有匹配元素都不可见，使用第一个');
            return elements[0];
        }

        // 如果有存储的元素信息，使用智能匹配
        if (elementInfo && elementInfo.text) {
            console.log(`🔍 使用存储的元素信息进行智能选择: "${elementInfo.text}"`);
            console.log(`🔍 存储的元素信息:`, elementInfo);
            console.log(`🔍 候选元素详细信息:`, visibleElements.map(el => ({
                text: el.textContent?.trim(),
                className: el.className,
                attributes: this.getAllAttributes(el),
                outerHTML: el.outerHTML.substring(0, 100) + '...'
            })));
            const candidates = [];

            for (const element of visibleElements) {
                let score = 0;
                const elementText = element.textContent?.trim() || '';

                // 文本匹配评分
                if (elementText === elementInfo.text) {
                    score += 100; // 精确匹配
                    console.log(`✅ 精确匹配: "${elementText}" = "${elementInfo.text}"`);
                } else if (elementText.includes(elementInfo.text)) {
                    score += 80; // 包含匹配
                    console.log(`🔍 包含匹配: "${elementText}" 包含 "${elementInfo.text}"`);
                } else if (elementInfo.text.includes(elementText)) {
                    score += 60; // 被包含匹配
                    console.log(`🔍 被包含匹配: "${elementInfo.text}" 包含 "${elementText}"`);
                }

                // 可用性评分
                if (!element.disabled && !element.classList.contains('is-disabled')) {
                    score += 20;
                }

                // 属性匹配评分（如果有存储的属性信息）
                if (elementInfo.attributes) {
                    score += this.calculateAttributeMatchScore(element, elementInfo.attributes);
                }

                candidates.push({ element, score, text: elementText });
            }

            // 按分数排序
            candidates.sort((a, b) => b.score - a.score);
            console.log(`🎯 智能选择结果:`, candidates.map(c => ({ text: c.text, score: c.score })));

            // 要求精确唯一匹配：只有当最高分明显高于其他候选者时才返回
            if (candidates.length > 0 && candidates[0].score >= 100) {
                // 检查是否有多个高分候选者（可能是重复元素）
                const highScoreCandidates = candidates.filter(c => c.score >= 100);
                if (highScoreCandidates.length === 1) {
                    console.log(`✅ 找到唯一精确匹配: "${candidates[0].text}"`);
                    return candidates[0].element;
                } else {
                    console.warn(`⚠️ 找到${highScoreCandidates.length}个精确匹配的元素，无法确定选择哪个`);
                    console.warn(`⚠️ 匹配的元素文本:`, highScoreCandidates.map(c => c.text));
                    return null; // 不选择任何元素
                }
            } else if (candidates.length > 0) {
                console.warn(`⚠️ 没有找到精确匹配的元素，最高分: ${candidates[0].score}`);
                return null; // 不选择任何元素
            }
        }

        // 如果智能匹配失败，检查是否只有一个可见元素
        if (visibleElements.length === 1) {
            console.log(`✅ 只有一个可见元素，直接使用`);
            return visibleElements[0];
        }

        // 如果有多个可见元素，不进行猜测，要求用户提供更精确的选择器
        if (visibleElements.length > 1) {
            console.warn(`⚠️ 找到${visibleElements.length}个匹配的可见元素，但无法精确确定选择哪个`);
            console.warn(`⚠️ 元素文本列表:`, visibleElements.map(el => el.textContent?.trim()));
            console.warn(`⚠️ 请检查选择器是否足够精确，或者页面绑定配置是否正确`);
            return null; // 不选择任何元素
        }

        // 如果没有可见元素
        console.warn(`⚠️ 没有找到可见的匹配元素`);
        return null;
    }

    // 计算属性匹配得分
    calculateAttributeMatchScore(element, storedAttributes) {
        let score = 0;

        // 高价值属性匹配
        const highValueAttrs = ['data-testid', 'data-cy', 'data-test', 'data-automation', 'data-qa', 'data-id'];
        for (const attr of highValueAttrs) {
            if (storedAttributes[attr] && element.getAttribute(attr) === storedAttributes[attr]) {
                score += 50; // 高价值属性匹配
                console.log(`🎯 高价值属性匹配: ${attr}="${storedAttributes[attr]}"`);
            }
        }

        // 按钮特有属性匹配（Bootstrap等）
        const buttonSpecificAttrs = ['data-toggle', 'data-target', 'data-bs-toggle', 'data-bs-target'];
        for (const attr of buttonSpecificAttrs) {
            if (storedAttributes[attr] && element.getAttribute(attr) === storedAttributes[attr]) {
                score += 40; // 按钮特有属性匹配
                console.log(`🎯 按钮特有属性匹配: ${attr}="${storedAttributes[attr]}"`);
            }
        }

        // 中等价值属性匹配
        const mediumValueAttrs = ['role', 'aria-label', 'type'];
        for (const attr of mediumValueAttrs) {
            if (storedAttributes[attr] && element.getAttribute(attr) === storedAttributes[attr]) {
                score += 30; // 中等价值属性匹配
                console.log(`🔍 中等价值属性匹配: ${attr}="${storedAttributes[attr]}"`);
            }
        }

        // 有意义的类名匹配
        if (storedAttributes.meaningfulClasses) {
            const elementClasses = Array.from(element.classList);
            const matchingClasses = storedAttributes.meaningfulClasses.filter(cls =>
                elementClasses.includes(cls)
            );
            if (matchingClasses.length > 0) {
                score += matchingClasses.length * 10; // 每个匹配的类名加10分
                console.log(`🔍 类名匹配: ${matchingClasses.join(', ')}`);
            }
        }

        return score;
    }

    // 获取元素的所有属性
    getAllAttributes(element) {
        const attributes = {};
        for (let i = 0; i < element.attributes.length; i++) {
            const attr = element.attributes[i];
            attributes[attr.name] = attr.value;
        }
        return attributes;
    }

    // 填充元素（增强版，支持第三方控件和异步处理）
    async fillElement(element, value, elementInfo = null) {
        const tagName = element.tagName.toLowerCase();
        const type = element.type ? element.type.toLowerCase() : '';

        // 检查是否为第三方控件
        const controlType = this.detectControlType(element);

        if (controlType !== 'native') {
            // 处理第三方控件（异步）
            return await this.fillThirdPartyControl(element, value, controlType, elementInfo);
        }

        // 处理原生HTML元素
        if (tagName === 'input') {
            if (type === 'checkbox') {
                // 复选框：支持多种真值表示
                const originalChecked = element.checked;
                element.checked = this.isTruthyValue(value);
                this.triggerElementEvents(element);
                return {
                    success: true,
                    method: 'checkbox',
                    originalValue: originalChecked,
                    finalValue: element.checked,
                    elementType: 'input-checkbox'
                };
            } else if (type === 'radio') {
                // 单选框：通过值或文本匹配
                return this.fillRadioButtonEnhanced(element, value);
            } else {
                const originalValue = element.value;
                element.value = value;
                this.triggerElementEvents(element);
                return {
                    success: true,
                    method: 'direct-input',
                    originalValue,
                    finalValue: value,
                    elementType: `input-${type}`
                };
            }
        } else if (tagName === 'select') {
            // 下拉框：智能匹配值或文本
            const result = this.fillSelectElement(element, value);
            this.triggerElementEvents(element);
            return result;
        } else if (tagName === 'textarea') {
            const originalValue = element.value;
            element.value = value;
            this.triggerElementEvents(element);
            return {
                success: true,
                method: 'textarea',
                originalValue,
                finalValue: value,
                elementType: 'textarea'
            };
        } else {
            const originalText = element.textContent;
            element.textContent = value;
            return {
                success: true,
                method: 'text-content',
                originalValue: originalText,
                finalValue: value,
                elementType: tagName,
                warning: 'Text content set, may not be form-submittable'
            };
        }
    }

    // 检测控件类型（增强版）
    detectControlType(element) {
        const tagName = element.tagName.toLowerCase();
        const classList = Array.from(element.classList);
        const className = element.className.toLowerCase();

        // 首先检查input元素是否为第三方控件的一部分
        if (tagName === 'input') {
            // 检查是否为Element UI Select内部的input
            if (this.isElementUISelectControl(element)) {
                console.log('✅ input元素识别为Element UI Select的一部分');
                return 'element-ui-select';
            }

            // 检查是否为其他第三方控件的input（按优先级检查）

            // Select2
            if (this.isSelect2Control(element)) {
                console.log('✅ input元素识别为Select2的一部分');
                return 'select2';
            }

            // Bootstrap Select
            if (this.isBootstrapSelectControl(element)) {
                console.log('✅ input元素识别为Bootstrap Select的一部分');
                return 'bootstrap-select';
            }

            // Chosen
            if (this.isChosenControl(element)) {
                console.log('✅ input元素识别为Chosen的一部分');
                return 'chosen';
            }

            // Selectize
            if (this.isSelectizeControl(element)) {
                console.log('✅ input元素识别为Selectize的一部分');
                return 'selectize';
            }

            // Multiselect
            if (this.isMultiselectControl(element)) {
                console.log('✅ input元素识别为Multiselect的一部分');
                return 'multiselect';
            }

            // 自定义下拉框
            if (this.isCustomDropdownControl(element)) {
                console.log('✅ input元素识别为自定义下拉框的一部分');
                return 'custom-dropdown';
            }

            // 其他第三方控件（通过类名模式匹配）
            const thirdPartyType = this.detectThirdPartyControlByInput(element);
            if (thirdPartyType) {
                console.log(`✅ input元素识别为${thirdPartyType}的一部分`);
                return thirdPartyType;
            }

            // 如果不是第三方控件的一部分，则识别为原生input
            console.log('✅ 识别为原生input元素');
            return 'native';
        }

        // 检查select元素是否为第三方控件
        if (tagName === 'select') {
            // 检查是否为Select2增强的select
            if (this.isSelect2EnhancedSelect(element)) {
                console.log('✅ select元素识别为Select2增强的select');
                return 'select2';
            }

            // 检查是否为Bootstrap Select增强的select
            if (this.isBootstrapSelectEnhancedSelect(element)) {
                console.log('✅ select元素识别为Bootstrap Select增强的select');
                return 'bootstrap-select';
            }

            // 检查是否为Chosen增强的select
            if (this.isChosenEnhancedSelect(element)) {
                console.log('✅ select元素识别为Chosen增强的select');
                return 'chosen';
            }

            // 检查是否为Selectize增强的select
            if (this.isSelectizeEnhancedSelect(element)) {
                console.log('✅ select元素识别为Selectize增强的select');
                return 'selectize';
            }

            // 检查是否为Multiselect增强的select
            if (this.isMultiselectEnhancedSelect(element)) {
                console.log('✅ select元素识别为Multiselect增强的select');
                return 'multiselect';
            }

            // 检查其他第三方控件增强的select
            const thirdPartyType = this.detectThirdPartyControlBySelect(element);
            if (thirdPartyType) {
                console.log(`✅ select元素识别为${thirdPartyType}增强的select`);
                return thirdPartyType;
            }

            // 如果不是第三方控件增强的，则识别为原生select
            console.log('✅ 识别为原生select元素');
            return 'native';
        }

        // 检查textarea元素
        if (tagName === 'textarea') {
            console.log('✅ 识别为原生textarea元素');
            return 'native';
        }

        // Select2控件（增强检测）
        if (this.isSelect2Control(element)) {
            return 'select2';
        }

        // Bootstrap Select控件
        if (this.isBootstrapSelectControl(element)) {
            return 'bootstrap-select';
        }

        // Element UI Select控件
        if (this.isElementUISelectControl(element)) {
            return 'element-ui-select';
        }

        // 自定义下拉框控件（常见的业务系统实现）
        if (this.isCustomDropdownControl(element)) {
            return 'custom-dropdown';
        }

        // Chosen控件
        if (classList.includes('chosen-container')) {
            return 'chosen';
        }

        // 其他第三方控件
        const thirdPartyPatterns = [
            { pattern: 'multiselect', type: 'multiselect' },
            { pattern: 'nice-select', type: 'nice-select' },
            { pattern: 'selectric', type: 'selectric' },
            { pattern: 'selectize', type: 'selectize' },
            { pattern: 'ui-selectmenu', type: 'jquery-ui-selectmenu' }
        ];

        for (const control of thirdPartyPatterns) {
            if (className.includes(control.pattern)) {
                return control.type;
            }
        }

        // 检查ARIA属性（但要排除原生元素）
        if (element.getAttribute('role') === 'combobox' ||
            element.getAttribute('role') === 'listbox') {
            return 'aria-control';
        }

        return 'native';
    }

    // 检测是否为Select2控件
    isSelect2Control(element) {
        const classList = Array.from(element.classList);

        // 首先排除Chosen控件（避免误判）
        if (classList.includes('chosen-container')) {
            console.log('❌ 这是Chosen控件，不是Select2');
            return false;
        }

        // 方法1: 检查标准的Select2类名
        if (classList.includes('select2-container')) {
            console.log('✅ 通过select2-container类名识别为Select2');
            return true;
        }

        // 方法2: 检查Select2的子元素
        if (element.querySelector('.select2-selection') ||
            element.querySelector('.select2-selection__rendered') ||
            element.querySelector('.select2-selection__arrow')) {
            console.log('✅ 通过Select2子元素识别为Select2');
            return true;
        }

        // 方法3: 检查data属性
        if (element.getAttribute('data-select2-id') ||
            element.hasAttribute('data-select2-id')) {
            console.log('✅ 通过data-select2-id属性识别为Select2');
            return true;
        }

        // 方法4: 检查是否有相邻的隐藏select元素（但要排除Chosen）
        const prevElement = element.previousElementSibling;
        if (prevElement &&
            prevElement.tagName.toLowerCase() === 'select' &&
            (prevElement.style.display === 'none' ||
             prevElement.classList.contains('select2-hidden-accessible')) &&
            !element.classList.contains('chosen-container')) {
            console.log('✅ 通过相邻隐藏select识别为Select2');
            return true;
        }

        // 方法5: 检查父容器中是否有Select2相关的select
        let parent = element.parentElement;
        while (parent && parent !== document.body) {
            const hiddenSelect = parent.querySelector('select.select2-hidden-accessible');
            if (hiddenSelect) {
                console.log('✅ 通过父容器中的select2-hidden-accessible识别为Select2');
                return true;
            }
            parent = parent.parentElement;
        }

        // 方法6: 通过jQuery检查（如果可用）
        if (window.$ && element.id) {
            try {
                const $element = $(`#${element.id}`);
                if ($element.length > 0 && $element.hasClass('select2-container')) {
                    console.log('✅ 通过jQuery检查识别为Select2');
                    return true;
                }
            } catch (error) {
                // jQuery检查失败，继续其他方法
            }
        }

        return false;
    }

    // 检测是否为Bootstrap Select控件
    isBootstrapSelectControl(element) {
        const classList = Array.from(element.classList);

        // 方法1: 检查标准的Bootstrap Select类名
        if (classList.includes('bootstrap-select') || classList.includes('selectpicker')) {
            console.log('✅ 通过类名识别为Bootstrap Select');
            return true;
        }

        // 方法2: 检查Bootstrap Select的子元素
        if (element.querySelector('.dropdown-toggle') &&
            element.querySelector('.dropdown-menu')) {
            console.log('✅ 通过子元素识别为Bootstrap Select');
            return true;
        }

        // 方法3: 检查是否有相邻的select元素（可能是Bootstrap Select的原始select）
        const prevElement = element.previousElementSibling;
        if (prevElement &&
            prevElement.tagName.toLowerCase() === 'select' &&
            prevElement.classList.contains('selectpicker')) {
            console.log('✅ 通过相邻select识别为Bootstrap Select');
            return true;
        }

        return false;
    }

    // 检测是否为Chosen控件
    isChosenControl(element) {
        // 检查是否在Chosen容器内
        const chosenContainer = element.closest('.chosen-container');
        if (chosenContainer) {
            console.log('✅ 通过容器识别为Chosen控件');
            return true;
        }

        // 检查是否为Chosen的搜索输入框
        if (element.classList.contains('chosen-search-input') ||
            element.classList.contains('default')) {
            console.log('✅ 通过类名识别为Chosen控件');
            return true;
        }

        return false;
    }

    // 检测是否为Selectize控件
    isSelectizeControl(element) {
        // 检查是否在Selectize容器内
        const selectizeContainer = element.closest('.selectize-control');
        if (selectizeContainer) {
            console.log('✅ 通过容器识别为Selectize控件');
            return true;
        }

        // 检查是否为Selectize的输入框
        if (element.classList.contains('selectize-input') ||
            element.parentElement?.classList.contains('selectize-input')) {
            console.log('✅ 通过类名识别为Selectize控件');
            return true;
        }

        return false;
    }

    // 检测是否为Multiselect控件
    isMultiselectControl(element) {
        // 检查是否在Multiselect容器内
        const multiselectContainer = element.closest('.multiselect-container') ||
                                   element.closest('.ms-container');
        if (multiselectContainer) {
            console.log('✅ 通过容器识别为Multiselect控件');
            return true;
        }

        // 检查类名
        if (element.classList.contains('ms-search') ||
            element.parentElement?.classList.contains('ms-search')) {
            console.log('✅ 通过类名识别为Multiselect控件');
            return true;
        }

        return false;
    }

    // 通过input元素检测第三方控件类型
    detectThirdPartyControlByInput(element) {
        const classList = Array.from(element.classList);
        const className = element.className.toLowerCase();

        // 检查常见的第三方控件模式
        const patterns = [
            { pattern: 'nice-select', type: 'nice-select' },
            { pattern: 'selectric', type: 'selectric' },
            { pattern: 'ui-selectmenu', type: 'jquery-ui-selectmenu' },
            { pattern: 'select2', type: 'select2' },
            { pattern: 'chosen', type: 'chosen' },
            { pattern: 'selectize', type: 'selectize' },
            { pattern: 'multiselect', type: 'multiselect' }
        ];

        for (const control of patterns) {
            if (className.includes(control.pattern)) {
                return control.type;
            }
        }

        // 检查父级容器
        let current = element.parentElement;
        let depth = 0;
        while (current && current !== document.body && depth < 5) {
            const parentClassName = current.className.toLowerCase();
            for (const control of patterns) {
                if (parentClassName.includes(control.pattern)) {
                    return control.type;
                }
            }
            current = current.parentElement;
            depth++;
        }

        return null;
    }

    // 检测select元素是否被Select2增强
    isSelect2EnhancedSelect(element) {
        // 检查是否有Select2的特征属性
        if (element.hasAttribute('data-select2-id') ||
            element.classList.contains('select2-hidden-accessible')) {
            return true;
        }

        // 检查是否有Select2容器作为下一个兄弟元素
        const nextSibling = element.nextElementSibling;
        if (nextSibling && nextSibling.classList.contains('select2-container')) {
            return true;
        }

        // 检查jQuery数据
        if (window.$ && $(element).data('select2')) {
            return true;
        }

        return false;
    }

    // 检测select元素是否被Bootstrap Select增强
    isBootstrapSelectEnhancedSelect(element) {
        // 检查是否有Bootstrap Select的类名
        if (element.classList.contains('selectpicker')) {
            return true;
        }

        // 检查是否有Bootstrap Select容器作为下一个兄弟元素
        const nextSibling = element.nextElementSibling;
        if (nextSibling && (
            nextSibling.classList.contains('bootstrap-select') ||
            nextSibling.classList.contains('btn-group')
        )) {
            return true;
        }

        return false;
    }

    // 检测select元素是否被Chosen增强
    isChosenEnhancedSelect(element) {
        // 检查是否有Chosen的类名
        if (element.classList.contains('chosen-select')) {
            return true;
        }

        // 检查是否有Chosen容器作为下一个兄弟元素
        const nextSibling = element.nextElementSibling;
        if (nextSibling && nextSibling.classList.contains('chosen-container')) {
            return true;
        }

        // 检查jQuery数据
        if (window.$ && $(element).data('chosen')) {
            return true;
        }

        return false;
    }

    // 检测select元素是否被Selectize增强
    isSelectizeEnhancedSelect(element) {
        // 检查是否有Selectize实例
        if (element.selectize) {
            return true;
        }

        // 检查是否有Selectize容器作为下一个兄弟元素
        const nextSibling = element.nextElementSibling;
        if (nextSibling && nextSibling.classList.contains('selectize-control')) {
            return true;
        }

        return false;
    }

    // 检测select元素是否被Multiselect增强
    isMultiselectEnhancedSelect(element) {
        // 检查jQuery数据
        if (window.$ && $(element).data('multiselect')) {
            return true;
        }

        // 检查是否有Multiselect容器
        const parent = element.parentElement;
        if (parent && (
            parent.classList.contains('multiselect-container') ||
            parent.classList.contains('ms-container')
        )) {
            return true;
        }

        return false;
    }

    // 通过select元素检测第三方控件类型
    detectThirdPartyControlBySelect(element) {
        const classList = Array.from(element.classList);
        const className = element.className.toLowerCase();

        // 检查常见的第三方控件类名
        const patterns = [
            { pattern: 'nice-select', type: 'nice-select' },
            { pattern: 'selectric', type: 'selectric' },
            { pattern: 'ui-selectmenu', type: 'jquery-ui-selectmenu' }
        ];

        for (const control of patterns) {
            if (className.includes(control.pattern)) {
                return control.type;
            }
        }

        // 检查data属性
        for (const attr of element.attributes) {
            if (attr.name.startsWith('data-')) {
                for (const control of patterns) {
                    if (attr.name.includes(control.pattern)) {
                        return control.type;
                    }
                }
            }
        }

        return null;
    }

    // 检测是否为Element UI Select控件（增强版 - 支持内部input元素）
    isElementUISelectControl(element) {
        const classList = Array.from(element.classList);

        // 方法1: 检查Element UI的类名
        if (classList.includes('el-select') ||
            classList.includes('el-select__wrapper')) {
            console.log('✅ 通过类名识别为Element UI Select');
            return true;
        }

        // 方法2: 检查Element UI的子元素
        if (element.querySelector('.el-input') ||
            element.querySelector('.el-select__input')) {
            console.log('✅ 通过子元素识别为Element UI Select');
            return true;
        }

        // 方法3: 检查是否为Element UI Select内部的input元素
        if (element.tagName.toLowerCase() === 'input' &&
            classList.includes('el-input__inner')) {
            // 检查父级是否为Element UI Select
            const selectContainer = element.closest('.el-select');
            if (selectContainer) {
                console.log('✅ 通过内部input识别为Element UI Select');
                return true;
            }
        }

        // 方法4: 检查是否为Element UI Select相关的input
        if (element.tagName.toLowerCase() === 'input' && element.readOnly) {
            const parent = element.parentElement;
            if (parent && (
                parent.classList.contains('el-input') ||
                parent.querySelector('.el-select__caret') ||
                element.closest('.el-select')
            )) {
                console.log('✅ 通过只读input识别为Element UI Select');
                return true;
            }
        }

        // 方法5: 检查Vue组件属性
        if (element.hasAttribute('data-v-') &&
            (element.className.includes('select') || element.querySelector('.el-input'))) {
            console.log('✅ 通过Vue属性识别为Element UI Select');
            return true;
        }

        return false;
    }

    // 检测是否为自定义下拉框控件
    isCustomDropdownControl(element) {
        // 方法1: 检查是否包含输入框和下拉按钮的组合
        const hasInput = element.querySelector('input[type="text"], input:not([type])');
        const hasDropdownIndicator = element.querySelector('.dropdown-toggle, .arrow, [class*="arrow"], [class*="dropdown"]') ||
                                   element.innerHTML.includes('▼') ||
                                   element.innerHTML.includes('dropdown');

        if (hasInput && hasDropdownIndicator) {
            console.log('✅ 通过输入框+下拉指示器识别为自定义下拉框');
            return true;
        }

        // 方法2: 检查是否有点击展开的行为特征
        const clickableArea = element.querySelector('[onclick], [data-toggle]') || element;
        const hasPlaceholder = hasInput && (hasInput.placeholder === '请选择...' ||
                                          hasInput.placeholder.includes('选择') ||
                                          hasInput.value === '请选择...');

        if (hasPlaceholder && clickableArea) {
            console.log('✅ 通过占位符+点击区域识别为自定义下拉框');
            return true;
        }

        // 方法3: 检查是否有隐藏的select + 自定义显示
        const hiddenSelect = element.querySelector('select[style*="display: none"], select.hidden');
        const customDisplay = element.querySelector('input, .selected-text, .display-text');

        if (hiddenSelect && customDisplay) {
            console.log('✅ 通过隐藏select+自定义显示识别为自定义下拉框');
            return true;
        }

        // 方法4: 检查常见的自定义下拉框类名模式
        const customDropdownPatterns = [
            'custom-select', 'dropdown-wrapper', 'select-wrapper',
            'form-select-custom', 'ui-select', 'select-box'
        ];

        const classList = Array.from(element.classList);
        const className = element.className.toLowerCase();

        for (const pattern of customDropdownPatterns) {
            if (className.includes(pattern) || classList.some(cls => cls.includes(pattern))) {
                console.log(`✅ 通过类名模式"${pattern}"识别为自定义下拉框`);
                return true;
            }
        }

        return false;
    }

    // 填充第三方控件（增强版，确保value正确性）
    async fillThirdPartyControl(element, value, controlType, elementInfo) {
        console.log(`🎯 填充第三方控件: ${controlType}`, { element, value });

        try {
            switch (controlType) {
                case 'select2':
                    return await this.fillSelect2Enhanced(element, value);
                case 'bootstrap-select':
                    return await this.fillBootstrapSelectEnhanced(element, value);
                case 'element-ui-select':
                    return await this.fillElementUISelectEnhanced(element, value);
                case 'custom-dropdown':
                    return await this.fillCustomDropdownEnhanced(element, value);
                case 'chosen':
                    return await this.fillChosenEnhanced(element, value);
                case 'selectize':
                    return await this.fillSelectizeEnhanced(element, value);
                case 'multiselect':
                    return await this.fillMultiselectEnhanced(element, value);
                default:
                    console.warn(`⚠️ 未支持的控件类型: ${controlType}`);
                    return await this.fillGenericThirdPartyControlEnhanced(element, value);
            }
        } catch (error) {
            console.error(`❌ 填充第三方控件失败: ${controlType}`, error);
            return {
                success: false,
                reason: 'exception',
                error: error.message,
                controlType,
                attemptedValue: value
            };
        }
    }

    // 填充Select2控件（增强版，仅支持已加载选项）
    async fillSelect2Enhanced(element, value) {
        console.log('🎯 开始填充Select2控件', { element, value });

        // 查找原始的select元素
        const originalSelect = this.findOriginalSelect(element, 'select2');
        if (!originalSelect) {
            console.error('❌ 未找到Select2的原始select元素');
            console.log('🔍 Select2容器信息:', {
                id: element.id,
                className: element.className,
                innerHTML: element.innerHTML.substring(0, 200) + '...'
            });

            return {
                success: false,
                reason: 'no-original-select',
                controlType: 'select2',
                attemptedValue: value,
                debugInfo: {
                    elementId: element.id,
                    elementClasses: element.className,
                    hasDataSelect2Id: !!element.getAttribute('data-select2-id'),
                    hasPreviousSelect: element.previousElementSibling?.tagName.toLowerCase() === 'select'
                }
            };
        }

        console.log('✅ 找到Select2的原始select元素', originalSelect);

        // 检查是否有可用选项
        const optionsCount = originalSelect.options.length;
        const hasOptions = optionsCount > 1; // 排除placeholder

        console.log(`📊 Select2选项统计: 总数=${optionsCount}, 有效选项=${hasOptions}`);

        if (!hasOptions) {
            console.warn('⚠️ Select2没有可用选项');
            return {
                success: false,
                reason: 'no-options-available',
                controlType: 'select2',
                attemptedValue: value,
                message: 'Select2控件没有可用选项，可能需要手动触发加载',
                debugInfo: {
                    optionsCount,
                    selectId: originalSelect.id,
                    selectName: originalSelect.name
                }
            };
        }

        // 使用增强的select填充方法
        console.log('🔄 开始填充原始select元素');
        const fillResult = this.fillSelectElement(originalSelect, value);

        if (fillResult.success) {
            console.log('✅ 原始select填充成功，开始同步Select2显示');

            // 触发Select2更新 - 使用稳定的方法确保同步
            try {
                if (window.$ && $(originalSelect).data('select2')) {
                    console.log('🔄 使用jQuery触发Select2更新');
                    // 触发change事件
                    $(originalSelect).trigger('change');

                    // 等待一下再手动同步显示
                    setTimeout(() => {
                        this.syncSelect2Display(element, originalSelect);
                    }, 100);
                } else {
                    console.log('🔄 使用原生事件触发Select2更新');
                    originalSelect.dispatchEvent(new Event('change', { bubbles: true }));
                    originalSelect.dispatchEvent(new Event('input', { bubbles: true }));

                    // 手动同步显示
                    setTimeout(() => {
                        this.syncSelect2Display(element, originalSelect);
                    }, 100);
                }
            } catch (error) {
                console.warn('⚠️ Select2更新触发失败:', error);
            }

            return {
                ...fillResult,
                controlType: 'select2',
                syncedWithDisplay: true,
                debugInfo: {
                    originalSelectId: originalSelect.id,
                    optionsCount,
                    selectedIndex: originalSelect.selectedIndex,
                    selectedValue: originalSelect.value
                }
            };
        } else {
            console.warn('❌ 原始select填充失败');
        }

        return {
            ...fillResult,
            controlType: 'select2',
            debugInfo: {
                originalSelectId: originalSelect.id,
                optionsCount,
                availableValues: Array.from(originalSelect.options).map(opt => opt.value)
            }
        };
    }

    // 同步Select2显示
    syncSelect2Display(container, originalSelect) {
        try {
            const selectedOption = originalSelect.options[originalSelect.selectedIndex];
            if (!selectedOption) return;

            const displayText = selectedOption.text;

            // 查找Select2的显示元素
            const selectionRendered = container.querySelector('.select2-selection__rendered');
            const selectionPlaceholder = container.querySelector('.select2-selection__placeholder');

            if (selectionRendered) {
                // 清除占位符
                if (selectionPlaceholder) {
                    selectionPlaceholder.style.display = 'none';
                }

                // 更新显示文本
                selectionRendered.textContent = displayText;
                selectionRendered.title = displayText;

                // 确保显示元素可见
                selectionRendered.style.display = '';

                console.log('✅ 成功同步Select2显示文本:', displayText);
            }

        } catch (error) {
            console.warn('⚠️ 同步Select2显示失败:', error);
        }
    }

    // 填充Bootstrap Select控件（简化版，仅支持已加载选项）
    async fillBootstrapSelectEnhanced(element, value) {
        // 查找原始的select元素
        const originalSelect = this.findOriginalSelect(element, 'bootstrap-select');
        if (!originalSelect) {
            return {
                success: false,
                reason: 'no-original-select',
                controlType: 'bootstrap-select',
                attemptedValue: value
            };
        }

        // 检查是否有可用选项
        const hasOptions = originalSelect.options.length > 1;
        if (!hasOptions) {
            return {
                success: false,
                reason: 'no-options-available',
                controlType: 'bootstrap-select',
                attemptedValue: value,
                message: 'Bootstrap Select控件没有可用选项，可能需要手动触发加载'
            };
        }

        // 使用增强的select填充方法
        const fillResult = this.fillSelectElement(originalSelect, value);

        if (fillResult.success) {
            // 触发Bootstrap Select更新
            if (window.$ && $(originalSelect).data('selectpicker')) {
                $(originalSelect).selectpicker('refresh');
            }

            return {
                ...fillResult,
                controlType: 'bootstrap-select',
                syncedWithDisplay: true
            };
        }

        return {
            ...fillResult,
            controlType: 'bootstrap-select'
        };
    }

    // 填充Element UI Select控件（真正的选项选择版本）
    async fillElementUISelectEnhanced(element, value) {
        console.log('🎯 开始填充Element UI Select控件', { element, value });
        console.log('💡 核心思路：通过label找到对应的value，然后真正选中该选项');

        // 重要：如果传入的是内部input元素，需要找到真正的Select容器
        const selectContainer = this.findElementUISelectContainer(element);
        if (!selectContainer) {
            console.warn('⚠️ 未找到Element UI Select容器');
            return {
                success: false,
                reason: 'no-select-container',
                controlType: 'element-ui-select',
                attemptedValue: value,
                message: '未找到Element UI Select容器'
            };
        }

        console.log('✅ 找到Select容器:', selectContainer);

        // 方法1: 完全模拟用户点击选择（最真实）
        const realUserClickResult = await this.fillElementUISelectByRealUserClick(selectContainer, value);
        if (realUserClickResult.success) {
            return realUserClickResult;
        }

        // 方法2: Vue选项对象完整设置（深度操作）
        const vueCompleteResult = await this.fillElementUISelectByVueComplete(selectContainer, value);
        if (vueCompleteResult.success) {
            return vueCompleteResult;
        }

        // 方法3: 强制选中状态同步（最后手段）
        const forceSelectResult = await this.fillElementUISelectByForceSelect(selectContainer, value);
        if (forceSelectResult.success) {
            return forceSelectResult;
        }

        return {
            success: false,
            reason: 'all-real-methods-failed',
            controlType: 'element-ui-select',
            attemptedValue: value,
            message: 'Element UI Select真实选择失败'
        };
    }

    // 填充自定义下拉框控件
    async fillCustomDropdownEnhanced(element, value) {
        console.log('🎯 开始填充自定义下拉框控件', { element, value });

        // 方法1: 查找并填充隐藏的select元素
        const hiddenSelect = element.querySelector('select[style*="display: none"], select.hidden') ||
                           element.querySelector('select');

        if (hiddenSelect) {
            console.log('🔄 找到隐藏的select元素，尝试填充');
            const fillResult = this.fillSelectElement(hiddenSelect, value);

            if (fillResult.success) {
                // 同步显示到自定义界面
                await this.syncCustomDropdownDisplay(element, hiddenSelect, fillResult);

                return {
                    ...fillResult,
                    controlType: 'custom-dropdown',
                    syncedWithDisplay: true
                };
            }
        }

        // 方法2: 尝试点击并选择选项
        try {
            const clickableArea = element.querySelector('input, .dropdown-toggle, [data-toggle]') || element;

            // 点击打开下拉框
            clickableArea.click();
            console.log('🔄 点击打开自定义下拉框');

            // 等待下拉框打开
            await this.delay(500);

            // 查找选项 - 使用更广泛的选择器
            const optionSelectors = [
                '.dropdown-item', '.option', '.select-option',
                '[role="option"]', 'li[data-value]', 'li',
                '.menu-item', '.choice', '.item',
                'div[onclick]', 'span[onclick]', 'a[onclick]'
            ];

            let foundOption = null;
            let matchMethod = '';

            for (const selector of optionSelectors) {
                const options = document.querySelectorAll(selector);
                console.log(`🔍 查找选项: ${selector}, 找到 ${options.length} 个`);

                for (const option of options) {
                    const optionText = option.textContent?.trim();
                    const optionValue = option.getAttribute('data-value') ||
                                      option.getAttribute('value') ||
                                      option.getAttribute('data-key');

                    // 精确匹配
                    if (optionText === value || optionValue === value) {
                        foundOption = option;
                        matchMethod = 'exact-match';
                        break;
                    }

                    // 包含匹配
                    if (optionText && optionText.includes(value)) {
                        foundOption = option;
                        matchMethod = 'contains-match';
                        break;
                    }
                }

                if (foundOption) break;
            }

            if (foundOption) {
                console.log('✅ 找到匹配选项，准备点击', foundOption);
                foundOption.click();

                // 等待选择完成
                await this.delay(200);

                return {
                    success: true,
                    method: `click-${matchMethod}`,
                    controlType: 'custom-dropdown',
                    finalValue: foundOption.getAttribute('data-value') || foundOption.textContent?.trim(),
                    displayText: foundOption.textContent?.trim(),
                    attemptedValue: value
                };
            } else {
                console.warn('⚠️ 未找到匹配的选项');

                // 关闭下拉框
                clickableArea.click();

                return {
                    success: false,
                    reason: 'no-matching-option',
                    controlType: 'custom-dropdown',
                    attemptedValue: value,
                    message: '未找到匹配的选项'
                };
            }

        } catch (error) {
            console.warn('⚠️ 自定义下拉框点击操作失败:', error);
        }

        // 方法3: 尝试直接设置输入框值（最后的备选方案）
        const input = element.querySelector('input[type="text"], input:not([type])');
        if (input && !input.readOnly) {
            const originalValue = input.value;
            input.value = value;

            // 触发事件
            input.dispatchEvent(new Event('input', { bubbles: true }));
            input.dispatchEvent(new Event('change', { bubbles: true }));
            input.dispatchEvent(new Event('blur', { bubbles: true }));

            return {
                success: true,
                method: 'input-direct',
                controlType: 'custom-dropdown',
                originalValue,
                finalValue: value,
                warning: '直接设置输入框值，可能无法正确提交表单'
            };
        }

        return {
            success: false,
            reason: 'no-suitable-method',
            controlType: 'custom-dropdown',
            attemptedValue: value,
            message: '无法找到合适的自定义下拉框填充方法'
        };
    }

    // 同步自定义下拉框显示
    async syncCustomDropdownDisplay(element, hiddenSelect, fillResult) {
        try {
            const selectedOption = hiddenSelect.options[hiddenSelect.selectedIndex];
            if (!selectedOption) return;

            const displayText = selectedOption.text;

            // 查找显示文本的元素
            const displayElements = [
                element.querySelector('input[type="text"], input:not([type])'),
                element.querySelector('.selected-text, .display-text, .current-selection'),
                element.querySelector('.dropdown-toggle span, .select-display span')
            ].filter(el => el);

            for (const displayEl of displayElements) {
                if (displayEl.tagName.toLowerCase() === 'input') {
                    displayEl.value = displayText;
                } else {
                    displayEl.textContent = displayText;
                }
                console.log('🔄 同步显示文本:', displayText);
            }

            // 触发显示更新事件
            element.dispatchEvent(new Event('change', { bubbles: true }));

        } catch (error) {
            console.warn('⚠️ 同步自定义下拉框显示失败:', error);
        }
    }

    // 填充Chosen控件（增强版）
    async fillChosenEnhanced(element, value) {
        console.log('🎯 开始填充Chosen控件', { element, value });

        const originalSelect = this.findOriginalSelect(element, 'chosen');
        if (!originalSelect) {
            console.error('❌ 未找到Chosen的原始select元素');
            return {
                success: false,
                reason: 'no-original-select',
                controlType: 'chosen',
                attemptedValue: value
            };
        }

        console.log('✅ 找到Chosen的原始select元素', originalSelect);

        // 检查是否有可用选项
        const optionsCount = originalSelect.options.length;
        const hasOptions = optionsCount > 1; // 排除placeholder

        console.log(`📊 Chosen选项统计: 总数=${optionsCount}, 有效选项=${hasOptions}`);

        if (!hasOptions) {
            console.warn('⚠️ Chosen没有可用选项');
            return {
                success: false,
                reason: 'no-options-available',
                controlType: 'chosen',
                attemptedValue: value,
                message: 'Chosen控件没有可用选项'
            };
        }

        // 填充原始select
        console.log('🔄 开始填充原始select元素');
        const fillResult = this.fillSelectElement(originalSelect, value);

        if (fillResult.success) {
            console.log('✅ 原始select填充成功，开始同步Chosen显示');

            // 触发Chosen更新 - 使用多种方法确保同步
            try {
                if (window.$ && $(originalSelect).data('chosen')) {
                    console.log('🔄 使用jQuery触发Chosen更新');
                    $(originalSelect).trigger('chosen:updated');
                    $(originalSelect).trigger('change');
                } else {
                    console.log('🔄 使用原生事件触发Chosen更新');
                    originalSelect.dispatchEvent(new Event('change', { bubbles: true }));
                }

                // 手动同步Chosen显示
                setTimeout(() => {
                    this.syncChosenDisplay(element, originalSelect);
                }, 100);

            } catch (error) {
                console.warn('⚠️ Chosen更新触发失败:', error);
            }

            return {
                ...fillResult,
                controlType: 'chosen',
                syncedWithDisplay: true,
                debugInfo: {
                    originalSelectId: originalSelect.id,
                    optionsCount,
                    selectedIndex: originalSelect.selectedIndex,
                    selectedValue: originalSelect.value
                }
            };
        } else {
            console.warn('❌ 原始select填充失败');
        }

        return {
            ...fillResult,
            controlType: 'chosen',
            debugInfo: {
                originalSelectId: originalSelect.id,
                optionsCount,
                availableValues: Array.from(originalSelect.options).map(opt => opt.value)
            }
        };
    }

    // 同步Chosen显示
    syncChosenDisplay(container, originalSelect) {
        try {
            const selectedOption = originalSelect.options[originalSelect.selectedIndex];
            if (!selectedOption) return;

            const displayText = selectedOption.text;
            const selectedIndex = originalSelect.selectedIndex;

            console.log(`🔄 同步Chosen显示: 索引=${selectedIndex}, 文本="${displayText}"`);

            // 查找Chosen的显示元素
            const chosenSingle = container.querySelector('.chosen-single span');

            if (chosenSingle) {
                // 更新显示文本
                chosenSingle.textContent = displayText;
                console.log('✅ 更新显示文本成功');

                // 确保下拉列表存在
                const chosenResults = container.querySelector('.chosen-results');
                if (chosenResults) {
                    // 移除所有选项的选中状态
                    const allResults = chosenResults.querySelectorAll('.active-result');
                    allResults.forEach((result, index) => {
                        result.classList.remove('result-selected');
                        result.classList.remove('highlighted');
                        console.log(`清除选项 ${index} 的选中状态`);
                    });

                    // 添加选中状态到正确的选项
                    const selectedResult = chosenResults.querySelector(`.active-result[data-option-array-index="${selectedIndex}"]`);
                    if (selectedResult) {
                        selectedResult.classList.add('result-selected');
                        selectedResult.classList.add('highlighted');
                        console.log(`✅ 设置选项 ${selectedIndex} 为选中状态`);
                    } else {
                        console.warn(`⚠️ 未找到索引为 ${selectedIndex} 的选项`);
                        // 尝试通过文本匹配
                        allResults.forEach((result, index) => {
                            if (result.textContent.trim() === displayText) {
                                result.classList.add('result-selected');
                                result.classList.add('highlighted');
                                console.log(`✅ 通过文本匹配设置选项 ${index} 为选中状态`);
                            }
                        });
                    }
                } else {
                    console.warn('⚠️ 未找到.chosen-results元素');
                }

                console.log('✅ 成功同步Chosen显示文本和选中状态:', displayText);
            } else {
                console.warn('⚠️ 未找到.chosen-single span元素');
            }

        } catch (error) {
            console.warn('⚠️ 同步Chosen显示失败:', error);
        }
    }

    // 填充Selectize控件（增强版）
    async fillSelectizeEnhanced(element, value) {
        const originalSelect = this.findOriginalSelect(element, 'selectize');
        if (!originalSelect || !originalSelect.selectize) {
            return {
                success: false,
                reason: 'no-selectize-instance',
                controlType: 'selectize',
                attemptedValue: value
            };
        }

        try {
            // 检查值是否在选项中
            const selectize = originalSelect.selectize;
            const options = selectize.options;

            // 查找匹配的选项
            let matchedValue = null;
            if (options[value]) {
                matchedValue = value;
            } else {
                // 通过文本查找
                for (const [optValue, optData] of Object.entries(options)) {
                    if (optData.text === value || optData.value === value) {
                        matchedValue = optValue;
                        break;
                    }
                }
            }

            if (matchedValue) {
                selectize.setValue(matchedValue);
                return {
                    success: true,
                    method: 'selectize-api',
                    controlType: 'selectize',
                    finalValue: matchedValue,
                    displayText: options[matchedValue]?.text || matchedValue,
                    attemptedValue: value
                };
            }

            return {
                success: false,
                reason: 'no-matching-option',
                controlType: 'selectize',
                attemptedValue: value,
                availableOptions: Object.values(options).map(opt => ({
                    value: opt.value,
                    text: opt.text
                }))
            };
        } catch (error) {
            return {
                success: false,
                reason: 'selectize-error',
                controlType: 'selectize',
                attemptedValue: value,
                error: error.message
            };
        }
    }

    // 填充Multiselect控件（增强版）
    async fillMultiselectEnhanced(element, value) {
        const originalSelect = this.findOriginalSelect(element, 'multiselect');
        if (!originalSelect) {
            return {
                success: false,
                reason: 'no-original-select',
                controlType: 'multiselect',
                attemptedValue: value
            };
        }

        const fillResult = this.fillSelectElement(originalSelect, value);

        if (fillResult.success) {
            // 触发multiselect更新
            if (window.$ && $(originalSelect).data('multiselect')) {
                $(originalSelect).multiselect('refresh');
            }

            return {
                ...fillResult,
                controlType: 'multiselect',
                syncedWithDisplay: true
            };
        }

        return {
            ...fillResult,
            controlType: 'multiselect'
        };
    }

    // 通用第三方控件填充方法（简化版）
    async fillGenericThirdPartyControlEnhanced(element, value) {
        console.log('🔧 尝试通用第三方控件填充方法');

        // 方法1: 查找隐藏的原始select
        const hiddenSelect = this.findHiddenSelect(element);
        if (hiddenSelect) {
            const fillResult = this.fillSelectElement(hiddenSelect, value);
            if (fillResult.success) {
                this.triggerElementEvents(hiddenSelect);
                return {
                    ...fillResult,
                    controlType: 'generic-hidden-select',
                    syncedWithDisplay: true
                };
            }
            return {
                ...fillResult,
                controlType: 'generic-hidden-select'
            };
        }

        // 方法2: 查找可编辑的输入框（警告：不保证value正确性）
        const input = element.querySelector('input[type="text"], input[type="search"], input:not([type])');
        if (input && !input.readOnly) {
            const originalValue = input.value;
            input.value = value;
            this.triggerElementEvents(input);

            return {
                success: true,
                method: 'input-direct',
                controlType: 'generic-input',
                originalValue,
                finalValue: value,
                warning: '直接设置输入框值，可能无法正确提交表单'
            };
        }

        return {
            success: false,
            reason: 'no-suitable-method',
            controlType: 'generic-unknown',
            attemptedValue: value,
            message: '无法找到合适的填充方法，可能需要手动操作'
        };
    }

    // 查找原始select元素（增强版）
    findOriginalSelect(element, controlType) {
        console.log(`🔍 查找${controlType}的原始select元素`, element);

        // 如果传入的就是select元素，直接返回
        if (element.tagName.toLowerCase() === 'select') {
            console.log('✅ 传入的就是select元素，直接返回');
            return element;
        }

        // 根据不同控件类型查找原始select
        switch (controlType) {
            case 'select2':
                return this.findSelect2OriginalSelect(element);

            case 'bootstrap-select':
                return this.findBootstrapSelectOriginalSelect(element);

            case 'element-ui-select':
                return this.findElementUISelectOriginalSelect(element);

            case 'custom-dropdown':
                return this.findCustomDropdownOriginalSelect(element);

            case 'chosen':
                return this.findChosenOriginalSelect(element);

            default:
                // 通用查找方法
                return this.findHiddenSelect(element);
        }
    }

    // 查找Select2的原始select元素（增强版 - 支持input元素）
    findSelect2OriginalSelect(element) {
        console.log('🔍 查找Select2原始select，元素类型:', element.tagName);

        // 如果传入的是input元素，先找到Select2容器
        let select2Container = element;
        if (element.tagName.toLowerCase() === 'input') {
            select2Container = element.closest('.select2-container') ||
                              element.closest('.select2') ||
                              element.parentElement;
            console.log('📦 从input元素找到Select2容器:', select2Container);
        }

        // 方法1: 通过data-select2-id属性查找
        const select2Id = select2Container.getAttribute('data-select2-id');
        if (select2Id) {
            const selectById = document.getElementById(select2Id);
            if (selectById && selectById.tagName.toLowerCase() === 'select') {
                console.log('✅ 通过data-select2-id找到原始select');
                return selectById;
            }
        }

        // 方法2: 查找前一个兄弟元素
        let prev = select2Container.previousElementSibling;
        if (prev && prev.tagName.toLowerCase() === 'select') {
            console.log('✅ 通过前一个兄弟元素找到原始select');
            return prev;
        }

        // 方法3: 在父容器中查找隐藏的select
        let parent = element.parentElement;
        while (parent && parent !== document.body) {
            const hiddenSelect = parent.querySelector('select[style*="display: none"], select[style*="display:none"], select.select2-hidden-accessible');
            if (hiddenSelect) {
                console.log('✅ 在父容器中找到隐藏的select');
                return hiddenSelect;
            }
            parent = parent.parentElement;
        }

        // 方法4: 通过Select2的jQuery数据查找
        if (window.$ && element.id) {
            try {
                const $element = $(`#${element.id}`);
                if ($element.length > 0) {
                    // 查找与此Select2容器关联的原始select
                    const $select = $element.prev('select');
                    if ($select.length > 0) {
                        console.log('✅ 通过jQuery查找到原始select');
                        return $select[0];
                    }
                }
            } catch (error) {
                console.log('jQuery查找失败:', error);
            }
        }

        // 方法5: 通过class名称查找附近的select
        const nearbySelects = document.querySelectorAll('select');
        for (const select of nearbySelects) {
            // 检查这个select是否被Select2初始化了
            if (select.style.display === 'none' ||
                select.classList.contains('select2-hidden-accessible') ||
                (window.$ && $(select).data('select2'))) {

                // 检查距离，找最近的
                const selectRect = select.getBoundingClientRect();
                const elementRect = element.getBoundingClientRect();
                const distance = Math.abs(selectRect.top - elementRect.top) + Math.abs(selectRect.left - elementRect.left);

                if (distance < 100) { // 距离小于100px认为是关联的
                    console.log('✅ 通过位置距离找到可能的原始select');
                    return select;
                }
            }
        }

        console.warn('❌ 未找到Select2的原始select元素');
        return null;
    }

    // 查找Bootstrap Select的原始select元素（增强版 - 支持input元素）
    findBootstrapSelectOriginalSelect(element) {
        console.log('🔍 查找Bootstrap Select原始select，元素类型:', element.tagName);

        // 如果传入的是input元素，先找到Bootstrap Select容器
        let bootstrapContainer = element;
        if (element.tagName.toLowerCase() === 'input') {
            bootstrapContainer = element.closest('.bootstrap-select') ||
                               element.closest('.selectpicker') ||
                               element.closest('.btn-group') ||
                               element.parentElement;
            console.log('📦 从input元素找到Bootstrap Select容器:', bootstrapContainer);
        }

        // 方法1: 在容器内查找
        const internalSelect = bootstrapContainer.querySelector('select');
        if (internalSelect) {
            console.log('✅ 在Bootstrap Select容器内找到原始select');
            return internalSelect;
        }

        // 方法2: 查找前一个兄弟元素
        let prev = bootstrapContainer.previousElementSibling;
        if (prev && prev.tagName.toLowerCase() === 'select') {
            console.log('✅ 通过前一个兄弟元素找到原始select');
            return prev;
        }

        // 方法3: 在父容器中查找
        let parent = element.parentElement;
        while (parent && parent !== document.body) {
            const hiddenSelect = parent.querySelector('select');
            if (hiddenSelect && hiddenSelect !== element) {
                console.log('✅ 在父容器中找到原始select');
                return hiddenSelect;
            }
            parent = parent.parentElement;
        }

        console.warn('❌ 未找到Bootstrap Select的原始select元素');
        return null;
    }

    // 查找Element UI的Vue实例
    findElementUIVueInstance(element) {
        // 方法1: 直接检查元素的Vue实例
        if (element.__vue__) {
            console.log('✅ 在元素上找到Vue实例');
            return element.__vue__;
        }

        // 方法2: 检查父级元素的Vue实例
        let current = element;
        while (current && current !== document.body) {
            if (current.__vue__) {
                console.log('✅ 在父级元素上找到Vue实例');
                return current.__vue__;
            }
            current = current.parentElement;
        }

        // 方法3: 查找el-select容器的Vue实例
        const selectContainer = element.closest('.el-select');
        if (selectContainer && selectContainer.__vue__) {
            console.log('✅ 在el-select容器上找到Vue实例');
            return selectContainer.__vue__;
        }

        console.warn('⚠️ 未找到Element UI的Vue实例');
        return null;
    }

    // 通过点击选择Element UI Select选项
    async fillElementUISelectByClick(element, value) {
        console.log('🔄 尝试通过点击选择Element UI Select选项');

        try {
            const input = element.querySelector('.el-input__inner') ||
                         element.querySelector('input') ||
                         element.querySelector('.select-trigger');

            if (!input) {
                console.warn('⚠️ 未找到可点击的输入框');
                return { success: false, reason: 'no-clickable-input' };
            }

            // 点击打开下拉框
            console.log('🖱️ 点击打开下拉框');
            input.click();

            // 等待下拉框打开
            await this.delay(500);

            // 查找下拉选项（多种可能的选择器）
            const optionSelectors = [
                '.el-select-dropdown__item',
                '.el-option',
                '.el-select-dropdown .el-select-dropdown__item',
                '[role="option"]'
            ];

            let options = [];
            for (const selector of optionSelectors) {
                options = document.querySelectorAll(selector);
                if (options.length > 0) {
                    console.log(`✅ 找到${options.length}个选项，使用选择器: ${selector}`);
                    break;
                }
            }

            if (options.length === 0) {
                console.warn('⚠️ 未找到下拉选项');
                // 尝试关闭下拉框
                input.click();
                return { success: false, reason: 'no-options-found' };
            }

            // 查找匹配的选项
            for (const option of options) {
                const optionText = option.textContent?.trim();
                const optionValue = option.getAttribute('data-value') ||
                                  option.getAttribute('value') ||
                                  option.getAttribute('data-option-value');

                console.log(`🔍 检查选项: text="${optionText}", value="${optionValue}"`);

                if (optionText === value || optionValue === value ||
                    optionText?.includes(value) || value?.includes(optionText)) {

                    console.log('🎯 找到匹配选项，点击选择');
                    option.click();

                    // 等待选择完成
                    await this.delay(200);

                    return {
                        success: true,
                        method: 'click-option',
                        controlType: 'element-ui-select',
                        finalValue: optionValue || optionText,
                        displayText: optionText,
                        attemptedValue: value
                    };
                }
            }

            // 如果没找到匹配项，关闭下拉框
            console.warn('⚠️ 未找到匹配的选项');
            input.click();
            return { success: false, reason: 'no-matching-option' };

        } catch (error) {
            console.warn('⚠️ Element UI Select点击操作失败:', error);
            return { success: false, reason: 'click-operation-failed', error: error.message };
        }
    }

    // 通过模拟输入填充Element UI Select
    async fillElementUISelectByInput(element, value) {
        console.log('🔄 尝试通过模拟输入填充Element UI Select');

        const input = element.querySelector('.el-input__inner') ||
                     element.querySelector('input');

        if (!input) {
            return { success: false, reason: 'no-input-found' };
        }

        try {
            // 聚焦输入框
            input.focus();

            // 清空现有值
            input.value = '';

            // 模拟逐字符输入
            for (const char of value) {
                input.value += char;
                input.dispatchEvent(new Event('input', { bubbles: true }));
                await this.delay(50);
            }

            // 触发change事件
            input.dispatchEvent(new Event('change', { bubbles: true }));

            // 等待一下看是否有自动完成
            await this.delay(300);

            // 检查是否有匹配的选项出现
            const options = document.querySelectorAll('.el-select-dropdown__item, .el-option');
            for (const option of options) {
                const optionText = option.textContent?.trim();
                if (optionText === value) {
                    option.click();
                    return {
                        success: true,
                        method: 'input-autocomplete',
                        controlType: 'element-ui-select',
                        finalValue: value,
                        displayText: optionText,
                        attemptedValue: value
                    };
                }
            }

            return { success: false, reason: 'no-autocomplete-match' };

        } catch (error) {
            console.warn('⚠️ Element UI Select输入操作失败:', error);
            return { success: false, reason: 'input-operation-failed', error: error.message };
        }
    }

    // 通过DOM操作填充Element UI Select
    async fillElementUISelectByDOM(element, value) {
        console.log('🔄 尝试通过DOM操作填充Element UI Select');

        const input = element.querySelector('.el-input__inner') ||
                     element.querySelector('input');

        if (!input) {
            return { success: false, reason: 'no-input-found' };
        }

        try {
            const originalValue = input.value;

            // 直接设置值
            input.value = value;

            // 触发所有可能的事件
            const events = ['input', 'change', 'blur', 'keyup', 'keydown'];
            for (const eventType of events) {
                input.dispatchEvent(new Event(eventType, { bubbles: true }));
            }

            // 如果有Vue实例，尝试手动触发更新
            const vueInstance = this.findElementUIVueInstance(element);
            if (vueInstance) {
                try {
                    vueInstance.$forceUpdate();
                    if (vueInstance.$parent) {
                        vueInstance.$parent.$forceUpdate();
                    }
                } catch (error) {
                    console.warn('⚠️ Vue强制更新失败:', error);
                }
            }

            return {
                success: true,
                method: 'dom-direct',
                controlType: 'element-ui-select',
                originalValue,
                finalValue: value,
                warning: '直接DOM操作，可能需要验证表单状态'
            };

        } catch (error) {
            console.warn('⚠️ Element UI Select DOM操作失败:', error);
            return { success: false, reason: 'dom-operation-failed', error: error.message };
        }
    }

    // 查找Element UI Select的原始select元素
    findElementUISelectOriginalSelect(element) {
        // Element UI Select通常不使用原始select，而是完全自定义实现
        // 但有些情况下可能会有隐藏的select用于表单提交

        // 方法1: 在容器内查找隐藏的select
        const internalSelect = element.querySelector('select[style*="display: none"], select.hidden');
        if (internalSelect) {
            console.log('✅ 在Element UI Select容器内找到隐藏select');
            return internalSelect;
        }

        // 方法2: 在父容器中查找
        let parent = element.parentElement;
        while (parent && parent !== document.body) {
            const hiddenSelect = parent.querySelector('select[style*="display: none"], select.hidden');
            if (hiddenSelect && hiddenSelect !== element) {
                console.log('✅ 在父容器中找到Element UI Select的隐藏select');
                return hiddenSelect;
            }
            parent = parent.parentElement;
        }

        // Element UI Select通常不需要原始select
        console.log('ℹ️ Element UI Select不使用原始select元素');
        return null;
    }

    // 查找自定义下拉框的原始select元素
    findCustomDropdownOriginalSelect(element) {
        // 方法1: 在容器内查找隐藏的select
        const internalSelect = element.querySelector('select[style*="display: none"], select.hidden, select');
        if (internalSelect) {
            console.log('✅ 在自定义下拉框容器内找到select');
            return internalSelect;
        }

        // 方法2: 在相邻元素中查找
        const siblings = [element.previousElementSibling, element.nextElementSibling];
        for (const sibling of siblings) {
            if (sibling && sibling.tagName.toLowerCase() === 'select') {
                console.log('✅ 在相邻元素中找到自定义下拉框的select');
                return sibling;
            }
        }

        // 方法3: 在父容器中查找
        let parent = element.parentElement;
        while (parent && parent !== document.body) {
            const hiddenSelect = parent.querySelector('select');
            if (hiddenSelect && hiddenSelect !== element) {
                console.log('✅ 在父容器中找到自定义下拉框的select');
                return hiddenSelect;
            }
            parent = parent.parentElement;
        }

        console.log('ℹ️ 自定义下拉框可能不使用原始select元素');
        return null;
    }

    // 查找Chosen的原始select元素（增强版 - 支持input元素）
    findChosenOriginalSelect(element) {
        console.log('🔍 查找Chosen原始select，元素类型:', element.tagName);

        // 如果传入的是input元素，先找到Chosen容器
        let chosenContainer = element;
        if (element.tagName.toLowerCase() === 'input') {
            chosenContainer = element.closest('.chosen-container') ||
                            element.closest('.chosen-single') ||
                            element.closest('.chosen-multiple') ||
                            element.parentElement;
            console.log('📦 从input元素找到Chosen容器:', chosenContainer);
        }

        // Chosen通常在select后面创建容器
        let prev = chosenContainer.previousElementSibling;
        if (prev && prev.tagName.toLowerCase() === 'select') {
            console.log('✅ 通过前一个兄弟元素找到原始select');
            return prev;
        }

        // 在父级中查找
        let parent = chosenContainer.parentElement;
        if (parent) {
            const select = parent.querySelector('select');
            if (select && select !== chosenContainer) {
                console.log('✅ 在父级中找到原始select');
                return select;
            }
        }

        console.warn('❌ 未找到Chosen的原始select元素');
        return null;
    }

    // 查找隐藏的select元素
    findHiddenSelect(element) {
        // 在当前元素内查找
        let select = element.querySelector('select');
        if (select) return select;

        // 在前一个兄弟元素中查找
        let prev = element.previousElementSibling;
        if (prev && prev.tagName.toLowerCase() === 'select') {
            return prev;
        }

        // 在父容器中查找
        let parent = element.parentElement;
        while (parent && parent !== document.body) {
            select = parent.querySelector('select');
            if (select && (select.style.display === 'none' || select.hidden)) {
                return select;
            }
            parent = parent.parentElement;
        }

        return null;
    }



    // 延迟工具方法
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // 触发元素事件
    triggerElementEvents(element) {
        const events = ['input', 'change', 'blur', 'focus'];
        events.forEach(eventType => {
            element.dispatchEvent(new Event(eventType, { bubbles: true }));
        });

        // 对于某些控件，还需要触发自定义事件
        if (element.classList.contains('select2')) {
            element.dispatchEvent(new CustomEvent('select2:select', { bubbles: true }));
        }
    }

    // 方法1: 完全模拟用户点击选择（最真实的方法）
    async fillElementUISelectByRealUserClick(element, value) {
        console.log('🔄 方法1: 完全模拟用户点击选择');
        console.log('💡 思路：打开下拉框 -> 通过label找选项 -> 点击选项 -> 验证结果');

        try {
            // 1. 找到触发器
            const trigger = this.findSelectTrigger(element);
            if (!trigger) {
                console.warn('⚠️ 未找到下拉框触发器');
                return { success: false, reason: 'no-trigger' };
            }

            console.log('🖱️ 点击触发器打开下拉框');

            // 2. 模拟点击打开下拉框
            this.simulateClick(trigger);

            // 3. 等待下拉框完全打开
            await this.delay(500);

            // 4. 查找匹配的选项（通过label查找）
            const option = await this.findOptionByLabel(value);
            if (!option) {
                console.warn('⚠️ 未找到匹配的选项');
                // 关闭下拉框
                this.simulateClick(trigger);
                return { success: false, reason: 'no-matching-option' };
            }

            console.log('🎯 找到匹配选项，模拟点击选择');

            // 5. 模拟点击选项（完整的鼠标事件序列）
            this.simulateCompleteClick(option);

            // 6. 等待选择完成和Vue更新
            await this.delay(300);

            // 7. 验证选择是否成功（检查多个维度）
            const verification = await this.verifyCompleteSelection(element, value);

            if (verification.success) {
                console.log('✅ 真实用户点击选择成功');
                return {
                    success: true,
                    method: 'real-user-click',
                    controlType: 'element-ui-select',
                    finalValue: verification.value,
                    displayText: verification.label,
                    attemptedValue: value,
                    verification: verification
                };
            } else {
                console.warn('⚠️ 选择验证失败:', verification);
                return { success: false, reason: 'selection-verification-failed', verification };
            }

        } catch (error) {
            console.warn('⚠️ 真实用户点击失败:', error);
            return { success: false, reason: 'real-click-failed', error: error.message };
        }
    }

    // 方法2: Vue选项对象完整设置
    async fillElementUISelectByVueComplete(element, value) {
        console.log('🔄 方法2: Vue选项对象完整设置');
        console.log('💡 思路：找到Vue实例 -> 找到完整选项对象 -> 设置所有相关属性');

        try {
            const selectVue = this.findSelectVueInstance(element);
            if (!selectVue) {
                console.warn('⚠️ 未找到Select的Vue实例');
                return { success: false, reason: 'no-vue-instance' };
            }

            // 1. 查找完整的选项对象
            const optionObject = this.findCompleteOptionObject(selectVue, value);
            if (!optionObject) {
                console.warn('⚠️ 未找到完整的选项对象');
                return { success: false, reason: 'no-option-object' };
            }

            console.log('🎯 找到完整选项对象:', optionObject);

            // 2. 设置Vue实例的所有相关属性
            this.setVueSelectState(selectVue, optionObject);

            // 3. 触发完整的事件链
            this.triggerVueSelectEvents(selectVue, optionObject);

            // 4. 强制更新和验证清除
            await this.forceVueUpdateAndClearValidation(selectVue, element);

            // 5. 验证设置结果
            const verification = await this.verifyCompleteSelection(element, value);

            if (verification.success) {
                console.log('✅ Vue完整设置成功');
                return {
                    success: true,
                    method: 'vue-complete',
                    controlType: 'element-ui-select',
                    finalValue: optionObject.value,
                    displayText: optionObject.label,
                    attemptedValue: value,
                    verification: verification
                };
            } else {
                console.warn('⚠️ Vue设置验证失败:', verification);
                return { success: false, reason: 'vue-verification-failed', verification };
            }

        } catch (error) {
            console.warn('⚠️ Vue完整设置失败:', error);
            return { success: false, reason: 'vue-complete-failed', error: error.message };
        }
    }

    // 方法3: 强制选中状态同步
    async fillElementUISelectByForceSelect(element, value) {
        console.log('🔄 方法3: 强制选中状态同步');
        console.log('💡 思路：强制设置所有可能的状态 -> 同步DOM和Vue -> 清除验证');

        try {
            const selectVue = this.findSelectVueInstance(element);
            const input = element.querySelector('.el-input__inner');

            if (!selectVue || !input) {
                console.warn('⚠️ 缺少必要的元素');
                return { success: false, reason: 'missing-elements' };
            }

            // 1. 查找选项对象
            const optionObject = this.findCompleteOptionObject(selectVue, value);
            if (!optionObject) {
                console.warn('⚠️ 未找到选项对象，使用强制映射');
                // 创建一个临时的选项对象
                const tempOption = { value: value, label: value };
                return await this.forceSetSelectState(selectVue, input, tempOption, element);
            }

            // 2. 强制设置选中状态
            return await this.forceSetSelectState(selectVue, input, optionObject, element);

        } catch (error) {
            console.warn('⚠️ 强制选中失败:', error);
            return { success: false, reason: 'force-select-failed', error: error.message };
        }
    }

    // 方法2: Vue实例直接操作
    async fillElementUISelectByVueDirect(element, value) {
        console.log('🔄 方法2: Vue实例直接操作');

        const selectVue = this.findSelectVueInstance(element);
        if (!selectVue) {
            console.warn('⚠️ 未找到Select的Vue实例');
            return { success: false, reason: 'no-vue-instance' };
        }

        try {
            // 1. 查找匹配的选项数据
            const optionData = this.findOptionData(selectVue, value);
            if (!optionData) {
                console.warn('⚠️ 未找到匹配的选项数据');
                return { success: false, reason: 'no-option-data' };
            }

            console.log('🎯 找到匹配选项数据:', optionData);

            // 2. 设置选中值（Element UI的标准方式）
            selectVue.value = optionData.value;
            selectVue.selectedLabel = optionData.label;

            // 3. 更新选中状态
            if (selectVue.selected) {
                selectVue.selected = optionData;
            }

            // 4. 触发标准的Element UI事件
            selectVue.$emit('input', optionData.value);
            selectVue.$emit('change', optionData.value);

            // 5. 触发表单验证更新
            selectVue.dispatch('ElFormItem', 'el.form.change', optionData.value);

            // 6. 强制更新组件
            selectVue.$forceUpdate();

            await this.delay(100);

            return {
                success: true,
                method: 'vue-direct',
                controlType: 'element-ui-select',
                finalValue: optionData.value,
                displayText: optionData.label,
                attemptedValue: value
            };

        } catch (error) {
            console.warn('⚠️ Vue直接操作失败:', error);
            return { success: false, reason: 'vue-direct-failed', error: error.message };
        }
    }

    // 方法3: 混合方法（Vue + DOM事件）
    async fillElementUISelectByHybrid(element, value) {
        console.log('🔄 方法3: 混合方法（Vue + DOM事件）');

        try {
            const selectVue = this.findSelectVueInstance(element);
            const input = element.querySelector('.el-input__inner');

            if (!selectVue || !input) {
                console.warn('⚠️ 缺少必要的元素');
                return { success: false, reason: 'missing-elements' };
            }

            // 1. 查找选项数据
            const optionData = this.findOptionData(selectVue, value);
            if (!optionData) {
                console.warn('⚠️ 未找到选项数据');
                return { success: false, reason: 'no-option-data' };
            }

            // 2. 同时设置Vue数据和DOM
            selectVue.value = optionData.value;
            selectVue.selectedLabel = optionData.label;
            input.value = optionData.label;

            // 3. 触发完整的事件链
            // DOM事件
            input.dispatchEvent(new Event('input', { bubbles: true }));
            input.dispatchEvent(new Event('change', { bubbles: true }));

            // Vue事件
            selectVue.$emit('input', optionData.value);
            selectVue.$emit('change', optionData.value);

            // Element UI内部事件
            if (selectVue.dispatch) {
                selectVue.dispatch('ElFormItem', 'el.form.change', optionData.value);
            }

            // 4. 强制更新
            selectVue.$forceUpdate();

            await this.delay(100);

            return {
                success: true,
                method: 'hybrid',
                controlType: 'element-ui-select',
                finalValue: optionData.value,
                displayText: optionData.label,
                attemptedValue: value
            };

        } catch (error) {
            console.warn('⚠️ 混合方法失败:', error);
            return { success: false, reason: 'hybrid-failed', error: error.message };
        }
    }

    // 方法2: 模拟真实用户操作
    async fillElementUISelectViaUserAction(element, value) {
        console.log('🔄 方法2: 模拟真实用户操作');

        try {
            // 查找可点击的触发器
            const triggers = [
                element.querySelector('.select-trigger'),
                element.querySelector('.el-input'),
                element.querySelector('.el-input__inner'),
                element
            ].filter(el => el);

            let trigger = null;
            for (const t of triggers) {
                if (t && t.offsetParent !== null) { // 确保元素可见
                    trigger = t;
                    break;
                }
            }

            if (!trigger) {
                console.warn('⚠️ 未找到可点击的触发器');
                return { success: false, reason: 'no-clickable-trigger' };
            }

            console.log('🖱️ 点击触发器打开下拉框');

            // 模拟鼠标点击事件
            trigger.dispatchEvent(new MouseEvent('mousedown', { bubbles: true }));
            trigger.dispatchEvent(new MouseEvent('click', { bubbles: true }));
            trigger.dispatchEvent(new MouseEvent('mouseup', { bubbles: true }));

            // 等待下拉框打开
            await this.delay(500);

            // 查找下拉选项
            const optionSelectors = [
                '.el-select-dropdown__item:not(.is-disabled)',
                '.el-option:not(.is-disabled)',
                '.el-select-dropdown .el-select-dropdown__item:not(.is-disabled)',
                '[role="option"]:not([aria-disabled="true"])'
            ];

            let matchedOption = null;
            for (const selector of optionSelectors) {
                const options = document.querySelectorAll(selector);
                console.log(`🔍 使用选择器 ${selector} 找到 ${options.length} 个选项`);

                for (const option of options) {
                    const optionText = option.textContent?.trim();
                    const optionValue = option.getAttribute('data-value') ||
                                      option.getAttribute('value') ||
                                      option.getAttribute('data-option-value');

                    console.log(`🔍 检查选项: "${optionText}" (value: "${optionValue}")`);

                    if (optionText === value || optionValue === value) {
                        matchedOption = option;
                        break;
                    }
                }

                if (matchedOption) break;
            }

            if (matchedOption) {
                console.log('🎯 找到匹配选项，模拟点击');

                // 模拟鼠标点击选项
                matchedOption.dispatchEvent(new MouseEvent('mousedown', { bubbles: true }));
                matchedOption.dispatchEvent(new MouseEvent('click', { bubbles: true }));
                matchedOption.dispatchEvent(new MouseEvent('mouseup', { bubbles: true }));

                await this.delay(300);

                return {
                    success: true,
                    method: 'user-action-simulation',
                    controlType: 'element-ui-select',
                    finalValue: matchedOption.getAttribute('data-value') || matchedOption.textContent?.trim(),
                    displayText: matchedOption.textContent?.trim(),
                    attemptedValue: value
                };
            } else {
                console.warn('⚠️ 未找到匹配的选项，关闭下拉框');
                // 尝试关闭下拉框
                trigger.click();
                return { success: false, reason: 'no-matching-option-found' };
            }

        } catch (error) {
            console.warn('⚠️ 用户操作模拟失败:', error);
            return { success: false, reason: 'user-action-failed', error: error.message };
        }
    }

    // 方法3: 强制DOM操作 + Vue响应式触发
    async fillElementUISelectViaForce(element, value) {
        console.log('🔄 方法3: 强制DOM操作 + Vue响应式触发');

        try {
            const input = element.querySelector('.el-input__inner') || element.querySelector('input');
            if (!input) {
                return { success: false, reason: 'no-input-element' };
            }

            // 强制设置显示值
            input.value = value;

            // 查找Vue实例并强制设置内部状态
            const vueInstance = this.findElementUIVueInstance(element);
            if (vueInstance) {
                // 直接设置Vue实例的内部状态
                vueInstance.value = value;
                vueInstance.selectedLabel = value;

                // 如果有选项，尝试找到匹配的选项值
                if (vueInstance.options && Array.isArray(vueInstance.options)) {
                    const matchedOption = vueInstance.options.find(opt =>
                        opt.label === value || opt.text === value || opt.value === value
                    );
                    if (matchedOption) {
                        vueInstance.value = matchedOption.value || matchedOption.label || matchedOption.text;
                    }
                }

                // 触发所有可能的事件
                vueInstance.$emit('input', vueInstance.value);
                vueInstance.$emit('change', vueInstance.value);

                // 清除验证错误
                if (vueInstance.clearValidate) {
                    vueInstance.clearValidate();
                }

                // 清除表单项验证
                const formItem = this.findElementUIFormItem(element);
                if (formItem && formItem.__vue__ && formItem.__vue__.clearValidate) {
                    formItem.__vue__.clearValidate();
                }

                // 强制更新组件
                vueInstance.$forceUpdate();

                // 如果有父组件，也更新
                if (vueInstance.$parent) {
                    vueInstance.$parent.$forceUpdate();
                }
            }

            // 触发DOM事件
            const events = ['input', 'change', 'blur', 'focus'];
            for (const eventType of events) {
                input.dispatchEvent(new Event(eventType, { bubbles: true }));
            }

            await this.delay(200);

            return {
                success: true,
                method: 'force-dom-vue',
                controlType: 'element-ui-select',
                finalValue: value,
                displayText: value,
                attemptedValue: value,
                warning: '使用强制方法，请验证表单状态'
            };

        } catch (error) {
            console.warn('⚠️ 强制操作失败:', error);
            return { success: false, reason: 'force-operation-failed', error: error.message };
        }
    }

    // 查找Element UI的Vue实例（增强版）
    findElementUIVueInstance(element) {
        // 方法1: 直接检查元素的Vue实例
        if (element.__vue__) {
            console.log('✅ 在元素上找到Vue实例');
            return element.__vue__;
        }

        // 方法2: 检查父级元素的Vue实例
        let current = element;
        while (current && current !== document.body) {
            if (current.__vue__) {
                console.log('✅ 在父级元素上找到Vue实例');
                return current.__vue__;
            }
            current = current.parentElement;
        }

        // 方法3: 查找el-select容器的Vue实例
        const selectContainer = element.closest('.el-select');
        if (selectContainer && selectContainer.__vue__) {
            console.log('✅ 在el-select容器上找到Vue实例');
            return selectContainer.__vue__;
        }

        // 方法4: 查找el-form-item容器的Vue实例
        const formItemContainer = element.closest('.el-form-item');
        if (formItemContainer) {
            const selectInFormItem = formItemContainer.querySelector('.el-select');
            if (selectInFormItem && selectInFormItem.__vue__) {
                console.log('✅ 在表单项中的el-select上找到Vue实例');
                return selectInFormItem.__vue__;
            }
        }

        console.warn('⚠️ 未找到Element UI的Vue实例');
        return null;
    }

    // 专门针对您的问题进行深度调试和修复
    async debugElementUISelectIssue(element, value) {
        console.log('🔧 开始深度调试Element UI Select问题');

        // 1. 详细分析DOM结构
        const formItem = element.closest('.el-form-item');
        const label = formItem?.querySelector('label');
        const input = element.querySelector('.el-input__inner');
        const errorDiv = formItem?.querySelector('.el-form-item__error');

        console.log('📊 DOM结构分析:', {
            formItem: !!formItem,
            formItemClasses: formItem?.className,
            label: label?.textContent?.trim(),
            labelFor: label?.getAttribute('for'),
            input: !!input,
            inputValue: input?.value,
            inputPlaceholder: input?.placeholder,
            errorDiv: !!errorDiv,
            errorText: errorDiv?.textContent?.trim()
        });

        // 2. 查找所有可能的Vue实例
        const vueInstances = this.findAllVueInstances(element);
        console.log('🔍 找到的Vue实例:', vueInstances.map(v => ({
            componentName: v.instance.$options.name || v.instance.$options._componentTag || 'Unknown',
            location: v.location,
            hasValue: 'value' in v.instance,
            currentValue: v.instance.value,
            hasOptions: !!v.instance.options,
            optionsCount: v.instance.options?.length || 0
        })));

        // 3. 尝试多种填充策略
        for (const vueInfo of vueInstances) {
            const result = await this.tryFillVueInstance(vueInfo.instance, value, formItem);
            if (result.success) {
                console.log('✅ 成功通过Vue实例填充:', vueInfo.location);
                return result;
            }
        }

        // 4. 如果Vue方法失败，尝试直接操作DOM + 强制清除验证
        const domResult = await this.forceFillAndClearValidation(element, value, formItem);
        if (domResult.success) {
            return domResult;
        }

        return { success: false, reason: 'debug-all-methods-failed' };
    }

    // 查找所有可能的Vue实例
    findAllVueInstances(element) {
        const instances = [];

        // 检查元素本身
        if (element.__vue__) {
            instances.push({ instance: element.__vue__, location: 'element-self' });
        }

        // 检查父级元素
        let current = element.parentElement;
        let depth = 0;
        while (current && current !== document.body && depth < 10) {
            if (current.__vue__) {
                instances.push({
                    instance: current.__vue__,
                    location: `parent-${depth}-${current.className.split(' ')[0] || current.tagName}`
                });
            }
            current = current.parentElement;
            depth++;
        }

        // 检查特定的Element UI容器
        const containers = [
            element.closest('.el-select'),
            element.closest('.el-form-item'),
            element.closest('.el-form')
        ].filter(el => el);

        containers.forEach((container, index) => {
            if (container.__vue__) {
                instances.push({
                    instance: container.__vue__,
                    location: `container-${index}-${container.className.split(' ')[0]}`
                });
            }
        });

        return instances;
    }

    // 尝试通过Vue实例填充
    async tryFillVueInstance(vueInstance, value, formItem) {
        try {
            console.log('🔄 尝试Vue实例填充:', {
                componentName: vueInstance.$options.name || 'Unknown',
                hasValue: 'value' in vueInstance,
                hasOptions: !!vueInstance.options
            });

            // 查找匹配的选项
            let targetValue = value;
            if (vueInstance.options && Array.isArray(vueInstance.options)) {
                const matchedOption = vueInstance.options.find(option =>
                    option.value === value ||
                    option.label === value ||
                    option.text === value ||
                    (typeof option === 'string' && option === value)
                );

                if (matchedOption) {
                    targetValue = matchedOption.value || matchedOption.label || matchedOption.text || matchedOption;
                    console.log('🎯 找到匹配选项:', matchedOption);
                } else {
                    console.warn('⚠️ 未找到匹配选项，使用原始值');
                }
            }

            // 设置值
            const oldValue = vueInstance.value;
            vueInstance.value = targetValue;

            // 如果有selectedLabel属性，也设置它
            if ('selectedLabel' in vueInstance) {
                vueInstance.selectedLabel = value;
            }

            // 触发事件
            vueInstance.$emit('input', targetValue);
            vueInstance.$emit('change', targetValue);

            // 强制更新
            vueInstance.$forceUpdate();

            // 清除验证 - 多种方式
            await this.clearElementUIValidation(vueInstance, formItem);

            await this.delay(200);

            // 验证是否成功
            const isSuccess = vueInstance.value === targetValue;
            console.log('📊 Vue填充结果:', {
                oldValue,
                newValue: vueInstance.value,
                targetValue,
                success: isSuccess
            });

            if (isSuccess) {
                return {
                    success: true,
                    method: 'vue-debug',
                    controlType: 'element-ui-select',
                    finalValue: targetValue,
                    attemptedValue: value
                };
            }

        } catch (error) {
            console.warn('⚠️ Vue实例填充失败:', error);
        }

        return { success: false, reason: 'vue-instance-failed' };
    }

    // 清除Element UI验证的多种方法
    async clearElementUIValidation(vueInstance, formItem) {
        console.log('🧹 开始清除Element UI验证');

        try {
            // 方法1: 直接调用组件的clearValidate
            if (vueInstance.clearValidate) {
                vueInstance.clearValidate();
                console.log('✅ 调用组件clearValidate');
            }

            // 方法2: 查找并调用表单项的clearValidate
            if (formItem && formItem.__vue__) {
                const formItemVue = formItem.__vue__;
                if (formItemVue.clearValidate) {
                    formItemVue.clearValidate();
                    console.log('✅ 调用表单项clearValidate');
                }

                // 直接移除错误状态
                formItem.classList.remove('is-error');
                const errorDiv = formItem.querySelector('.el-form-item__error');
                if (errorDiv) {
                    errorDiv.style.display = 'none';
                    console.log('✅ 隐藏错误提示');
                }
            }

            // 方法3: 查找并调用表单的clearValidate
            const form = formItem?.closest('.el-form');
            if (form && form.__vue__) {
                const formVue = form.__vue__;
                if (formVue.clearValidate) {
                    // 获取字段名
                    const label = formItem?.querySelector('label');
                    const fieldName = label?.getAttribute('for') || 'specialGoods';
                    formVue.clearValidate(fieldName);
                    console.log('✅ 调用表单clearValidate:', fieldName);
                }
            }

            // 方法4: 强制移除验证状态
            setTimeout(() => {
                if (formItem) {
                    formItem.classList.remove('is-error');
                    const errorDiv = formItem.querySelector('.el-form-item__error');
                    if (errorDiv) {
                        errorDiv.remove();
                    }
                }
            }, 100);

        } catch (error) {
            console.warn('⚠️ 清除验证失败:', error);
        }
    }

    // 查找下拉框触发器
    findSelectTrigger(element) {
        // 按优先级查找可点击的触发器
        const selectors = [
            '.el-input__inner',           // Element UI输入框
            '.el-input',                  // Element UI输入容器
            '.select-trigger',            // 自定义触发器
            '.el-select',                 // Element UI Select容器
        ];

        for (const selector of selectors) {
            const trigger = element.querySelector(selector);
            if (trigger && trigger.offsetParent !== null) { // 确保可见
                console.log(`✅ 找到触发器: ${selector}`);
                return trigger;
            }
        }

        // 如果都没找到，返回元素本身
        return element.offsetParent !== null ? element : null;
    }

    // 模拟点击事件
    simulateClick(element) {
        const events = [
            new MouseEvent('mousedown', { bubbles: true, cancelable: true }),
            new MouseEvent('click', { bubbles: true, cancelable: true }),
            new MouseEvent('mouseup', { bubbles: true, cancelable: true })
        ];

        events.forEach(event => element.dispatchEvent(event));
    }

    // 查找匹配的选项（增强版 - 支持label到value的智能匹配）
    async findMatchingOption(value) {
        // 等待下拉框完全打开
        await this.delay(100);

        const optionSelectors = [
            '.el-select-dropdown__item:not(.is-disabled)',
            '.el-option:not(.is-disabled)',
            '.el-select-dropdown .el-select-dropdown__item:not(.is-disabled)',
            '[role="option"]:not([aria-disabled="true"])'
        ];

        for (const selector of optionSelectors) {
            const options = document.querySelectorAll(selector);
            console.log(`🔍 使用选择器 ${selector} 找到 ${options.length} 个选项`);

            // 记录所有选项信息用于调试
            const optionInfos = Array.from(options).map(option => ({
                text: option.textContent?.trim(),
                value: option.getAttribute('data-value') || option.getAttribute('value'),
                element: option
            }));

            console.log('📊 所有可用选项:', optionInfos);

            // 优先精确匹配value
            for (const option of options) {
                const optionValue = option.getAttribute('data-value') ||
                                  option.getAttribute('value');

                if (optionValue === value || optionValue === String(value)) {
                    console.log(`🎯 通过value精确匹配: "${optionValue}"`);
                    return option;
                }
            }

            // 然后精确匹配text/label
            for (const option of options) {
                const optionText = option.textContent?.trim();

                if (optionText === value) {
                    console.log(`🎯 通过text精确匹配: "${optionText}"`);
                    return option;
                }
            }

            // 最后尝试包含匹配
            for (const option of options) {
                const optionText = option.textContent?.trim();
                const optionValue = option.getAttribute('data-value') ||
                                  option.getAttribute('value');

                if (optionText?.includes(value) || value?.includes(optionText) ||
                    optionValue?.includes(value) || value?.includes(optionValue)) {
                    console.log(`🎯 通过包含匹配: "${optionText}" (value: "${optionValue}")`);
                    return option;
                }
            }
        }

        console.warn('⚠️ 未找到任何匹配的选项');
        return null;
    }

    // 查找Select的Vue实例
    findSelectVueInstance(element) {
        // 1. 查找.el-select容器的Vue实例
        const selectContainer = element.closest('.el-select');
        if (selectContainer && selectContainer.__vue__) {
            console.log('✅ 在.el-select容器上找到Vue实例');
            return selectContainer.__vue__;
        }

        // 2. 查找元素本身的Vue实例
        if (element.__vue__) {
            console.log('✅ 在元素本身找到Vue实例');
            return element.__vue__;
        }

        // 3. 向上查找父级的Vue实例
        let current = element.parentElement;
        while (current && current !== document.body) {
            if (current.__vue__ && current.classList.contains('el-select')) {
                console.log('✅ 在父级.el-select上找到Vue实例');
                return current.__vue__;
            }
            current = current.parentElement;
        }

        console.warn('⚠️ 未找到Select的Vue实例');
        return null;
    }

    // 查找选项数据（增强版 - 支持label到value的映射）
    findOptionData(selectVue, value) {
        if (!selectVue.options || !Array.isArray(selectVue.options)) {
            console.warn('⚠️ Vue实例中没有options数据');
            return null;
        }

        console.log('🔍 查找选项数据:', {
            searchValue: value,
            optionsCount: selectVue.options.length,
            options: selectVue.options.map(opt => ({
                value: opt.value,
                label: opt.label,
                text: opt.text
            }))
        });

        // 查找匹配的选项 - 优先匹配value，然后匹配label/text
        let option = selectVue.options.find(opt => {
            return opt.value === value || opt.value === String(value);
        });

        if (!option) {
            // 如果value没匹配到，尝试通过label/text匹配
            option = selectVue.options.find(opt => {
                return opt.label === value ||
                       opt.text === value ||
                       (typeof opt === 'string' && opt === value);
            });
        }

        if (option) {
            // 标准化选项数据 - 确保返回正确的value和label
            const result = {
                value: option.value || option.label || option.text || option,
                label: option.label || option.text || option.value || option
            };

            console.log('✅ 找到匹配选项:', {
                input: value,
                matched: option,
                result: result
            });

            return result;
        }

        console.warn('⚠️ 未找到匹配的选项数据');
        return null;
    }

    // 验证选择是否成功
    async verifySelection(element, expectedValue) {
        await this.delay(100);

        const selectVue = this.findSelectVueInstance(element);
        const input = element.querySelector('.el-input__inner');

        // 检查Vue实例的值
        if (selectVue && selectVue.value) {
            const vueValue = selectVue.value;
            const vueLabel = selectVue.selectedLabel;

            if (vueValue === expectedValue || vueLabel === expectedValue) {
                console.log('✅ Vue实例验证通过');
                return true;
            }
        }

        // 检查输入框的值
        if (input && input.value === expectedValue) {
            console.log('✅ 输入框验证通过');
            return true;
        }

        console.warn('⚠️ 选择验证失败');
        return false;
    }

    // 通过label查找选项元素
    async findOptionByLabel(labelValue) {
        console.log('🔍 通过label查找选项:', labelValue);

        const optionSelectors = [
            '.el-select-dropdown__item:not(.is-disabled)',
            '.el-option:not(.is-disabled)',
            '.el-select-dropdown .el-select-dropdown__item:not(.is-disabled)',
            '[role="option"]:not([aria-disabled="true"])'
        ];

        for (const selector of optionSelectors) {
            const options = document.querySelectorAll(selector);
            console.log(`🔍 使用选择器 ${selector} 找到 ${options.length} 个选项`);

            for (const option of options) {
                const optionText = option.textContent?.trim();

                // 精确匹配label
                if (optionText === labelValue) {
                    console.log(`🎯 通过label精确匹配: "${optionText}"`);
                    return option;
                }
            }
        }

        console.warn('⚠️ 未通过label找到匹配选项');
        return null;
    }

    // 模拟完整的点击事件
    simulateCompleteClick(element) {
        console.log('🖱️ 模拟完整点击事件');

        const events = [
            new MouseEvent('mouseenter', { bubbles: true, cancelable: true }),
            new MouseEvent('mouseover', { bubbles: true, cancelable: true }),
            new MouseEvent('mousedown', { bubbles: true, cancelable: true }),
            new MouseEvent('click', { bubbles: true, cancelable: true }),
            new MouseEvent('mouseup', { bubbles: true, cancelable: true })
        ];

        events.forEach(event => {
            element.dispatchEvent(event);
        });
    }

    // 查找完整的选项对象
    findCompleteOptionObject(selectVue, value) {
        console.log('🔍 查找完整选项对象:', { value, optionsCount: selectVue.options?.length });

        if (!selectVue.options || !Array.isArray(selectVue.options)) {
            console.warn('⚠️ Vue实例中没有options数据');
            return null;
        }

        // 优先通过label匹配
        let option = selectVue.options.find(opt =>
            opt.label === value || opt.text === value
        );

        // 如果label没匹配到，尝试value匹配
        if (!option) {
            option = selectVue.options.find(opt =>
                opt.value === value || opt.value === String(value)
            );
        }

        if (option) {
            const result = {
                value: option.value,
                label: option.label || option.text,
                originalOption: option
            };

            console.log('✅ 找到完整选项对象:', result);
            return result;
        }

        console.warn('⚠️ 未找到完整选项对象');
        return null;
    }

    // 设置Vue Select的状态
    setVueSelectState(selectVue, optionObject) {
        console.log('🔧 设置Vue Select状态:', optionObject);

        // 设置基本属性
        selectVue.value = optionObject.value;
        selectVue.selectedLabel = optionObject.label;

        // 设置选中的选项对象
        if (selectVue.selected !== undefined) {
            selectVue.selected = optionObject.originalOption;
        }

        // 设置其他可能的属性
        if (selectVue.currentLabel !== undefined) {
            selectVue.currentLabel = optionObject.label;
        }

        if (selectVue.displayLabel !== undefined) {
            selectVue.displayLabel = optionObject.label;
        }
    }

    // 触发Vue Select事件
    triggerVueSelectEvents(selectVue, optionObject) {
        console.log('📡 触发Vue Select事件');

        try {
            // 标准的Element UI事件
            selectVue.$emit('input', optionObject.value);
            selectVue.$emit('change', optionObject.value);

            // Element UI内部事件（用于表单验证）
            if (selectVue.dispatch) {
                selectVue.dispatch('ElFormItem', 'el.form.change', optionObject.value);
                selectVue.dispatch('ElFormItem', 'el.form.blur');
            }

            // 其他可能的事件
            selectVue.$emit('select', optionObject.originalOption);
            selectVue.$emit('visible-change', false);

        } catch (error) {
            console.warn('⚠️ 触发Vue事件失败:', error);
        }
    }

    // 强制Vue更新和清除验证
    async forceVueUpdateAndClearValidation(selectVue, element) {
        console.log('🔄 强制Vue更新和清除验证');

        try {
            // 强制更新Vue组件
            selectVue.$forceUpdate();

            // 更新父组件
            if (selectVue.$parent) {
                selectVue.$parent.$forceUpdate();
            }

            // 清除表单验证
            const formItem = element.closest('.el-form-item');
            if (formItem) {
                // 移除错误状态
                formItem.classList.remove('is-error');

                // 隐藏错误信息
                const errorDiv = formItem.querySelector('.el-form-item__error');
                if (errorDiv) {
                    errorDiv.style.display = 'none';
                }

                // 调用Vue的清除验证方法
                if (formItem.__vue__ && formItem.__vue__.clearValidate) {
                    formItem.__vue__.clearValidate();
                }
            }

            await this.delay(100);

        } catch (error) {
            console.warn('⚠️ 强制更新失败:', error);
        }
    }

    // 验证完整的选择结果
    async verifyCompleteSelection(element, expectedValue) {
        console.log('🔍 验证完整选择结果:', expectedValue);

        await this.delay(100);

        const selectVue = this.findSelectVueInstance(element);
        const input = element.querySelector('.el-input__inner');
        const formItem = element.closest('.el-form-item');

        const verification = {
            success: false,
            value: null,
            label: null,
            vueValue: null,
            inputValue: null,
            hasError: false,
            details: {}
        };

        // 检查Vue实例的值
        if (selectVue) {
            verification.vueValue = selectVue.value;
            verification.label = selectVue.selectedLabel;
            verification.details.vueState = {
                value: selectVue.value,
                selectedLabel: selectVue.selectedLabel,
                selected: selectVue.selected
            };
        }

        // 检查输入框的值
        if (input) {
            verification.inputValue = input.value;
            verification.details.inputValue = input.value;
        }

        // 检查表单验证状态
        if (formItem) {
            verification.hasError = formItem.classList.contains('is-error');
            const errorDiv = formItem.querySelector('.el-form-item__error');
            verification.details.hasErrorDiv = !!errorDiv;
            verification.details.errorVisible = errorDiv ? errorDiv.style.display !== 'none' : false;
        }

        // 判断成功条件（更宽松的验证逻辑）
        const hasValidValue = verification.vueValue && verification.vueValue !== '';
        const hasValidLabel = verification.label && verification.label !== '';
        const noValidationError = !verification.hasError;
        const inputHasValue = verification.inputValue && verification.inputValue !== '';

        // 成功条件：至少满足以下之一
        // 1. Vue实例有值且没有验证错误
        // 2. 输入框有值且没有验证错误
        // 3. Vue实例有值和标签（即使有验证错误，可能是延迟清除）
        const condition1 = hasValidValue && noValidationError;
        const condition2 = inputHasValue && noValidationError;
        const condition3 = hasValidValue && hasValidLabel; // 允许有验证错误，因为可能延迟清除

        verification.success = condition1 || condition2 || condition3;
        verification.value = verification.vueValue || verification.inputValue;

        console.log('📊 验证结果:', {
            ...verification,
            conditions: {
                hasValidValue,
                hasValidLabel,
                noValidationError,
                inputHasValue,
                condition1,
                condition2,
                condition3
            }
        });

        return verification;
    }

    // 强制设置选中状态
    async forceSetSelectState(selectVue, input, optionObject, element) {
        console.log('💪 强制设置选中状态:', optionObject);

        try {
            // 1. 设置Vue状态
            this.setVueSelectState(selectVue, optionObject);

            // 2. 设置DOM状态
            input.value = optionObject.label;

            // 3. 触发事件
            this.triggerVueSelectEvents(selectVue, optionObject);

            // 4. 触发DOM事件
            const events = ['input', 'change', 'blur'];
            events.forEach(eventType => {
                input.dispatchEvent(new Event(eventType, { bubbles: true }));
            });

            // 5. 强制更新和清除验证
            await this.forceVueUpdateAndClearValidation(selectVue, element);

            // 6. 延迟再次清除验证（防止异步验证）
            setTimeout(() => {
                const formItem = element.closest('.el-form-item');
                if (formItem) {
                    formItem.classList.remove('is-error');
                    const errorDiv = formItem.querySelector('.el-form-item__error');
                    if (errorDiv) {
                        errorDiv.style.display = 'none';
                    }
                }
            }, 500);

            // 7. 验证结果
            const verification = await this.verifyCompleteSelection(element, optionObject.label);

            return {
                success: verification.success,
                method: 'force-select',
                controlType: 'element-ui-select',
                finalValue: optionObject.value,
                displayText: optionObject.label,
                attemptedValue: optionObject.label,
                verification: verification
            };

        } catch (error) {
            console.warn('⚠️ 强制设置状态失败:', error);
            return { success: false, reason: 'force-set-failed', error: error.message };
        }
    }

    // 查找Element UI Select容器
    findElementUISelectContainer(element) {
        console.log('🔍 查找Element UI Select容器');

        // 如果传入的就是Select容器，直接返回
        if (element.classList.contains('el-select')) {
            console.log('✅ 传入的就是Select容器');
            return element;
        }

        // 如果传入的是内部元素，向上查找Select容器
        const selectContainer = element.closest('.el-select');
        if (selectContainer) {
            console.log('✅ 通过closest找到Select容器');
            return selectContainer;
        }

        // 如果是input元素，检查父级结构
        if (element.tagName.toLowerCase() === 'input') {
            let current = element.parentElement;
            while (current && current !== document.body) {
                if (current.classList.contains('el-select')) {
                    console.log('✅ 通过父级查找找到Select容器');
                    return current;
                }
                // 检查是否有Element UI Select的特征
                if (current.querySelector('.el-select__caret') ||
                    current.querySelector('.select-trigger')) {
                    console.log('✅ 通过特征元素找到Select容器');
                    return current;
                }
                current = current.parentElement;
            }
        }

        console.warn('⚠️ 未找到Element UI Select容器');
        return null;
    }

    // 查找Element UI表单项
    findElementUIFormItem(element) {
        return element.closest('.el-form-item');
    }

    // 强制填充并清除验证（最后的手段）
    async forceFillAndClearValidation(element, value, formItem) {
        console.log('💪 使用强制方法填充并清除验证');

        try {
            const input = element.querySelector('.el-input__inner');
            if (!input) {
                return { success: false, reason: 'no-input-found' };
            }

            // 1. 强制设置显示值
            input.value = value;

            // 2. 查找所有Vue实例并强制设置
            const vueInstances = this.findAllVueInstances(element);
            for (const vueInfo of vueInstances) {
                try {
                    if ('value' in vueInfo.instance) {
                        vueInfo.instance.value = value;
                    }
                    if ('selectedLabel' in vueInfo.instance) {
                        vueInfo.instance.selectedLabel = value;
                    }
                    vueInfo.instance.$forceUpdate();
                } catch (error) {
                    console.warn('⚠️ 强制设置Vue实例失败:', error);
                }
            }

            // 3. 触发所有可能的事件
            const events = ['input', 'change', 'blur', 'focus', 'click'];
            for (const eventType of events) {
                input.dispatchEvent(new Event(eventType, { bubbles: true }));
                element.dispatchEvent(new Event(eventType, { bubbles: true }));
            }

            // 4. 强制清除验证状态
            if (formItem) {
                // 移除错误类
                formItem.classList.remove('is-error');

                // 移除或隐藏错误提示
                const errorDiv = formItem.querySelector('.el-form-item__error');
                if (errorDiv) {
                    errorDiv.style.display = 'none';
                    errorDiv.textContent = '';
                }

                // 如果有Vue实例，尝试清除验证
                if (formItem.__vue__ && formItem.__vue__.clearValidate) {
                    formItem.__vue__.clearValidate();
                }
            }

            // 5. 延迟再次清除（防止异步验证重新触发）
            setTimeout(() => {
                if (formItem) {
                    formItem.classList.remove('is-error');
                    const errorDiv = formItem.querySelector('.el-form-item__error');
                    if (errorDiv) {
                        errorDiv.style.display = 'none';
                    }
                }
            }, 500);

            console.log('✅ 强制填充和清除验证完成');

            return {
                success: true,
                method: 'force-fill-clear-validation',
                controlType: 'element-ui-select',
                finalValue: value,
                attemptedValue: value,
                warning: '使用强制方法，已清除验证错误'
            };

        } catch (error) {
            console.warn('⚠️ 强制填充失败:', error);
            return { success: false, reason: 'force-fill-failed', error: error.message };
        }
    }

    // 判断是否为真值
    isTruthyValue(value) {
        if (typeof value === 'boolean') return value;
        if (typeof value === 'string') {
            const lowerValue = value.toLowerCase().trim();
            return lowerValue === 'true' || lowerValue === '是' || lowerValue === 'yes' ||
                   lowerValue === '1' || lowerValue === 'on' || lowerValue === 'checked';
        }
        return Boolean(value);
    }

    // 填充单选框
    fillRadioButton(element, value) {
        const name = element.name;
        if (!name) {
            // 如果没有name属性，直接设置当前元素
            element.checked = (element.value === value || element.value === String(value));
            return;
        }

        // 查找同名的所有单选框
        const radioButtons = document.querySelectorAll(`input[type="radio"][name="${name}"]`);
        let matched = false;

        radioButtons.forEach(radio => {
            // 先尝试精确匹配value
            if (radio.value === value || radio.value === String(value)) {
                radio.checked = true;
                matched = true;
            } else {
                // 尝试匹配关联的文本标签
                const label = this.getRadioLabel(radio);
                if (label && (label === value || label === String(value))) {
                    radio.checked = true;
                    matched = true;
                } else {
                    radio.checked = false;
                }
            }
        });

        // 如果没有匹配到，记录警告
        if (!matched) {
            console.warn(`⚠️ 单选框未找到匹配值: ${value}, 可选值:`,
                Array.from(radioButtons).map(r => ({value: r.value, label: this.getRadioLabel(r)})));
        }
    }

    // 填充单选框（增强版）
    fillRadioButtonEnhanced(element, value) {
        const name = element.name;
        if (!name) {
            // 如果没有name属性，直接设置当前元素
            const originalChecked = element.checked;
            const matched = (element.value === value || element.value === String(value));
            element.checked = matched;

            return {
                success: matched,
                method: 'single-radio',
                originalValue: originalChecked,
                finalValue: element.checked,
                elementType: 'input-radio',
                reason: matched ? 'value-matched' : 'value-not-matched'
            };
        }

        // 查找同名的所有单选框
        const radioButtons = document.querySelectorAll(`input[type="radio"][name="${name}"]`);
        let matched = false;
        let matchedRadio = null;
        let matchMethod = null;

        // 先清除所有选中状态
        radioButtons.forEach(radio => radio.checked = false);

        // 尝试精确值匹配
        for (const radio of radioButtons) {
            if (radio.value === value || radio.value === String(value)) {
                radio.checked = true;
                matched = true;
                matchedRadio = radio;
                matchMethod = 'exact-value';
                break;
            }
        }

        // 如果值匹配失败，尝试标签文本匹配
        if (!matched) {
            for (const radio of radioButtons) {
                const label = this.getRadioLabel(radio);
                if (label && (label === value || label === String(value))) {
                    radio.checked = true;
                    matched = true;
                    matchedRadio = radio;
                    matchMethod = 'label-text';
                    break;
                }
            }
        }

        if (matched) {
            this.triggerElementEvents(matchedRadio);
            return {
                success: true,
                method: matchMethod,
                finalValue: matchedRadio.value,
                displayText: this.getRadioLabel(matchedRadio) || matchedRadio.value,
                elementType: 'input-radio',
                attemptedValue: value
            };
        }

        return {
            success: false,
            reason: 'no-matching-radio',
            elementType: 'input-radio',
            attemptedValue: value,
            availableOptions: Array.from(radioButtons).map(radio => ({
                value: radio.value,
                label: this.getRadioLabel(radio)
            }))
        };
    }

    // 获取单选框的标签文本
    getRadioLabel(radioElement) {
        // 查找关联的label元素
        if (radioElement.id) {
            const label = document.querySelector(`label[for="${radioElement.id}"]`);
            if (label) return label.textContent.trim();
        }

        // 查找父级label
        const parentLabel = radioElement.closest('label');
        if (parentLabel) {
            return parentLabel.textContent.replace(radioElement.outerHTML, '').trim();
        }

        // 查找相邻的文本节点
        const nextSibling = radioElement.nextSibling;
        if (nextSibling && nextSibling.nodeType === Node.TEXT_NODE) {
            return nextSibling.textContent.trim();
        }

        return null;
    }

    // 填充下拉框（增强版，确保value正确性）
    fillSelectElement(element, value) {
        const originalValue = element.value; // 保存原始值
        const options = Array.from(element.options);

        // 1. 尝试直接匹配value
        if (this.hasOptionWithValue(element, value)) {
            element.value = value;
            return {
                success: true,
                method: 'direct-value',
                originalValue,
                finalValue: value,
                displayText: element.options[element.selectedIndex]?.text || value
            };
        }

        // 2. 尝试精确文本匹配
        const exactTextMatch = this.findOptionByText(element, value, true);
        if (exactTextMatch) {
            element.value = exactTextMatch.value;
            return {
                success: true,
                method: 'exact-text-match',
                originalValue,
                finalValue: exactTextMatch.value,
                displayText: exactTextMatch.text,
                matchedText: value
            };
        }

        // 3. 尝试忽略大小写的文本匹配
        const caseInsensitiveMatch = this.findOptionByText(element, value, false);
        if (caseInsensitiveMatch) {
            element.value = caseInsensitiveMatch.value;
            return {
                success: true,
                method: 'case-insensitive-match',
                originalValue,
                finalValue: caseInsensitiveMatch.value,
                displayText: caseInsensitiveMatch.text,
                matchedText: value
            };
        }

        // 4. 所有匹配失败，保持原值不变
        element.value = originalValue;
        return {
            success: false,
            reason: 'no-matching-option',
            originalValue,
            finalValue: originalValue,
            attemptedValue: value,
            availableOptions: options.map(opt => ({value: opt.value, text: opt.text}))
        };
    }

    // 检查select是否有指定value的option
    hasOptionWithValue(selectElement, value) {
        return Array.from(selectElement.options).some(option => option.value === value);
    }

    // 根据文本查找option
    findOptionByText(selectElement, text, exactMatch = true) {
        const options = Array.from(selectElement.options);
        const searchText = exactMatch ? text : text.toLowerCase().trim();

        for (const option of options) {
            const optionText = exactMatch ? option.text : option.text.toLowerCase().trim();
            if (optionText === searchText) {
                return { value: option.value, text: option.text, element: option };
            }
        }
        return null;
    }

    // 获取select的所有可用选项
    getAvailableOptions(selectElement) {
        return Array.from(selectElement.options).map(option => ({
            value: option.value,
            text: option.text,
            disabled: option.disabled
        }));
    }

    // 执行步骤
    async executeStep(step, data) {
        if (step.action === 'click') {
            const element = this.findElement(step.selector);
            if (element) {
                element.click();
                console.log(`🖱️ 点击元素: ${step.selector}`);
                
                // 等待页面加载
                if (step.waitTime) {
                    await new Promise(resolve => setTimeout(resolve, step.waitTime));
                }
            }
        } else if (step.action === 'fill') {
            // 填充当前步骤的字段
            if (step.fieldMappings) {
                // 并发填充步骤中的所有字段
                const fillPromises = step.fieldMappings.map(mapping =>
                    this.fillSingleField(mapping, data, 1000) // 1秒超时
                );

                const results = await Promise.allSettled(fillPromises);

                results.forEach((result, index) => {
                    const mapping = step.fieldMappings[index];
                    const fieldKey = mapping.fieldKey || mapping.jsonField;

                    if (result.status === 'fulfilled') {
                        const fillResult = result.value;
                        if (fillResult.success) {
                            console.log(`✅ 步骤填充: ${fieldKey} -> ${mapping.selector} = ${fillResult.value}`);
                        } else {
                            console.warn(`⚠️ 步骤填充失败: ${fieldKey} - ${fillResult.reason}`);
                        }
                    } else {
                        console.warn(`⚠️ 步骤填充异常: ${fieldKey}`, result.reason);
                    }
                });
            }
        }
    }

    // 处理表单检测
    async handleDetectForms() {
        try {
            console.log('🔍 检测页面表单...');

            const forms = this.detectFormFields();

            return {
                success: true,
                data: forms,
                message: `检测到 ${forms.length} 个表单`
            };
        } catch (error) {
            console.error('❌ 表单检测失败:', error);
            throw error;
        }
    }

    // 检测表单字段
    detectFormFields() {
        const forms = [];
        const formElements = document.querySelectorAll('form');

        formElements.forEach((form, formIndex) => {
            const formData = {
                formIndex,
                action: form.action || '',
                method: form.method || 'GET',
                fields: []
            };

            // 检测表单内的可填充输入字段
            const inputs = form.querySelectorAll('input, select, textarea');
            inputs.forEach((input, fieldIndex) => {
                // 只包含可填充的表单元素
                if (this.isEditableFormElement(input)) {
                    const field = {
                        fieldIndex,
                        tagName: input.tagName.toLowerCase(),
                        type: input.type || '',
                        name: input.name || '',
                        id: input.id || '',
                        placeholder: input.placeholder || '',
                        value: input.value || '',
                        required: input.required || false,
                        disabled: input.disabled || false,
                        readonly: input.readOnly || false,
                        selector: this.generateSelector(input)
                    };

                    formData.fields.push(field);
                }
            });

            // 只添加有可填充字段的表单
            if (formData.fields.length > 0) {
                forms.push(formData);
            }
        });

        // 也检测iframe中的表单
        const iframes = document.querySelectorAll('iframe');
        iframes.forEach((iframe, iframeIndex) => {
            try {
                const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                const iframeForms = iframeDoc.querySelectorAll('form');

                iframeForms.forEach((form, formIndex) => {
                    const formData = {
                        formIndex: `iframe-${iframeIndex}-${formIndex}`,
                        action: form.action || '',
                        method: form.method || 'GET',
                        isInIframe: true,
                        iframeIndex,
                        fields: []
                    };

                    const inputs = form.querySelectorAll('input, select, textarea');
                    inputs.forEach((input, fieldIndex) => {
                        // 只包含可填充的表单元素
                        if (this.isEditableFormElement(input)) {
                            const field = {
                                fieldIndex,
                                tagName: input.tagName.toLowerCase(),
                                type: input.type || '',
                                name: input.name || '',
                                id: input.id || '',
                                placeholder: input.placeholder || '',
                                value: input.value || '',
                                required: input.required || false,
                                disabled: input.disabled || false,
                                readonly: input.readOnly || false,
                                selector: this.generateSelector(input)
                            };

                            formData.fields.push(field);
                        }
                    });

                    // 只添加有可填充字段的表单
                    if (formData.fields.length > 0) {
                        forms.push(formData);
                    }
                });
            } catch (error) {
                console.warn('无法访问iframe内容:', error);
            }
        });

        // 添加页面中的可操作按钮信息
        const pageButtons = this.detectPageButtons();
        if (pageButtons.length > 0) {
            forms.push({
                formIndex: 'page-buttons',
                action: '',
                method: '',
                isPageButtons: true,
                fields: pageButtons
            });
        }

        return forms;
    }

    // 检测页面中的可操作按钮
    detectPageButtons() {
        const buttons = [];

        // 检测各种类型的按钮
        const buttonSelectors = [
            'button',
            'input[type="submit"]',
            'input[type="button"]',
            'input[type="reset"]',
            '[role="button"]',
            'a[onclick]'
        ];

        buttonSelectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach((element, index) => {
                // 排除隐藏或不可见的按钮
                if (element.offsetWidth > 0 && element.offsetHeight > 0 &&
                    !element.disabled &&
                    element.style.display !== 'none' &&
                    element.style.visibility !== 'hidden') {

                    const buttonInfo = {
                        fieldIndex: `btn_${index}`,
                        tagName: element.tagName.toLowerCase(),
                        type: element.type || 'button',
                        name: element.name || '',
                        id: element.id || '',
                        text: element.textContent?.trim() || element.value || '',
                        className: element.className || '',
                        onclick: element.onclick ? 'has-onclick' : '',
                        selector: this.generateSelector(element),
                        isButton: true
                    };

                    // 只添加有意义的按钮（有文本或有功能）
                    if (buttonInfo.text || buttonInfo.onclick || buttonInfo.type === 'submit') {
                        buttons.push(buttonInfo);
                    }
                }
            });
        });

        return buttons;
    }

    // 生成CSS选择器 - 增强版智能规则
    generateSelector(element) {
        const tagName = element.tagName.toLowerCase();
        console.log(`🎯 开始为${tagName}元素生成选择器`, {
            id: element.id,
            name: element.name,
            className: element.className,
            placeholder: element.getAttribute('placeholder'),
            text: element.textContent?.trim().substring(0, 30)
        });

        // 规则1: 优先使用ID（如果存在且唯一）
        if (element.id) {
            const idSelector = `#${element.id}`;
            if (this.isUniqueSelector(idSelector)) {
                console.log(`✅ 使用ID选择器: ${idSelector}`);
                return idSelector;
            } else {
                console.log(`⚠️ ID选择器不唯一: ${idSelector}`);
            }
        }

        // 规则2: 检查name属性（如果唯一或记录位置）
        if (element.name && ['input', 'select', 'textarea'].includes(tagName)) {
            const nameResult = this.generateNameBasedSelector(element);
            if (nameResult) {
                console.log(`✅ 使用Name选择器: ${nameResult}`);
                return nameResult;
            }
        }

        // 规则3: 优先使用label关联的精确选择器
        if (['input', 'select', 'textarea'].includes(tagName)) {
            const labelResult = this.generateEnhancedLabelBasedSelector(element);
            if (labelResult) {
                console.log(`✅ 使用增强Label关联选择器: ${labelResult}`);
                return labelResult;
            }
        }

        // 规则4: 对于表单元素，检查placeholder唯一性（增强版）
        if (['input', 'select', 'textarea'].includes(tagName)) {
            const placeholderResult = this.generateEnhancedPlaceholderBasedSelector(element);
            if (placeholderResult) {
                console.log(`✅ 使用增强Placeholder选择器: ${placeholderResult}`);
                return placeholderResult;
            }
        }

        // 规则5: 对于按钮，检查文本内容唯一性
        if (tagName === 'button' || (tagName === 'a' && element.getAttribute('role') === 'button')) {
            const textResult = this.generateTextBasedSelector(element);
            if (textResult) {
                console.log(`✅ 使用文本选择器: ${textResult}`);
                return textResult;
            }
        }

        // 规则6: 生成多层级上下文选择器
        const contextResult = this.generateMultiLevelContextSelector(element);
        console.log(`✅ 使用多层级上下文选择器: ${contextResult}`);
        return contextResult;
    }

    // 生成基于name的选择器
    generateNameBasedSelector(element) {
        const name = element.name;
        const tagName = element.tagName.toLowerCase();

        // 查找所有同name的元素
        const sameNameElements = document.querySelectorAll(`${tagName}[name="${name}"]`);

        if (sameNameElements.length === 1) {
            // 如果只有一个，直接使用name选择器
            return `${tagName}[name="${name}"]`;
        } else if (sameNameElements.length > 1) {
            // 如果有多个，记录是第几个
            const index = Array.from(sameNameElements).indexOf(element);
            if (index >= 0) {
                return `${tagName}[name="${name}"]:nth-of-type(${index + 1})`;
            }
        }

        return null;
    }

    // 生成增强的基于placeholder的选择器
    generateEnhancedPlaceholderBasedSelector(element) {
        const placeholder = element.getAttribute('placeholder');
        if (!placeholder) return null;

        const tagName = element.tagName.toLowerCase();
        // 清理placeholder中的字段名前缀
        const cleanPlaceholder = placeholder.replace(/^\[[\w.]+\]\s*/, '');

        console.log(`📝 处理placeholder: "${cleanPlaceholder}"`);

        // 检查清理后的placeholder是否唯一
        const samePlaceholderElements = document.querySelectorAll(`${tagName}[placeholder*="${cleanPlaceholder}"]`);

        if (samePlaceholderElements.length === 1) {
            return `${tagName}[placeholder="${cleanPlaceholder}"]`;
        }

        console.log(`⚠️ Placeholder选择器不唯一，找到${samePlaceholderElements.length}个相同元素，尝试增强选择器`);

        // 如果不唯一，生成多种增强选择器
        const candidateSelectors = [];

        // 1. 结合type属性
        const type = element.getAttribute('type');
        if (type) {
            candidateSelectors.push(`${tagName}[type="${type}"][placeholder="${cleanPlaceholder}"]`);
        }

        // 2. 结合name属性
        const name = element.getAttribute('name');
        if (name) {
            candidateSelectors.push(`${tagName}[name="${name}"][placeholder="${cleanPlaceholder}"]`);
        }

        // 3. 结合父容器
        const container = this.findFormItemContainer(element);
        if (container) {
            const containerSelector = this.generateElementBasicSelector(container);
            candidateSelectors.push(`${containerSelector} ${tagName}[placeholder="${cleanPlaceholder}"]`);

            // 4. 结合容器和位置
            const inputsInContainer = Array.from(container.querySelectorAll(`${tagName}[placeholder*="${cleanPlaceholder}"]`));
            const elementIndex = inputsInContainer.indexOf(element);
            if (elementIndex >= 0 && inputsInContainer.length > 1) {
                candidateSelectors.push(`${containerSelector} ${tagName}[placeholder="${cleanPlaceholder}"]:nth-of-type(${elementIndex + 1})`);
            }
        }

        // 5. 结合兄弟元素位置
        const parent = element.parentElement;
        if (parent) {
            const siblings = Array.from(parent.querySelectorAll(`${tagName}[placeholder*="${cleanPlaceholder}"]`));
            const siblingIndex = siblings.indexOf(element);
            if (siblingIndex >= 0 && siblings.length > 1) {
                const parentSelector = this.generateElementBasicSelector(parent);
                candidateSelectors.push(`${parentSelector} > ${tagName}[placeholder="${cleanPlaceholder}"]:nth-of-type(${siblingIndex + 1})`);
            }
        }

        // 检查哪个选择器唯一
        for (const selector of candidateSelectors) {
            if (this.isUniqueSelector(selector)) {
                console.log(`✅ 找到唯一的增强placeholder选择器: ${selector}`);
                return selector;
            }
        }

        console.log(`⚠️ 所有增强placeholder选择器都不唯一`);
        return null;
    }

    // 生成增强的基于label关联的选择器
    generateEnhancedLabelBasedSelector(element) {
        const labelInfo = this.findAssociatedLabel(element);
        if (!labelInfo) return null;

        const tagName = element.tagName.toLowerCase();
        const labelText = labelInfo.text.replace(/[:\s()（）]+$/, '').trim(); // 移除末尾的冒号、空格、括号

        console.log(`🏷️ 找到关联label: "${labelText}", 类型: ${labelInfo.type}`);

        if (labelInfo.type === 'for' && element.id) {
            // 使用for关联的选择器
            const forSelector = `${tagName}#${element.id}`;
            if (this.isUniqueSelector(forSelector)) {
                return forSelector;
            }
        }

        if (labelInfo.type === 'sibling' && labelInfo.container) {
            // 使用容器 + label文本的组合，生成多种精确选择器
            const containerSelector = this.generateElementBasicSelector(labelInfo.container);

            // 生成多种候选选择器，按精确度排序
            const candidateSelectors = [];

            // 1. 基于label文本的精确定位（通过for属性）
            if (labelInfo.element.getAttribute('for')) {
                const forId = labelInfo.element.getAttribute('for');
                candidateSelectors.push(`${containerSelector} ${tagName}#${forId}`);
            }

            // 2. 基于容器内label文本 + 输入框位置
            const labelSelector = this.generateElementBasicSelector(labelInfo.element);
            candidateSelectors.push(`${containerSelector} ${labelSelector} ~ ${tagName}`);
            candidateSelectors.push(`${containerSelector} ${labelSelector} + * ${tagName}`);

            // 3. 基于容器内的输入框顺序
            const inputsInContainer = Array.from(labelInfo.container.querySelectorAll(tagName));
            const elementIndex = inputsInContainer.indexOf(element);
            if (elementIndex >= 0 && inputsInContainer.length > 1) {
                candidateSelectors.push(`${containerSelector} ${tagName}:nth-of-type(${elementIndex + 1})`);
            }

            // 4. 基于placeholder和容器的组合
            const placeholder = element.getAttribute('placeholder');
            if (placeholder) {
                candidateSelectors.push(`${containerSelector} ${tagName}[placeholder="${placeholder}"]`);
            }

            // 5. 基本容器选择器
            candidateSelectors.push(`${containerSelector} ${tagName}`);

            // 检查哪个选择器唯一且有效
            for (const selector of candidateSelectors) {
                if (this.isUniqueSelector(selector)) {
                    console.log(`✅ 找到唯一的label关联选择器: ${selector}`);
                    return selector;
                }
            }

            // 如果都不唯一，返回最具体的一个
            console.log(`⚠️ 所有label关联选择器都不唯一，返回最具体的: ${candidateSelectors[0]}`);
            return candidateSelectors[0] || `${containerSelector} ${tagName}`;
        }

        return null;
    }

    // 生成基于文本内容的选择器（主要用于按钮）
    generateTextBasedSelector(element) {
        const text = element.textContent?.trim();
        if (!text || text.length === 0) {
            console.log('⚠️ 元素没有文本内容，跳过文本选择器');
            return null;
        }

        const tagName = element.tagName.toLowerCase();
        const cleanText = text.replace(/\s+/g, ' ').trim();
        console.log(`🔍 检查文本选择器: "${cleanText}"`);

        // 检查相同文本的按钮数量
        const sameTextButtons = Array.from(document.querySelectorAll(tagName)).filter(btn => {
            const btnText = btn.textContent?.trim();
            if (!btnText) return false;
            return btnText.replace(/\s+/g, ' ') === cleanText;
        });

        console.log(`🔍 找到${sameTextButtons.length}个相同文本的${tagName}元素`);

        if (sameTextButtons.length === 1) {
            // 如果文本唯一，结合类名生成选择器
            const meaningfulClasses = this.getMeaningfulClasses(element);
            if (meaningfulClasses.length > 0) {
                const selector = `${tagName}.${meaningfulClasses.slice(0, 2).join('.')}`;
                console.log(`✅ 生成文本+类名选择器: ${selector}`);
                return selector;
            } else {
                // 没有有意义的类名，使用type属性
                const type = element.getAttribute('type');
                if (type) {
                    const selector = `${tagName}[type="${type}"]`;
                    console.log(`✅ 生成文本+type选择器: ${selector}`);
                    return selector;
                }
            }
        }

        console.log('⚠️ 文本不唯一或无法生成有效选择器');
        return null;
    }

    // 生成多层级上下文选择器
    generateMultiLevelContextSelector(element) {
        const tagName = element.tagName.toLowerCase();
        console.log(`🔍 生成多层级上下文选择器`);

        // 收集元素的所有可用信息
        const elementInfo = {
            id: element.id,
            name: element.name,
            className: element.className,
            type: element.getAttribute('type'),
            placeholder: element.getAttribute('placeholder'),
            text: element.textContent?.trim()
        };

        // 生成候选选择器列表，按精确度排序
        const candidateSelectors = [];

        // 1. 基于多层级父容器的精确定位
        const multiLevelSelectors = this.generateMultiLevelParentSelectors(element);
        candidateSelectors.push(...multiLevelSelectors);

        // 2. 基于兄弟元素位置的精确定位
        const siblingSelectors = this.generateSiblingPositionSelectors(element);
        candidateSelectors.push(...siblingSelectors);

        // 3. 基于属性组合的选择器
        const attributeSelectors = this.generateCombinedAttributeSelectors(element);
        candidateSelectors.push(...attributeSelectors);

        // 4. 基础选择器作为后备
        const baseSelector = this.generateBaseSelector(element);
        if (baseSelector) {
            candidateSelectors.push(baseSelector);
        }

        // 检查每个候选选择器的唯一性
        for (const selector of candidateSelectors) {
            if (selector && this.isUniqueSelector(selector)) {
                console.log(`✅ 找到唯一的多层级选择器: ${selector}`);
                return selector;
            }
        }

        // 如果都不唯一，返回最具体的一个
        const fallbackSelector = candidateSelectors[0] || tagName;
        console.log(`⚠️ 所有选择器都不唯一，使用后备选择器: ${fallbackSelector}`);
        return fallbackSelector;
    }

    // 生成元素的基础选择器（不包含标签名）
    generateElementBasicSelector(element) {
        if (element.id) {
            return `#${element.id}`;
        }

        const classes = this.getMeaningfulClasses(element);
        if (classes.length > 0) {
            return `.${classes[0]}`;
        }

        return element.tagName.toLowerCase();
    }

    // 生成表单元素选择器（基于label关联和上下文）
    generateFormElementSelector(element) {
        const tagName = element.tagName.toLowerCase();
        const selectors = [];

        // 1. 查找关联的label
        const labelInfo = this.findAssociatedLabel(element);
        if (labelInfo) {
            // 基于label生成选择器
            const labelSelector = this.generateLabelBasedSelectorWithInfo(element, labelInfo);
            if (labelSelector) {
                selectors.push(labelSelector);
            }
        }

        // 2. 基于父容器和属性组合
        const containerSelector = this.generateContainerBasedSelector(element);
        if (containerSelector) {
            selectors.push(containerSelector);
        }

        // 3. 基于多重属性组合
        const attributeSelector = this.generateMultiAttributeSelector(element);
        if (attributeSelector) {
            selectors.push(attributeSelector);
        }

        // 返回第一个唯一的选择器
        for (const selector of selectors) {
            if (this.isUniqueSelector(selector)) {
                return selector;
            }
        }

        return selectors[0] || null;
    }

    // 生成按钮选择器（基于文本内容和上下文）
    generateButtonSelector(element) {
        const selectors = [];
        const buttonText = element.textContent?.trim() || '';

        // 1. 基于文本内容生成选择器（存储文本信息用于后续匹配）
        if (buttonText) {
            const textSelector = this.generateTextBasedSelector(element, buttonText);
            if (textSelector) {
                selectors.push(textSelector);
            }
        }

        // 2. 基于父容器和类名组合
        const containerSelector = this.generateContainerBasedSelector(element);
        if (containerSelector) {
            selectors.push(containerSelector);
        }

        // 3. 基于多重属性组合
        const attributeSelector = this.generateMultiAttributeSelector(element);
        if (attributeSelector) {
            selectors.push(attributeSelector);
        }

        // 返回第一个唯一的选择器
        for (const selector of selectors) {
            if (this.isUniqueSelector(selector)) {
                return selector;
            }
        }

        return selectors[0] || null;
    }

    // 查找关联的label
    findAssociatedLabel(element) {
        // 1. 通过for属性查找label
        if (element.id) {
            const label = document.querySelector(`label[for="${element.id}"]`);
            if (label) {
                return {
                    type: 'for',
                    element: label,
                    text: label.textContent?.trim() || '',
                    selector: this.generateElementSelector(label)
                };
            }
        }

        // 2. 查找父级label
        let parent = element.parentElement;
        while (parent && parent.tagName.toLowerCase() !== 'body') {
            if (parent.tagName.toLowerCase() === 'label') {
                return {
                    type: 'parent',
                    element: parent,
                    text: parent.textContent?.trim() || '',
                    selector: this.generateElementSelector(parent)
                };
            }
            parent = parent.parentElement;
        }

        // 3. 查找同级或相邻的label
        const formItem = this.findFormItemContainer(element);
        if (formItem) {
            const label = formItem.querySelector('label');
            if (label) {
                return {
                    type: 'sibling',
                    element: label,
                    text: label.textContent?.trim() || '',
                    selector: this.generateElementSelector(label),
                    container: formItem
                };
            }
        }

        return null;
    }

    // 查找表单项容器（如.el-form-item）
    findFormItemContainer(element) {
        let parent = element.parentElement;
        while (parent && parent.tagName.toLowerCase() !== 'body') {
            const classes = Array.from(parent.classList);
            // 检查是否为表单项容器
            if (classes.some(cls =>
                cls.includes('form-item') ||
                cls.includes('field') ||
                cls.includes('input-group') ||
                cls.includes('form-group')
            )) {
                return parent;
            }
            parent = parent.parentElement;
        }
        return null;
    }

    // 基于label生成选择器（带label信息参数版本）
    generateLabelBasedSelectorWithInfo(element, labelInfo) {
        const tagName = element.tagName.toLowerCase();
        const selectors = [];

        if (labelInfo.type === 'for' && element.id) {
            // 使用label[for] + input[id]的组合
            selectors.push(`label[for="${element.id}"] ~ * ${tagName}#${element.id}`);
            selectors.push(`${tagName}#${element.id}`);
        } else if (labelInfo.type === 'sibling' && labelInfo.container) {
            // 使用容器内的label + input组合
            const containerSelector = this.generateElementSelector(labelInfo.container);
            const labelText = labelInfo.text.replace(/[:\s]+$/, ''); // 移除末尾的冒号和空格

            // 生成基于容器和label文本的选择器
            const elementSelector = this.generateElementSelector(element);
            selectors.push(`${containerSelector} ${elementSelector}`);

            // 如果有placeholder，结合使用
            const placeholder = element.getAttribute('placeholder');
            if (placeholder) {
                const cleanPlaceholder = placeholder.replace(/^\[[\w.]+\]\s*/, '');
                selectors.push(`${containerSelector} ${tagName}[placeholder="${cleanPlaceholder}"]`);
            }
        }

        return selectors.find(s => s && this.isUniqueSelector(s)) || selectors[0];
    }

    // 基于容器生成选择器
    generateContainerBasedSelector(element) {
        const tagName = element.tagName.toLowerCase();
        const containers = [];

        // 查找有意义的父容器
        let parent = element.parentElement;
        let depth = 0;
        while (parent && depth < 5) {
            const parentClasses = this.getMeaningfulClasses(parent);
            if (parentClasses.length > 0) {
                const parentSelector = `${parent.tagName.toLowerCase()}.${parentClasses[0]}`;
                containers.push(parentSelector);
            }
            parent = parent.parentElement;
            depth++;
        }

        if (containers.length === 0) return null;

        // 生成元素自身的选择器
        const elementSelector = this.generateElementSelector(element);

        // 组合容器和元素选择器
        const containerPath = containers.slice(0, 2).join(' > '); // 最多使用2层容器
        return `${containerPath} ${elementSelector}`;
    }

    // 基于多重属性生成选择器
    generateMultiAttributeSelector(element) {
        const tagName = element.tagName.toLowerCase();
        const attributes = [];

        // 收集所有有用的属性
        const importantAttrs = [
            'type', 'placeholder', 'name', 'class', 'role', 'aria-label',
            'data-testid', 'data-cy', 'data-test', 'autocomplete'
        ];

        importantAttrs.forEach(attr => {
            let value = element.getAttribute(attr);
            if (value) {
                if (attr === 'placeholder') {
                    // 清理placeholder中的字段名前缀
                    value = value.replace(/^\[[\w.]+\]\s*/, '');
                }
                if (attr === 'class') {
                    // 只使用有意义的类名
                    const meaningfulClasses = this.getMeaningfulClasses(element);
                    if (meaningfulClasses.length > 0) {
                        attributes.push(`.${meaningfulClasses.slice(0, 3).join('.')}`);
                    }
                } else {
                    attributes.push(`[${attr}="${value}"]`);
                }
            }
        });

        if (attributes.length === 0) return null;

        // 组合标签名和属性
        return `${tagName}${attributes.join('')}`;
    }

    // 基于文本内容生成选择器（主要用于按钮）
    generateTextBasedSelector(element, text) {
        const tagName = element.tagName.toLowerCase();
        const cleanText = text.replace(/\s+/g, ' ').trim();

        // 由于CSS不支持:contains，我们生成一个包含文本信息的复合选择器
        const classes = this.getMeaningfulClasses(element);
        let selector = tagName;

        if (classes.length > 0) {
            selector += `.${classes.slice(0, 2).join('.')}`;
        }

        // 添加其他属性来增加唯一性
        const type = element.getAttribute('type');
        if (type) {
            selector += `[type="${type}"]`;
        }

        // 注意：这里我们不能直接使用:contains，但可以在后续匹配时使用文本信息
        return selector;
    }

    // 生成元素的基本选择器
    generateElementSelector(element) {
        const tagName = element.tagName.toLowerCase();

        if (element.id) {
            return `${tagName}#${element.id}`;
        }

        const classes = this.getMeaningfulClasses(element);
        if (classes.length > 0) {
            return `${tagName}.${classes.slice(0, 2).join('.')}`;
        }

        return tagName;
    }

    // 生成多层级父容器选择器
    generateMultiLevelParentSelectors(element) {
        const tagName = element.tagName.toLowerCase();
        const selectors = [];

        // 收集多层级父容器信息
        const parentChain = [];
        let current = element.parentElement;
        let depth = 0;

        while (current && depth < 5 && current.tagName.toLowerCase() !== 'body') {
            const parentInfo = {
                element: current,
                selector: this.generateElementBasicSelector(current),
                classes: this.getMeaningfulClasses(current),
                depth: depth
            };
            parentChain.push(parentInfo);
            current = current.parentElement;
            depth++;
        }

        if (parentChain.length === 0) return selectors;

        // 生成基于不同层级组合的选择器
        for (let i = 0; i < Math.min(parentChain.length, 3); i++) {
            const parentPath = parentChain.slice(0, i + 1)
                .map(p => p.selector)
                .reverse()
                .join(' ');

            // 1. 直接子元素选择器
            const elementSelector = this.generateElementBasicSelector(element);
            selectors.push(`${parentPath} ${elementSelector}`);

            // 2. 基于位置的选择器
            const siblings = Array.from(parentChain[i].element.querySelectorAll(tagName));
            const elementIndex = siblings.indexOf(element);
            if (elementIndex >= 0 && siblings.length > 1) {
                selectors.push(`${parentPath} ${tagName}:nth-of-type(${elementIndex + 1})`);
            }

            // 3. 基于属性的选择器
            const placeholder = element.getAttribute('placeholder');
            if (placeholder) {
                selectors.push(`${parentPath} ${tagName}[placeholder="${placeholder}"]`);
            }
        }

        return selectors;
    }

    // 生成基于兄弟元素位置的选择器
    generateSiblingPositionSelectors(element) {
        const tagName = element.tagName.toLowerCase();
        const selectors = [];
        const parent = element.parentElement;

        if (!parent) return selectors;

        const parentSelector = this.generateElementBasicSelector(parent);

        // 1. 基于同类型兄弟元素的位置
        const sameTypeSiblings = Array.from(parent.children).filter(el =>
            el.tagName.toLowerCase() === tagName
        );

        if (sameTypeSiblings.length > 1) {
            const index = sameTypeSiblings.indexOf(element);
            if (index >= 0) {
                selectors.push(`${parentSelector} > ${tagName}:nth-of-type(${index + 1})`);
            }
        }

        // 2. 基于所有兄弟元素的位置
        const allSiblings = Array.from(parent.children);
        if (allSiblings.length > 1) {
            const index = allSiblings.indexOf(element);
            if (index >= 0) {
                selectors.push(`${parentSelector} > :nth-child(${index + 1})`);
                selectors.push(`${parentSelector} > ${tagName}:nth-child(${index + 1})`);
            }
        }

        return selectors;
    }

    // 检查选择器是否唯一
    isUniqueSelector(selector) {
        try {
            const elements = document.querySelectorAll(selector);
            return elements.length === 1;
        } catch (e) {
            console.warn('选择器语法错误:', selector, e);
            return false;
        }
    }

    // 生成精确的组合选择器
    generatePreciseSelector(element) {
        const tagName = element.tagName.toLowerCase();
        let selectors = [tagName];

        // 添加有意义的类名
        const meaningfulClasses = this.getMeaningfulClasses(element);
        if (meaningfulClasses.length > 0) {
            selectors.push(`.${meaningfulClasses.join('.')}`);
        }

        // 添加重要属性
        const attributes = this.getImportantAttributes(element);
        selectors.push(...attributes);

        // 对于表单元素，添加label关联信息
        if (['input', 'select', 'textarea'].includes(tagName)) {
            const labelInfo = this.getLabelInfo(element);
            if (labelInfo) {
                selectors.push(labelInfo);
            }
        }

        // 对于按钮等可点击元素，添加文本信息
        if (['button', 'a', 'span', 'div'].includes(tagName)) {
            const textInfo = this.getTextInfo(element);
            if (textInfo) {
                selectors.push(textInfo);
            }
        }

        return selectors.join('');
    }

    // 生成组合属性选择器
    generateCombinedAttributeSelectors(element) {
        const tagName = element.tagName.toLowerCase();
        const selectors = [];

        // 收集所有可用属性
        const attributes = {
            id: element.id,
            name: element.name,
            type: element.getAttribute('type'),
            placeholder: element.getAttribute('placeholder'),
            class: element.className,
            value: element.value,
            title: element.getAttribute('title'),
            'data-field': element.getAttribute('data-field'),
            'data-key': element.getAttribute('data-key')
        };

        // 生成不同属性组合的选择器
        const attributePairs = [];

        // 1. 基础属性组合
        if (attributes.type && attributes.placeholder) {
            const cleanPlaceholder = attributes.placeholder.replace(/^\[[\w.]+\]\s*/, '');
            attributePairs.push(`[type="${attributes.type}"][placeholder="${cleanPlaceholder}"]`);
        }

        if (attributes.name && attributes.type) {
            attributePairs.push(`[name="${attributes.name}"][type="${attributes.type}"]`);
        }

        if (attributes.class) {
            const meaningfulClasses = this.getMeaningfulClasses(element);
            if (meaningfulClasses.length > 0) {
                attributePairs.push(`.${meaningfulClasses.slice(0, 2).join('.')}`);

                // 结合其他属性
                if (attributes.type) {
                    attributePairs.push(`.${meaningfulClasses[0]}[type="${attributes.type}"]`);
                }
            }
        }

        // 2. 生成完整选择器
        for (const attrPair of attributePairs) {
            selectors.push(`${tagName}${attrPair}`);
        }

        return selectors;
    }

    // 生成基础选择器
    generateBaseSelector(element) {
        const tagName = element.tagName.toLowerCase();
        let selector = tagName;

        // 添加最重要的属性
        if (element.id) {
            selector += `#${element.id}`;
        } else {
            const meaningfulClasses = this.getMeaningfulClasses(element);
            if (meaningfulClasses.length > 0) {
                selector += `.${meaningfulClasses[0]}`;
            }

            const type = element.getAttribute('type');
            if (type) {
                selector += `[type="${type}"]`;
            }
        }

        return selector;
    }

    // 获取有意义的类名
    getMeaningfulClasses(element) {
        if (!element.className) return [];

        const classes = element.className.split(' ').filter(c => c.trim());
        return classes.filter(c =>
            c &&
            c.length > 2 &&
            !c.includes('hover') &&
            !c.includes('active') &&
            !c.includes('focus') &&
            !c.includes('selected') &&
            !c.includes('disabled')
        ).slice(0, 3); // 最多取3个类名
    }

    // 获取重要属性
    getImportantAttributes(element) {
        const attributes = [];

        // Vue相关属性
        const vueAttrs = ['v-model', 'v-bind', 'v-on', ':class', ':style'];
        vueAttrs.forEach(attr => {
            if (element.hasAttribute(attr)) {
                const value = element.getAttribute(attr);
                if (value) {
                    attributes.push(`[${attr}="${value}"]`);
                }
            }
        });

        // 其他重要属性，但要特殊处理placeholder
        const importantAttrs = [
            'data-toggle', 'data-bs-toggle', 'data-target', 'data-bs-target',
            'role', 'aria-label', 'aria-labelledby', 'type',
            'data-testid', 'data-cy', 'data-test'
        ];

        importantAttrs.forEach(attr => {
            const value = element.getAttribute(attr);
            if (value) {
                attributes.push(`[${attr}="${value}"]`);
            }
        });

        // 特殊处理placeholder - 清理字段名前缀
        const placeholder = element.getAttribute('placeholder');
        if (placeholder) {
            // 移除字段名前缀，如 [preconfigured_entry.master_airwaybill_number]
            const cleanPlaceholder = placeholder.replace(/^\[[\w.]+\]\s*/, '');
            if (cleanPlaceholder && cleanPlaceholder !== placeholder) {
                // 如果清理后的placeholder不同，使用清理后的版本
                attributes.push(`[placeholder="${cleanPlaceholder}"]`);
            } else {
                // 否则使用原始placeholder
                attributes.push(`[placeholder="${placeholder}"]`);
            }
        }

        return attributes;
    }

    // 获取label关联信息
    getLabelInfo(element) {
        // 通过for属性查找label
        if (element.id) {
            const label = document.querySelector(`label[for="${element.id}"]`);
            if (label && label.textContent.trim()) {
                return `[aria-labelledby*="${element.id}"], [id="${element.id}"]`;
            }
        }

        // 查找父级label
        let parent = element.parentElement;
        while (parent && parent.tagName.toLowerCase() !== 'body') {
            if (parent.tagName.toLowerCase() === 'label') {
                break;
            }
            parent = parent.parentElement;
        }

        return null;
    }

    // 获取文本信息（用于按钮等）
    getTextInfo(element) {
        const text = element.textContent?.trim();
        if (!text || text.length === 0 || text.length > 50) {
            return null;
        }

        // 对于包含特殊字符的文本，使用属性选择器
        if (element.getAttribute('title') === text) {
            return `[title="${text}"]`;
        }
        if (element.getAttribute('aria-label') === text) {
            return `[aria-label="${text}"]`;
        }
        if (element.getAttribute('alt') === text) {
            return `[alt="${text}"]`;
        }

        // 对于按钮，尝试使用文本内容作为识别特征
        const tagName = element.tagName.toLowerCase();
        if (['button', 'a'].includes(tagName)) {
            // 清理文本，移除多余的空格和特殊字符
            const cleanText = text.replace(/\s+/g, ' ').trim();
            if (cleanText.length > 0 && cleanText.length <= 20) {
                // 不直接返回:contains选择器，而是在上下文选择器中使用
                return null; // 让上下文选择器处理
            }
        }

        return null;
    }

    // 生成包含上下文的选择器
    generateContextualSelector(element) {
        const selectors = [];
        const tagName = element.tagName.toLowerCase();

        // 对于按钮，优先使用文本内容结合类名
        if (tagName === 'button') {
            const text = element.textContent?.trim();
            if (text && text.length > 0 && text.length <= 30) {
                // 清理文本，移除多余空格
                const cleanText = text.replace(/\s+/g, ' ').trim();
                const meaningfulClasses = this.getMeaningfulClasses(element);

                if (meaningfulClasses.length > 0) {
                    // 使用类名 + 文本内容的组合
                    const classSelector = meaningfulClasses.slice(0, 2).map(c => `.${c}`).join('');
                    // 由于CSS不支持:contains，我们生成一个基于类名和位置的选择器
                    selectors.push(`button${classSelector}`);

                    // 如果有父容器，加上父容器信息
                    const parent = element.parentElement;
                    if (parent) {
                        const parentClasses = this.getMeaningfulClasses(parent);
                        if (parentClasses.length > 0) {
                            const parentSelector = `.${parentClasses[0]}`;
                            selectors.push(`${parentSelector} button${classSelector}`);
                        }
                    }
                }
            }
        }

        // 获取父元素上下文
        const parent = element.parentElement;
        if (parent && parent.tagName.toLowerCase() !== 'body') {
            const parentSelector = this.getElementBasicSelector(parent);
            if (parentSelector) {
                // 生成父子选择器
                const childSelector = this.getElementBasicSelector(element);
                if (childSelector) {
                    selectors.push(`${parentSelector} > ${childSelector}`);
                }

                // 如果有兄弟元素，使用nth-child
                const siblings = Array.from(parent.children);
                const index = siblings.indexOf(element);
                if (siblings.length > 1) {
                    selectors.push(`${parentSelector} > ${tagName}:nth-child(${index + 1})`);
                }
            }
        }

        // 查找相邻的label或描述性元素
        const prevElement = element.previousElementSibling;
        if (prevElement) {
            const prevSelector = this.getElementBasicSelector(prevElement);
            if (prevSelector) {
                const currentSelector = this.getElementBasicSelector(element);
                if (currentSelector) {
                    selectors.push(`${prevSelector} + ${currentSelector}`);
                }
            }
        }

        // 返回最短的有效选择器
        return selectors.find(selector => selector && this.isUniqueSelector(selector)) || selectors[0];
    }

    // 生成完整路径选择器
    generateFullPathSelector(element) {
        const path = [];
        let current = element;
        let depth = 0;
        const maxDepth = 8; // 限制深度避免选择器过长

        while (current && current !== document.body && depth < maxDepth) {
            const selector = this.getElementPathSelector(current);
            if (selector) {
                path.unshift(selector);
            }
            current = current.parentElement;
            depth++;
        }

        return path.length > 0 ? path.join(' > ') : element.tagName.toLowerCase();
    }

    // 获取元素的基本选择器
    getElementBasicSelector(element) {
        const tagName = element.tagName.toLowerCase();

        // 优先使用ID
        if (element.id) {
            return `#${element.id}`;
        }

        // 使用有意义的类名
        const meaningfulClasses = this.getMeaningfulClasses(element);
        if (meaningfulClasses.length > 0) {
            return `${tagName}.${meaningfulClasses[0]}`;
        }

        // 使用重要属性
        const attributes = this.getImportantAttributes(element);
        if (attributes.length > 0) {
            return `${tagName}${attributes[0]}`;
        }

        return tagName;
    }

    // 获取元素在路径中的选择器
    getElementPathSelector(element) {
        const tagName = element.tagName.toLowerCase();

        // 优先使用ID
        if (element.id) {
            return `#${element.id}`;
        }

        // 使用类名
        const meaningfulClasses = this.getMeaningfulClasses(element);
        if (meaningfulClasses.length > 0) {
            return `${tagName}.${meaningfulClasses[0]}`;
        }

        // 使用nth-child定位
        const parent = element.parentElement;
        if (parent) {
            const siblings = Array.from(parent.children).filter(el =>
                el.tagName.toLowerCase() === tagName
            );
            if (siblings.length > 1) {
                const index = siblings.indexOf(element);
                return `${tagName}:nth-of-type(${index + 1})`;
            }
        }

        return tagName;
    }

    // 检查是否为可编辑的表单元素
    isEditableFormElement(element) {
        const tagName = element.tagName.toLowerCase();
        const type = element.type ? element.type.toLowerCase() : '';

        // 排除不可编辑或不需要填充的元素
        if (element.disabled || element.readOnly) {
            return false;
        }

        // 排除隐藏元素
        if (element.style.display === 'none' || element.style.visibility === 'hidden') {
            return false;
        }

        // 排除不可见元素（宽高为0）
        if (element.offsetWidth === 0 && element.offsetHeight === 0) {
            return false;
        }

        // textarea 和 select 都是可编辑的
        if (tagName === 'textarea' || tagName === 'select') {
            return true;
        }

        // input 元素需要进一步判断
        if (tagName === 'input') {
            // 排除不需要填充的input类型
            const excludedTypes = [
                'submit', 'button', 'reset', 'image',
                'file', 'hidden'
            ];

            if (excludedTypes.includes(type)) {
                return false;
            }

            // 包含可填充的input类型
            const editableTypes = [
                'text', 'password', 'email', 'tel', 'url', 'search',
                'number', 'range', 'date', 'datetime-local', 'time',
                'week', 'month', 'color', 'checkbox', 'radio'
            ];

            // 如果没有type或type为空，默认为text类型
            if (!type || type === '') {
                return true;
            }

            return editableTypes.includes(type);
        }

        return false;
    }

    // 处理数据提取（旧版本，保留作为备用）
    async handleExtractDataOld() {
        try {
            console.log('📤 提取表单数据（旧版本）...');

            const data = this.extractFormDataOld();

            return {
                success: true,
                data: data,
                message: '数据提取完成'
            };
        } catch (error) {
            console.error('❌ 数据提取失败:', error);
            throw error;
        }
    }

    // 提取表单数据（旧版本）
    extractFormDataOld() {
        const data = {};

        // 提取所有可编辑输入字段的值
        const inputs = document.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            // 只提取可编辑的表单元素数据
            if (this.isEditableFormElement(input)) {
                const key = input.name || input.id || `field_${Math.random().toString(36).substr(2, 9)}`;

                if (input.type === 'checkbox' || input.type === 'radio') {
                    data[key] = input.checked;
                } else {
                    data[key] = input.value;
                }
            }
        });

        // 也提取iframe中的数据
        const iframes = document.querySelectorAll('iframe');
        iframes.forEach((iframe, iframeIndex) => {
            try {
                const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                const iframeInputs = iframeDoc.querySelectorAll('input, select, textarea');

                iframeInputs.forEach(input => {
                    // 只提取可编辑的表单元素数据
                    if (this.isEditableFormElement(input)) {
                        const key = `iframe_${iframeIndex}_${input.name || input.id || Math.random().toString(36).substr(2, 9)}`;

                        if (input.type === 'checkbox' || input.type === 'radio') {
                            data[key] = input.checked;
                        } else {
                            data[key] = input.value;
                        }
                    }
                });
            } catch (error) {
                console.warn('无法访问iframe内容:', error);
            }
        });

        return data;
    }

    // 处理HTML提取
    async handleExtractHTML() {
        try {
            console.log('📋 提取页面HTML...');

            // 智能检测最上层显示的内容
            const topLayerContent = this.detectTopLayerContent();

            if (topLayerContent.type === 'iframe') {
                console.log('🎯 检测到最上层iframe，优先使用iframe内容');
                // 使用最上层iframe的内容
                const extractedData = {
                    url: window.location.href,
                    title: document.title,
                    html: topLayerContent.html,
                    contentType: 'iframe',
                    iframeInfo: topLayerContent.info,
                    timestamp: new Date().toISOString()
                };

                const textToCopy = JSON.stringify(extractedData, null, 2);
                const success = await this.copyToClipboard(textToCopy);

                return {
                    success: true,
                    data: extractedData,
                    message: success ? 'iframe内容已复制到剪贴板' : 'iframe内容提取完成，但复制到剪贴板失败',
                    copied: success
                };
            }

            // 如果没有检测到iframe或iframe不可访问，使用原有逻辑
            console.log('📄 使用完整页面内容');
            let htmlContent = this.convertRelativeToAbsoluteUrls(document.documentElement.outerHTML);

            // 也尝试获取iframe内容（作为备选）
            const iframes = document.querySelectorAll('iframe');
            const iframeContents = [];

            iframes.forEach((iframe, index) => {
                try {
                    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                    const iframeHtml = iframeDoc.documentElement.outerHTML;
                    // 对iframe内容也进行URL转换
                    const convertedIframeHtml = this.convertRelativeToAbsoluteUrls(iframeHtml);
                    iframeContents.push({
                        index,
                        src: iframe.src,
                        html: convertedIframeHtml,
                        zIndex: this.getElementZIndex(iframe),
                        visible: this.isElementVisible(iframe)
                    });
                } catch (error) {
                    console.warn(`无法访问iframe ${index} 内容:`, error);
                }
            });

            const extractedData = {
                url: window.location.href,
                title: document.title,
                html: htmlContent,
                contentType: 'full-page',
                iframes: iframeContents,
                timestamp: new Date().toISOString()
            };

            // 复制到剪贴板 - 使用更可靠的方法
            const textToCopy = JSON.stringify(extractedData, null, 2);
            console.log('📋 准备复制的数据大小:', textToCopy.length, '字符');

            const success = await this.copyToClipboard(textToCopy);

            return {
                success: true, // 提取总是成功的
                data: extractedData,
                message: success ? 'HTML已复制到剪贴板' : 'HTML提取完成，但复制到剪贴板失败',
                copySuccess: success
            };
        } catch (error) {
            console.error('❌ HTML提取失败:', error);
            throw error;
        }
    }

    // 复制文本到剪贴板的可靠方法
    async copyToClipboard(text) {
        console.log('🔄 开始复制到剪贴板...');
        console.log('📍 当前页面URL:', window.location.href);
        console.log('🔒 是否为安全上下文:', window.isSecureContext);
        console.log('📋 Clipboard API可用:', !!navigator.clipboard);
        console.log('📊 文本长度:', text.length);

        // 跳过用户交互方法，因为在自动复制场景下不适用

        // 方法1: 尝试使用现代的Clipboard API
        try {
            if (navigator.clipboard && window.isSecureContext) {
                console.log('🔄 方法1: 尝试使用Clipboard API...');
                await navigator.clipboard.writeText(text);
                console.log('✅ 使用Clipboard API复制成功');
                return true;
            } else {
                console.log('⚠️ Clipboard API不可用，原因:');
                console.log('  - navigator.clipboard:', !!navigator.clipboard);
                console.log('  - window.isSecureContext:', window.isSecureContext);
            }
        } catch (error) {
            console.log('❌ Clipboard API失败:', error.message);
        }

        // 方法2: 使用传统的execCommand方法
        try {
            console.log('🔄 方法2: 尝试使用execCommand方法...');
            const success = await this.copyWithExecCommand(text);
            if (success) {
                console.log('✅ 使用execCommand复制成功');
                return true;
            }
        } catch (error) {
            console.log('❌ execCommand失败:', error.message);
        }

        // 方法3: 通过Background Script复制
        try {
            console.log('🔄 方法3: 尝试通过Background Script复制...');
            const bgResult = await this.copyViaBackground(text);
            if (bgResult) {
                console.log('✅ 通过Background Script复制成功');
                return true;
            }
        } catch (error) {
            console.log('❌ Background Script复制失败:', error.message);
        }

        console.error('❌ 所有复制方法都失败了');

        // 注意：不再验证剪贴板内容，因为需要clipboardRead权限且不可靠
        // 如果到这里，说明所有方法都报告失败，但实际可能已经复制成功
        // 建议用户手动检查剪贴板内容

        return false;
    }

    // 用户交互触发的复制方法
    async copyWithUserInteraction(text) {
        return new Promise((resolve) => {
            // 创建一个临时的可见按钮
            const button = document.createElement('button');
            button.textContent = '点击复制';
            button.style.position = 'fixed';
            button.style.top = '10px';
            button.style.right = '10px';
            button.style.zIndex = '999999';
            button.style.padding = '8px 16px';
            button.style.backgroundColor = '#007bff';
            button.style.color = 'white';
            button.style.border = 'none';
            button.style.borderRadius = '4px';
            button.style.cursor = 'pointer';

            let resolved = false;

            button.onclick = async () => {
                try {
                    if (navigator.clipboard) {
                        await navigator.clipboard.writeText(text);
                    } else {
                        // 降级到execCommand
                        const textArea = document.createElement('textarea');
                        textArea.value = text;
                        document.body.appendChild(textArea);
                        textArea.select();
                        document.execCommand('copy');
                        document.body.removeChild(textArea);
                    }

                    button.textContent = '复制成功!';
                    button.style.backgroundColor = '#28a745';

                    setTimeout(() => {
                        if (button.parentNode) {
                            button.parentNode.removeChild(button);
                        }
                        if (!resolved) {
                            resolved = true;
                            resolve(true);
                        }
                    }, 1000);
                } catch (error) {
                    button.textContent = '复制失败';
                    button.style.backgroundColor = '#dc3545';
                    setTimeout(() => {
                        if (button.parentNode) {
                            button.parentNode.removeChild(button);
                        }
                        if (!resolved) {
                            resolved = true;
                            resolve(false);
                        }
                    }, 2000);
                }
            };

            document.body.appendChild(button);

            // 5秒后自动移除按钮
            setTimeout(() => {
                if (button.parentNode) {
                    button.parentNode.removeChild(button);
                }
                if (!resolved) {
                    resolved = true;
                    resolve(false);
                }
            }, 5000);
        });
    }

    // execCommand复制方法
    async copyWithExecCommand(text) {
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        textArea.style.opacity = '0';
        textArea.style.pointerEvents = 'none';
        textArea.setAttribute('readonly', '');

        document.body.appendChild(textArea);

        try {
            // 确保元素获得焦点
            textArea.focus();
            textArea.select();
            textArea.setSelectionRange(0, text.length);

            const successful = document.execCommand('copy');
            return successful;
        } finally {
            document.body.removeChild(textArea);
        }
    }

    // 通过Background Script复制文本
    async copyViaBackground(text) {
        try {
            return new Promise((resolve) => {
                chrome.runtime.sendMessage({
                    action: 'copyToClipboard',
                    text: text
                }, (response) => {
                    resolve(response && response.success);
                });
            });
        } catch (error) {
            console.error('❌ Background Script复制失败:', error);
            return false;
        }
    }

    // 转换相对路径为绝对路径
    convertRelativeToAbsoluteUrls(html) {
        try {
            const baseUrl = window.location.origin;
            const currentPath = window.location.pathname;
            const currentDir = currentPath.substring(0, currentPath.lastIndexOf('/') + 1);

            return html
                // 处理相对路径的src属性
                .replace(/src=["'](?!https?:\/\/|\/\/|data:|javascript:|#)([^"']+)["']/gi, (match, url) => {
                    const absoluteUrl = this.resolveUrl(url, baseUrl, currentDir);
                    return `src="${absoluteUrl}"`;
                })
                // 处理相对路径的href属性
                .replace(/href=["'](?!https?:\/\/|\/\/|mailto:|tel:|javascript:|#)([^"']+)["']/gi, (match, url) => {
                    const absoluteUrl = this.resolveUrl(url, baseUrl, currentDir);
                    return `href="${absoluteUrl}"`;
                })
                // 处理相对路径的action属性
                .replace(/action=["'](?!https?:\/\/|\/\/|javascript:|#)([^"']+)["']/gi, (match, url) => {
                    const absoluteUrl = this.resolveUrl(url, baseUrl, currentDir);
                    return `action="${absoluteUrl}"`;
                })
                // 处理CSS中的url()
                .replace(/url\(["']?(?!https?:\/\/|\/\/|data:)([^"')]+)["']?\)/gi, (match, url) => {
                    const absoluteUrl = this.resolveUrl(url, baseUrl, currentDir);
                    return `url("${absoluteUrl}")`;
                });
        } catch (error) {
            console.warn('转换相对路径失败:', error);
            return html;
        }
    }

    // 解析相对URL为绝对URL
    resolveUrl(url, baseUrl, currentDir) {
        try {
            // 移除查询参数和锚点中的空格
            url = url.trim();

            if (url.startsWith('/')) {
                // 绝对路径（相对于域名）
                return baseUrl + url;
            } else if (url.startsWith('./')) {
                // 相对于当前目录
                return baseUrl + currentDir + url.substring(2);
            } else if (url.startsWith('../')) {
                // 相对于父目录
                let pathParts = currentDir.split('/').filter(part => part);
                const urlParts = url.split('/');

                for (const part of urlParts) {
                    if (part === '..') {
                        pathParts.pop();
                    } else if (part !== '.') {
                        pathParts.push(part);
                    }
                }

                return baseUrl + '/' + pathParts.join('/');
            } else {
                // 相对于当前目录（没有./前缀）
                return baseUrl + currentDir + url;
            }
        } catch (error) {
            console.warn('解析URL失败:', url, error);
            return url;
        }
    }

    // 智能检测最上层显示的内容
    detectTopLayerContent() {
        console.log('🔍 开始检测最上层显示内容...');

        // 1. 检测是否有模态框或弹窗
        const modals = this.detectModals();
        if (modals.length > 0) {
            console.log('🎭 检测到模态框:', modals.length, '个');
            const topModal = modals[0]; // 取z-index最高的
            return {
                type: 'modal',
                html: this.convertRelativeToAbsoluteUrls(topModal.outerHTML),
                info: {
                    selector: this.generateSelector(topModal),
                    zIndex: this.getElementZIndex(topModal),
                    className: topModal.className
                }
            };
        }

        // 2. 检测最上层的iframe
        const topIframe = this.detectTopIframe();
        if (topIframe) {
            console.log('🖼️ 检测到最上层iframe');
            try {
                const iframeDoc = topIframe.contentDocument || topIframe.contentWindow.document;
                const iframeHtml = iframeDoc.documentElement.outerHTML;
                return {
                    type: 'iframe',
                    html: this.convertRelativeToAbsoluteUrls(iframeHtml),
                    info: {
                        src: topIframe.src,
                        selector: this.generateSelector(topIframe),
                        zIndex: this.getElementZIndex(topIframe),
                        dimensions: {
                            width: topIframe.offsetWidth,
                            height: topIframe.offsetHeight
                        }
                    }
                };
            } catch (error) {
                console.warn('❌ 无法访问最上层iframe内容:', error);
            }
        }

        // 3. 检测是否有覆盖层
        const overlay = this.detectOverlay();
        if (overlay) {
            console.log('🎨 检测到覆盖层');
            return {
                type: 'overlay',
                html: this.convertRelativeToAbsoluteUrls(overlay.outerHTML),
                info: {
                    selector: this.generateSelector(overlay),
                    zIndex: this.getElementZIndex(overlay)
                }
            };
        }

        console.log('📄 未检测到特殊层级，使用完整页面');
        return {
            type: 'full-page',
            html: null,
            info: null
        };
    }

    // 检测模态框
    detectModals() {
        const modalSelectors = [
            '.modal.show', '.modal.in', '.modal.fade.show',
            '.dialog', '.popup', '.overlay',
            '[role="dialog"]', '[aria-modal="true"]',
            '.ant-modal', '.el-dialog', '.layui-layer',
            '.ui-dialog', '.bootstrap-modal'
        ];

        const modals = [];
        modalSelectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(el => {
                if (this.isElementVisible(el) && this.isElementOnTop(el)) {
                    modals.push(el);
                }
            });
        });

        // 按z-index排序，最高的在前
        return modals.sort((a, b) => this.getElementZIndex(b) - this.getElementZIndex(a));
    }

    // 检测最上层iframe（当前用户正在查看的iframe）
    detectTopIframe() {
        const iframes = Array.from(document.querySelectorAll('iframe'));

        // 过滤出真正可见且可交互的iframe
        const visibleIframes = iframes.filter(iframe => {
            return this.isIframeCurrentlyVisible(iframe);
        });

        if (visibleIframes.length === 0) {
            console.log('🔍 未找到可见的iframe');
            return null;
        }

        if (visibleIframes.length === 1) {
            console.log('🎯 找到唯一可见iframe');
            return visibleIframes[0];
        }

        // 如果有多个可见iframe，按优先级排序
        const sortedIframes = visibleIframes.sort((a, b) => {
            // 1. 优先选择在视口中心区域的iframe
            const aInCenter = this.isIframeInViewportCenter(a);
            const bInCenter = this.isIframeInViewportCenter(b);
            if (aInCenter !== bInCenter) {
                return bInCenter ? 1 : -1;
            }

            // 2. 优先选择z-index更高的
            const aZIndex = this.getElementZIndex(a);
            const bZIndex = this.getElementZIndex(b);
            if (aZIndex !== bZIndex) {
                return bZIndex - aZIndex;
            }

            // 3. 优先选择面积更大的
            const aArea = a.offsetWidth * a.offsetHeight;
            const bArea = b.offsetWidth * b.offsetHeight;
            if (aArea !== bArea) {
                return bArea - aArea;
            }

            // 4. 优先选择DOM中位置更靠后的（通常是最新显示的）
            return a.compareDocumentPosition(b) & Node.DOCUMENT_POSITION_PRECEDING ? 1 : -1;
        });

        const topIframe = sortedIframes[0];
        console.log(`🎯 选择最上层iframe:`, {
            src: topIframe.src,
            zIndex: this.getElementZIndex(topIframe),
            area: topIframe.offsetWidth * topIframe.offsetHeight,
            inCenter: this.isIframeInViewportCenter(topIframe)
        });

        return topIframe;
    }

    // 检测覆盖层
    detectOverlay() {
        const overlaySelectors = [
            '.overlay', '.mask', '.backdrop',
            '.cover', '.screen', '.layer',
            '[style*="position: fixed"]',
            '[style*="position: absolute"]'
        ];

        const overlays = [];
        overlaySelectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(el => {
                if (this.isElementVisible(el) &&
                    this.isElementOnTop(el) &&
                    el.offsetWidth > window.innerWidth * 0.5 &&
                    el.offsetHeight > window.innerHeight * 0.5) {
                    overlays.push(el);
                }
            });
        });

        return overlays.sort((a, b) => this.getElementZIndex(b) - this.getElementZIndex(a))[0];
    }

    // 获取元素的z-index
    getElementZIndex(element) {
        const style = window.getComputedStyle(element);
        const zIndex = parseInt(style.zIndex) || 0;

        // 如果元素本身没有z-index，检查父元素
        if (zIndex === 0 && element.parentElement) {
            return this.getElementZIndex(element.parentElement);
        }

        return zIndex;
    }

    // 检查元素是否可见
    isElementVisible(element) {
        const style = window.getComputedStyle(element);
        return style.display !== 'none' &&
               style.visibility !== 'hidden' &&
               style.opacity !== '0' &&
               element.offsetWidth > 0 &&
               element.offsetHeight > 0;
    }

    // 检查iframe是否当前真正可见且可交互
    isIframeCurrentlyVisible(iframe) {
        // 基础可见性检查
        if (!this.isElementVisible(iframe)) {
            console.log(`🔍 iframe基础可见性检查失败:`, iframe.src || 'inline');
            return false;
        }

        // 尺寸检查 - 过滤掉太小的iframe
        if (iframe.offsetWidth < 200 || iframe.offsetHeight < 200) {
            console.log(`🔍 iframe尺寸太小: ${iframe.offsetWidth}x${iframe.offsetHeight}`);
            return false;
        }

        // 检查是否在视口内
        const rect = iframe.getBoundingClientRect();
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;

        // iframe必须至少有一部分在视口内
        if (rect.bottom < 0 || rect.top > viewportHeight ||
            rect.right < 0 || rect.left > viewportWidth) {
            console.log(`🔍 iframe不在视口内:`, rect);
            return false;
        }

        // 检查iframe的父容器是否真正可见（重要：检测modal等隐藏容器）
        if (!this.isElementAndParentsVisible(iframe)) {
            console.log(`🔍 iframe的父容器不可见`);
            return false;
        }

        // 检查iframe是否被其他元素完全遮挡
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;

        // 如果iframe中心点在视口内，检查是否被遮挡
        if (centerX >= 0 && centerX <= viewportWidth &&
            centerY >= 0 && centerY <= viewportHeight) {
            const topElement = document.elementFromPoint(centerX, centerY);
            if (topElement && !iframe.contains(topElement) && !topElement.contains(iframe)) {
                // 检查遮挡元素是否是覆盖层
                const style = window.getComputedStyle(topElement);
                if (style.position === 'fixed' || style.position === 'absolute') {
                    console.log(`🔍 iframe被遮挡元素覆盖:`, topElement.tagName, topElement.className);
                    return false;
                }
            }
        }

        console.log(`✅ iframe通过所有可见性检查`);
        return true;
    }

    // 检查元素及其所有父元素是否真正可见（用于检测modal等）
    isElementAndParentsVisible(element) {
        let current = element;
        let level = 0;

        console.log(`🔍 开始检查元素可见性:`, element.textContent?.trim());

        while (current && current !== document.body && level < 20) { // 防止无限循环
            const style = window.getComputedStyle(current);

            console.log(`🔍 检查第${level}层元素:`, {
                tagName: current.tagName,
                className: current.className,
                id: current.id,
                display: style.display,
                visibility: style.visibility,
                opacity: style.opacity
            });

            // 检查基本可见性
            if (style.display === 'none' || style.visibility === 'hidden' || style.opacity === '0') {
                console.log(`❌ 第${level}层元素不可见:`, current.tagName, current.className, {
                    display: style.display,
                    visibility: style.visibility,
                    opacity: style.opacity
                });
                return false;
            }

            // 特别检查modal相关的类名和属性
            if (current.classList) {
                // Bootstrap modal检查 - 更严格的检查
                if (current.classList.contains('modal')) {
                    const hasShow = current.classList.contains('show');
                    const hasIn = current.classList.contains('in'); // Bootstrap 3
                    const ariaHidden = current.getAttribute('aria-hidden');
                    const modalStyle = current.style.display;

                    console.log(`🔍 发现modal元素:`, {
                        className: current.className,
                        hasShow: hasShow,
                        hasIn: hasIn,
                        ariaHidden: ariaHidden,
                        styleDisplay: modalStyle
                    });

                    // 如果是modal但没有显示标识，认为是隐藏的
                    if (!hasShow && !hasIn && ariaHidden !== 'false' && modalStyle !== 'block') {
                        console.log(`❌ Modal未显示，过滤元素`);
                        return false;
                    }
                }

                // 其他常见的隐藏类名
                const hiddenClasses = ['hidden', 'd-none', 'invisible', 'collapse', 'fade'];
                for (const hiddenClass of hiddenClasses) {
                    if (current.classList.contains(hiddenClass)) {
                        // 对于fade类，需要检查是否同时有show类
                        if (hiddenClass === 'fade' && current.classList.contains('show')) {
                            continue; // fade + show 是可见的
                        }
                        console.log(`❌ 发现隐藏类名: ${hiddenClass} 在`, current.className);
                        return false;
                    }
                }
            }

            // 检查aria-hidden属性
            if (current.getAttribute && current.getAttribute('aria-hidden') === 'true') {
                console.log(`❌ 发现aria-hidden=true:`, current.tagName, current.className);
                return false;
            }

            current = current.parentElement;
            level++;
        }

        console.log(`✅ 元素通过所有父容器可见性检查`);
        return true;
    }

    // 检查iframe是否在视口中心区域
    isIframeInViewportCenter(iframe) {
        const rect = iframe.getBoundingClientRect();
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;

        // 计算iframe中心点
        const iframeCenterX = rect.left + rect.width / 2;
        const iframeCenterY = rect.top + rect.height / 2;

        // 计算视口中心区域（中间50%区域）
        const centerAreaLeft = viewportWidth * 0.25;
        const centerAreaRight = viewportWidth * 0.75;
        const centerAreaTop = viewportHeight * 0.25;
        const centerAreaBottom = viewportHeight * 0.75;

        return iframeCenterX >= centerAreaLeft && iframeCenterX <= centerAreaRight &&
               iframeCenterY >= centerAreaTop && iframeCenterY <= centerAreaBottom;
    }

    // 检查元素是否在最上层
    isElementOnTop(element) {
        const rect = element.getBoundingClientRect();
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;

        // 检查元素中心点是否被其他元素遮挡
        const topElement = document.elementFromPoint(centerX, centerY);
        return topElement && (element.contains(topElement) || topElement.contains(element));
    }



    // 等待页面稳定（用于等待弹窗等动态内容加载完成）
    async waitForPageStable(timeout = 3000) {
        return new Promise(resolve => {
            let lastHeight = document.body.scrollHeight;
            let stableCount = 0;
            const requiredStableCount = 5; // 需要连续5次高度不变才认为稳定

            const checkStability = () => {
                const currentHeight = document.body.scrollHeight;
                if (currentHeight === lastHeight) {
                    stableCount++;
                    if (stableCount >= requiredStableCount) {
                        resolve();
                        return;
                    }
                } else {
                    stableCount = 0;
                    lastHeight = currentHeight;
                }

                setTimeout(checkStability, 200);
            };

            checkStability();

            // 超时保护
            setTimeout(() => {
                resolve();
            }, timeout);
        });
    }

    // 通知侧边栏更新进度
    notifyProgress(currentStep, totalSteps, message) {
        try {
            chrome.runtime.sendMessage({
                action: 'updateProgress',
                currentStep: currentStep,
                totalSteps: totalSteps,
                message: message
            });
        } catch (error) {
            console.warn('通知进度更新失败:', error);
        }
    }

    // 执行下一步动作
    async executeNextAction(nextAction, stepDescription, waitTime = 3000) {
        if (!nextAction) {
            console.log(`📝 ${stepDescription}: 无下一步动作，直接继续`);
            return;
        }

        try {
            const action = typeof nextAction === 'string'
                ? JSON.parse(nextAction)
                : nextAction;

            if (action && action.type === 'click' && action.selector) {
                console.log(`🖱️ 执行${stepDescription}动作: 点击 ${action.selector}`);
                console.log(`🔍 动作详情:`, action);

                // 设置全局元素信息，供selectBestElement使用
                if (action.element) {
                    window.currentActionElement = {
                        text: action.element.text || '',
                        tagName: action.element.tagName || 'button',
                        className: action.element.className || ''
                    };
                    console.log(`🔍 设置全局动作元素信息:`, window.currentActionElement);
                }

                // 使用增强的findElement方法（支持iframe和元素信息）
                let actionElement = this.findElement(action.selector, action.element);

                // 如果主选择器没找到，尝试智能匹配
                if (!actionElement && action.element) {
                    console.log(`🔍 主选择器未找到，尝试智能匹配...`);

                    // 构造元素信息用于智能匹配
                    const elementInfo = {
                        tagName: action.element.tagName || 'button',
                        text: action.element.text || '',
                        attributes: {
                            class: action.element.className || ''
                        }
                    };

                    actionElement = this.findElementByIntelligentMatching(action.selector, elementInfo);
                }

                // 如果智能匹配也失败，尝试备用选择器
                if (!actionElement && action.element) {
                    console.log(`🔄 主选择器未找到，尝试备用选择器...`);

                    // 尝试根据文本内容查找（使用优先级策略）
                    if (action.element.text) {
                        actionElement = this.findButtonByTextWithPriority(action.element.text);
                    }

                    // 尝试根据类名查找
                    if (!actionElement && action.element.className) {
                        const classNames = action.element.className.split(' ').filter(cls =>
                            cls && !cls.includes('smart-form') && !cls.includes('action-mode') && !cls.includes('clickable')
                        );
                        if (classNames.length > 0) {
                            const classSelector = `button.${classNames.join('.')}`;
                            console.log(`🔍 尝试类名选择器: ${classSelector}`);
                            actionElement = this.findElement(classSelector);
                        }
                    }
                }

                if (actionElement) {
                    // 详细记录找到的按钮信息
                    console.log(`🎯 找到目标按钮，详细信息:`, {
                        tagName: actionElement.tagName,
                        text: actionElement.textContent?.trim(),
                        innerHTML: actionElement.innerHTML,
                        className: actionElement.className,
                        id: actionElement.id,
                        attributes: this.getAllAttributes(actionElement),
                        outerHTML: actionElement.outerHTML.substring(0, 200) + '...'
                    });

                    actionElement.click();
                    console.log(`✅ 已点击按钮，等待页面响应...`);

                    // 等待页面稳定
                    await this.waitForPageStable(waitTime);
                    console.log(`✅ 页面已稳定，继续下一步`);
                } else {
                    console.error(`❌ 未找到动作元素: ${action.selector}`);
                    console.error(`❌ 动作详情:`, action);
                    console.error(`❌ ${stepDescription}: 关键按钮未找到，停止执行多步骤流程`);

                    // 提供调试信息
                    console.log('🔍 当前页面所有按钮:');
                    const allButtons = document.querySelectorAll('button');
                    allButtons.forEach((btn, index) => {
                        console.log(`  按钮${index + 1}:`, {
                            text: btn.textContent.trim(),
                            className: btn.className,
                            id: btn.id,
                            tagName: btn.tagName
                        });
                    });

                    // 检查iframe中的按钮（按优先级显示）
                    const topIframe = this.detectTopIframe();
                    const iframes = document.querySelectorAll('iframe');

                    if (topIframe) {
                        console.log('🎯 最上层iframe中的按钮:');
                        try {
                            const iframeDoc = topIframe.contentDocument || topIframe.contentWindow.document;
                            const iframeButtons = iframeDoc.querySelectorAll('button');
                            iframeButtons.forEach((btn, btnIndex) => {
                                console.log(`  最上层iframe-按钮${btnIndex + 1}:`, {
                                    text: btn.textContent.trim(),
                                    className: btn.className,
                                    id: btn.id,
                                    visible: this.isElementVisible(btn)
                                });
                            });
                        } catch (error) {
                            console.warn('无法访问最上层iframe:', error);
                        }
                    }

                    if (iframes.length > 1) {
                        console.log('📋 其他iframe中的按钮:');
                        iframes.forEach((iframe, iframeIndex) => {
                            if (iframe === topIframe) return; // 跳过已显示的最上层iframe

                            try {
                                const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                                const iframeButtons = iframeDoc.querySelectorAll('button');
                                iframeButtons.forEach((btn, btnIndex) => {
                                    console.log(`  iframe${iframeIndex + 1}-按钮${btnIndex + 1}:`, {
                                        text: btn.textContent.trim(),
                                        className: btn.className,
                                        id: btn.id,
                                        visible: this.isElementVisible(btn)
                                    });
                                });
                            } catch (error) {
                                console.warn(`无法访问iframe${iframeIndex + 1}:`, error);
                            }
                        });
                    }

                    // 抛出错误，停止执行后续步骤
                    throw new Error(`${stepDescription}失败：未找到按钮 "${action.selector}"，无法继续执行后续步骤`);
                }
            } else {
                console.log(`📝 ${stepDescription}: 动作配置无效或不是点击类型`);
            }
        } catch (error) {
            console.error(`❌ 执行${stepDescription}动作失败:`, error);
            console.log(`📝 ${stepDescription}: 动作执行失败，继续下一步`);
        } finally {
            // 清理全局元素信息
            if (window.currentActionElement) {
                delete window.currentActionElement;
            }
        }
    }
}

// 防止重复注入
if (!window.intelligentMatrixContentLoaded) {
    window.intelligentMatrixContentLoaded = true;
    try {
        window.intelligentMatrixContentScript = new IntelligentMatrixContentScript();
        console.log('✅ 智能体矩阵 Content Script 已加载');
    } catch (error) {
        console.error('❌ 智能体矩阵 Content Script 加载失败:', error);
        console.error('❌ 错误堆栈:', error.stack);

        // 创建一个最小的备用处理器
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            console.log('🔧 备用消息处理器收到消息:', message.action);
            if (message.action === 'ping') {
                sendResponse({ success: true, message: 'Fallback handler active' });
                return true;
            }
            sendResponse({ success: false, message: 'Content script error: ' + error.message });
            return true;
        });
    }
}
