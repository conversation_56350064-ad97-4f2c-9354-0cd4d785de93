<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能体矩阵助手</title>
    <link rel="stylesheet" href="sidepanel.css">
</head>
<body>
    <!-- 加载遮罩层 -->
    <div id="loadingOverlay" class="loading-overlay" style="display: none;">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <div id="loadingText" class="loading-text">正在处理...</div>
            <div id="loadingProgress" class="loading-progress" style="display: none;">
                <div class="progress-bar">
                    <div id="progressFill" class="progress-fill"></div>
                </div>
                <div id="progressText" class="progress-text">第1步 / 共1步</div>
            </div>
        </div>
    </div>

    <div class="sidepanel-container">
<!--        &lt;!&ndash; 头部 &ndash;&gt;-->
<!--        <div class="header">-->
<!--            <div class="logo">-->
<!--                <img src="icons/icon32.png" alt="智能体矩阵">-->
<!--                <span>智能体矩阵助手</span>-->
<!--            </div>-->
<!--        </div>-->

        <!-- 状态指示器 -->
        <div id="loginStatus" class="status-indicator">
            <div class="status-dot error"></div>
            <span class="status-text">未登录，请先登录</span>
        </div>

        <!-- 主要内容区域 -->
        <div class="main-content">

        <!-- 登录表单 -->
        <div id="loginForm" class="feature-section">
            <h4 class="feature-title">邮箱验证码登录</h4>
            <div class="form-group">
                <label for="email">邮箱地址:</label>
                <input type="email" id="email" placeholder="请输入邮箱地址">
            </div>
            <div class="form-group">
                <label for="captchaCode">图形验证码:</label>
                <div class="captcha-container">
                    <input type="text" id="captchaCode" placeholder="请输入验证码" style="flex: 1;">
                    <img id="captchaImg" class="captcha-img" src="" alt="验证码" style="width: 80px; height: 32px; margin-left: 8px; cursor: pointer;">
                    <button id="refreshCaptchaBtn" class="refresh-captcha-btn" type="button">刷新</button>
                </div>
            </div>
            <div class="form-group">
                <button id="sendCodeBtn" class="secondary-button" style="width: 100%; margin-bottom: 8px;">发送验证码</button>
            </div>
            <div class="form-group">
                <label for="verificationCode">邮箱验证码:</label>
                <input type="text" id="verificationCode" placeholder="请输入邮箱验证码">
            </div>
            <button id="loginBtn" class="primary-button">登录</button>
        </div>

        <!-- 主功能区 -->
        <div id="mainPanel" class="hidden">
            <!-- Agent选择 -->
            <div class="feature-section">
                <h4 class="feature-title">Agent管理</h4>

                <!-- 当前选择的Agent显示 -->
                <div id="currentAgentDisplay" class="current-agent-display" style="display: none;">
                    <div class="current-agent-info">
                        <span class="current-agent-label">当前Agent:</span>
                        <span id="currentAgentName" class="current-agent-name">未选择</span>
                    </div>
                    <div class="agent-actions">
                        <button id="changeAgentBtn" class="change-agent-btn">更换</button>
                    </div>
                </div>

                <!-- Agent选择区域 -->
                <div id="agentSelectionArea" class="agent-selection-area">
                    <button id="loadAgentsBtn" class="secondary-button">加载Agent列表</button>
                    <div id="agentSearchContainer" class="agent-search-container" style="display: none;">
                        <input type="text" id="agentSearchInput" class="agent-search-input" placeholder="🔍 搜索Agent...">
                    </div>
                    <div id="agentList" class="agent-list"></div>
                </div>
            </div>

            <!-- 文档识别 -->
            <div class="feature-section">
                <h4 class="feature-title">文档识别</h4>

                <!-- 当前识别状态显示 -->
                <div id="recognitionStatus" class="recognition-status" style="display: none;">
                    <!-- 保留的操作按钮区域 -->
                    <div id="activeRecognitionButton" class="active-recognition-button" style="display: none;">
                        <!-- 动态插入当前使用的按钮 -->
                    </div>

                    <div class="recognition-info">
                        <div class="recognition-main">
                            <span class="recognition-label">识别状态:</span>
                            <span id="recognitionStatusText" class="recognition-text">未识别</span>
                        </div>
                        <div id="recognitionTiming" class="recognition-timing" style="display: none;">
                            <span id="recognitionTimingText" class="timing-text"></span>
                        </div>
                    </div>
                    <div class="recognition-actions">
                        <button id="viewResultBtn" class="view-result-btn">查看结果</button>
                        <button id="reRecognizeBtn" class="re-recognize-btn">重新选择</button>
                    </div>
                </div>

                <!-- 识别操作区域 -->
                <div id="recognitionActions" class="recognition-actions-area">
                    <button id="captureBtn" class="primary-button">📷 截图识别</button>
                    <button id="uploadBtn" class="primary-button">📁 上传文件识别</button>
                    <button id="uploadJsonBtn" class="secondary-button">📝 上传JSON数据</button>
                    <input type="file" id="fileInput" accept="image/*,.pdf" style="display: none;">
                </div>
            </div>

            <!-- 自动回填 -->
            <div class="feature-section">
                <h4 class="feature-title">自动回填</h4>

                <!-- 当前绑定状态显示 -->
                <div id="bindingStatus" class="binding-status" style="display: none;">
                    <div class="binding-info">
                        <span class="binding-label">页面绑定:</span>
                        <span id="bindingStatusText" class="binding-text">未检测</span>
                    </div>
                    <div class="binding-actions">
                        <button id="changeBindingBtn" class="change-binding-btn">更换</button>
                    </div>
                </div>

                <!-- 回填操作区域 -->
                <div id="fillActions" class="fill-actions-area">
                    <button id="checkBindingBtn" class="secondary-button">🔍 选择目标页面</button>

                    <!-- 绑定列表显示 -->
                    <div id="bindingListContainer" class="binding-list-container" style="display: none;">
                        <div id="bindingList" class="binding-list"></div>
                    </div>

                    <button id="fillFormBtn" class="primary-button">✏️ 自动回填表单</button>
                </div>

                <div id="fillResult" class="result-area" style="display: none;"></div>
            </div>

            <!-- 工具功能 -->
            <div class="feature-section">
                <h4 class="feature-title">工具</h4>
                <button id="detectFormBtn" class="secondary-button">检测页面表单</button>
                <button id="extractDataBtn" class="secondary-button">提取表单数据</button>
                <button id="htmlExtractBtn" class="secondary-button">📋 复制页面HTML</button>
                <div id="detectResult" class="result-area" style="display: none;"></div>
                <div id="extractResult" class="result-area" style="display: none;"></div>
            </div>

            <!-- 设置区域 -->
            <div class="feature-section">
                <h4 class="feature-title">设置</h4>
                <button id="clearStorageBtn" class="secondary-button">清除存储数据</button>
                <button id="logoutBtn" class="secondary-button">退出登录</button>
            </div>
        </div>

        <!-- JSON上传模态框 -->
        <div id="jsonModal" class="themed-result-modal" style="display: none;">
            <div class="themed-modal-dialog">
                <div class="themed-modal-content">
                    <div class="themed-modal-header">
                        <h5 class="themed-modal-title">上传JSON数据</h5>
                        <button type="button" class="themed-close-btn" id="closeJsonModal">×</button>
                    </div>
                    <div class="themed-modal-body">
                        <textarea id="jsonInput" class="themed-textarea" placeholder="请粘贴JSON数据..." rows="10"></textarea>
                    </div>
                    <div class="themed-modal-footer">
                        <button id="cancelJsonBtn" class="themed-secondary-btn">取消</button>
                        <button id="submitJsonBtn" class="themed-primary-btn">提交</button>
                    </div>
                </div>
            </div>
        </div>

        </div> <!-- 结束 main-content -->

        <!-- 主题切换器 -->
        <div id="themeSwitcher" class="theme-switcher collapsed">
            <div class="theme-switcher-header">
                <h6 class="theme-switcher-title">主题风格</h6>
                <button id="themeSwitcherToggle" class="theme-switcher-toggle" title="展开/收起主题选择">
                    <span>🎨</span>
                </button>
            </div>
            <div class="theme-options">
                <div class="theme-option active" data-theme="default" title="深色主题（默认）"></div>
                <div class="theme-option" data-theme="light" title="浅色主题"></div>
                <div class="theme-option" data-theme="tech-blue" title="科技蓝主题"></div>
            </div>
        </div>

        <!-- 状态消息 -->
        <div id="statusMessage" class="status-message" style="display: none;"></div>
    </div>

    <script src="sidepanel.js"></script>
</body>
</html>
