# 图标文件说明

这个目录包含Chrome插件所需的图标文件。

## 图标规格

- icon16.png - 16x16像素，用于扩展管理页面
- icon32.png - 32x32像素，用于Windows系统
- icon48.png - 48x48像素，用于扩展管理页面
- icon128.png - 128x128像素，用于Chrome Web Store

## 图标设计要求

1. **清晰度**：在各种尺寸下都要保持清晰
2. **识别性**：能够清楚表达智能体矩阵的功能
3. **一致性**：各尺寸图标保持设计一致性
4. **格式**：PNG格式，支持透明背景

## 当前状态

目前使用占位符图标，建议替换为专业设计的图标。

## 生成图标

可以使用以下工具生成图标：
- Adobe Illustrator
- Figma
- Canva
- 在线图标生成器

## 图标主题

建议图标包含以下元素：
- 机器人或AI相关图标
- 文档识别相关元素
- 蓝色渐变配色方案
- 现代简约风格
