// SinoairAgent Chrome插件 - 后台脚本 (独立生命周期版本)

console.log('🚀 SinoairAgent Background Script 启动');

// 插件安装时
chrome.runtime.onInstalled.addListener((details) => {
    console.log('✅ SinoairAgent插件已安装', details);
});

// 监听扩展图标点击 - 打开Side Panel
chrome.action.onClicked.addListener(async (tab) => {
    console.log('🎯 扩展图标被点击，打开Side Panel，标签页:', tab.id);

    try {
        // 打开Side Panel
        await chrome.sidePanel.open({ tabId: tab.id });
        console.log('✅ Side Panel已打开');
    } catch (error) {
        console.error('❌ 打开Side Panel失败:', error);
        // 显示通知
        try {
            chrome.notifications.create({
                type: 'basic',
                iconUrl: 'icons/icon48.png',
                title: 'SinoairAgent',
                message: '打开侧边栏失败，请重试'
            });
        } catch (notificationError) {
            console.error('❌ 显示通知失败:', notificationError);
        }
    }
});

// 处理来自content script和side panel的消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    console.log('📨 Background收到消息:', message.action, message);

    if (message.action === 'captureScreen') {
        // 处理截图请求
        handleCaptureScreen(message.options)
            .then(result => sendResponse(result))
            .catch(error => sendResponse({ success: false, error: error.message }));
        return true; // 保持消息通道开放
    }

    if (message.action === 'copyToClipboard') {
        // 处理剪贴板复制请求
        handleCopyToClipboard(message.text)
            .then(result => sendResponse(result))
            .catch(error => sendResponse({ success: false, error: error.message }));
        return true; // 保持消息通道开放
    }

    if (message.action === 'performPageBindingCheck') {
        // 在 Background 中直接处理页面绑定检查
        console.log('🔄 Background处理页面绑定检查:', message);
        handlePageBindingCheck(message.currentUrl, message.agent)
            .then(result => sendResponse(result))
            .catch(error => sendResponse({ success: false, message: error.message }));
        return true; // 保持消息通道开放
    }

    // 其他消息直接返回成功
    sendResponse({ success: true });
});

// 处理截图
async function handleCaptureScreen(options = {}) {
    try {
        console.log('📷 开始截图...');
        
        // 获取当前活动标签页
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
        
        // 截取可见区域
        const dataUrl = await chrome.tabs.captureVisibleTab(tab.windowId, {
            format: options.format || 'png',
            quality: options.quality || 90
        });
        
        console.log('✅ 截图完成');
        
        return {
            success: true,
            dataUrl: dataUrl,
            tabInfo: {
                url: tab.url,
                title: tab.title
            }
        };
    } catch (error) {
        console.error('❌ 截图失败:', error);
        throw error;
    }
}


// 处理页面绑定检查
async function handlePageBindingCheck(currentUrl, agent) {
    try {
        console.log('🔍 Background处理页面绑定检查...');
        console.log('🌐 目标URL:', currentUrl);
        console.log('🤖 使用Agent:', agent?.agentName);

        if (!agent || !agent.id) {
            throw new Error('Agent信息缺失，请先选择Agent');
        }

        // 获取存储的认证信息
        const result = await chrome.storage.local.get(['serverUrl', 'authToken']);
        const serverUrl = result.serverUrl || 'http://localhost:8080';
        const authToken = result.authToken || '';

        if (!authToken) {
            throw new Error('未登录，请先登录');
        }

        console.log('🔗 发起API请求:', `${serverUrl}/api/v1/page-bindings/auto-fill-data`);

        // 直接在 Background 中调用 API
        const response = await fetch(`${serverUrl}/api/v1/page-bindings/auto-fill-data?url=${encodeURIComponent(currentUrl)}&agentId=${agent.id}`, {
            headers: {
                'Authorization': `Bearer ${authToken}`,
                'Content-Type': 'application/json'
            }
        });

        if (response.status === 401) {
            throw new Error('登录已过期，请重新登录');
        }

        const apiResult = await response.json();
        console.log('📋 Background页面绑定API调用结果:', apiResult);

        return apiResult;
    } catch (error) {
        console.error('❌ Background页面绑定检查失败:', error);
        throw error;
    }
}

// 处理剪贴板复制
async function handleCopyToClipboard(text) {
    try {
        console.log('📋 Background Script开始复制到剪贴板...');

        // 在Service Worker中，我们需要通过offscreen document来访问剪贴板
        // 但是对于简单的文本复制，我们可以尝试直接使用
        if (typeof navigator !== 'undefined' && navigator.clipboard) {
            await navigator.clipboard.writeText(text);
            console.log('✅ Background Script复制成功');
            return { success: true };
        }

        // 如果直接复制失败，尝试通过注入脚本的方式
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

        const result = await chrome.scripting.executeScript({
            target: { tabId: tab.id },
            func: (textToCopy) => {
                try {
                    // 创建临时元素
                    const textArea = document.createElement('textarea');
                    textArea.value = textToCopy;
                    textArea.style.position = 'fixed';
                    textArea.style.left = '-999999px';
                    textArea.style.top = '-999999px';
                    textArea.style.opacity = '0';
                    document.body.appendChild(textArea);

                    textArea.focus();
                    textArea.select();
                    textArea.setSelectionRange(0, textToCopy.length);

                    const successful = document.execCommand('copy');
                    document.body.removeChild(textArea);

                    return { success: successful };
                } catch (error) {
                    return { success: false, error: error.message };
                }
            },
            args: [text]
        });

        const copyResult = result[0].result;
        if (copyResult.success) {
            console.log('✅ Background Script通过注入脚本复制成功');
            return { success: true };
        } else {
            throw new Error(copyResult.error || '注入脚本复制失败');
        }

    } catch (error) {
        console.error('❌ Background Script复制失败:', error);
        return { success: false, error: error.message };
    }
}

console.log('✅ Background Script 初始化完成');
