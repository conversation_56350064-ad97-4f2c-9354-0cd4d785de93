<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能表单配置调试工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .debug-container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .debug-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .debug-section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .result.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .result.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .result.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        textarea {
            width: 100%;
            height: 200px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
        #previewContent {
            width: 100%;
            height: 400px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: #f8f9fa;
            position: relative;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-indicator.success { background: #28a745; }
        .status-indicator.error { background: #dc3545; }
        .status-indicator.warning { background: #ffc107; }
        .status-indicator.info { background: #17a2b8; }
    </style>
</head>
<body>
    <div class="debug-container">
        <h2>智能表单配置调试工具</h2>
        
        <!-- 状态检查 -->
        <div class="debug-section">
            <h3>1. 系统状态检查</h3>
            <button onclick="checkSystemStatus()">检查系统状态</button>
            <button onclick="initializeManager()">初始化管理器</button>
            <div id="statusResult" class="result" style="display: none;"></div>
        </div>

        <!-- HTML粘贴测试 -->
        <div class="debug-section">
            <h3>2. HTML粘贴测试</h3>
            <textarea id="testHtml" placeholder="粘贴HTML内容或Chrome插件复制的JSON数据..."></textarea>
            <br><br>
            <button onclick="testHtmlPaste()">测试HTML渲染</button>
            <button onclick="clearPreview()">清空预览</button>
            <div id="pasteResult" class="result" style="display: none;"></div>
        </div>

        <!-- 预览区域 -->
        <div class="debug-section">
            <h3>3. 预览区域</h3>
            <div id="previewContent"></div>
        </div>

        <!-- 日志输出 -->
        <div class="debug-section">
            <h3>4. 实时日志</h3>
            <button onclick="clearLogs()">清空日志</button>
            <button onclick="toggleLogCapture()">开始/停止日志捕获</button>
            <div id="logOutput" class="result info" style="display: block; height: 200px;"></div>
        </div>
    </div>

    <script>
        let originalConsoleLog = console.log;
        let originalConsoleError = console.error;
        let originalConsoleWarn = console.warn;
        let logCapture = true;
        let smartFormManager = null;

        // 劫持console输出
        function setupLogCapture() {
            const logOutput = document.getElementById('logOutput');
            
            console.log = function(...args) {
                originalConsoleLog.apply(console, args);
                if (logCapture) {
                    appendLog('LOG', args.join(' '));
                }
            };

            console.error = function(...args) {
                originalConsoleError.apply(console, args);
                if (logCapture) {
                    appendLog('ERROR', args.join(' '));
                }
            };

            console.warn = function(...args) {
                originalConsoleWarn.apply(console, args);
                if (logCapture) {
                    appendLog('WARN', args.join(' '));
                }
            };
        }

        function appendLog(level, message) {
            const logOutput = document.getElementById('logOutput');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${level}: ${message}\n`;
            logOutput.textContent += logEntry;
            logOutput.scrollTop = logOutput.scrollHeight;
        }

        function clearLogs() {
            document.getElementById('logOutput').textContent = '';
        }

        function toggleLogCapture() {
            logCapture = !logCapture;
            showResult('statusResult', 'info', `日志捕获已${logCapture ? '开启' : '关闭'}`);
        }

        // 检查系统状态
        function checkSystemStatus() {
            let status = '';
            
            // 检查DOM元素
            const previewContent = document.getElementById('previewContent');
            status += `previewContent元素: ${getStatusIcon(!!previewContent)} ${!!previewContent}\n`;
            
            // 检查全局变量
            status += `window.smartFormManager: ${getStatusIcon(!!window.smartFormManager)} ${!!window.smartFormManager}\n`;
            status += `window.smartFormConfigManager: ${getStatusIcon(!!window.smartFormConfigManager)} ${!!window.smartFormConfigManager}\n`;
            status += `window.SmartFormConfigManager: ${getStatusIcon(!!window.SmartFormConfigManager)} ${!!window.SmartFormConfigManager}\n`;
            
            // 检查本地变量
            status += `本地smartFormManager: ${getStatusIcon(!!smartFormManager)} ${!!smartFormManager}\n`;
            
            // 检查剪贴板API
            const hasClipboard = navigator.clipboard && navigator.clipboard.readText;
            status += `剪贴板API: ${getStatusIcon(hasClipboard)} ${hasClipboard}\n`;
            status += `安全上下文: ${getStatusIcon(window.isSecureContext)} ${window.isSecureContext}\n`;
            
            showResult('statusResult', 'info', status);
        }

        function getStatusIcon(condition) {
            return condition ? '✅' : '❌';
        }

        // 初始化管理器
        function initializeManager() {
            try {
                // 尝试创建SmartFormConfigManager实例
                if (window.SmartFormConfigManager) {
                    smartFormManager = new window.SmartFormConfigManager();
                    window.smartFormManager = smartFormManager;
                    showResult('statusResult', 'success', '✅ SmartFormConfigManager实例创建成功');
                } else {
                    showResult('statusResult', 'error', '❌ SmartFormConfigManager类不存在，请确保已加载smart-form-config.js');
                }
            } catch (error) {
                showResult('statusResult', 'error', `❌ 初始化失败: ${error.message}\n${error.stack}`);
            }
        }

        // 测试HTML粘贴
        function testHtmlPaste() {
            const htmlContent = document.getElementById('testHtml').value.trim();
            
            if (!htmlContent) {
                showResult('pasteResult', 'error', '请输入HTML内容');
                return;
            }

            if (!smartFormManager) {
                // 尝试使用全局变量
                smartFormManager = window.smartFormManager || window.smartFormConfigManager;
                if (!smartFormManager) {
                    showResult('pasteResult', 'error', '管理器未初始化，请先点击"初始化管理器"');
                    return;
                }
            }

            try {
                showResult('pasteResult', 'info', '开始测试HTML渲染...');
                
                // 尝试解析为JSON（Chrome插件格式）
                try {
                    const data = JSON.parse(htmlContent);
                    if (data.html && data.url) {
                        console.log('检测到Chrome插件JSON格式');
                        smartFormManager.renderPageContent(data.html, data.url);
                        showResult('pasteResult', 'success', `✅ JSON格式HTML渲染成功\n内容类型: ${data.contentType || 'unknown'}\nURL: ${data.url}`);
                        return;
                    }
                } catch (e) {
                    // 不是JSON格式，继续作为HTML处理
                }

                // 作为纯HTML处理
                if (htmlContent.includes('<html') || htmlContent.includes('<body') || htmlContent.includes('<div')) {
                    smartFormManager.renderPageContent(htmlContent);
                    showResult('pasteResult', 'success', '✅ HTML格式内容渲染成功');
                } else {
                    showResult('pasteResult', 'warning', '⚠️ 内容不像是有效的HTML');
                }

            } catch (error) {
                showResult('pasteResult', 'error', `❌ 渲染失败: ${error.message}\n${error.stack}`);
            }
        }

        // 清空预览
        function clearPreview() {
            const previewContent = document.getElementById('previewContent');
            if (previewContent) {
                previewContent.innerHTML = '';
                showResult('pasteResult', 'info', '预览区域已清空');
            }
        }

        // 显示结果
        function showResult(elementId, type, message) {
            const element = document.getElementById(elementId);
            element.className = `result ${type}`;
            element.textContent = message;
            element.style.display = 'block';
        }

        // 页面加载时初始化
        window.onload = function() {
            setupLogCapture();
            console.log('调试工具已加载');
            
            // 尝试加载smart-form-config.js
            if (!window.SmartFormConfigManager) {
                console.warn('SmartFormConfigManager未找到，尝试动态加载...');
                const script = document.createElement('script');
                script.src = '/assets/js/smart-form-config.js';
                script.onload = () => {
                    console.log('smart-form-config.js加载成功');
                    checkSystemStatus();
                };
                script.onerror = () => {
                    console.error('smart-form-config.js加载失败');
                };
                document.head.appendChild(script);
            } else {
                checkSystemStatus();
            }
        };
    </script>
</body>
</html>
