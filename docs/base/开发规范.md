# 智能体矩阵 开发规范文档

## 1. 项目概述

### 1.1 项目介绍
智能体矩阵是一个智能文档识别与自动填写平台，包含管理端和浏览器端两个核心组件：
- **管理端**：基于Spring Boot的后端服务，提供Agent管理、用户管理、文档识别等功能
- **浏览器端**：Chrome插件，支持网页截图识别、文件上传识别、表单自动填写等功能

### 1.2 核心功能
- 智能文档识别（支持图片、PDF等格式）
- 多种LLM集成（千问、OpenAI、DeepSeek等）
- 表单自动填写和页面绑定
- 用户权限管理和角色控制
- Agent版本管理和优化记录

## 2. 技术架构

### 2.1 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Chrome插件     │───▶│   Spring Boot   │───▶│   外部LLM服务    │
│   (前端界面)     │    │   (后端API)     │    │  (AI识别服务)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       ▼                       │
         │              ┌─────────────────┐              │
         │              │   MySQL数据库    │              │
         │              │   (数据存储)     │              │
         │              └─────────────────┘              │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   本地存储       │    │   Redis缓存     │    │   MinIO文件存储  │
│   (插件配置)     │    │   (会话管理)     │    │   (文件管理)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 2.2 技术栈

#### 后端技术栈
- **框架**: Spring Boot 3.2.1
- **Java版本**: JDK 17
- **数据库**: MySQL 8.0
- **ORM**: MyBatis-Plus 3.5.12
- **缓存**: Redis
- **连接池**: Druid 1.2.20
- **安全**: Spring Security + JWT
- **文档**: Knife4j 4.4.0 (Swagger)
- **工具库**: Hutool 5.8.24
- **HTTP客户端**: Apache HttpClient5 + WebFlux
- **文件存储**: MinIO 8.5.7

#### 前端技术栈
- **插件框架**: Chrome Extension Manifest V3
- **脚本语言**: JavaScript (ES6+)
- **样式**: CSS3
- **构建工具**: 原生开发（无构建工具）

#### 开发工具
- **构建工具**: Maven 3.x
- **IDE**: IntelliJ IDEA / VS Code
- **版本控制**: Git
- **API测试**: Postman / Knife4j

## 3. 项目结构

### 3.1 后端项目结构
```
src/main/java/com/sinoair/agent/
├── SinoairAgentApplication.java          # 主启动类
├── common/                               # 通用组件
│   ├── PageResult.java                   # 分页结果
│   ├── Result.java                       # 统一响应结果
│   └── ResultCode.java                   # 响应状态码
├── config/                               # 配置类
│   ├── DatabaseConfig.java              # 数据库配置
│   ├── SecurityConfig.java              # 安全配置
│   ├── SwaggerConfig.java               # API文档配置
│   ├── RedisConfig.java                 # Redis配置
│   └── WebConfig.java                   # Web配置
├── controller/                           # 控制器层
│   ├── AuthController.java              # 认证控制器
│   ├── AgentController.java             # Agent管理
│   ├── UserController.java              # 用户管理
│   ├── FileController.java              # 文件管理
│   └── RecognitionController.java       # 识别服务
├── dto/                                  # 数据传输对象
│   ├── request/                          # 请求DTO
│   └── response/                         # 响应DTO
├── entity/                               # 实体类
│   ├── BaseEntity.java                  # 基础实体
│   ├── User.java                        # 用户实体
│   ├── Agent.java                       # Agent实体
│   └── ...                              # 其他实体
├── mapper/                               # MyBatis映射器
├── service/                              # 服务层
│   ├── impl/                            # 服务实现
│   └── llm/                             # LLM服务
├── security/                             # 安全组件
│   ├── JwtTokenProvider.java            # JWT工具
│   └── UserPrincipal.java               # 用户主体
└── exception/                            # 异常处理
    ├── BusinessException.java           # 业务异常
    └── GlobalExceptionHandler.java      # 全局异常处理
```

### 3.2 前端项目结构
```
chrome-extension/
├── manifest.json                         # 插件配置文件
├── background.js                         # 后台脚本
├── content.js                           # 内容脚本
├── popup.html                           # 弹窗页面
├── popup.js                             # 弹窗脚本
├── sidebar.html                         # 侧边栏页面
├── sidebar.js                           # 侧边栏脚本
├── sidebar.css                          # 样式文件
├── icons/                               # 图标文件
│   ├── icon16.png
│   ├── icon32.png
│   ├── icon48.png
│   └── icon128.png
└── 安装指南.md                           # 安装说明
```

### 3.3 资源文件结构
```
src/main/resources/
├── application.yml                       # 主配置文件
├── db/migration/                         # 数据库迁移脚本
│   └── V1__init_database.sql
├── mapper/                               # MyBatis XML映射文件
└── static/                               # 静态资源
```

## 4. 数据库设计规范

### 4.1 表命名规范
- 使用小写字母和下划线
- 系统表以`sys_`开头
- 业务表使用复数形式
- 关联表使用两个表名组合

### 4.2 字段命名规范
- 使用小写字母和下划线
- 主键统一使用`id`
- 创建时间：`created_time`
- 更新时间：`updated_time`
- 删除标记：`deleted`
- 状态字段：`status`

### 4.3 核心表结构

#### 用户管理
- `sys_user` - 用户表
- `sys_role` - 角色表
- `sys_permission` - 权限表
- `sys_user_role` - 用户角色关联表
- `sys_role_permission` - 角色权限关联表

#### 业务核心
- `agents` - Agent主表
- `agent_categories` - Agent分类表
- `agent_versions` - Agent版本表
- `business_types` - 业务类型表
- `business_templates` - 业务模板表

#### 文件和识别
- `uploaded_files` - 上传文件表
- `recognition_records` - 识别记录表
- `page_bindings` - 页面绑定表

## 5. API设计规范

### 5.1 RESTful API规范
- 使用HTTP动词：GET(查询)、POST(创建)、PUT(更新)、DELETE(删除)
- URL使用复数名词：`/api/v1/users`、`/api/v1/agents`
- 版本控制：`/api/v1/`、`/api/v2/`
- 状态码规范：200(成功)、201(创建)、400(参数错误)、401(未授权)、403(禁止)、404(未找到)、500(服务器错误)

### 5.2 统一响应格式
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {},
  "timestamp": "2024-01-01T12:00:00"
}
```

### 5.3 分页响应格式
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "records": [],
    "total": 100,
    "size": 10,
    "current": 1,
    "pages": 10
  }
}
```

## 6. 代码规范

### 6.1 Java代码规范
- 使用阿里巴巴Java开发手册规范
- 类名使用大驼峰命名法
- 方法名和变量名使用小驼峰命名法
- 常量使用全大写加下划线
- 包名使用小写字母

### 6.2 注释规范
- 类和接口必须有JavaDoc注释
- 公共方法必须有注释说明
- 复杂业务逻辑必须有行内注释
- 使用`@author`、`@version`、`@param`、`@return`等标签

### 6.3 异常处理规范
- 使用统一的业务异常类`BusinessException`
- 使用全局异常处理器`GlobalExceptionHandler`
- 不要捕获并忽略异常
- 记录异常日志

## 7. 安全规范

### 7.1 认证授权
- 使用JWT Token进行身份认证
- Token有效期：2小时，刷新Token：7天
- 实现基于角色的权限控制(RBAC)
- 敏感操作需要权限验证

### 7.2 数据安全
- 密码使用BCrypt加密存储
- 敏感信息不记录到日志
- SQL注入防护（使用MyBatis参数化查询）
- XSS防护（输入验证和输出编码）

## 8. 性能规范

### 8.1 数据库优化
- 合理使用索引
- 避免N+1查询问题
- 使用分页查询大数据集
- 定期分析慢查询

### 8.2 缓存策略
- 使用Redis缓存热点数据
- 设置合理的缓存过期时间
- 实现缓存更新策略
- 避免缓存穿透和雪崩

## 9. 测试规范

### 9.1 单元测试
- 使用JUnit 5进行单元测试
- 测试覆盖率不低于70%
- 重要业务逻辑必须有测试用例
- 使用Mock对象隔离依赖

### 9.2 集成测试
- 使用TestContainers进行数据库集成测试
- API接口测试使用MockMvc
- 测试环境使用H2内存数据库

## 10. 部署规范

### 10.1 环境配置
- 开发环境：`application-dev.yml`
- 测试环境：`application-test.yml`
- 生产环境：`application-prod.yml`
- 敏感配置使用环境变量

### 10.2 Docker部署
```dockerfile
FROM openjdk:17-jre-slim
COPY target/sinoair-agent-1.0.0.jar app.jar
EXPOSE 8080
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

## 11. Chrome插件开发规范

### 11.1 插件架构规范
- 使用Manifest V3规范
- 分离关注点：background.js(后台)、content.js(内容)、popup.js(弹窗)
- 使用消息传递机制进行组件间通信
- 遵循Chrome插件安全策略

### 11.2 权限申请规范
```json
{
  "permissions": [
    "activeTab",      // 当前标签页访问
    "storage",        // 本地存储
    "scripting",      // 脚本注入
    "tabs"           // 标签页管理
  ],
  "host_permissions": [
    "http://localhost:8080/*",     // 开发环境
    "https://api.sinoair-agent.com/*"  // 生产环境
  ]
}
```

### 11.3 消息传递规范
```javascript
// 发送消息
chrome.tabs.sendMessage(tabId, {
  action: 'actionName',
  data: payload
}, response => {
  // 处理响应
});

// 接收消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  switch(message.action) {
    case 'actionName':
      handleAction(message.data);
      sendResponse({success: true});
      break;
  }
});
```

### 11.4 存储管理规范
```javascript
// 使用chrome.storage.local存储配置
const config = {
  serverUrl: 'http://localhost:8080',
  authToken: 'jwt-token',
  selectedAgent: agentInfo
};

chrome.storage.local.set(config);
chrome.storage.local.get(['serverUrl', 'authToken'], (result) => {
  // 使用配置
});
```

## 12. LLM集成规范

### 12.1 支持的LLM提供商
- **千问(Qianwen)**: 阿里云通义千问
- **OpenAI**: GPT系列模型
- **DeepSeek**: DeepSeek Chat模型

### 12.2 LLM配置规范
```yaml
app:
  llm:
    default-provider: qianwen
    timeout: 30000
    retry-count: 3
    providers:
      qianwen:
        api-key: ${QIANWEN_API_KEY}
        base-url: https://dashscope.aliyuncs.com/api/v1
        model: qwen-plus
```

### 12.3 LLM服务接口规范
```java
public interface LLMProvider {
    RecognitionResult recognize(Agent agent, RecognitionRequest request);
    boolean isAvailable();
    String getProviderName();
}
```

## 13. 业务流程规范

### 13.1 文档识别流程
1. 用户上传文件或截图
2. 文件存储到MinIO
3. 根据Agent配置选择LLM提供商
4. 调用LLM API进行识别
5. 解析识别结果为JSON格式
6. 存储识别记录到数据库
7. 返回结构化数据给前端

### 13.2 表单填写流程
1. 检查页面绑定配置
2. 获取最新识别结果
3. 匹配表单字段和数据字段
4. 执行自动填写操作
5. 记录填写日志

### 13.3 Agent管理流程
1. 创建Agent基本信息
2. 配置业务类型和模板
3. 设置提示词和输出格式
4. 版本管理和发布
5. 性能监控和优化

## 14. 监控和日志规范

### 14.1 日志级别规范
- **ERROR**: 系统错误、异常情况
- **WARN**: 警告信息、潜在问题
- **INFO**: 重要业务操作、系统状态
- **DEBUG**: 调试信息、详细执行过程

### 14.2 日志格式规范
```
[时间] [级别] [线程] [类名] : 日志内容
2024-01-01 12:00:00.000 INFO [main] c.s.a.service.AgentService : Agent创建成功, id=1
```

### 14.3 监控指标
- API响应时间
- 数据库连接池状态
- Redis连接状态
- LLM调用成功率
- 文件上传成功率

## 15. 版本管理规范

### 15.1 版本号规范
使用语义化版本号：`主版本号.次版本号.修订号`
- 主版本号：不兼容的API修改
- 次版本号：向下兼容的功能性新增
- 修订号：向下兼容的问题修正

### 15.2 Git分支规范
- `main`: 主分支，生产环境代码
- `develop`: 开发分支，集成最新功能
- `feature/*`: 功能分支，开发新功能
- `hotfix/*`: 热修复分支，紧急修复
- `release/*`: 发布分支，准备发布版本

### 15.3 提交信息规范
```
<type>(<scope>): <subject>

<body>

<footer>
```

类型说明：
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

## 16. 开发环境搭建

### 16.1 后端环境要求
- JDK 17+
- Maven 3.6+
- MySQL 8.0+
- Redis 6.0+
- IntelliJ IDEA 2023+

### 16.2 前端环境要求
- Chrome 88+
- VS Code (推荐插件：Chrome Extension、JavaScript)

### 16.3 开发环境配置
1. 克隆项目代码
2. 配置数据库连接
3. 启动Redis服务
4. 运行Spring Boot应用
5. 加载Chrome插件
6. 配置插件服务器地址

## 17. 故障排除指南

### 17.1 常见后端问题
- **数据库连接失败**: 检查MySQL服务状态和连接配置
- **Redis连接失败**: 检查Redis服务状态和密码配置
- **JWT Token过期**: 检查Token有效期配置
- **LLM调用失败**: 检查API Key和网络连接

### 17.2 常见前端问题
- **插件加载失败**: 检查manifest.json配置和图标文件
- **服务器连接失败**: 检查服务器地址和CORS配置
- **权限不足**: 检查插件权限申请
- **消息传递失败**: 检查content script注入状态

### 17.3 调试技巧
- 使用Chrome DevTools调试插件
- 查看background页面控制台
- 使用Knife4j测试API接口
- 查看应用日志文件

## 18. 代码示例和最佳实践

### 18.1 Controller层示例
```java
@RestController
@RequestMapping("/api/v1/agents")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Agent管理", description = "Agent相关接口")
public class AgentController {

    private final AgentService agentService;

    @GetMapping
    @Operation(summary = "获取Agent列表")
    @PreAuthorize("hasPermission('agent', 'read')")
    public Result<PageResult<AgentVO>> getAgents(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size) {

        PageResult<AgentVO> result = agentService.getAgents(current, size);
        return Result.success(result);
    }

    @PostMapping
    @Operation(summary = "创建Agent")
    @PreAuthorize("hasPermission('agent', 'create')")
    public Result<AgentVO> createAgent(@Valid @RequestBody CreateAgentRequest request) {
        AgentVO agent = agentService.createAgent(request);
        return Result.success(agent);
    }
}
```

### 18.2 Service层示例
```java
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class AgentServiceImpl implements AgentService {

    private final AgentMapper agentMapper;
    private final RedisTemplate<String, Object> redisTemplate;

    @Override
    public PageResult<AgentVO> getAgents(Integer current, Integer size) {
        // 构建分页查询
        Page<Agent> page = new Page<>(current, size);

        // 查询数据
        Page<Agent> agentPage = agentMapper.selectPage(page,
            Wrappers.<Agent>lambdaQuery()
                .eq(Agent::getDeleted, 0)
                .orderByDesc(Agent::getCreatedTime));

        // 转换VO
        List<AgentVO> agentVOs = agentPage.getRecords().stream()
            .map(this::convertToVO)
            .collect(Collectors.toList());

        return PageResult.of(agentVOs, agentPage.getTotal(), current, size);
    }

    @Override
    @Cacheable(value = "agent", key = "#id")
    public AgentVO getAgentById(Long id) {
        Agent agent = agentMapper.selectById(id);
        if (agent == null || agent.getDeleted() == 1) {
            throw new BusinessException(ResultCode.AGENT_NOT_FOUND);
        }
        return convertToVO(agent);
    }
}
```

### 18.3 异常处理示例
```java
@RestControllerAdvice
@Slf4j
public class GlobalExceptionHandler {

    @ExceptionHandler(BusinessException.class)
    public Result<Void> handleBusinessException(BusinessException e) {
        log.warn("业务异常: {}", e.getMessage());
        return Result.error(e.getCode(), e.getMessage());
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public Result<Void> handleValidationException(MethodArgumentNotValidException e) {
        String message = e.getBindingResult().getFieldErrors().stream()
            .map(error -> error.getField() + ": " + error.getDefaultMessage())
            .collect(Collectors.joining(", "));

        log.warn("参数验证失败: {}", message);
        return Result.error(ResultCode.INVALID_PARAMETER.getCode(), message);
    }
}
```

### 18.4 Chrome插件最佳实践
```javascript
// content.js - 内容脚本最佳实践
class IntelligentMatrixContent {
    constructor() {
        this.init();
    }

    init() {
        this.setupMessageListener();
        this.injectStyles();
        this.detectPageType();
    }

    setupMessageListener() {
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            try {
                switch(message.action) {
                    case 'captureScreen':
                        this.handleCaptureScreen(message, sendResponse);
                        return true; // 保持消息通道开放
                    case 'fillForm':
                        this.handleFillForm(message, sendResponse);
                        return true;
                    default:
                        sendResponse({success: false, error: 'Unknown action'});
                }
            } catch (error) {
                console.error('消息处理失败:', error);
                sendResponse({success: false, error: error.message});
            }
        });
    }

    async handleCaptureScreen(message, sendResponse) {
        try {
            // 显示截图选择界面
            const selection = await this.showScreenshotSelector();

            // 捕获选定区域
            const imageData = await this.captureArea(selection);

            // 上传并识别
            const result = await this.uploadAndRecognize(imageData, message);

            sendResponse({success: true, data: result});
        } catch (error) {
            sendResponse({success: false, error: error.message});
        }
    }
}

// 初始化
new IntelligentMatrixContent();
```

## 19. 配置管理规范

### 19.1 配置文件层次
```yaml
# application.yml - 基础配置
spring:
  profiles:
    active: ${SPRING_PROFILES_ACTIVE:dev}

---
# application-dev.yml - 开发环境
spring:
  config:
    activate:
      on-profile: dev
  datasource:
    url: *********************************************

---
# application-prod.yml - 生产环境
spring:
  config:
    activate:
      on-profile: prod
  datasource:
    url: ${DATABASE_URL}
```

### 19.2 敏感信息管理
```yaml
# 使用环境变量
app:
  jwt:
    secret: ${JWT_SECRET:default-secret}
  llm:
    providers:
      openai:
        api-key: ${OPENAI_API_KEY:}
      qianwen:
        api-key: ${QIANWEN_API_KEY:}
```

### 19.3 配置验证
```java
@ConfigurationProperties(prefix = "app.llm")
@Validated
@Data
public class LLMProperties {

    @NotBlank
    private String defaultProvider;

    @Min(1000)
    @Max(60000)
    private Integer timeout = 30000;

    @Valid
    private Map<String, ProviderConfig> providers = new HashMap<>();

    @Data
    @Validated
    public static class ProviderConfig {
        @NotBlank
        private String apiKey;

        @NotBlank
        private String baseUrl;

        @NotBlank
        private String model;
    }
}
```

## 20. 性能优化指南

### 20.1 数据库优化
```sql
-- 创建复合索引
CREATE INDEX idx_recognition_records_user_time
ON recognition_records(user_id, created_time);

-- 创建覆盖索引
CREATE INDEX idx_agents_status_category
ON agents(status, category_id)
INCLUDE (agent_name, description);

-- 分区表设计（按月分区）
CREATE TABLE recognition_records_2024_01
PARTITION OF recognition_records
FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');
```

### 20.2 缓存策略
```java
@Service
public class AgentCacheService {

    private final RedisTemplate<String, Object> redisTemplate;
    private static final String AGENT_CACHE_KEY = "agent:";
    private static final Duration CACHE_TTL = Duration.ofHours(1);

    @Cacheable(value = "agents", key = "#id", unless = "#result == null")
    public AgentVO getAgent(Long id) {
        // 查询逻辑
    }

    @CacheEvict(value = "agents", key = "#id")
    public void evictAgent(Long id) {
        // 清除缓存
    }

    // 预热缓存
    @PostConstruct
    public void warmUpCache() {
        List<Agent> hotAgents = agentMapper.selectHotAgents();
        hotAgents.forEach(agent -> {
            String key = AGENT_CACHE_KEY + agent.getId();
            redisTemplate.opsForValue().set(key, agent, CACHE_TTL);
        });
    }
}
```

### 20.3 异步处理
```java
@Service
@Slf4j
public class AsyncRecognitionService {

    @Async("taskExecutor")
    @Retryable(value = {Exception.class}, maxAttempts = 3)
    public CompletableFuture<RecognitionResult> recognizeAsync(
            Agent agent, RecognitionRequest request) {

        try {
            // 执行识别逻辑
            RecognitionResult result = performRecognition(agent, request);

            // 异步保存结果
            saveRecognitionRecord(result);

            return CompletableFuture.completedFuture(result);
        } catch (Exception e) {
            log.error("异步识别失败", e);
            throw e;
        }
    }
}
```

## 21. 安全最佳实践

### 21.1 输入验证
```java
@Data
@Validated
public class CreateAgentRequest {

    @NotBlank(message = "Agent名称不能为空")
    @Size(min = 2, max = 100, message = "Agent名称长度必须在2-100之间")
    @Pattern(regexp = "^[\\u4e00-\\u9fa5a-zA-Z0-9_-]+$",
             message = "Agent名称只能包含中文、英文、数字、下划线和横线")
    private String agentName;

    @NotNull(message = "分类ID不能为空")
    @Min(value = 1, message = "分类ID必须大于0")
    private Long categoryId;

    @Valid
    private AgentConfig config;
}
```

### 21.2 SQL注入防护
```java
// 正确的参数化查询
@Select("SELECT * FROM agents WHERE agent_name = #{agentName} AND status = #{status}")
List<Agent> findByNameAndStatus(@Param("agentName") String agentName,
                                @Param("status") Integer status);

// 动态SQL使用MyBatis-Plus
QueryWrapper<Agent> queryWrapper = new QueryWrapper<>();
queryWrapper.lambda()
    .eq(Agent::getAgentName, agentName)
    .eq(Agent::getStatus, status);
List<Agent> agents = agentMapper.selectList(queryWrapper);
```

### 21.3 XSS防护
```java
@Component
public class XssFilter implements Filter {

    @Override
    public void doFilter(ServletRequest request, ServletResponse response,
                        FilterChain chain) throws IOException, ServletException {

        XssHttpServletRequestWrapper wrappedRequest =
            new XssHttpServletRequestWrapper((HttpServletRequest) request);

        chain.doFilter(wrappedRequest, response);
    }
}

public class XssHttpServletRequestWrapper extends HttpServletRequestWrapper {

    @Override
    public String getParameter(String parameter) {
        return cleanXSS(super.getParameter(parameter));
    }

    private String cleanXSS(String value) {
        if (value == null) return null;

        // 移除脚本标签
        value = value.replaceAll("<script[^>]*>.*?</script>", "");
        value = value.replaceAll("javascript:", "");
        value = value.replaceAll("vbscript:", "");

        return value;
    }
}
```

---

**文档版本**: 1.0.0
**最后更新**: 2024-06-23
**维护团队**: 智能体矩阵开发团队
**联系方式**: <EMAIL>

> 本文档将随着项目发展持续更新，请开发团队定期查阅最新版本。
