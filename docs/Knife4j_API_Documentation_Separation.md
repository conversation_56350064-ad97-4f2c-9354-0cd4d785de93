# Knife4j API文档分离配置说明

## 概述

本文档说明了如何使用Knife4j将公开API接口和内部接口文档分开显示，提供更好的用户体验和文档管理。

## 实现方案

### 1. 配置文件结构

```
src/main/java/com/sinoair/agent/config/
├── Knife4jConfig.java          # 全局Knife4j配置
├── PublicApiConfig.java        # 公开API分组配置
└── InternalApiConfig.java      # 内部管理API分组配置
```

### 2. 核心配置类

#### Knife4jConfig.java - 全局配置
- 提供全局OpenAPI配置
- 统一管理服务器信息和基础配置
- 启用Knife4j增强功能

#### PublicApiConfig.java - 公开API配置
- **分组名称**: `public`
- **显示名称**: `🌐 公开API`
- **路径匹配**: `/api/v1/public/**`
- **认证方式**: API Key
- **目标用户**: 第三方开发者

#### InternalApiConfig.java - 内部管理API配置
- **分组名称**: `internal`
- **显示名称**: `🔧 内部管理API`
- **路径匹配**: `/api/v1/**`（排除公开API）
- **认证方式**: JWT Bearer Token
- **目标用户**: 内部管理人员

### 3. 应用配置增强

在 `application.yml` 中增强了Knife4j配置：

```yaml
knife4j:
  enable: true
  setting:
    language: zh_cn                    # 中文界面
    enable-version: true               # 显示版本信息
    enable-reload-cache-parameter: true # 启用缓存刷新
    enable-after-script: true          # 启用后置脚本
    enable-request-cache: true         # 启用请求缓存
    enable-home-custom: true           # 启用自定义首页
    home-custom-path: classpath:markdown/home.md
    enable-swagger-models: true        # 显示实体类模型
    enable-document-manage: true       # 启用文档管理
    swagger-model-name: 实体类列表      # 实体类列表名称
    enable-search: true                # 启用搜索功能
    enable-footer: false               # 禁用页脚
    enable-footer-custom: true         # 启用自定义页脚
    footer-custom-content: "© 2024 SinoairAgent Team. All rights reserved."
  cors: true                          # 启用CORS
  production: false                   # 开发模式
```

### 4. 自定义首页

创建了 `src/main/resources/markdown/home.md` 自定义首页，包含：
- API分组说明
- 快速链接
- 使用流程指南
- 联系方式

## 访问方式

### 开发环境访问地址
- **Knife4j文档**: http://localhost:8080/doc.html
- **Swagger UI**: http://localhost:8080/swagger-ui.html

### 分组选择
在Knife4j界面右上角的下拉菜单中可以选择不同的API分组：
- 🌐 公开API
- 🔧 内部管理API

## 功能特性

### 公开API文档特性
- ✅ API Key认证说明
- ✅ 详细的使用指南
- ✅ 客户门户链接
- ✅ 技术支持信息
- ✅ 使用限制说明

### 内部管理API文档特性
- ✅ JWT认证说明
- ✅ 内部功能模块介绍
- ✅ 示例用户信息
- ✅ 开发环境配置
- ✅ 内部技术支持

### Knife4j增强功能
- ✅ 中文界面
- ✅ 自定义首页
- ✅ 搜索功能
- ✅ 请求缓存
- ✅ 版本信息显示
- ✅ 实体类模型展示

## 优势

1. **清晰分离**: 公开API和内部API文档完全分离，避免混淆
2. **用户友好**: 针对不同用户群体提供定制化的文档体验
3. **安全性**: 内部API文档不会暴露给外部用户
4. **易于维护**: 配置结构清晰，便于后续维护和扩展
5. **美观界面**: 使用Knife4j提供的美观界面，提升用户体验

## 注意事项

1. 确保Controller类使用正确的`@Tag`注解进行分类
2. 公开API Controller应该放在`/api/v1/public/**`路径下
3. 内部API Controller应该放在`/api/v1/**`路径下（排除public）
4. 认证配置需要与Security配置保持一致

## 后续扩展

可以根据需要添加更多的API分组，例如：
- 插件API分组
- 移动端API分组
- 第三方集成API分组

每个分组都可以有独立的配置和文档说明。
