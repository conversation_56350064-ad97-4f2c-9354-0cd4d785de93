# FileParseService 集成 AgentCallService 说明

## 修改概述

我已经修改了 `FileParseService`，使其能够真正调用现有的 `AgentCallService` 来执行文件解析，而不是使用模拟数据。

## 主要修改内容

### 1. 核心调用流程

```java
// 原来的模拟调用
Map<String, Object> parsedContent = simulateFileParsing(file);

// 修改后的真实调用
Map<String, Object> parsedContent = callAgentForParsing(agent, file, userId);
```

### 2. 集成 AgentCallService

#### 新增方法：`callAgentCallService()`
```java
private String callAgentCallService(Agent agent, MultipartFile file) {
    // 构建 AgentCallRequest
    AgentCallRequest request = AgentCallRequest.builder()
        .agentCode(agent.getAgentCode())
        .file(file)
        .businessParams(null)
        .build();

    // 调用 AgentCallService
    Result<AgentCallResponse> result = agentCallService.recognizeDocument(request);
    
    // 处理同步/异步结果
    if (result.isSuccess()) {
        AgentCallResponse response = result.getData();
        if ("PROCESSING".equals(response.getStatus())) {
            return waitForAsyncResult(response.getTaskId(), agent.getAgentCode());
        } else {
            return response.getResult();
        }
    }
}
```

#### 新增方法：`waitForAsyncResult()`
```java
private String waitForAsyncResult(String taskId, String agentCode) {
    // 轮询等待异步结果，最多等待60秒
    // 每2秒查询一次任务状态
    // 支持 SUCCESS/FAILED/PROCESSING 状态处理
}
```

### 3. 改进结果解析

#### 增强的 `parseAgentResult()` 方法
```java
private Map<String, Object> parseAgentResult(String agentResult, Agent agent) {
    // 1. 空结果检查
    // 2. JSON解析
    // 3. 添加元数据（agentCode, agentName, processedAt）
    // 4. 解析失败时的降级处理（返回文本结果）
}
```

### 4. 错误处理改进

- **网络错误**：捕获 AgentCallService 调用异常
- **超时处理**：异步结果等待超时机制
- **解析错误**：JSON解析失败时的降级处理
- **状态错误**：处理 Agent 返回的失败状态

## 调用流程图

```
PublicApiController.parseFile()
    ↓
FileParseService.parseFileForPublicApi()
    ↓
handleSyncParsing() / handleAsyncParsing()
    ↓
callAgentForParsing()
    ↓
callAgentCallService()
    ↓
AgentCallService.recognizeDocument()
    ↓
[如果异步] waitForAsyncResult()
    ↓
parseAgentResult()
    ↓
返回结构化结果
```

## 支持的功能

### 1. 同步处理
- 直接调用 AgentCallService
- 如果 Agent 返回异步任务，自动等待结果
- 实时返回解析结果

### 2. 异步处理
- 在后台线程中调用 AgentCallService
- 支持回调通知
- 任务状态存储在 Redis 中

### 3. 错误恢复
- Agent 调用失败时返回错误信息
- JSON 解析失败时返回原始文本
- 超时处理和重试机制

## 配置要求

### 1. 依赖注入
```java
@RequiredArgsConstructor
public class FileParseService {
    private final AgentCallService agentCallService;  // 新增依赖
    private final RedisTemplate<String, Object> redisTemplate;
    private final ObjectMapper objectMapper;
}
```

### 2. Agent 状态要求
- Agent 必须处于 `STATUS_PUBLISHED` 状态
- Agent 必须配置正确的识别逻辑

## 使用示例

### 1. 同步调用
```bash
curl -X POST "/api/v1/public/files/parse" \
  -H "X-API-Key: your_key" \
  -F "agentId=agent_001" \
  -F "file=@document.pdf" \
  -F "async=false"
```

**响应**：
```json
{
  "code": 200,
  "message": "文件解析成功",
  "data": {
    "taskId": "task_xxx",
    "agentId": "agent_001",
    "agentName": "文档解析Agent",
    "fileName": "document.pdf",
    "fileSize": "1.2MB",
    "parsedContent": {
      "title": "合同文档",
      "content": "...",
      "extractedData": {...},
      "agentCode": "agent_001",
      "processedAt": "2024-01-22T15:30:00"
    },
    "processingTime": "3.2s"
  }
}
```

### 2. 异步调用
```bash
curl -X POST "/api/v1/public/files/parse" \
  -H "X-API-Key: your_key" \
  -F "agentId=agent_001" \
  -F "file=@large-document.pdf" \
  -F "async=true" \
  -F "callbackUrl=https://your-domain.com/webhook"
```

**立即响应**：
```json
{
  "code": 200,
  "message": "文件正在处理中",
  "data": {
    "taskId": "task_xxx",
    "status": "processing",
    "estimatedTime": "30s",
    "statusUrl": "/api/v1/public/files/parse/status/task_xxx"
  }
}
```

## 性能优化

### 1. 异步处理优势
- 避免长时间HTTP连接
- 支持大文件处理
- 更好的并发能力

### 2. 缓存机制
- Redis 存储任务状态
- 7天自动过期
- 支持任务进度查询

### 3. 错误处理
- 自动重试机制
- 降级处理策略
- 详细的错误日志

## 总结

通过这次修改，`FileParseService` 现在能够：

1. **真实调用**：集成现有的 `AgentCallService`，执行真正的文件解析
2. **异步支持**：完整的异步处理流程，包括状态查询和回调
3. **错误处理**：完善的错误处理和降级机制
4. **性能优化**：支持大文件处理和高并发场景

这样的设计既保持了公开API的简洁性，又充分利用了现有的Agent基础设施，为客户提供了强大而可靠的文件解析服务。
