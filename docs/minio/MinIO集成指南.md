# MinIO集成指南

## 概述

SinoairAgent平台集成了MinIO对象存储服务，提供高性能、可扩展的文件存储解决方案。本文档详细介绍了MinIO组件的配置、使用和测试方法。

## 功能特性

- **多存储支持**: 支持MinIO和本地存储两种方式
- **自动切换**: 根据配置自动选择存储方式
- **完整API**: 提供文件上传、下载、删除、预览等完整功能
- **预签名URL**: 支持生成临时访问链接
- **存储桶管理**: 支持存储桶的创建和管理
- **元数据支持**: 支持文件元数据的存储和检索
- **并发安全**: 支持多线程并发操作
- **异常处理**: 完善的错误处理和日志记录

## 配置说明

### 1. 基础配置

在 `application.yml` 中添加MinIO配置：

```yaml
app:
  minio:
    # 是否启用MinIO存储，false时使用本地存储
    enabled: true
    # MinIO服务端点
    endpoint: http://*************:9000
    # 访问密钥
    access-key: minioadmin
    # 秘密密钥
    secret-key: minioadmin
    # 存储桶名称
    bucket-name: sinoair-agent
    # 区域
    region: us-east-1
    # 连接超时时间（毫秒）
    connect-timeout: 10000
    # 写入超时时间（毫秒）
    write-timeout: 60000
    # 读取超时时间（毫秒）
    read-timeout: 10000
```

### 2. 环境变量配置

支持通过环境变量覆盖配置：

```bash
export MINIO_ENDPOINT=http://localhost:9000
export MINIO_ACCESS_KEY=your-access-key
export MINIO_SECRET_KEY=your-secret-key
export MINIO_BUCKET_NAME=your-bucket
export MINIO_ENABLED=true
```

### 3. 禁用MinIO

如果需要禁用MinIO使用本地存储：

```yaml
app:
  minio:
    enabled: false
```

## 核心组件

### 1. MinioProperties

配置属性类，管理MinIO相关配置参数。

### 2. MinioConfig

配置类，创建和配置MinIO客户端。

### 3. MinioService

核心服务类，提供所有MinIO操作功能：

- `uploadFile()`: 上传文件
- `downloadFile()`: 下载文件
- `deleteFile()`: 删除文件
- `getFileInfo()`: 获取文件信息
- `generatePresignedUrl()`: 生成预签名URL
- `listFiles()`: 列出文件
- `fileExists()`: 检查文件是否存在
- `createBucketIfNotExists()`: 创建存储桶
- `listBuckets()`: 列出存储桶

### 4. FileService

文件服务类，集成MinIO和本地存储，根据配置自动选择存储方式。

## API接口

### 1. 文件管理接口

```http
# 上传文件
POST /api/v1/files/upload
Content-Type: multipart/form-data

# 下载文件
GET /api/v1/files/{id}/download

# 预览文件
GET /api/v1/files/{id}/preview

# 获取文件信息
GET /api/v1/files/{id}/info

# 删除文件
DELETE /api/v1/files/{id}
```

### 2. MinIO专用接口

```http
# 列出文件
GET /api/v1/minio/files?prefix=test/

# 获取文件信息
GET /api/v1/minio/files/{objectKey}/info

# 检查文件是否存在
GET /api/v1/minio/files/{objectKey}/exists

# 生成预签名URL
POST /api/v1/minio/files/{objectKey}/presigned-url

# 删除文件
DELETE /api/v1/minio/files/{objectKey}

# 列出存储桶
GET /api/v1/minio/buckets

# 创建存储桶
POST /api/v1/minio/buckets/{bucketName}
```

## 使用示例

### 1. Java代码示例

```java
@Autowired
private FileService fileService;

@Autowired
private MinioService minioService;

// 上传文件
public void uploadExample(MultipartFile file) {
    Result<FileUploadResponse> result = fileService.uploadFile(file, "documents");
    if (result.isSuccess()) {
        FileUploadResponse response = result.getData();
        System.out.println("文件上传成功: " + response.getFileId());
    }
}

// 直接使用MinIO服务
public void minioExample(MultipartFile file) throws Exception {
    String objectKey = "test/example.txt";
    
    // 上传文件
    MinioUploadResponse response = minioService.uploadFile(file, objectKey);
    
    // 生成预签名URL
    String presignedUrl = minioService.generatePresignedUrl(
        objectKey, Method.GET, 24, TimeUnit.HOURS);
    
    // 下载文件
    try (InputStream inputStream = minioService.downloadFile(objectKey)) {
        // 处理文件流
    }
    
    // 删除文件
    minioService.deleteFile(objectKey);
}
```

### 2. 前端调用示例

```javascript
// 上传文件
async function uploadFile(file, businessType) {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('businessType', businessType);
    
    const response = await fetch('/api/v1/files/upload', {
        method: 'POST',
        body: formData
    });
    
    return await response.json();
}

// 下载文件
function downloadFile(fileId, filename) {
    const link = document.createElement('a');
    link.href = `/api/v1/files/${fileId}/download`;
    link.download = filename;
    link.click();
}

// 预览文件
function previewFile(fileId) {
    window.open(`/api/v1/files/${fileId}/preview`, '_blank');
}
```

## 测试

### 1. 单元测试

运行MinIO服务的单元测试：

```bash
mvn test -Dtest=MinioServiceTest
```

### 2. 集成测试

需要真实的MinIO服务器环境：

```bash
# 设置环境变量启用集成测试
export MINIO_INTEGRATION_TEST=true

# 运行集成测试
mvn test -Dtest=MinioIntegrationTest
```

### 3. 配置测试

测试MinIO配置：

```bash
mvn test -Dtest=MinioConfigTest
```

## 部署指南

### 1. Docker部署MinIO

```bash
# 启动MinIO服务器
docker run -d \
  --name minio \
  -p 9000:9000 \
  -p 9001:9001 \
  -e MINIO_ROOT_USER=minioadmin \
  -e MINIO_ROOT_PASSWORD=minioadmin \
  -v /data/minio:/data \
  minio/minio server /data --console-address ":9001"
```

### 2. 生产环境配置

```yaml
app:
  minio:
    enabled: true
    endpoint: https://minio.your-domain.com
    access-key: ${MINIO_ACCESS_KEY}
    secret-key: ${MINIO_SECRET_KEY}
    bucket-name: sinoair-agent-prod
    region: us-east-1
    connect-timeout: 10000
    write-timeout: 120000
    read-timeout: 30000
```

## 监控和维护

### 1. 健康检查

MinIO服务会在启动时自动初始化存储桶，可以通过日志查看状态：

```
INFO  - MinIO存储桶初始化完成: sinoair-agent
```

### 2. 错误处理

所有MinIO操作都有完善的异常处理和日志记录：

```java
try {
    minioService.uploadFile(file, objectKey);
} catch (RuntimeException e) {
    log.error("MinIO操作失败", e);
    // 处理错误
}
```

### 3. 性能优化

- 调整超时时间以适应网络环境
- 使用连接池优化并发性能
- 合理设置存储桶策略

## 故障排除

### 1. 连接问题

- 检查MinIO服务器是否运行
- 验证网络连接和防火墙设置
- 确认访问密钥和秘密密钥正确

### 2. 权限问题

- 检查MinIO用户权限
- 验证存储桶策略设置
- 确认应用程序权限配置

### 3. 性能问题

- 调整超时时间设置
- 检查网络带宽和延迟
- 优化文件大小和并发数

## 最佳实践

1. **安全性**: 使用强密码和HTTPS连接
2. **备份**: 定期备份重要数据
3. **监控**: 监控存储使用情况和性能指标
4. **版本控制**: 启用对象版本控制
5. **生命周期**: 配置对象生命周期策略
6. **访问控制**: 使用最小权限原则
7. **日志记录**: 启用详细的操作日志
