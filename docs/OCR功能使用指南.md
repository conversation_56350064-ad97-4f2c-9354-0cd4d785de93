# OCR图片文字识别功能使用指南

## 功能概述

智能体矩阵平台现已集成Tesseract OCR引擎，支持从图片中识别和提取文字内容。该功能可以处理多种图片格式，并支持中英文混合识别。

## 支持的功能特性

### 1. 支持的图片格式
- **JPG/JPEG** - 最常用的图片格式
- **PNG** - 支持透明背景的图片
- **BMP** - Windows位图格式
- **TIFF/TIF** - 高质量图片格式
- **GIF** - 动图格式（识别第一帧）
- **WebP** - 现代Web图片格式

### 2. 语言支持
- **中文简体** (chi_sim)
- **英文** (eng)
- **中英文混合识别**

### 3. 图片预处理
- **自动缩放** - 提高小图片识别率
- **灰度处理** - 提升识别准确性
- **降噪处理** - 减少图片噪点影响

## 配置说明

### 1. 应用配置 (application.yml)

```yaml
app:
  ocr:
    tesseract:
      # Tesseract数据路径（可选，留空使用系统默认）
      data-path: 
      # 识别语言配置
      language: chi_sim+eng
      # OCR引擎模式
      oem: 3
      # 页面分割模式
      psm: 6
      # 图片预处理配置
      preprocessing:
        enabled: true
        scale-factor: 2.0
        grayscale: true
        denoise: true
    # 支持的图片格式
    supported-formats:
      - jpg
      - jpeg
      - png
      - bmp
      - tiff
      - gif
      - webp
    # 最大文件大小（MB）
    max-file-size: 10
```

### 2. Tesseract安装

#### Windows环境
1. 下载Tesseract安装包：https://github.com/UB-Mannheim/tesseract/wiki
2. 安装到默认路径：`C:\Program Files\Tesseract-OCR`
3. 下载中文语言包：https://github.com/tesseract-ocr/tessdata
4. 将语言包文件放到：`C:\Program Files\Tesseract-OCR\tessdata`

#### Linux环境
```bash
# Ubuntu/Debian
sudo apt-get update
sudo apt-get install tesseract-ocr tesseract-ocr-chi-sim

# CentOS/RHEL
sudo yum install tesseract tesseract-langpack-chi-sim
```

#### Docker环境
```dockerfile
FROM openjdk:17-jdk-slim

# 安装Tesseract
RUN apt-get update && \
    apt-get install -y tesseract-ocr tesseract-ocr-chi-sim && \
    rm -rf /var/lib/apt/lists/*

# 复制应用
COPY target/sinoair-agent-1.0.0.jar app.jar

ENTRYPOINT ["java", "-jar", "/app.jar"]
```

## API接口使用

### 1. OCR文字识别接口

**接口地址：** `POST /api/v1/ocr/recognize`

**请求参数：**
- `file` (MultipartFile) - 图片文件

**响应示例：**
```json
{
  "code": 200,
  "message": "OCR识别成功",
  "data": "识别出的文字内容",
  "timestamp": "2024-01-01T12:00:00"
}
```

**使用示例：**
```javascript
const formData = new FormData();
formData.append('file', imageFile);

fetch('/api/v1/ocr/recognize', {
    method: 'POST',
    body: formData
})
.then(response => response.json())
.then(data => {
    if (data.success) {
        console.log('识别结果:', data.data);
    } else {
        console.error('识别失败:', data.message);
    }
});
```

### 2. 服务状态检查接口

**接口地址：** `GET /api/v1/ocr/status`

**响应示例：**
```json
{
  "code": 200,
  "message": "OCR服务可用",
  "data": true,
  "timestamp": "2024-01-01T12:00:00"
}
```

## 集成到文档解析服务

OCR功能已自动集成到现有的`DocumentParserService`中，支持以下场景：

### 1. 通过Agent调用
```java
// 在Agent识别请求中，如果上传的是图片文件，会自动使用OCR识别
RecognitionRequest request = new RecognitionRequest();
request.setFileId(imageFileId);

RecognitionResult result = llmService.recognize(agent, request);
```

### 2. 直接调用文档解析服务
```java
@Autowired
private DocumentParserService documentParserService;

// 解析图片文件
Result<String> result = documentParserService.parseDocument(imageFileId);
if (result.isSuccess()) {
    String recognizedText = result.getData();
    // 处理识别结果
}
```

## 测试页面

访问 `http://localhost:8080/test-ocr.html` 可以使用内置的OCR测试页面：

1. **服务状态检查** - 确认OCR服务是否正常运行
2. **图片上传** - 支持点击选择或拖拽上传
3. **实时预览** - 上传后立即显示图片预览
4. **一键识别** - 点击按钮执行OCR识别
5. **结果展示** - 显示识别的文字内容和统计信息

## 性能优化建议

### 1. 图片质量
- **分辨率**：建议300DPI以上
- **对比度**：文字与背景对比度要高
- **清晰度**：避免模糊、倾斜的图片
- **格式**：推荐使用PNG或TIFF格式

### 2. 系统配置
- **内存**：建议至少2GB可用内存
- **CPU**：多核CPU可提升处理速度
- **存储**：SSD硬盘可提升I/O性能

### 3. 参数调优
```yaml
app:
  ocr:
    tesseract:
      # 根据图片类型调整页面分割模式
      psm: 6  # 6=统一文本块，7=单一文本行，8=单一单词
      # 根据需要调整OCR引擎模式
      oem: 3  # 3=默认，基于LSTM的OCR引擎
```

## 常见问题

### 1. 识别准确率低
- 检查图片质量和清晰度
- 尝试调整预处理参数
- 考虑使用不同的页面分割模式

### 2. 服务启动失败
- 确认Tesseract已正确安装
- 检查语言包是否存在
- 验证系统环境变量配置

### 3. 内存不足
- 增加JVM堆内存设置
- 限制并发处理数量
- 优化图片大小限制

### 4. 中文识别效果差
- 确认已安装中文语言包
- 检查语言配置是否正确
- 尝试使用繁体中文语言包

## 更新日志

### v1.0.0 (2024-01-01)
- ✅ 集成Tesseract OCR引擎
- ✅ 支持多种图片格式识别
- ✅ 中英文混合识别
- ✅ 图片预处理功能
- ✅ REST API接口
- ✅ Web测试页面
- ✅ 集成到文档解析服务
- ✅ 数据库支持和日志记录
