# Agent文件解析API接口文档

## 项目信息
- **项目名称**: Agent文件解析服务
- **版本**: v1.0.0
- **基础URL**: `https://api.yourcompany.com/v1/public`

## 认证方式
- **类型**: API Key
- **Header**: `X-API-Key: {your_api_key}`
- **格式**: `keyId.keySecret`
- **API Key获取**: 通过客户门户网站申请获取
- **有效期**: 根据申请时设置的过期时间

### API Key申请流程
1. 访问客户门户网站：`https://portal.yourcompany.com`
2. 注册账号并完成邮箱验证
3. 登录后进入控制台，点击"创建API Key"
4. 填写API Key名称和描述，设置过期时间（可选）
5. 创建成功后，请立即复制并保存完整的API Key
6. 在API请求中使用该API Key进行认证

### 认证方式说明
支持以下三种方式传递API Key：

1. **HTTP Header（推荐）**：
   ```
   X-API-Key: ak_1234567890abcdef.abcdef1234567890abcdef1234567890abcdef12
   ```

2. **Authorization Header**：
   ```
   Authorization: Bearer ak_1234567890abcdef.abcdef1234567890abcdef1234567890abcdef12
   ```

3. **查询参数**：
   ```
   https://api.yourcompany.com/v1/public/agents?api_key=ak_1234567890abcdef.abcdef1234567890abcdef1234567890abcdef12
   ```

---

## 1. Agent列表查询接口

### 接口概述
获取所有可用的Agent列表，用于获取AgentId供文件解析接口使用。

### 请求信息
- **HTTP方法**: `GET`
- **请求URL**: `/agents`
- **Content-Type**: `application/json`

### 请求参数
#### Query Parameters
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| page | int | 否 | 1 | 页码，从1开始 |
| size | int | 否 | 10 | 每页数量，最大100 |
| agentId | string | 否 | - | 指定Agent ID，用于查询特定Agent信息 |

### 响应信息
#### 成功响应 (200 OK)
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 25,
    "page": 1,
    "size": 10,
    "agents": [
      {
        "agentId": "agent_001",
        "name": "PDF文档解析Agent",
        "description": "专门用于解析PDF文档内容",
        "supportedFileTypes": ["pdf"],
        "maxFileSize": "10MB"
      },
      {
        "agentId": "agent_002",
        "name": "图片识别Agent",
        "description": "用于识别和解析图片中的文字和内容",
        "supportedFileTypes": ["jpg", "jpeg", "png", "gif"],
        "maxFileSize": "10MB"
      }
    ]
  },
  "timestamp": "2024-01-22T15:30:00Z"
}
```

---

## 2. 文件解析接口

### 接口概述
根据指定的AgentId上传单个文件，调用对应的Agent解析文件内容并返回JSON格式的结构化数据。注意：一次只能上传一个文件进行解析。

**异步处理建议**：
- 📁 **文件大小 > 1MB**：强烈建议使用异步模式
- ⏱️ **响应时间要求 < 10秒**：建议使用异步模式
- 🔄 **批量处理场景**：必须使用异步模式
- 📱 **移动端应用**：建议使用异步模式避免超时

### 请求信息
- **HTTP方法**: `POST`
- **请求URL**: `/files/parse`
- **Content-Type**: `multipart/form-data`

### 请求参数
#### Form Data Parameters
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| agentId | string | 是 | Agent标识符，通过Agent List接口获取 |
| file | file | 是 | 上传的文件 |
| async | boolean | 否 | 是否异步处理，**默认true**。推荐大文件使用异步模式 |
| callbackUrl | string | 否 | 异步模式下的回调地址，解析完成后会POST结果到此URL |
| priority | string | 否 | 任务优先级：high/normal/low，默认normal |

#### 文件限制
- **支持格式**: PDF (.pdf), 图片 (.jpg, .jpeg, .png, .gif)
- **文件大小**: 最大5MB（所有格式统一限制）
- **文件名**: 支持中英文，不超过255个字符
- **上传限制**: 一次只能上传一个文件，不支持批量上传



### 响应信息

#### 同步处理成功响应 (async=false, 200 OK)
```json
{
  "code": 200,
  "message": "File parsed successfully",
  "data": {
    "taskId": "task_20240122_001",
    "agentId": "agent_001",
    "fileName": "document.pdf",
    "fileSize": "2.5MB",
    "parsedContent": {
      "title": "项目需求文档",
      "author": "张三",
      "createdDate": "2024-01-20",
      "content": [
        {
          "type": "text",
          "page": 1,
          "content": "这是文档的主要内容..."
        },
        {
          "type": "table",
          "page": 2,
          "headers": ["项目名称", "负责人", "截止日期"],
          "rows": [
            ["项目A", "李四", "2024-02-01"],
            ["项目B", "王五", "2024-02-15"]
          ]
        }
      ],
      "metadata": {
        "totalPages": 5,
        "wordCount": 1250,
        "language": "zh-cn"
      }
    },
    "processingTime": "3.2s",
    "timestamp": "2024-01-22T15:30:00Z"
  }
}
```

#### 异步处理响应 (async=true, 202 Accepted)
```json
{
  "code": 202,
  "message": "File is being processed",
  "data": {
    "taskId": "task_20240122_001",
    "status": "processing",
    "estimatedTime": "30s",
    "statusUrl": "/files/parse/status/task_20240122_001"
  },
  "timestamp": "2024-01-22T15:30:00Z"
}
```

#### 错误响应示例
```json
{
  "code": 400,
  "message": "File format not supported",
  "data": {
    "error": "UNSUPPORTED_FILE_FORMAT",
    "details": "Only PDF and image files are supported",
    "supportedFormats": ["pdf", "jpg", "jpeg", "png", "gif"]
  },
  "timestamp": "2024-01-22T15:30:00Z"
}
```

---

## 3. 解析状态查询接口

### 接口概述
查询异步文件解析任务的状态和结果。

### 请求信息
- **HTTP方法**: `GET`
- **请求URL**: `/files/parse/status/{taskId}`

### 请求参数
#### Path Parameters
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| taskId | string | 是 | 任务ID |

### 响应信息
#### 成功响应 (200 OK)
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "taskId": "task_20240122_001",
    "status": "completed",
    "progress": 100,
    "result": {
      // 解析结果，格式同文件解析接口的成功响应
    }
  },
  "timestamp": "2024-01-22T15:30:00Z"
}
```

---

## 状态码说明
| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 202 | 文件正在处理中（异步处理） |
| 400 | 请求参数错误或文件格式不支持 |
| 401 | 未授权访问或Token无效 |
| 413 | 文件大小超出限制 |
| 422 | 文件内容无法解析 |
| 500 | 服务器内部错误 |

---

## 错误码对照表

| 错误码 | HTTP状态码 | 说明 |
|--------|------------|------|
| 10001 | 400 | 缺少必填参数 |
| 10002 | 400 | 参数格式错误 |
| 10003 | 400 | AgentId不存在 |
| 10004 | 400 | 文件格式不支持 |
| 10005 | 413 | 文件大小超出限制 |
| 10006 | 422 | 文件内容损坏或无法解析 |
| 20001 | 401 | Token无效或已过期 |
| 20002 | 403 | 权限不足 |
| 30001 | 500 | Agent服务不可用 |
| 30002 | 500 | 文件存储服务异常 |

---

## 使用示例

### 1. 获取Agent列表
```bash
curl -X GET "https://api.yourcompany.com/v1/public/agents" \
  -H "X-API-Key: ak_1234567890abcdef.abcdef1234567890abcdef1234567890abcdef12" \
  -H "Content-Type: application/json"
```

### 2. 同步文件解析
```bash
curl -X POST "https://api.yourcompany.com/v1/public/files/parse" \
  -H "X-API-Key: ak_1234567890abcdef.abcdef1234567890abcdef1234567890abcdef12" \
  -F "agentId=agent_001" \
  -F "file=@/path/to/document.pdf" \
  -F "async=false"
```

### 3. 异步文件解析（推荐）
```bash
# 基础异步解析
curl -X POST "https://api.yourcompany.com/v1/public/files/parse" \
  -H "X-API-Key: ak_1234567890abcdef.abcdef1234567890abcdef1234567890abcdef12" \
  -F "agentId=agent_001" \
  -F "file=@/path/to/document.pdf" \
  -F "async=true"

# 带回调的异步解析
curl -X POST "https://api.yourcompany.com/v1/public/files/parse" \
  -H "X-API-Key: ak_1234567890abcdef.abcdef1234567890abcdef1234567890abcdef12" \
  -F "agentId=agent_001" \
  -F "file=@/path/to/large-document.pdf" \
  -F "async=true" \
  -F "callbackUrl=https://your-domain.com/webhook/parse-result" \
  -F "priority=high"
```

### 4. 查询解析状态
```bash
curl -X GET "https://api.yourcompany.com/v1/public/files/parse/status/task_20240122_001" \
  -H "X-API-Key: ak_1234567890abcdef.abcdef1234567890abcdef1234567890abcdef12"
```

### 5. 使用Authorization Header方式
```bash
curl -X GET "https://api.yourcompany.com/v1/public/agents" \
  -H "Authorization: Bearer ak_1234567890abcdef.abcdef1234567890abcdef1234567890abcdef12" \
  -H "Content-Type: application/json"
```

### 6. 使用查询参数方式
```bash
curl -X GET "https://api.yourcompany.com/v1/public/agents?api_key=ak_1234567890abcdef.abcdef1234567890abcdef1234567890abcdef12"
```

---

## 注意事项

1. **API Key申请**: 请访问客户门户网站 `https://portal.yourcompany.com` 注册账号并申请API Key
2. **API Key安全**: 请妥善保管您的API Key，不要在客户端代码中硬编码或公开分享
3. **文件大小限制**: 请确保上传的文件不超过5MB
4. **处理模式选择**:
   - 🚀 **异步模式（推荐）**: 适用于所有场景，特别是大文件（>1MB）
   - ⚡ **同步模式**: 仅适用于小文件（<1MB）且对实时性要求极高的场景
   - 📊 **性能对比**: 异步模式可处理更大文件，避免超时，支持并发
5. **单文件处理**: 每次请求只能上传一个文件，如需处理多个文件请分别调用
6. **请求限制**: 每个API Key有不同的请求频率限制，请查看您的配额设置
7. **任务保留**: 解析任务结果保留7天，过期后自动删除
8. **重试机制**: 如果解析失败，系统会自动重试最多3次
9. **IP限制**: 如果设置了IP白名单，请确保从允许的IP地址发起请求
10. **过期时间**: 请注意API Key的过期时间，过期后需要重新创建

---

## 联系方式

如有问题或需要技术支持，请联系：
- **客户门户**: https://portal.yourcompany.com
- **技术支持邮箱**: <EMAIL>
- **销售咨询**: <EMAIL>
- **文档更新**: 请关注本文档的最新版本

## 快速开始

1. **注册账号**: 访问 https://portal.yourcompany.com 注册账号
2. **创建API Key**: 登录后在控制台创建您的第一个API Key
3. **测试接口**: 使用API Key调用Agent列表接口进行测试
4. **集成开发**: 参考本文档进行API集成开发

## 版本更新

### v1.0.0 (2024-01-22)
- 初始版本发布
- 支持API Key认证方式
- 提供Agent列表查询和文件解析功能
- 支持同步和异步处理模式
