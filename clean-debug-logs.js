// 清理调试日志的脚本
const fs = require('fs');
const path = require('path');

const filePath = 'src/main/resources/static/assets/js/smart-form-config.js';

// 读取文件内容
let content = fs.readFileSync(filePath, 'utf8');

// 定义要移除的调试日志模式
const debugPatterns = [
    // 移除包含emoji的console.log
    /^\s*console\.log\([^)]*[🎯✅❌🔍🎬📊🌐🎭📝🔄⏰🚀🏗️🧹🔧🎨🎉📋🔍⚠️🎧🧪]/gm,
    // 移除包含emoji的console.error和console.warn
    /^\s*console\.(error|warn)\([^)]*[🎯✅❌🔍🎬📊🌐🎭📝🔄⏰🚀🏗️🧹🔧🎨🎉📋🔍⚠️🎧🧪]/gm,
    // 移除空的console.log行
    /^\s*console\.log\(\s*\);\s*$/gm,
    // 移除简单的状态检查日志
    /^\s*console\.log\('.*状态.*'\);\s*$/gm,
    /^\s*console\.log\('.*检查.*'\);\s*$/gm,
    /^\s*console\.log\('.*开始.*'\);\s*$/gm,
    /^\s*console\.log\('.*完成.*'\);\s*$/gm,
    /^\s*console\.log\('.*成功.*'\);\s*$/gm,
    /^\s*console\.log\('.*失败.*'\);\s*$/gm,
];

// 应用所有模式
debugPatterns.forEach(pattern => {
    content = content.replace(pattern, '');
});

// 移除多余的空行（连续的空行合并为单个空行）
content = content.replace(/\n\s*\n\s*\n/g, '\n\n');

// 写回文件
fs.writeFileSync(filePath, content, 'utf8');

console.log('调试日志清理完成！');
